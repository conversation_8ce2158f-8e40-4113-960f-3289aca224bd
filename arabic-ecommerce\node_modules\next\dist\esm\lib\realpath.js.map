{"version": 3, "sources": ["../../src/lib/realpath.ts"], "sourcesContent": ["import fs from 'fs'\n\nconst isWindows = process.platform === 'win32'\n\n// Interesting learning from this, that fs.realpathSync is 70x slower than fs.realpathSync.native:\n// https://sun0day.github.io/blog/vite/why-vite4_3-is-faster.html#fs-realpathsync-issue\n// https://github.com/nodejs/node/issues/2680\n// However, we can't use fs.realpathSync.native on Windows due to behavior differences.\nexport const realpathSync = isWindows ? fs.realpathSync : fs.realpathSync.native\n"], "names": ["fs", "isWindows", "process", "platform", "realpathSync", "native"], "mappings": "AAAA,OAAOA,QAAQ,KAAI;AAEnB,MAAMC,YAAYC,QAAQC,QAAQ,KAAK;AAEvC,kGAAkG;AAClG,uFAAuF;AACvF,6CAA6C;AAC7C,uFAAuF;AACvF,OAAO,MAAMC,eAAeH,YAAYD,GAAGI,YAAY,GAAGJ,GAAGI,YAAY,CAACC,MAAM,CAAA"}