import React, { useState, useEffect, useMemo } from 'react';
import { Product } from '@/types';
import { useProducts } from '@/contexts/ProductContext';

export interface FilterOptions {
  category?: string;
  minPrice?: number;
  maxPrice?: number;
  searchTerm?: string;
  sortBy?: 'name' | 'price' | 'date';
  sortOrder?: 'asc' | 'desc';
}

export function useFilteredProducts(initialFilters: FilterOptions = {}) {
  const { products, loading } = useProducts();
  const [filters, setFilters] = useState<FilterOptions>(initialFilters);

  const filteredProducts = useMemo(() => {
    if (!products) return [];

    let filtered = [...products];

    // تطبيق فلتر التصنيف
    if (filters.category && filters.category !== 'all') {
      filtered = filtered.filter(product => product.category === filters.category);
    }

    // تطبيق فلتر السعر
    if (filters.minPrice !== undefined) {
      filtered = filtered.filter(product => product.price >= filters.minPrice!);
    }

    if (filters.maxPrice !== undefined) {
      filtered = filtered.filter(product => product.price <= filters.maxPrice!);
    }

    // تطبيق فلتر البحث
    if (filters.searchTerm) {
      const searchLower = filters.searchTerm.toLowerCase();
      filtered = filtered.filter(product =>
        product.name.toLowerCase().includes(searchLower) ||
        product.description.toLowerCase().includes(searchLower)
      );
    }

    // تطبيق الترتيب
    if (filters.sortBy) {
      filtered.sort((a, b) => {
        let aValue: any;
        let bValue: any;

        switch (filters.sortBy) {
          case 'name':
            aValue = a.name.toLowerCase();
            bValue = b.name.toLowerCase();
            break;
          case 'price':
            aValue = a.price;
            bValue = b.price;
            break;
          case 'date':
            aValue = new Date(a.createdAt || 0);
            bValue = new Date(b.createdAt || 0);
            break;
          default:
            return 0;
        }

        if (aValue < bValue) {
          return filters.sortOrder === 'desc' ? 1 : -1;
        }
        if (aValue > bValue) {
          return filters.sortOrder === 'desc' ? -1 : 1;
        }
        return 0;
      });
    }

    return filtered;
  }, [products, filters]);

  const updateFilters = (newFilters: Partial<FilterOptions>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  };

  const resetFilters = () => {
    setFilters({});
  };

  return {
    products: filteredProducts,
    loading,
    filters,
    updateFilters,
    resetFilters,
    totalCount: filteredProducts.length,
    allProducts: products || []
  };
}
