'use client';

import { Order, OrderStatus } from '@/types';
import { useState } from 'react';

interface OrderDetailsModalProps {
  order: Order | null;
  isOpen: boolean;
  onClose: () => void;
  onUpdateStatus: (orderId: string, status: OrderStatus) => void;
}

const statusLabels: Record<OrderStatus, string> = {
  pending: 'في الانتظار',
  confirmed: 'مؤكد',
  processing: 'قيد المعالجة',
  shipped: 'تم الشحن',
  delivered: 'تم التسليم',
  cancelled: 'ملغي',
};

const statusColors: Record<OrderStatus, string> = {
  pending: 'bg-yellow-100 text-yellow-800',
  confirmed: 'bg-blue-100 text-blue-800',
  processing: 'bg-purple-100 text-purple-800',
  shipped: 'bg-indigo-100 text-indigo-800',
  delivered: 'bg-green-100 text-green-800',
  cancelled: 'bg-red-100 text-red-800',
};

const paymentMethodLabels = {
  cash: 'الدفع عند الاستلام',
  card: 'بطاقة ائتمانية',
  bank_transfer: 'تحويل بنكي',
};

const paymentStatusLabels = {
  pending: 'في الانتظار',
  paid: 'مدفوع',
  failed: 'فشل',
  refunded: 'مسترد',
};

export default function OrderDetailsModal({ order, isOpen, onClose, onUpdateStatus }: OrderDetailsModalProps) {
  const [selectedStatus, setSelectedStatus] = useState<OrderStatus>(order?.status || 'pending');
  const [isUpdating, setIsUpdating] = useState(false);

  if (!isOpen || !order) return null;

  const handleStatusUpdate = async () => {
    if (selectedStatus === order.status) return;
    
    setIsUpdating(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      onUpdateStatus(order.id, selectedStatus);
    } catch (error) {
      console.error('Error updating order status:', error);
    } finally {
      setIsUpdating(false);
    }
  };

  const handlePrint = () => {
    window.print();
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex justify-between items-center">
            <div>
              <h2 className="text-xl font-bold text-gray-800">تفاصيل الطلب</h2>
              <p className="text-gray-600">رقم الطلب: {order.orderNumber}</p>
            </div>
            <div className="flex items-center space-x-2 space-x-reverse">
              <button
                onClick={handlePrint}
                className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition-colors duration-200"
              >
                طباعة
              </button>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600 text-2xl"
              >
                ×
              </button>
            </div>
          </div>
        </div>

        <div className="p-6 space-y-6">
          {/* Order Status */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="text-lg font-semibold text-gray-800 mb-3">حالة الطلب</h3>
            <div className="flex items-center space-x-4 space-x-reverse">
              <span className={`inline-flex px-3 py-1 text-sm font-semibold rounded-full ${statusColors[order.status]}`}>
                {statusLabels[order.status]}
              </span>
              <select
                value={selectedStatus}
                onChange={(e) => setSelectedStatus(e.target.value as OrderStatus)}
                className="border border-gray-300 rounded-md px-3 py-1 text-sm"
              >
                {Object.entries(statusLabels).map(([status, label]) => (
                  <option key={status} value={status}>
                    {label}
                  </option>
                ))}
              </select>
              {selectedStatus !== order.status && (
                <button
                  onClick={handleStatusUpdate}
                  disabled={isUpdating}
                  className={`px-4 py-1 text-sm text-white rounded-md transition-colors duration-200 ${
                    isUpdating
                      ? 'bg-gray-400 cursor-not-allowed'
                      : 'bg-primary-600 hover:bg-primary-700'
                  }`}
                >
                  {isUpdating ? 'جاري التحديث...' : 'تحديث الحالة'}
                </button>
              )}
            </div>
          </div>

          {/* Customer Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-gray-50 rounded-lg p-4">
              <h3 className="text-lg font-semibold text-gray-800 mb-3">معلومات العميل</h3>
              <div className="space-y-2">
                <p><span className="font-medium">الاسم:</span> {order.customerName}</p>
                <p><span className="font-medium">البريد الإلكتروني:</span> {order.customerEmail}</p>
                <p><span className="font-medium">تاريخ الطلب:</span> {order.createdAt.toLocaleDateString('ar-SA')}</p>
                {order.deliveredAt && (
                  <p><span className="font-medium">تاريخ التسليم:</span> {order.deliveredAt.toLocaleDateString('ar-SA')}</p>
                )}
              </div>
            </div>

            <div className="bg-gray-50 rounded-lg p-4">
              <h3 className="text-lg font-semibold text-gray-800 mb-3">عنوان الشحن</h3>
              <div className="space-y-2">
                <p><span className="font-medium">الاسم:</span> {order.shippingAddress.fullName}</p>
                <p><span className="font-medium">الهاتف:</span> {order.shippingAddress.phone}</p>
                <p><span className="font-medium">العنوان:</span> {order.shippingAddress.address}</p>
                <p><span className="font-medium">المدينة:</span> {order.shippingAddress.city}</p>
                <p><span className="font-medium">الرمز البريدي:</span> {order.shippingAddress.postalCode}</p>
                <p><span className="font-medium">البلد:</span> {order.shippingAddress.country}</p>
              </div>
            </div>
          </div>

          {/* Order Items */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="text-lg font-semibold text-gray-800 mb-3">المنتجات</h3>
            <div className="overflow-x-auto">
              <table className="min-w-full">
                <thead>
                  <tr className="border-b border-gray-200">
                    <th className="text-right py-2">المنتج</th>
                    <th className="text-right py-2">السعر</th>
                    <th className="text-right py-2">الكمية</th>
                    <th className="text-right py-2">المجموع</th>
                  </tr>
                </thead>
                <tbody>
                  {order.items.map((item) => (
                    <tr key={item.id} className="border-b border-gray-100">
                      <td className="py-3">
                        <div className="flex items-center">
                          <img
                            src={item.productImage}
                            alt={item.productName}
                            className="w-12 h-12 object-cover rounded-md ml-3"
                            onError={(e) => {
                              e.currentTarget.src = 'https://via.placeholder.com/48x48?text=صورة';
                            }}
                          />
                          <span className="font-medium">{item.productName}</span>
                        </div>
                      </td>
                      <td className="py-3">{item.price.toLocaleString('ar-SA')} ر.س</td>
                      <td className="py-3">{item.quantity}</td>
                      <td className="py-3 font-medium">{item.total.toLocaleString('ar-SA')} ر.س</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {/* Payment and Total */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-gray-50 rounded-lg p-4">
              <h3 className="text-lg font-semibold text-gray-800 mb-3">معلومات الدفع</h3>
              <div className="space-y-2">
                <p><span className="font-medium">طريقة الدفع:</span> {paymentMethodLabels[order.paymentMethod]}</p>
                <p><span className="font-medium">حالة الدفع:</span> 
                  <span className={`mr-2 px-2 py-1 text-xs rounded-full ${
                    order.paymentStatus === 'paid' ? 'bg-green-100 text-green-800' :
                    order.paymentStatus === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                    order.paymentStatus === 'failed' ? 'bg-red-100 text-red-800' :
                    'bg-gray-100 text-gray-800'
                  }`}>
                    {paymentStatusLabels[order.paymentStatus]}
                  </span>
                </p>
              </div>
            </div>

            <div className="bg-gray-50 rounded-lg p-4">
              <h3 className="text-lg font-semibold text-gray-800 mb-3">ملخص الطلب</h3>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span>المجموع الفرعي:</span>
                  <span>{order.subtotal.toLocaleString('ar-SA')} ر.س</span>
                </div>
                <div className="flex justify-between">
                  <span>الشحن:</span>
                  <span>{order.shipping.toLocaleString('ar-SA')} ر.س</span>
                </div>
                <div className="flex justify-between">
                  <span>الضريبة:</span>
                  <span>{order.tax.toLocaleString('ar-SA')} ر.س</span>
                </div>
                <div className="flex justify-between font-bold text-lg border-t border-gray-200 pt-2">
                  <span>المجموع الكلي:</span>
                  <span>{order.total.toLocaleString('ar-SA')} ر.س</span>
                </div>
              </div>
            </div>
          </div>

          {/* Notes */}
          {order.notes && (
            <div className="bg-gray-50 rounded-lg p-4">
              <h3 className="text-lg font-semibold text-gray-800 mb-3">ملاحظات</h3>
              <p className="text-gray-700">{order.notes}</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
