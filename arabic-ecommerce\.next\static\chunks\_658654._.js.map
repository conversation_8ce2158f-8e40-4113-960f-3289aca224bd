{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/New%20folder%20%288%29/arabic-ecommerce/src/components/payment/PaymentMethodSelector.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { PaymentMethod, PaymentOption, Currency } from '@/types/payment';\nimport { usePayment, usePaymentMethod } from '@/contexts/PaymentContext';\n\ninterface PaymentMethodSelectorProps {\n  selectedMethod: PaymentMethod | null;\n  onMethodSelect: (method: PaymentMethod) => void;\n  amount: number;\n  currency: Currency;\n  className?: string;\n}\n\nexport default function PaymentMethodSelector({\n  selectedMethod,\n  onMethodSelect,\n  amount,\n  currency,\n  className = '',\n}: PaymentMethodSelectorProps) {\n  const { getAvailablePaymentMethods, calculateTotalWithFees } = usePayment();\n  \n  // الحصول على طرق الدفع المتاحة للمبلغ والعملة المحددة\n  const availableMethods = getAvailablePaymentMethods(amount, currency);\n\n  const handleMethodSelect = (method: PaymentMethod) => {\n    onMethodSelect(method);\n  };\n\n  return (\n    <div className={`space-y-4 ${className}`}>\n      <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">\n        اختر طريقة الدفع\n      </h3>\n      \n      <div className=\"grid gap-3\">\n        {availableMethods.map((option) => {\n          const totalWithFees = calculateTotalWithFees(amount, option.method);\n          const fees = totalWithFees - amount;\n          const isSelected = selectedMethod === option.method;\n          \n          return (\n            <PaymentMethodCard\n              key={option.method}\n              option={option}\n              isSelected={isSelected}\n              onSelect={() => handleMethodSelect(option.method)}\n              amount={amount}\n              totalWithFees={totalWithFees}\n              fees={fees}\n              currency={currency}\n            />\n          );\n        })}\n      </div>\n      \n      {availableMethods.length === 0 && (\n        <div className=\"text-center py-8 text-gray-500\">\n          <p>لا توجد طرق دفع متاحة للمبلغ المحدد</p>\n          <p className=\"text-sm mt-1\">\n            المبلغ: {amount} {currency}\n          </p>\n        </div>\n      )}\n    </div>\n  );\n}\n\ninterface PaymentMethodCardProps {\n  option: PaymentOption;\n  isSelected: boolean;\n  onSelect: () => void;\n  amount: number;\n  totalWithFees: number;\n  fees: number;\n  currency: Currency;\n}\n\nfunction PaymentMethodCard({\n  option,\n  isSelected,\n  onSelect,\n  amount,\n  totalWithFees,\n  fees,\n  currency,\n}: PaymentMethodCardProps) {\n  return (\n    <div\n      className={`\n        relative border-2 rounded-lg p-4 cursor-pointer transition-all duration-200\n        ${isSelected \n          ? 'border-primary-500 bg-primary-50 shadow-md' \n          : 'border-gray-200 bg-white hover:border-gray-300 hover:shadow-sm'\n        }\n      `}\n      onClick={onSelect}\n    >\n      {/* زر الاختيار */}\n      <div className=\"flex items-start justify-between\">\n        <div className=\"flex items-center space-x-3 space-x-reverse\">\n          <div className=\"text-2xl\">{option.icon}</div>\n          <div className=\"flex-1\">\n            <h4 className=\"font-semibold text-gray-900\">\n              {option.nameAr}\n            </h4>\n            <p className=\"text-sm text-gray-600 mt-1\">\n              {option.descriptionAr}\n            </p>\n            <div className=\"flex items-center space-x-2 space-x-reverse mt-2 text-xs text-gray-500\">\n              <span>⏱️ {option.processingTimeAr}</span>\n              {option.minAmount && (\n                <span>• الحد الأدنى: {option.minAmount} {currency}</span>\n              )}\n              {option.maxAmount && option.maxAmount !== Infinity && (\n                <span>• الحد الأقصى: {option.maxAmount} {currency}</span>\n              )}\n            </div>\n          </div>\n        </div>\n        \n        {/* دائرة الاختيار */}\n        <div className={`\n          w-5 h-5 rounded-full border-2 flex items-center justify-center\n          ${isSelected \n            ? 'border-primary-500 bg-primary-500' \n            : 'border-gray-300'\n          }\n        `}>\n          {isSelected && (\n            <div className=\"w-2 h-2 rounded-full bg-white\"></div>\n          )}\n        </div>\n      </div>\n      \n      {/* معلومات الرسوم والمجموع */}\n      <div className=\"mt-3 pt-3 border-t border-gray-100\">\n        <div className=\"flex justify-between items-center text-sm\">\n          <span className=\"text-gray-600\">المبلغ الأساسي:</span>\n          <span className=\"font-medium\">{amount} {currency}</span>\n        </div>\n        \n        {fees > 0 && (\n          <div className=\"flex justify-between items-center text-sm mt-1\">\n            <span className=\"text-gray-600\">رسوم الخدمة:</span>\n            <span className=\"font-medium text-orange-600\">+{fees} {currency}</span>\n          </div>\n        )}\n        \n        <div className=\"flex justify-between items-center text-base font-semibold mt-2 pt-2 border-t border-gray-100\">\n          <span className=\"text-gray-900\">المجموع:</span>\n          <span className=\"text-primary-600\">{totalWithFees} {currency}</span>\n        </div>\n      </div>\n      \n      {/* مؤشر الاختيار */}\n      {isSelected && (\n        <div className=\"absolute top-2 left-2\">\n          <div className=\"bg-primary-500 text-white text-xs px-2 py-1 rounded-full\">\n            ✓ محدد\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n\n// مكون مبسط لعرض طريقة دفع واحدة\nexport function PaymentMethodBadge({ \n  method, \n  className = '' \n}: { \n  method: PaymentMethod; \n  className?: string; \n}) {\n  const { option } = usePaymentMethod(method);\n  \n  if (!option) return null;\n  \n  return (\n    <div className={`inline-flex items-center space-x-2 space-x-reverse px-3 py-1 bg-gray-100 rounded-full text-sm ${className}`}>\n      <span>{option.icon}</span>\n      <span className=\"font-medium\">{option.nameAr}</span>\n    </div>\n  );\n}\n\n// مكون لعرض ملخص طريقة الدفع المختارة\nexport function PaymentMethodSummary({ \n  method, \n  amount, \n  currency,\n  className = '' \n}: { \n  method: PaymentMethod; \n  amount: number;\n  currency: Currency;\n  className?: string; \n}) {\n  const { calculateTotalWithFees } = usePayment();\n  const { option } = usePaymentMethod(method);\n  \n  if (!option) return null;\n  \n  const totalWithFees = calculateTotalWithFees(amount, method);\n  const fees = totalWithFees - amount;\n  \n  return (\n    <div className={`bg-gray-50 rounded-lg p-4 ${className}`}>\n      <div className=\"flex items-center space-x-3 space-x-reverse mb-3\">\n        <div className=\"text-xl\">{option.icon}</div>\n        <div>\n          <h4 className=\"font-semibold text-gray-900\">{option.nameAr}</h4>\n          <p className=\"text-sm text-gray-600\">{option.descriptionAr}</p>\n        </div>\n      </div>\n      \n      <div className=\"space-y-2 text-sm\">\n        <div className=\"flex justify-between\">\n          <span className=\"text-gray-600\">المبلغ:</span>\n          <span>{amount} {currency}</span>\n        </div>\n        \n        {fees > 0 && (\n          <div className=\"flex justify-between\">\n            <span className=\"text-gray-600\">رسوم الخدمة:</span>\n            <span className=\"text-orange-600\">+{fees} {currency}</span>\n          </div>\n        )}\n        \n        <div className=\"flex justify-between font-semibold pt-2 border-t border-gray-200\">\n          <span>المجموع:</span>\n          <span className=\"text-primary-600\">{totalWithFees} {currency}</span>\n        </div>\n        \n        <div className=\"flex items-center space-x-2 space-x-reverse text-xs text-gray-500 mt-2\">\n          <span>⏱️</span>\n          <span>وقت المعالجة: {option.processingTimeAr}</span>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;;AAIA;;;AAJA;;AAce,SAAS,sBAAsB,EAC5C,cAAc,EACd,cAAc,EACd,MAAM,EACN,QAAQ,EACR,YAAY,EAAE,EACa;;IAC3B,MAAM,EAAE,0BAA0B,EAAE,sBAAsB,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,aAAU,AAAD;IAExE,sDAAsD;IACtD,MAAM,mBAAmB,2BAA2B,QAAQ;IAE5D,MAAM,qBAAqB,CAAC;QAC1B,eAAe;IACjB;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAC,UAAU,EAAE,WAAW;;0BACtC,6LAAC;gBAAG,WAAU;0BAA2C;;;;;;0BAIzD,6LAAC;gBAAI,WAAU;0BACZ,iBAAiB,GAAG,CAAC,CAAC;oBACrB,MAAM,gBAAgB,uBAAuB,QAAQ,OAAO,MAAM;oBAClE,MAAM,OAAO,gBAAgB;oBAC7B,MAAM,aAAa,mBAAmB,OAAO,MAAM;oBAEnD,qBACE,6LAAC;wBAEC,QAAQ;wBACR,YAAY;wBACZ,UAAU,IAAM,mBAAmB,OAAO,MAAM;wBAChD,QAAQ;wBACR,eAAe;wBACf,MAAM;wBACN,UAAU;uBAPL,OAAO,MAAM;;;;;gBAUxB;;;;;;YAGD,iBAAiB,MAAM,KAAK,mBAC3B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;kCAAE;;;;;;kCACH,6LAAC;wBAAE,WAAU;;4BAAe;4BACjB;4BAAO;4BAAE;;;;;;;;;;;;;;;;;;;AAM9B;GArDwB;;QAOyC,qIAAA,CAAA,aAAU;;;KAPnD;AAiExB,SAAS,kBAAkB,EACzB,MAAM,EACN,UAAU,EACV,QAAQ,EACR,MAAM,EACN,aAAa,EACb,IAAI,EACJ,QAAQ,EACe;IACvB,qBACE,6LAAC;QACC,WAAW,CAAC;;QAEV,EAAE,aACE,+CACA,iEACH;MACH,CAAC;QACD,SAAS;;0BAGT,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CAAY,OAAO,IAAI;;;;;;0CACtC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDACX,OAAO,MAAM;;;;;;kDAEhB,6LAAC;wCAAE,WAAU;kDACV,OAAO,aAAa;;;;;;kDAEvB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;oDAAK;oDAAI,OAAO,gBAAgB;;;;;;;4CAChC,OAAO,SAAS,kBACf,6LAAC;;oDAAK;oDAAgB,OAAO,SAAS;oDAAC;oDAAE;;;;;;;4CAE1C,OAAO,SAAS,IAAI,OAAO,SAAS,KAAK,0BACxC,6LAAC;;oDAAK;oDAAgB,OAAO,SAAS;oDAAC;oDAAE;;;;;;;;;;;;;;;;;;;;;;;;;kCAOjD,6LAAC;wBAAI,WAAW,CAAC;;UAEf,EAAE,aACE,sCACA,kBACH;QACH,CAAC;kCACE,4BACC,6LAAC;4BAAI,WAAU;;;;;;;;;;;;;;;;;0BAMrB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;0CAAgB;;;;;;0CAChC,6LAAC;gCAAK,WAAU;;oCAAe;oCAAO;oCAAE;;;;;;;;;;;;;oBAGzC,OAAO,mBACN,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;0CAAgB;;;;;;0CAChC,6LAAC;gCAAK,WAAU;;oCAA8B;oCAAE;oCAAK;oCAAE;;;;;;;;;;;;;kCAI3D,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;0CAAgB;;;;;;0CAChC,6LAAC;gCAAK,WAAU;;oCAAoB;oCAAc;oCAAE;;;;;;;;;;;;;;;;;;;YAKvD,4BACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BAA2D;;;;;;;;;;;;;;;;;AAOpF;MAvFS;AA0FF,SAAS,mBAAmB,EACjC,MAAM,EACN,YAAY,EAAE,EAIf;;IACC,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,mBAAgB,AAAD,EAAE;IAEpC,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,6LAAC;QAAI,WAAW,CAAC,8FAA8F,EAAE,WAAW;;0BAC1H,6LAAC;0BAAM,OAAO,IAAI;;;;;;0BAClB,6LAAC;gBAAK,WAAU;0BAAe,OAAO,MAAM;;;;;;;;;;;;AAGlD;IAjBgB;;QAOK,qIAAA,CAAA,mBAAgB;;;MAPrB;AAoBT,SAAS,qBAAqB,EACnC,MAAM,EACN,MAAM,EACN,QAAQ,EACR,YAAY,EAAE,EAMf;;IACC,MAAM,EAAE,sBAAsB,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,aAAU,AAAD;IAC5C,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,mBAAgB,AAAD,EAAE;IAEpC,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,gBAAgB,uBAAuB,QAAQ;IACrD,MAAM,OAAO,gBAAgB;IAE7B,qBACE,6LAAC;QAAI,WAAW,CAAC,0BAA0B,EAAE,WAAW;;0BACtD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCAAW,OAAO,IAAI;;;;;;kCACrC,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAA+B,OAAO,MAAM;;;;;;0CAC1D,6LAAC;gCAAE,WAAU;0CAAyB,OAAO,aAAa;;;;;;;;;;;;;;;;;;0BAI9D,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;0CAAgB;;;;;;0CAChC,6LAAC;;oCAAM;oCAAO;oCAAE;;;;;;;;;;;;;oBAGjB,OAAO,mBACN,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;0CAAgB;;;;;;0CAChC,6LAAC;gCAAK,WAAU;;oCAAkB;oCAAE;oCAAK;oCAAE;;;;;;;;;;;;;kCAI/C,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;0CAAK;;;;;;0CACN,6LAAC;gCAAK,WAAU;;oCAAoB;oCAAc;oCAAE;;;;;;;;;;;;;kCAGtD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;0CAAK;;;;;;0CACN,6LAAC;;oCAAK;oCAAe,OAAO,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;AAKtD;IAtDgB;;QAWqB,qIAAA,CAAA,aAAU;QAC1B,qIAAA,CAAA,mBAAgB;;;MAZrB"}}, {"offset": {"line": 572, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 578, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/New%20folder%20%288%29/arabic-ecommerce/src/app/checkout/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { useCart } from '@/contexts/CartContext';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { usePayment } from '@/contexts/PaymentContext';\nimport PaymentMethodSelector from '@/components/payment/PaymentMethodSelector';\nimport { PaymentMethod, Currency, CreatePaymentRequest } from '@/types/payment';\n\nexport default function CheckoutPage() {\n  const router = useRouter();\n  const { items: cartItems, totalPrice: cartTotal, clearCart } = useCart();\n  const { user } = useAuth();\n  const { state: paymentState, createPayment, confirmPayment, resetPaymentState } = usePayment();\n\n  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<PaymentMethod | null>(null);\n  const [customerInfo, setCustomerInfo] = useState({\n    name: user?.name || '',\n    email: user?.email || '',\n    phone: user?.phone || '',\n  });\n  const [currentStep, setCurrentStep] = useState<'info' | 'payment' | 'processing'>('info');\n\n  const currency: Currency = 'SAR';\n  const subtotal = cartItems?.reduce((sum, item) => sum + (item.product.price * item.quantity), 0) || 0;\n  const shipping = 25;\n  const total = subtotal + shipping;\n\n  useEffect(() => {\n    if (cartItems.length === 0) {\n      router.push('/cart');\n    }\n  }, [cartItems.length, router]);\n\n  useEffect(() => {\n    resetPaymentState();\n  }, [resetPaymentState]);\n\n  const handleCustomerInfoSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    if (customerInfo.name && customerInfo.email) {\n      setCurrentStep('payment');\n    }\n  };\n\n  const handlePaymentMethodSelect = async (method: PaymentMethod) => {\n    setSelectedPaymentMethod(method);\n\n    if (method === 'cash_on_delivery') {\n      await processPayment(method);\n    }\n  };\n\n  const processPayment = async (method: PaymentMethod) => {\n    setCurrentStep('processing');\n\n    const orderId = `order_${Date.now()}`;\n    const paymentRequest: CreatePaymentRequest = {\n      orderId,\n      amount: total,\n      currency,\n      method,\n      customerInfo,\n      shippingAddress: {\n        street: '',\n        city: '',\n        state: '',\n        postalCode: '',\n        country: 'SA',\n      },\n      items: cartItems.map(item => ({\n        id: item.product.id,\n        name: item.product.name,\n        quantity: item.quantity,\n        price: item.product.price,\n      })),\n      metadata: {\n        subtotal,\n        shipping,\n        total,\n      },\n    };\n\n    const result = await createPayment(paymentRequest);\n\n    if (result.success) {\n      if (method === 'cash_on_delivery') {\n        await confirmPayment({\n          paymentId: result.paymentId!,\n          method,\n        });\n\n        clearCart();\n        router.push(`/checkout/success?orderId=${orderId}`);\n      }\n    }\n  };\n\n  if (cartItems.length === 0) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"text-6xl mb-4\">🛒</div>\n          <h2 className=\"text-2xl font-bold text-gray-900 mb-2\">السلة فارغة</h2>\n          <p className=\"text-gray-600 mb-4\">أضف بعض المنتجات للمتابعة</p>\n          <button\n            onClick={() => router.push('/products')}\n            className=\"bg-primary-600 text-white px-6 py-2 rounded-lg hover:bg-primary-700\"\n          >\n            تصفح المنتجات\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 py-8\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"mb-8\">\n          <h1 className=\"text-3xl font-bold text-gray-900 text-center\">إتمام الطلب</h1>\n        </div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n          <div className=\"lg:col-span-2\">\n            {currentStep === 'info' && (\n              <div className=\"bg-white rounded-lg p-6\">\n                <h2 className=\"text-xl font-semibold mb-4\">معلومات العميل</h2>\n                <form onSubmit={handleCustomerInfoSubmit} className=\"space-y-4\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      الاسم *\n                    </label>\n                    <input\n                      type=\"text\"\n                      value={customerInfo.name}\n                      onChange={(e) => setCustomerInfo({...customerInfo, name: e.target.value})}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500\"\n                      required\n                    />\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      البريد الإلكتروني *\n                    </label>\n                    <input\n                      type=\"email\"\n                      value={customerInfo.email}\n                      onChange={(e) => setCustomerInfo({...customerInfo, email: e.target.value})}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500\"\n                      required\n                    />\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      رقم الهاتف\n                    </label>\n                    <input\n                      type=\"tel\"\n                      value={customerInfo.phone}\n                      onChange={(e) => setCustomerInfo({...customerInfo, phone: e.target.value})}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500\"\n                    />\n                  </div>\n                  <button\n                    type=\"submit\"\n                    className=\"w-full bg-primary-600 text-white py-2 px-4 rounded-md hover:bg-primary-700\"\n                  >\n                    متابعة للدفع\n                  </button>\n                </form>\n              </div>\n            )}\n\n            {currentStep === 'payment' && (\n              <div className=\"space-y-6\">\n                <PaymentMethodSelector\n                  selectedMethod={selectedPaymentMethod}\n                  onMethodSelect={handlePaymentMethodSelect}\n                  amount={total}\n                  currency={currency}\n                />\n              </div>\n            )}\n\n            {currentStep === 'processing' && (\n              <div className=\"bg-white rounded-lg p-8 text-center\">\n                <div className=\"w-16 h-16 border-4 border-primary-500 border-t-transparent rounded-full animate-spin mx-auto mb-4\"></div>\n                <h3 className=\"text-lg font-semibold mb-2\">جاري معالجة طلبك</h3>\n                <p className=\"text-gray-600\">يرجى الانتظار...</p>\n              </div>\n            )}\n          </div>\n\n          <div className=\"lg:col-span-1\">\n            <div className=\"bg-white rounded-lg p-6\">\n              <h3 className=\"text-lg font-semibold mb-4\">ملخص الطلب</h3>\n              <div className=\"space-y-3\">\n                {cartItems.map((item) => (\n                  <div key={item.id} className=\"flex justify-between\">\n                    <span className=\"text-sm\">{item.product.name} × {item.quantity}</span>\n                    <span className=\"text-sm font-medium\">{item.product.price * item.quantity} ريال</span>\n                  </div>\n                ))}\n                <div className=\"border-t pt-3\">\n                  <div className=\"flex justify-between\">\n                    <span>المجموع الفرعي:</span>\n                    <span>{subtotal} ريال</span>\n                  </div>\n                  <div className=\"flex justify-between\">\n                    <span>الشحن:</span>\n                    <span>{shipping} ريال</span>\n                  </div>\n                  <div className=\"flex justify-between font-semibold text-lg border-t pt-2 mt-2\">\n                    <span>المجموع:</span>\n                    <span>{total} ريال</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;;AAUe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,OAAO,SAAS,EAAE,YAAY,SAAS,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACrE,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACvB,MAAM,EAAE,OAAO,YAAY,EAAE,aAAa,EAAE,cAAc,EAAE,iBAAiB,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,aAAU,AAAD;IAE3F,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAwB;IACzF,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAC/C,MAAM,MAAM,QAAQ;QACpB,OAAO,MAAM,SAAS;QACtB,OAAO,MAAM,SAAS;IACxB;IACA,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqC;IAElF,MAAM,WAAqB;IAC3B,MAAM,WAAW,WAAW,OAAO,CAAC,KAAK,OAAS,MAAO,KAAK,OAAO,CAAC,KAAK,GAAG,KAAK,QAAQ,EAAG,MAAM;IACpG,MAAM,WAAW;IACjB,MAAM,QAAQ,WAAW;IAEzB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,UAAU,MAAM,KAAK,GAAG;gBAC1B,OAAO,IAAI,CAAC;YACd;QACF;iCAAG;QAAC,UAAU,MAAM;QAAE;KAAO;IAE7B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR;QACF;iCAAG;QAAC;KAAkB;IAEtB,MAAM,2BAA2B,CAAC;QAChC,EAAE,cAAc;QAChB,IAAI,aAAa,IAAI,IAAI,aAAa,KAAK,EAAE;YAC3C,eAAe;QACjB;IACF;IAEA,MAAM,4BAA4B,OAAO;QACvC,yBAAyB;QAEzB,IAAI,WAAW,oBAAoB;YACjC,MAAM,eAAe;QACvB;IACF;IAEA,MAAM,iBAAiB,OAAO;QAC5B,eAAe;QAEf,MAAM,UAAU,CAAC,MAAM,EAAE,KAAK,GAAG,IAAI;QACrC,MAAM,iBAAuC;YAC3C;YACA,QAAQ;YACR;YACA;YACA;YACA,iBAAiB;gBACf,QAAQ;gBACR,MAAM;gBACN,OAAO;gBACP,YAAY;gBACZ,SAAS;YACX;YACA,OAAO,UAAU,GAAG,CAAC,CAAA,OAAQ,CAAC;oBAC5B,IAAI,KAAK,OAAO,CAAC,EAAE;oBACnB,MAAM,KAAK,OAAO,CAAC,IAAI;oBACvB,UAAU,KAAK,QAAQ;oBACvB,OAAO,KAAK,OAAO,CAAC,KAAK;gBAC3B,CAAC;YACD,UAAU;gBACR;gBACA;gBACA;YACF;QACF;QAEA,MAAM,SAAS,MAAM,cAAc;QAEnC,IAAI,OAAO,OAAO,EAAE;YAClB,IAAI,WAAW,oBAAoB;gBACjC,MAAM,eAAe;oBACnB,WAAW,OAAO,SAAS;oBAC3B;gBACF;gBAEA;gBACA,OAAO,IAAI,CAAC,CAAC,0BAA0B,EAAE,SAAS;YACpD;QACF;IACF;IAEA,IAAI,UAAU,MAAM,KAAK,GAAG;QAC1B,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCAAgB;;;;;;kCAC/B,6LAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,6LAAC;wBAAE,WAAU;kCAAqB;;;;;;kCAClC,6LAAC;wBACC,SAAS,IAAM,OAAO,IAAI,CAAC;wBAC3B,WAAU;kCACX;;;;;;;;;;;;;;;;;IAMT;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAG,WAAU;kCAA+C;;;;;;;;;;;8BAG/D,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;gCACZ,gBAAgB,wBACf,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,6LAAC;4CAAK,UAAU;4CAA0B,WAAU;;8DAClD,6LAAC;;sEACC,6LAAC;4DAAM,WAAU;sEAA+C;;;;;;sEAGhE,6LAAC;4DACC,MAAK;4DACL,OAAO,aAAa,IAAI;4DACxB,UAAU,CAAC,IAAM,gBAAgB;oEAAC,GAAG,YAAY;oEAAE,MAAM,EAAE,MAAM,CAAC,KAAK;gEAAA;4DACvE,WAAU;4DACV,QAAQ;;;;;;;;;;;;8DAGZ,6LAAC;;sEACC,6LAAC;4DAAM,WAAU;sEAA+C;;;;;;sEAGhE,6LAAC;4DACC,MAAK;4DACL,OAAO,aAAa,KAAK;4DACzB,UAAU,CAAC,IAAM,gBAAgB;oEAAC,GAAG,YAAY;oEAAE,OAAO,EAAE,MAAM,CAAC,KAAK;gEAAA;4DACxE,WAAU;4DACV,QAAQ;;;;;;;;;;;;8DAGZ,6LAAC;;sEACC,6LAAC;4DAAM,WAAU;sEAA+C;;;;;;sEAGhE,6LAAC;4DACC,MAAK;4DACL,OAAO,aAAa,KAAK;4DACzB,UAAU,CAAC,IAAM,gBAAgB;oEAAC,GAAG,YAAY;oEAAE,OAAO,EAAE,MAAM,CAAC,KAAK;gEAAA;4DACxE,WAAU;;;;;;;;;;;;8DAGd,6LAAC;oDACC,MAAK;oDACL,WAAU;8DACX;;;;;;;;;;;;;;;;;;gCAON,gBAAgB,2BACf,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,yJAAA,CAAA,UAAqB;wCACpB,gBAAgB;wCAChB,gBAAgB;wCAChB,QAAQ;wCACR,UAAU;;;;;;;;;;;gCAKf,gBAAgB,8BACf,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,6LAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;;;;;;;sCAKnC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,6LAAC;wCAAI,WAAU;;4CACZ,UAAU,GAAG,CAAC,CAAC,qBACd,6LAAC;oDAAkB,WAAU;;sEAC3B,6LAAC;4DAAK,WAAU;;gEAAW,KAAK,OAAO,CAAC,IAAI;gEAAC;gEAAI,KAAK,QAAQ;;;;;;;sEAC9D,6LAAC;4DAAK,WAAU;;gEAAuB,KAAK,OAAO,CAAC,KAAK,GAAG,KAAK,QAAQ;gEAAC;;;;;;;;mDAFlE,KAAK,EAAE;;;;;0DAKnB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;0EAAK;;;;;;0EACN,6LAAC;;oEAAM;oEAAS;;;;;;;;;;;;;kEAElB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;0EAAK;;;;;;0EACN,6LAAC;;oEAAM;oEAAS;;;;;;;;;;;;;kEAElB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;0EAAK;;;;;;0EACN,6LAAC;;oEAAM;oEAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjC;GAxNwB;;QACP,qIAAA,CAAA,YAAS;QACuC,kIAAA,CAAA,UAAO;QACrD,kIAAA,CAAA,UAAO;QAC0D,qIAAA,CAAA,aAAU;;;KAJtE"}}, {"offset": {"line": 1124, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1134, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/New%20folder%20%288%29/arabic-ecommerce/node_modules/next/navigation.js"], "sourcesContent": ["module.exports = require('./dist/client/components/navigation')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0]}}, {"offset": {"line": 1135, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}