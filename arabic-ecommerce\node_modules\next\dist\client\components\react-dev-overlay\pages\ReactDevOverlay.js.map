{"version": 3, "sources": ["../../../../../src/client/components/react-dev-overlay/pages/ReactDevOverlay.tsx"], "sourcesContent": ["import * as React from 'react'\n\nimport * as Bus from './bus'\nimport { ShadowPortal } from '../internal/components/ShadowPortal'\nimport { BuildError } from '../internal/container/BuildError'\nimport { Errors } from '../internal/container/Errors'\nimport { ErrorBoundary } from './ErrorBoundary'\nimport { Base } from '../internal/styles/Base'\nimport { ComponentStyles } from '../internal/styles/ComponentStyles'\nimport { CssReset } from '../internal/styles/CssReset'\nimport { useErrorOverlayReducer } from '../shared'\n\ntype ErrorType = 'runtime' | 'build'\n\nconst shouldPreventDisplay = (\n  errorType?: ErrorType | null,\n  preventType?: ErrorType[] | null\n) => {\n  if (!preventType || !errorType) {\n    return false\n  }\n  return preventType.includes(errorType)\n}\n\ninterface ReactDevOverlayProps {\n  children?: React.ReactNode\n  preventDisplay?: ErrorType[]\n  globalOverlay?: boolean\n}\n\nexport default function ReactDevOverlay({\n  children,\n  preventDisplay,\n  globalOverlay,\n}: ReactDevOverlayProps) {\n  const [state, dispatch] = useErrorOverlayReducer()\n\n  React.useEffect(() => {\n    Bus.on(dispatch)\n    return function () {\n      Bus.off(dispatch)\n    }\n  }, [dispatch])\n\n  const onComponentError = React.useCallback(\n    (_error: Error, _componentStack: string | null) => {\n      // TODO: special handling\n    },\n    []\n  )\n\n  const hasBuildError = state.buildError != null\n  const hasRuntimeErrors = Boolean(state.errors.length)\n  const errorType = hasBuildError\n    ? 'build'\n    : hasRuntimeErrors\n      ? 'runtime'\n      : null\n  const isMounted = errorType !== null\n\n  const displayPrevented = shouldPreventDisplay(errorType, preventDisplay)\n\n  return (\n    <>\n      <ErrorBoundary\n        globalOverlay={globalOverlay}\n        isMounted={isMounted}\n        onError={onComponentError}\n      >\n        {children ?? null}\n      </ErrorBoundary>\n      {isMounted ? (\n        <ShadowPortal>\n          <CssReset />\n          <Base />\n          <ComponentStyles />\n\n          {displayPrevented ? null : hasBuildError ? (\n            <BuildError\n              message={state.buildError!}\n              versionInfo={state.versionInfo}\n            />\n          ) : hasRuntimeErrors ? (\n            <Errors\n              isAppDir={false}\n              errors={state.errors}\n              versionInfo={state.versionInfo}\n              initialDisplayState={'fullscreen'}\n            />\n          ) : undefined}\n        </ShadowPortal>\n      ) : undefined}\n    </>\n  )\n}\n"], "names": ["ReactDevOverlay", "shouldPreventDisplay", "errorType", "preventType", "includes", "children", "preventDisplay", "globalOverlay", "state", "dispatch", "useErrorOverlayReducer", "React", "useEffect", "Bus", "on", "off", "onComponentError", "useCallback", "_error", "_componentStack", "hasBuildError", "buildError", "hasRuntimeErrors", "Boolean", "errors", "length", "isMounted", "displayPrevented", "Error<PERSON>ou<PERSON><PERSON>", "onError", "ShadowPort<PERSON>", "CssReset", "Base", "ComponentStyles", "BuildError", "message", "versionInfo", "Errors", "isAppDir", "initialDisplayState", "undefined"], "mappings": ";;;;+BA8BA;;;eAAwBA;;;;;iEA9BD;+DAEF;8BACQ;4BACF;wBACJ;+BACO;sBACT;iCACW;0BACP;wBACc;AAIvC,MAAMC,uBAAuB,CAC3BC,WACAC;IAEA,IAAI,CAACA,eAAe,CAACD,WAAW;QAC9B,OAAO;IACT;IACA,OAAOC,YAAYC,QAAQ,CAACF;AAC9B;AAQe,SAASF,gBAAgB,KAIjB;IAJiB,IAAA,EACtCK,QAAQ,EACRC,cAAc,EACdC,aAAa,EACQ,GAJiB;IAKtC,MAAM,CAACC,OAAOC,SAAS,GAAGC,IAAAA,8BAAsB;IAEhDC,OAAMC,SAAS,CAAC;QACdC,KAAIC,EAAE,CAACL;QACP,OAAO;YACLI,KAAIE,GAAG,CAACN;QACV;IACF,GAAG;QAACA;KAAS;IAEb,MAAMO,mBAAmBL,OAAMM,WAAW,CACxC,CAACC,QAAeC;IACd,yBAAyB;IAC3B,GACA,EAAE;IAGJ,MAAMC,gBAAgBZ,MAAMa,UAAU,IAAI;IAC1C,MAAMC,mBAAmBC,QAAQf,MAAMgB,MAAM,CAACC,MAAM;IACpD,MAAMvB,YAAYkB,gBACd,UACAE,mBACE,YACA;IACN,MAAMI,YAAYxB,cAAc;IAEhC,MAAMyB,mBAAmB1B,qBAAqBC,WAAWI;IAEzD,qBACE;;0BACE,qBAACsB,4BAAa;gBACZrB,eAAeA;gBACfmB,WAAWA;gBACXG,SAASb;0BAERX,mBAAAA,WAAY;;YAEdqB,0BACC,sBAACI,0BAAY;;kCACX,qBAACC,kBAAQ;kCACT,qBAACC,UAAI;kCACL,qBAACC,gCAAe;oBAEfN,mBAAmB,OAAOP,8BACzB,qBAACc,sBAAU;wBACTC,SAAS3B,MAAMa,UAAU;wBACzBe,aAAa5B,MAAM4B,WAAW;yBAE9Bd,iCACF,qBAACe,cAAM;wBACLC,UAAU;wBACVd,QAAQhB,MAAMgB,MAAM;wBACpBY,aAAa5B,MAAM4B,WAAW;wBAC9BG,qBAAqB;yBAErBC;;iBAEJA;;;AAGV"}