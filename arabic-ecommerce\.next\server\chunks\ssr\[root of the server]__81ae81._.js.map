{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/New%20folder%20%288%29/arabic-ecommerce/src/lib/data.ts"], "sourcesContent": ["import { Product, User } from '@/types';\n\n// Sample products data\nexport const sampleProducts: Product[] = [\n  {\n    id: '1',\n    name: 'هاتف ذكي متطور',\n    description: 'هاتف ذكي بمواصفات عالية وكاميرا متقدمة',\n    price: 2500,\n    image: '/images/phone.jpg',\n    category: 'إلكترونيات',\n    stock: 15,\n    featured: true,\n    createdAt: new Date(),\n    updatedAt: new Date(),\n  },\n  {\n    id: '2',\n    name: 'لابتوب للألعاب',\n    description: 'لابتوب قوي مخصص للألعاب والتصميم',\n    price: 4500,\n    image: '/images/laptop.jpg',\n    category: 'إلكترونيات',\n    stock: 8,\n    featured: true,\n    createdAt: new Date(),\n    updatedAt: new Date(),\n  },\n  {\n    id: '3',\n    name: 'ساعة ذكية',\n    description: 'ساعة ذكية لتتبع اللياقة البدنية',\n    price: 800,\n    image: '/images/watch.jpg',\n    category: 'إكسسوارات',\n    stock: 25,\n    featured: false,\n    createdAt: new Date(),\n    updatedAt: new Date(),\n  },\n  {\n    id: '4',\n    name: 'سماعات لاسلكية',\n    description: 'سماعات بلوتوث عالية الجودة',\n    price: 350,\n    image: '/images/headphones.jpg',\n    category: 'إكسسوارات',\n    stock: 30,\n    featured: true,\n    createdAt: new Date(),\n    updatedAt: new Date(),\n  },\n  {\n    id: '5',\n    name: 'كاميرا رقمية',\n    description: 'كاميرا احترافية للتصوير الفوتوغرافي',\n    price: 3200,\n    image: '/images/camera.jpg',\n    category: 'إلكترونيات',\n    stock: 12,\n    featured: false,\n    createdAt: new Date(),\n    updatedAt: new Date(),\n  },\n  {\n    id: '6',\n    name: 'تابلت للرسم',\n    description: 'تابلت مخصص للرسم والتصميم الرقمي',\n    price: 1800,\n    image: '/images/tablet.jpg',\n    category: 'إلكترونيات',\n    stock: 18,\n    featured: true,\n    createdAt: new Date(),\n    updatedAt: new Date(),\n  },\n];\n\n// Sample users data\nexport const sampleUsers: User[] = [\n  {\n    id: '1',\n    name: 'أحمد محمد',\n    email: '<EMAIL>',\n    role: 'admin',\n    createdAt: new Date(),\n    updatedAt: new Date(),\n  },\n  {\n    id: '2',\n    name: 'فاطمة علي',\n    email: '<EMAIL>',\n    role: 'customer',\n    createdAt: new Date(),\n    updatedAt: new Date(),\n  },\n];\n\n// Categories\nexport const categories = [\n  'إلكترونيات',\n  'إكسسوارات',\n  'ملابس',\n  'كتب',\n  'رياضة',\n  'منزل وحديقة',\n];\n"], "names": [], "mappings": ";;;;;AAGO,MAAM,iBAA4B;IACvC;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,OAAO;QACP,UAAU;QACV,OAAO;QACP,UAAU;QACV,WAAW,IAAI;QACf,WAAW,IAAI;IACjB;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,OAAO;QACP,UAAU;QACV,OAAO;QACP,UAAU;QACV,WAAW,IAAI;QACf,WAAW,IAAI;IACjB;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,OAAO;QACP,UAAU;QACV,OAAO;QACP,UAAU;QACV,WAAW,IAAI;QACf,WAAW,IAAI;IACjB;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,OAAO;QACP,UAAU;QACV,OAAO;QACP,UAAU;QACV,WAAW,IAAI;QACf,WAAW,IAAI;IACjB;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,OAAO;QACP,UAAU;QACV,OAAO;QACP,UAAU;QACV,WAAW,IAAI;QACf,WAAW,IAAI;IACjB;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,OAAO;QACP,UAAU;QACV,OAAO;QACP,UAAU;QACV,WAAW,IAAI;QACf,WAAW,IAAI;IACjB;CACD;AAGM,MAAM,cAAsB;IACjC;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,MAAM;QACN,WAAW,IAAI;QACf,WAAW,IAAI;IACjB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,MAAM;QACN,WAAW,IAAI;QACf,WAAW,IAAI;IACjB;CACD;AAGM,MAAM,aAAa;IACxB;IACA;IACA;IACA;IACA;IACA;CACD"}}, {"offset": {"line": 120, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 126, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/New%20folder%20%288%29/arabic-ecommerce/src/components/ProductForm.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Product } from '@/types';\nimport { categories } from '@/lib/data';\n\ninterface ProductFormProps {\n  product?: Product | null;\n  isOpen: boolean;\n  onClose: () => void;\n  onSave: (product: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>) => void;\n}\n\nexport default function ProductForm({ product, isOpen, onClose, onSave }: ProductFormProps) {\n  const [formData, setFormData] = useState({\n    name: '',\n    description: '',\n    price: '',\n    image: '',\n    category: '',\n    stock: '',\n    featured: false,\n  });\n  const [isLoading, setIsLoading] = useState(false);\n  const [errors, setErrors] = useState<Record<string, string>>({});\n\n  useEffect(() => {\n    if (product) {\n      setFormData({\n        name: product.name,\n        description: product.description,\n        price: product.price.toString(),\n        image: product.image,\n        category: product.category,\n        stock: product.stock.toString(),\n        featured: product.featured,\n      });\n    } else {\n      setFormData({\n        name: '',\n        description: '',\n        price: '',\n        image: '',\n        category: '',\n        stock: '',\n        featured: false,\n      });\n    }\n    setErrors({});\n  }, [product, isOpen]);\n\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {\n    const { name, value, type } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value,\n    }));\n    // Clear error when user starts typing\n    if (errors[name]) {\n      setErrors(prev => ({ ...prev, [name]: '' }));\n    }\n  };\n\n  const validateForm = () => {\n    const newErrors: Record<string, string> = {};\n\n    if (!formData.name.trim()) {\n      newErrors.name = 'اسم المنتج مطلوب';\n    }\n    if (!formData.description.trim()) {\n      newErrors.description = 'وصف المنتج مطلوب';\n    }\n    if (!formData.price || isNaN(Number(formData.price)) || Number(formData.price) <= 0) {\n      newErrors.price = 'السعر يجب أن يكون رقم صحيح أكبر من صفر';\n    }\n    if (!formData.category) {\n      newErrors.category = 'التصنيف مطلوب';\n    }\n    if (!formData.stock || isNaN(Number(formData.stock)) || Number(formData.stock) < 0) {\n      newErrors.stock = 'الكمية يجب أن تكون رقم صحيح أكبر من أو يساوي صفر';\n    }\n    if (!formData.image.trim()) {\n      newErrors.image = 'رابط الصورة مطلوب';\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (!validateForm()) {\n      return;\n    }\n\n    setIsLoading(true);\n\n    try {\n      // Simulate API call\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      \n      const productData = {\n        name: formData.name.trim(),\n        description: formData.description.trim(),\n        price: Number(formData.price),\n        image: formData.image.trim(),\n        category: formData.category,\n        stock: Number(formData.stock),\n        featured: formData.featured,\n      };\n\n      onSave(productData);\n      onClose();\n    } catch (error) {\n      console.error('Error saving product:', error);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  if (!isOpen) return null;\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n      <div className=\"bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto\">\n        <div className=\"px-6 py-4 border-b border-gray-200\">\n          <div className=\"flex justify-between items-center\">\n            <h2 className=\"text-xl font-bold text-gray-800\">\n              {product ? 'تعديل المنتج' : 'إضافة منتج جديد'}\n            </h2>\n            <button\n              onClick={onClose}\n              className=\"text-gray-400 hover:text-gray-600 text-2xl\"\n            >\n              ×\n            </button>\n          </div>\n        </div>\n\n        <form onSubmit={handleSubmit} className=\"p-6 space-y-6\">\n          {/* Product Name */}\n          <div>\n            <label htmlFor=\"name\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n              اسم المنتج *\n            </label>\n            <input\n              type=\"text\"\n              id=\"name\"\n              name=\"name\"\n              value={formData.name}\n              onChange={handleChange}\n              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 ${\n                errors.name ? 'border-red-500' : 'border-gray-300'\n              }`}\n              placeholder=\"أدخل اسم المنتج\"\n            />\n            {errors.name && <p className=\"text-red-500 text-sm mt-1\">{errors.name}</p>}\n          </div>\n\n          {/* Description */}\n          <div>\n            <label htmlFor=\"description\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n              وصف المنتج *\n            </label>\n            <textarea\n              id=\"description\"\n              name=\"description\"\n              value={formData.description}\n              onChange={handleChange}\n              rows={3}\n              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 ${\n                errors.description ? 'border-red-500' : 'border-gray-300'\n              }`}\n              placeholder=\"أدخل وصف المنتج\"\n            />\n            {errors.description && <p className=\"text-red-500 text-sm mt-1\">{errors.description}</p>}\n          </div>\n\n          {/* Price and Stock */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div>\n              <label htmlFor=\"price\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                السعر (ر.س) *\n              </label>\n              <input\n                type=\"number\"\n                id=\"price\"\n                name=\"price\"\n                value={formData.price}\n                onChange={handleChange}\n                min=\"0\"\n                step=\"0.01\"\n                className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 ${\n                  errors.price ? 'border-red-500' : 'border-gray-300'\n                }`}\n                placeholder=\"0.00\"\n              />\n              {errors.price && <p className=\"text-red-500 text-sm mt-1\">{errors.price}</p>}\n            </div>\n\n            <div>\n              <label htmlFor=\"stock\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                الكمية المتوفرة *\n              </label>\n              <input\n                type=\"number\"\n                id=\"stock\"\n                name=\"stock\"\n                value={formData.stock}\n                onChange={handleChange}\n                min=\"0\"\n                className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 ${\n                  errors.stock ? 'border-red-500' : 'border-gray-300'\n                }`}\n                placeholder=\"0\"\n              />\n              {errors.stock && <p className=\"text-red-500 text-sm mt-1\">{errors.stock}</p>}\n            </div>\n          </div>\n\n          {/* Category */}\n          <div>\n            <label htmlFor=\"category\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n              التصنيف *\n            </label>\n            <select\n              id=\"category\"\n              name=\"category\"\n              value={formData.category}\n              onChange={handleChange}\n              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 ${\n                errors.category ? 'border-red-500' : 'border-gray-300'\n              }`}\n            >\n              <option value=\"\">اختر التصنيف</option>\n              {categories.map((category) => (\n                <option key={category} value={category}>\n                  {category}\n                </option>\n              ))}\n            </select>\n            {errors.category && <p className=\"text-red-500 text-sm mt-1\">{errors.category}</p>}\n          </div>\n\n          {/* Image URL */}\n          <div>\n            <label htmlFor=\"image\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n              رابط الصورة *\n            </label>\n            <input\n              type=\"url\"\n              id=\"image\"\n              name=\"image\"\n              value={formData.image}\n              onChange={handleChange}\n              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 ${\n                errors.image ? 'border-red-500' : 'border-gray-300'\n              }`}\n              placeholder=\"https://example.com/image.jpg\"\n            />\n            {errors.image && <p className=\"text-red-500 text-sm mt-1\">{errors.image}</p>}\n          </div>\n\n          {/* Featured */}\n          <div className=\"flex items-center\">\n            <input\n              type=\"checkbox\"\n              id=\"featured\"\n              name=\"featured\"\n              checked={formData.featured}\n              onChange={handleChange}\n              className=\"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded\"\n            />\n            <label htmlFor=\"featured\" className=\"mr-2 block text-sm text-gray-700\">\n              منتج مميز\n            </label>\n          </div>\n\n          {/* Form Actions */}\n          <div className=\"flex justify-end space-x-3 space-x-reverse pt-6 border-t border-gray-200\">\n            <button\n              type=\"button\"\n              onClick={onClose}\n              className=\"px-4 py-2 text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-lg transition-colors duration-200\"\n            >\n              إلغاء\n            </button>\n            <button\n              type=\"submit\"\n              disabled={isLoading}\n              className={`px-4 py-2 text-white rounded-lg transition-colors duration-200 ${\n                isLoading\n                  ? 'bg-gray-400 cursor-not-allowed'\n                  : 'bg-primary-600 hover:bg-primary-700'\n              }`}\n            >\n              {isLoading ? 'جاري الحفظ...' : product ? 'تحديث المنتج' : 'إضافة المنتج'}\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAJA;;;;AAae,SAAS,YAAY,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAoB;IACxF,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,aAAa;QACb,OAAO;QACP,OAAO;QACP,UAAU;QACV,OAAO;QACP,UAAU;IACZ;IACA,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAE9D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,SAAS;YACX,YAAY;gBACV,MAAM,QAAQ,IAAI;gBAClB,aAAa,QAAQ,WAAW;gBAChC,OAAO,QAAQ,KAAK,CAAC,QAAQ;gBAC7B,OAAO,QAAQ,KAAK;gBACpB,UAAU,QAAQ,QAAQ;gBAC1B,OAAO,QAAQ,KAAK,CAAC,QAAQ;gBAC7B,UAAU,QAAQ,QAAQ;YAC5B;QACF,OAAO;YACL,YAAY;gBACV,MAAM;gBACN,aAAa;gBACb,OAAO;gBACP,OAAO;gBACP,UAAU;gBACV,OAAO;gBACP,UAAU;YACZ;QACF;QACA,UAAU,CAAC;IACb,GAAG;QAAC;QAAS;KAAO;IAEpB,MAAM,eAAe,CAAC;QACpB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM;QACtC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE,SAAS,aAAa,AAAC,EAAE,MAAM,CAAsB,OAAO,GAAG;YACzE,CAAC;QACD,sCAAsC;QACtC,IAAI,MAAM,CAAC,KAAK,EAAE;YAChB,UAAU,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,CAAC,KAAK,EAAE;gBAAG,CAAC;QAC5C;IACF;IAEA,MAAM,eAAe;QACnB,MAAM,YAAoC,CAAC;QAE3C,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,IAAI;YACzB,UAAU,IAAI,GAAG;QACnB;QACA,IAAI,CAAC,SAAS,WAAW,CAAC,IAAI,IAAI;YAChC,UAAU,WAAW,GAAG;QAC1B;QACA,IAAI,CAAC,SAAS,KAAK,IAAI,MAAM,OAAO,SAAS,KAAK,MAAM,OAAO,SAAS,KAAK,KAAK,GAAG;YACnF,UAAU,KAAK,GAAG;QACpB;QACA,IAAI,CAAC,SAAS,QAAQ,EAAE;YACtB,UAAU,QAAQ,GAAG;QACvB;QACA,IAAI,CAAC,SAAS,KAAK,IAAI,MAAM,OAAO,SAAS,KAAK,MAAM,OAAO,SAAS,KAAK,IAAI,GAAG;YAClF,UAAU,KAAK,GAAG;QACpB;QACA,IAAI,CAAC,SAAS,KAAK,CAAC,IAAI,IAAI;YAC1B,UAAU,KAAK,GAAG;QACpB;QAEA,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;IAC3C;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,gBAAgB;YACnB;QACF;QAEA,aAAa;QAEb,IAAI;YACF,oBAAoB;YACpB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,MAAM,cAAc;gBAClB,MAAM,SAAS,IAAI,CAAC,IAAI;gBACxB,aAAa,SAAS,WAAW,CAAC,IAAI;gBACtC,OAAO,OAAO,SAAS,KAAK;gBAC5B,OAAO,SAAS,KAAK,CAAC,IAAI;gBAC1B,UAAU,SAAS,QAAQ;gBAC3B,OAAO,OAAO,SAAS,KAAK;gBAC5B,UAAU,SAAS,QAAQ;YAC7B;YAEA,OAAO;YACP;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;QACzC,SAAU;YACR,aAAa;QACf;IACF;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CACX,UAAU,iBAAiB;;;;;;0CAE9B,8OAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;;;;;;8BAML,8OAAC;oBAAK,UAAU;oBAAc,WAAU;;sCAEtC,8OAAC;;8CACC,8OAAC;oCAAM,SAAQ;oCAAO,WAAU;8CAA+C;;;;;;8CAG/E,8OAAC;oCACC,MAAK;oCACL,IAAG;oCACH,MAAK;oCACL,OAAO,SAAS,IAAI;oCACpB,UAAU;oCACV,WAAW,CAAC,0FAA0F,EACpG,OAAO,IAAI,GAAG,mBAAmB,mBACjC;oCACF,aAAY;;;;;;gCAEb,OAAO,IAAI,kBAAI,8OAAC;oCAAE,WAAU;8CAA6B,OAAO,IAAI;;;;;;;;;;;;sCAIvE,8OAAC;;8CACC,8OAAC;oCAAM,SAAQ;oCAAc,WAAU;8CAA+C;;;;;;8CAGtF,8OAAC;oCACC,IAAG;oCACH,MAAK;oCACL,OAAO,SAAS,WAAW;oCAC3B,UAAU;oCACV,MAAM;oCACN,WAAW,CAAC,0FAA0F,EACpG,OAAO,WAAW,GAAG,mBAAmB,mBACxC;oCACF,aAAY;;;;;;gCAEb,OAAO,WAAW,kBAAI,8OAAC;oCAAE,WAAU;8CAA6B,OAAO,WAAW;;;;;;;;;;;;sCAIrF,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAQ,WAAU;sDAA+C;;;;;;sDAGhF,8OAAC;4CACC,MAAK;4CACL,IAAG;4CACH,MAAK;4CACL,OAAO,SAAS,KAAK;4CACrB,UAAU;4CACV,KAAI;4CACJ,MAAK;4CACL,WAAW,CAAC,0FAA0F,EACpG,OAAO,KAAK,GAAG,mBAAmB,mBAClC;4CACF,aAAY;;;;;;wCAEb,OAAO,KAAK,kBAAI,8OAAC;4CAAE,WAAU;sDAA6B,OAAO,KAAK;;;;;;;;;;;;8CAGzE,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAQ,WAAU;sDAA+C;;;;;;sDAGhF,8OAAC;4CACC,MAAK;4CACL,IAAG;4CACH,MAAK;4CACL,OAAO,SAAS,KAAK;4CACrB,UAAU;4CACV,KAAI;4CACJ,WAAW,CAAC,0FAA0F,EACpG,OAAO,KAAK,GAAG,mBAAmB,mBAClC;4CACF,aAAY;;;;;;wCAEb,OAAO,KAAK,kBAAI,8OAAC;4CAAE,WAAU;sDAA6B,OAAO,KAAK;;;;;;;;;;;;;;;;;;sCAK3E,8OAAC;;8CACC,8OAAC;oCAAM,SAAQ;oCAAW,WAAU;8CAA+C;;;;;;8CAGnF,8OAAC;oCACC,IAAG;oCACH,MAAK;oCACL,OAAO,SAAS,QAAQ;oCACxB,UAAU;oCACV,WAAW,CAAC,0FAA0F,EACpG,OAAO,QAAQ,GAAG,mBAAmB,mBACrC;;sDAEF,8OAAC;4CAAO,OAAM;sDAAG;;;;;;wCAChB,kHAAA,CAAA,aAAU,CAAC,GAAG,CAAC,CAAC,yBACf,8OAAC;gDAAsB,OAAO;0DAC3B;+CADU;;;;;;;;;;;gCAKhB,OAAO,QAAQ,kBAAI,8OAAC;oCAAE,WAAU;8CAA6B,OAAO,QAAQ;;;;;;;;;;;;sCAI/E,8OAAC;;8CACC,8OAAC;oCAAM,SAAQ;oCAAQ,WAAU;8CAA+C;;;;;;8CAGhF,8OAAC;oCACC,MAAK;oCACL,IAAG;oCACH,MAAK;oCACL,OAAO,SAAS,KAAK;oCACrB,UAAU;oCACV,WAAW,CAAC,0FAA0F,EACpG,OAAO,KAAK,GAAG,mBAAmB,mBAClC;oCACF,aAAY;;;;;;gCAEb,OAAO,KAAK,kBAAI,8OAAC;oCAAE,WAAU;8CAA6B,OAAO,KAAK;;;;;;;;;;;;sCAIzE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,MAAK;oCACL,IAAG;oCACH,MAAK;oCACL,SAAS,SAAS,QAAQ;oCAC1B,UAAU;oCACV,WAAU;;;;;;8CAEZ,8OAAC;oCAAM,SAAQ;oCAAW,WAAU;8CAAmC;;;;;;;;;;;;sCAMzE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,MAAK;oCACL,SAAS;oCACT,WAAU;8CACX;;;;;;8CAGD,8OAAC;oCACC,MAAK;oCACL,UAAU;oCACV,WAAW,CAAC,+DAA+D,EACzE,YACI,mCACA,uCACJ;8CAED,YAAY,kBAAkB,UAAU,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOxE"}}, {"offset": {"line": 614, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 620, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/New%20folder%20%288%29/arabic-ecommerce/src/components/DeleteConfirmModal.tsx"], "sourcesContent": ["'use client';\n\ninterface DeleteConfirmModalProps {\n  isOpen: boolean;\n  title: string;\n  message: string;\n  onConfirm: () => void;\n  onCancel: () => void;\n  isLoading?: boolean;\n}\n\nexport default function DeleteConfirmModal({\n  isOpen,\n  title,\n  message,\n  onConfirm,\n  onCancel,\n  isLoading = false,\n}: DeleteConfirmModalProps) {\n  if (!isOpen) return null;\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n      <div className=\"bg-white rounded-lg shadow-xl max-w-md w-full\">\n        <div className=\"p-6\">\n          {/* Icon */}\n          <div className=\"flex items-center justify-center w-12 h-12 mx-auto mb-4 bg-red-100 rounded-full\">\n            <svg\n              className=\"w-6 h-6 text-red-600\"\n              fill=\"none\"\n              stroke=\"currentColor\"\n              viewBox=\"0 0 24 24\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                strokeWidth={2}\n                d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z\"\n              />\n            </svg>\n          </div>\n\n          {/* Title */}\n          <h3 className=\"text-lg font-medium text-gray-900 text-center mb-2\">\n            {title}\n          </h3>\n\n          {/* Message */}\n          <p className=\"text-sm text-gray-500 text-center mb-6\">\n            {message}\n          </p>\n\n          {/* Actions */}\n          <div className=\"flex space-x-3 space-x-reverse\">\n            <button\n              onClick={onCancel}\n              disabled={isLoading}\n              className=\"flex-1 px-4 py-2 text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed\"\n            >\n              إلغاء\n            </button>\n            <button\n              onClick={onConfirm}\n              disabled={isLoading}\n              className={`flex-1 px-4 py-2 text-white rounded-lg transition-colors duration-200 ${\n                isLoading\n                  ? 'bg-gray-400 cursor-not-allowed'\n                  : 'bg-red-600 hover:bg-red-700'\n              }`}\n            >\n              {isLoading ? 'جاري الحذف...' : 'تأكيد الحذف'}\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAWe,SAAS,mBAAmB,EACzC,MAAM,EACN,KAAK,EACL,OAAO,EACP,SAAS,EACT,QAAQ,EACR,YAAY,KAAK,EACO;IACxB,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,WAAU;4BACV,MAAK;4BACL,QAAO;4BACP,SAAQ;sCAER,cAAA,8OAAC;gCACC,eAAc;gCACd,gBAAe;gCACf,aAAa;gCACb,GAAE;;;;;;;;;;;;;;;;kCAMR,8OAAC;wBAAG,WAAU;kCACX;;;;;;kCAIH,8OAAC;wBAAE,WAAU;kCACV;;;;;;kCAIH,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS;gCACT,UAAU;gCACV,WAAU;0CACX;;;;;;0CAGD,8OAAC;gCACC,SAAS;gCACT,UAAU;gCACV,WAAW,CAAC,sEAAsE,EAChF,YACI,mCACA,+BACJ;0CAED,YAAY,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO7C"}}, {"offset": {"line": 724, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 730, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/New%20folder%20%288%29/arabic-ecommerce/src/app/admin/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\nimport { sampleProducts, sampleUsers } from '@/lib/data';\nimport { Product } from '@/types';\nimport ProductForm from '@/components/ProductForm';\nimport DeleteConfirmModal from '@/components/DeleteConfirmModal';\n\nexport default function AdminDashboard() {\n  const [activeTab, setActiveTab] = useState('overview');\n  const [products, setProducts] = useState<Product[]>(sampleProducts);\n  const [isProductFormOpen, setIsProductFormOpen] = useState(false);\n  const [editingProduct, setEditingProduct] = useState<Product | null>(null);\n  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);\n  const [productToDelete, setProductToDelete] = useState<Product | null>(null);\n  const [isDeleting, setIsDeleting] = useState(false);\n\n  const stats = {\n    totalProducts: products.length,\n    totalUsers: sampleUsers.length,\n    totalOrders: 25,\n    totalRevenue: 45000,\n  };\n\n  // Product management functions\n  const handleAddProduct = () => {\n    setEditingProduct(null);\n    setIsProductFormOpen(true);\n  };\n\n  const handleEditProduct = (product: Product) => {\n    setEditingProduct(product);\n    setIsProductFormOpen(true);\n  };\n\n  const handleDeleteProduct = (product: Product) => {\n    setProductToDelete(product);\n    setIsDeleteModalOpen(true);\n  };\n\n  const handleSaveProduct = (productData: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>) => {\n    if (editingProduct) {\n      // Update existing product\n      setProducts(prev => prev.map(p =>\n        p.id === editingProduct.id\n          ? { ...p, ...productData, updatedAt: new Date() }\n          : p\n      ));\n    } else {\n      // Add new product\n      const newProduct: Product = {\n        ...productData,\n        id: Date.now().toString(),\n        createdAt: new Date(),\n        updatedAt: new Date(),\n      };\n      setProducts(prev => [...prev, newProduct]);\n    }\n  };\n\n  const confirmDeleteProduct = async () => {\n    if (!productToDelete) return;\n\n    setIsDeleting(true);\n    try {\n      // Simulate API call\n      await new Promise(resolve => setTimeout(resolve, 1000));\n\n      setProducts(prev => prev.filter(p => p.id !== productToDelete.id));\n      setIsDeleteModalOpen(false);\n      setProductToDelete(null);\n    } catch (error) {\n      console.error('Error deleting product:', error);\n    } finally {\n      setIsDeleting(false);\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <div className=\"bg-white shadow\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center py-6\">\n            <div>\n              <Link href=\"/\" className=\"text-2xl font-bold text-primary-600\">\n                🛍️ المتجر العربي\n              </Link>\n              <p className=\"text-gray-600 mt-1\">لوحة التحكم</p>\n            </div>\n            <div className=\"flex items-center space-x-4 space-x-reverse\">\n              <span className=\"text-gray-700\">مرحباً، أحمد</span>\n              <Link\n                href=\"/\"\n                className=\"bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg transition-colors duration-200\"\n              >\n                عرض المتجر\n              </Link>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Navigation Tabs */}\n        <div className=\"border-b border-gray-200 mb-8\">\n          <nav className=\"-mb-px flex space-x-8 space-x-reverse\">\n            {[\n              { id: 'overview', name: 'نظرة عامة', icon: '📊' },\n              { id: 'products', name: 'المنتجات', icon: '📦' },\n              { id: 'orders', name: 'الطلبات', icon: '🛒' },\n              { id: 'users', name: 'المستخدمين', icon: '👥' },\n            ].map((tab) => (\n              <button\n                key={tab.id}\n                onClick={() => setActiveTab(tab.id)}\n                className={`py-2 px-1 border-b-2 font-medium text-sm ${\n                  activeTab === tab.id\n                    ? 'border-primary-500 text-primary-600'\n                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                }`}\n              >\n                {tab.icon} {tab.name}\n              </button>\n            ))}\n          </nav>\n        </div>\n\n        {/* Overview Tab */}\n        {activeTab === 'overview' && (\n          <div>\n            <h2 className=\"text-2xl font-bold text-gray-800 mb-6\">نظرة عامة</h2>\n\n            {/* Stats Cards */}\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">\n              <div className=\"bg-white rounded-lg shadow p-6\">\n                <div className=\"flex items-center\">\n                  <div className=\"text-3xl\">📦</div>\n                  <div className=\"mr-4\">\n                    <p className=\"text-sm font-medium text-gray-600\">إجمالي المنتجات</p>\n                    <p className=\"text-2xl font-bold text-gray-900\">{stats.totalProducts}</p>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"bg-white rounded-lg shadow p-6\">\n                <div className=\"flex items-center\">\n                  <div className=\"text-3xl\">👥</div>\n                  <div className=\"mr-4\">\n                    <p className=\"text-sm font-medium text-gray-600\">إجمالي المستخدمين</p>\n                    <p className=\"text-2xl font-bold text-gray-900\">{stats.totalUsers}</p>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"bg-white rounded-lg shadow p-6\">\n                <div className=\"flex items-center\">\n                  <div className=\"text-3xl\">🛒</div>\n                  <div className=\"mr-4\">\n                    <p className=\"text-sm font-medium text-gray-600\">إجمالي الطلبات</p>\n                    <p className=\"text-2xl font-bold text-gray-900\">{stats.totalOrders}</p>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"bg-white rounded-lg shadow p-6\">\n                <div className=\"flex items-center\">\n                  <div className=\"text-3xl\">💰</div>\n                  <div className=\"mr-4\">\n                    <p className=\"text-sm font-medium text-gray-600\">إجمالي المبيعات</p>\n                    <p className=\"text-2xl font-bold text-gray-900\">{stats.totalRevenue.toLocaleString('ar-SA')} ر.س</p>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* Recent Activity */}\n            <div className=\"bg-white rounded-lg shadow\">\n              <div className=\"px-6 py-4 border-b border-gray-200\">\n                <h3 className=\"text-lg font-medium text-gray-900\">النشاط الأخير</h3>\n              </div>\n              <div className=\"p-6\">\n                <div className=\"space-y-4\">\n                  <div className=\"flex items-center\">\n                    <div className=\"text-2xl\">🛒</div>\n                    <div className=\"mr-3\">\n                      <p className=\"text-sm font-medium text-gray-900\">طلب جديد #1234</p>\n                      <p className=\"text-sm text-gray-500\">منذ 5 دقائق</p>\n                    </div>\n                  </div>\n                  <div className=\"flex items-center\">\n                    <div className=\"text-2xl\">👤</div>\n                    <div className=\"mr-3\">\n                      <p className=\"text-sm font-medium text-gray-900\">مستخدم جديد: سارة أحمد</p>\n                      <p className=\"text-sm text-gray-500\">منذ 15 دقيقة</p>\n                    </div>\n                  </div>\n                  <div className=\"flex items-center\">\n                    <div className=\"text-2xl\">📦</div>\n                    <div className=\"mr-3\">\n                      <p className=\"text-sm font-medium text-gray-900\">تم تحديث المنتج: هاتف ذكي</p>\n                      <p className=\"text-sm text-gray-500\">منذ ساعة</p>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Products Tab */}\n        {activeTab === 'products' && (\n          <div>\n            <div className=\"flex justify-between items-center mb-6\">\n              <h2 className=\"text-2xl font-bold text-gray-800\">إدارة المنتجات</h2>\n              <button\n                onClick={handleAddProduct}\n                className=\"bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg transition-colors duration-200\"\n              >\n                إضافة منتج جديد\n              </button>\n            </div>\n\n            {products.length === 0 ? (\n              <div className=\"bg-white rounded-lg shadow p-12 text-center\">\n                <div className=\"text-6xl mb-4\">📦</div>\n                <h3 className=\"text-xl font-semibold text-gray-700 mb-2\">\n                  لا توجد منتجات\n                </h3>\n                <p className=\"text-gray-500 mb-6\">\n                  لم يتم إضافة أي منتجات بعد. ابدأ بإضافة منتجك الأول!\n                </p>\n                <button\n                  onClick={handleAddProduct}\n                  className=\"bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-lg transition-colors duration-200\"\n                >\n                  إضافة منتج جديد\n                </button>\n              </div>\n            ) : (\n              <div className=\"bg-white rounded-lg shadow overflow-hidden\">\n                <table className=\"min-w-full divide-y divide-gray-200\">\n                  <thead className=\"bg-gray-50\">\n                    <tr>\n                      <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        المنتج\n                      </th>\n                      <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        السعر\n                      </th>\n                      <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        المخزون\n                      </th>\n                      <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        الحالة\n                      </th>\n                      <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        الإجراءات\n                      </th>\n                    </tr>\n                  </thead>\n                  <tbody className=\"bg-white divide-y divide-gray-200\">\n                    {products.map((product) => (\n                    <tr key={product.id}>\n                      <td className=\"px-6 py-4 whitespace-nowrap\">\n                        <div className=\"flex items-center\">\n                          <div className=\"h-10 w-10 bg-gray-200 rounded-lg flex items-center justify-center\">\n                            <span className=\"text-xs text-gray-500\">📦</span>\n                          </div>\n                          <div className=\"mr-4\">\n                            <div className=\"text-sm font-medium text-gray-900\">{product.name}</div>\n                            <div className=\"text-sm text-gray-500\">{product.category}</div>\n                            {product.featured && (\n                              <span className=\"inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800\">\n                                مميز\n                              </span>\n                            )}\n                          </div>\n                        </div>\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                        {product.price.toLocaleString('ar-SA')} ر.س\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                        {product.stock}\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap\">\n                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${\n                          product.stock > 0 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'\n                        }`}>\n                          {product.stock > 0 ? 'متوفر' : 'نفد المخزون'}\n                        </span>\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                        <button\n                          onClick={() => handleEditProduct(product)}\n                          className=\"text-primary-600 hover:text-primary-900 ml-4\"\n                        >\n                          تعديل\n                        </button>\n                        <button\n                          onClick={() => handleDeleteProduct(product)}\n                          className=\"text-red-600 hover:text-red-900\"\n                        >\n                          حذف\n                        </button>\n                      </td>\n                    </tr>\n                  ))}\n                  </tbody>\n                </table>\n              </div>\n            )}\n          </div>\n        )}\n\n        {/* Orders Tab */}\n        {activeTab === 'orders' && (\n          <div>\n            <h2 className=\"text-2xl font-bold text-gray-800 mb-6\">إدارة الطلبات</h2>\n            <div className=\"bg-white rounded-lg shadow p-6\">\n              <p className=\"text-gray-600 text-center\">سيتم إضافة إدارة الطلبات قريباً...</p>\n            </div>\n          </div>\n        )}\n\n        {/* Users Tab */}\n        {activeTab === 'users' && (\n          <div>\n            <h2 className=\"text-2xl font-bold text-gray-800 mb-6\">إدارة المستخدمين</h2>\n            <div className=\"bg-white rounded-lg shadow p-6\">\n              <p className=\"text-gray-600 text-center\">سيتم إضافة إدارة المستخدمين قريباً...</p>\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* Product Form Modal */}\n      <ProductForm\n        product={editingProduct}\n        isOpen={isProductFormOpen}\n        onClose={() => {\n          setIsProductFormOpen(false);\n          setEditingProduct(null);\n        }}\n        onSave={handleSaveProduct}\n      />\n\n      {/* Delete Confirmation Modal */}\n      <DeleteConfirmModal\n        isOpen={isDeleteModalOpen}\n        title=\"تأكيد حذف المنتج\"\n        message={`هل أنت متأكد من حذف المنتج \"${productToDelete?.name}\"؟ لا يمكن التراجع عن هذا الإجراء.`}\n        onConfirm={confirmDeleteProduct}\n        onCancel={() => {\n          setIsDeleteModalOpen(false);\n          setProductToDelete(null);\n        }}\n        isLoading={isDeleting}\n      />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AACA;AAPA;;;;;;;AASe,SAAS;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa,kHAAA,CAAA,iBAAc;IAClE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IACrE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IACvE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,QAAQ;QACZ,eAAe,SAAS,MAAM;QAC9B,YAAY,kHAAA,CAAA,cAAW,CAAC,MAAM;QAC9B,aAAa;QACb,cAAc;IAChB;IAEA,+BAA+B;IAC/B,MAAM,mBAAmB;QACvB,kBAAkB;QAClB,qBAAqB;IACvB;IAEA,MAAM,oBAAoB,CAAC;QACzB,kBAAkB;QAClB,qBAAqB;IACvB;IAEA,MAAM,sBAAsB,CAAC;QAC3B,mBAAmB;QACnB,qBAAqB;IACvB;IAEA,MAAM,oBAAoB,CAAC;QACzB,IAAI,gBAAgB;YAClB,0BAA0B;YAC1B,YAAY,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,IAC3B,EAAE,EAAE,KAAK,eAAe,EAAE,GACtB;wBAAE,GAAG,CAAC;wBAAE,GAAG,WAAW;wBAAE,WAAW,IAAI;oBAAO,IAC9C;QAER,OAAO;YACL,kBAAkB;YAClB,MAAM,aAAsB;gBAC1B,GAAG,WAAW;gBACd,IAAI,KAAK,GAAG,GAAG,QAAQ;gBACvB,WAAW,IAAI;gBACf,WAAW,IAAI;YACjB;YACA,YAAY,CAAA,OAAQ;uBAAI;oBAAM;iBAAW;QAC3C;IACF;IAEA,MAAM,uBAAuB;QAC3B,IAAI,CAAC,iBAAiB;QAEtB,cAAc;QACd,IAAI;YACF,oBAAoB;YACpB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,YAAY,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,gBAAgB,EAAE;YAChE,qBAAqB;YACrB,mBAAmB;QACrB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;QAC3C,SAAU;YACR,cAAc;QAChB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAI,WAAU;kDAAsC;;;;;;kDAG/D,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;;;;;;;0CAEpC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;kDAChC,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQT,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACZ;gCACC;oCAAE,IAAI;oCAAY,MAAM;oCAAa,MAAM;gCAAK;gCAChD;oCAAE,IAAI;oCAAY,MAAM;oCAAY,MAAM;gCAAK;gCAC/C;oCAAE,IAAI;oCAAU,MAAM;oCAAW,MAAM;gCAAK;gCAC5C;oCAAE,IAAI;oCAAS,MAAM;oCAAc,MAAM;gCAAK;6BAC/C,CAAC,GAAG,CAAC,CAAC,oBACL,8OAAC;oCAEC,SAAS,IAAM,aAAa,IAAI,EAAE;oCAClC,WAAW,CAAC,yCAAyC,EACnD,cAAc,IAAI,EAAE,GAChB,wCACA,8EACJ;;wCAED,IAAI,IAAI;wCAAC;wCAAE,IAAI,IAAI;;mCARf,IAAI,EAAE;;;;;;;;;;;;;;;oBAelB,cAAc,4BACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAwC;;;;;;0CAGtD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAW;;;;;;8DAC1B,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;sEAAoC;;;;;;sEACjD,8OAAC;4DAAE,WAAU;sEAAoC,MAAM,aAAa;;;;;;;;;;;;;;;;;;;;;;;kDAK1E,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAW;;;;;;8DAC1B,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;sEAAoC;;;;;;sEACjD,8OAAC;4DAAE,WAAU;sEAAoC,MAAM,UAAU;;;;;;;;;;;;;;;;;;;;;;;kDAKvE,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAW;;;;;;8DAC1B,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;sEAAoC;;;;;;sEACjD,8OAAC;4DAAE,WAAU;sEAAoC,MAAM,WAAW;;;;;;;;;;;;;;;;;;;;;;;kDAKxE,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAW;;;;;;8DAC1B,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;sEAAoC;;;;;;sEACjD,8OAAC;4DAAE,WAAU;;gEAAoC,MAAM,YAAY,CAAC,cAAc,CAAC;gEAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAOpG,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAG,WAAU;sDAAoC;;;;;;;;;;;kDAEpD,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEAAW;;;;;;sEAC1B,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAE,WAAU;8EAAoC;;;;;;8EACjD,8OAAC;oEAAE,WAAU;8EAAwB;;;;;;;;;;;;;;;;;;8DAGzC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEAAW;;;;;;sEAC1B,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAE,WAAU;8EAAoC;;;;;;8EACjD,8OAAC;oEAAE,WAAU;8EAAwB;;;;;;;;;;;;;;;;;;8DAGzC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEAAW;;;;;;sEAC1B,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAE,WAAU;8EAAoC;;;;;;8EACjD,8OAAC;oEAAE,WAAU;8EAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAUlD,cAAc,4BACb,8OAAC;;0CACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAmC;;;;;;kDACjD,8OAAC;wCACC,SAAS;wCACT,WAAU;kDACX;;;;;;;;;;;;4BAKF,SAAS,MAAM,KAAK,kBACnB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAgB;;;;;;kDAC/B,8OAAC;wCAAG,WAAU;kDAA2C;;;;;;kDAGzD,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;kDAGlC,8OAAC;wCACC,SAAS;wCACT,WAAU;kDACX;;;;;;;;;;;qDAKH,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAM,WAAU;;sDACf,8OAAC;4CAAM,WAAU;sDACf,cAAA,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAkF;;;;;;kEAGhG,8OAAC;wDAAG,WAAU;kEAAkF;;;;;;kEAGhG,8OAAC;wDAAG,WAAU;kEAAkF;;;;;;kEAGhG,8OAAC;wDAAG,WAAU;kEAAkF;;;;;;kEAGhG,8OAAC;wDAAG,WAAU;kEAAkF;;;;;;;;;;;;;;;;;sDAKpG,8OAAC;4CAAM,WAAU;sDACd,SAAS,GAAG,CAAC,CAAC,wBACf,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEACZ,cAAA,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC;4EAAK,WAAU;sFAAwB;;;;;;;;;;;kFAE1C,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAI,WAAU;0FAAqC,QAAQ,IAAI;;;;;;0FAChE,8OAAC;gFAAI,WAAU;0FAAyB,QAAQ,QAAQ;;;;;;4EACvD,QAAQ,QAAQ,kBACf,8OAAC;gFAAK,WAAU;0FAAiG;;;;;;;;;;;;;;;;;;;;;;;sEAOzH,8OAAC;4DAAG,WAAU;;gEACX,QAAQ,KAAK,CAAC,cAAc,CAAC;gEAAS;;;;;;;sEAEzC,8OAAC;4DAAG,WAAU;sEACX,QAAQ,KAAK;;;;;;sEAEhB,8OAAC;4DAAG,WAAU;sEACZ,cAAA,8OAAC;gEAAK,WAAW,CAAC,yDAAyD,EACzE,QAAQ,KAAK,GAAG,IAAI,gCAAgC,2BACpD;0EACC,QAAQ,KAAK,GAAG,IAAI,UAAU;;;;;;;;;;;sEAGnC,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC;oEACC,SAAS,IAAM,kBAAkB;oEACjC,WAAU;8EACX;;;;;;8EAGD,8OAAC;oEACC,SAAS,IAAM,oBAAoB;oEACnC,WAAU;8EACX;;;;;;;;;;;;;mDAxCI,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAsD9B,cAAc,0BACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAwC;;;;;;0CACtD,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAE,WAAU;8CAA4B;;;;;;;;;;;;;;;;;oBAM9C,cAAc,yBACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAwC;;;;;;0CACtD,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAE,WAAU;8CAA4B;;;;;;;;;;;;;;;;;;;;;;;0BAOjD,8OAAC,iIAAA,CAAA,UAAW;gBACV,SAAS;gBACT,QAAQ;gBACR,SAAS;oBACP,qBAAqB;oBACrB,kBAAkB;gBACpB;gBACA,QAAQ;;;;;;0BAIV,8OAAC,wIAAA,CAAA,UAAkB;gBACjB,QAAQ;gBACR,OAAM;gBACN,SAAS,CAAC,4BAA4B,EAAE,iBAAiB,KAAK,kCAAkC,CAAC;gBACjG,WAAW;gBACX,UAAU;oBACR,qBAAqB;oBACrB,mBAAmB;gBACrB;gBACA,WAAW;;;;;;;;;;;;AAInB"}}, {"offset": {"line": 1717, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}