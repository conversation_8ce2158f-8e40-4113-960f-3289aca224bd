# Database Mode Configuration
# Set to 'true' to use Supabase database, 'false' to use mock data
NEXT_PUBLIC_USE_DATABASE=false

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# Payment Gateway Configuration
# Stripe
NEXT_PUBLIC_STRIPE_PUBLIC_KEY=pk_test_placeholder
STRIPE_SECRET_KEY=sk_test_placeholder
STRIPE_WEBHOOK_SECRET=whsec_placeholder

# Tabby
NEXT_PUBLIC_TABBY_PUBLIC_KEY=pk_test_placeholder
TABBY_SECRET_KEY=sk_test_placeholder
TABBY_MERCHANT_CODE=merchant_placeholder
TABBY_ENVIRONMENT=sandbox

# STC Pay
STC_PAY_MERCHANT_ID=merchant_placeholder
STC_PAY_API_KEY=api_key_placeholder
STC_PAY_SECRET_KEY=secret_placeholder
STC_PAY_ENVIRONMENT=sandbox

# App Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000

# Database Configuration
DATABASE_URL=your_database_connection_string

# App Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_APP_NAME=المتجر العربي

# Feature Flags
NEXT_PUBLIC_ENABLE_PAYMENTS=false
NEXT_PUBLIC_ENABLE_AUTH=false
NEXT_PUBLIC_ENABLE_ANALYTICS=false

# Payment Configuration (for future use)
STRIPE_SECRET_KEY=your_stripe_secret_key
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key

# Email Configuration (for future use)
SMTP_HOST=your_smtp_host
SMTP_PORT=587
SMTP_USER=your_smtp_user
SMTP_PASS=your_smtp_password
