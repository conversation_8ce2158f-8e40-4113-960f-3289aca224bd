{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/New%20folder%20%288%29/arabic-ecommerce/src/components/Navbar.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { useState } from 'react';\n\nexport default function Navbar() {\n  const [isLoggedIn, setIsLoggedIn] = useState(false);\n  const [cartItemsCount, setCartItemsCount] = useState(0);\n\n  return (\n    <nav className=\"bg-white shadow-lg border-b border-gray-200\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center h-16\">\n          {/* Logo */}\n          <div className=\"flex-shrink-0\">\n            <Link href=\"/\" className=\"text-2xl font-bold text-primary-600\">\n              🛍️ المتجر العربي\n            </Link>\n          </div>\n\n          {/* Navigation Links */}\n          <div className=\"hidden md:block\">\n            <div className=\"mr-10 flex items-baseline space-x-4 space-x-reverse\">\n              <Link\n                href=\"/\"\n                className=\"text-gray-700 hover:text-primary-600 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 flex items-center\"\n              >\n                🏠 الصفحة الرئيسية\n              </Link>\n              <Link\n                href=\"/products\"\n                className=\"text-gray-700 hover:text-primary-600 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 flex items-center\"\n              >\n                📦 المنتجات\n              </Link>\n              {isLoggedIn && (\n                <Link\n                  href=\"/admin\"\n                  className=\"text-gray-700 hover:text-primary-600 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 flex items-center\"\n                >\n                  ⚙️ لوحة التحكم\n                </Link>\n              )}\n            </div>\n          </div>\n\n          {/* Right side - Cart and Auth */}\n          <div className=\"flex items-center space-x-4 space-x-reverse\">\n            {/* Cart */}\n            <Link\n              href=\"/cart\"\n              className=\"relative text-gray-700 hover:text-primary-600 transition-colors duration-200\"\n            >\n              <span className=\"text-2xl\">🛒</span>\n              {cartItemsCount > 0 && (\n                <span className=\"absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center\">\n                  {cartItemsCount}\n                </span>\n              )}\n            </Link>\n\n            {/* Auth Links */}\n            {isLoggedIn ? (\n              <div className=\"flex items-center space-x-2 space-x-reverse\">\n                <span className=\"text-gray-700 text-sm\">مرحباً، أحمد</span>\n                <button\n                  onClick={() => setIsLoggedIn(false)}\n                  className=\"bg-red-500 hover:bg-red-600 text-white px-3 py-1 rounded text-sm transition-colors duration-200\"\n                >\n                  تسجيل الخروج\n                </button>\n              </div>\n            ) : (\n              <div className=\"flex items-center space-x-2 space-x-reverse\">\n                <Link\n                  href=\"/login\"\n                  className=\"text-gray-700 hover:text-primary-600 px-3 py-1 rounded text-sm transition-colors duration-200\"\n                >\n                  تسجيل الدخول\n                </Link>\n                <Link\n                  href=\"/register\"\n                  className=\"bg-primary-600 hover:bg-primary-700 text-white px-3 py-1 rounded text-sm transition-colors duration-200\"\n                >\n                  إنشاء حساب\n                </Link>\n              </div>\n            )}\n          </div>\n\n          {/* Mobile menu button */}\n          <div className=\"md:hidden\">\n            <button\n              type=\"button\"\n              className=\"text-gray-700 hover:text-primary-600 focus:outline-none focus:text-primary-600\"\n            >\n              <svg className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\n              </svg>\n            </button>\n          </div>\n        </div>\n      </div>\n    </nav>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;sCAAsC;;;;;;;;;;;kCAMjE,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCA<PERSON>,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;gCAGA,4BACC,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;kCAQP,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;kDAEV,8OAAC;wCAAK,WAAU;kDAAW;;;;;;oCAC1B,iBAAiB,mBAChB,8OAAC;wCAAK,WAAU;kDACb;;;;;;;;;;;;4BAMN,2BACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAwB;;;;;;kDACxC,8OAAC;wCACC,SAAS,IAAM,cAAc;wCAC7B,WAAU;kDACX;;;;;;;;;;;qDAKH,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;kCAQP,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,MAAK;4BACL,WAAU;sCAEV,cAAA,8OAAC;gCAAI,WAAU;gCAAU,MAAK;gCAAO,SAAQ;gCAAY,QAAO;0CAC9D,cAAA,8OAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQrF"}}, {"offset": {"line": 233, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 239, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/New%20folder%20%288%29/arabic-ecommerce/src/components/ProductCard.tsx"], "sourcesContent": ["'use client';\n\nimport { Product } from '@/types';\nimport Image from 'next/image';\nimport Link from 'next/link';\n\ninterface ProductCardProps {\n  product: Product;\n  onAddToCart?: (productId: string) => void;\n}\n\nexport default function ProductCard({ product, onAddToCart }: ProductCardProps) {\n  const handleAddToCart = () => {\n    if (onAddToCart) {\n      onAddToCart(product.id);\n    }\n  };\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 overflow-hidden\">\n      {/* Product Image */}\n      <div className=\"relative h-48 bg-gray-200\">\n        <Image\n          src={product.image}\n          alt={product.name}\n          fill\n          className=\"object-cover\"\n          onError={(e) => {\n            // Fallback image if the original fails to load\n            const target = e.target as HTMLImageElement;\n            target.src = '/images/placeholder.jpg';\n          }}\n        />\n        {product.featured && (\n          <div className=\"absolute top-2 right-2 bg-yellow-500 text-white px-2 py-1 rounded text-xs font-bold\">\n            مميز\n          </div>\n        )}\n        {product.stock === 0 && (\n          <div className=\"absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center\">\n            <span className=\"text-white font-bold text-lg\">نفد المخزون</span>\n          </div>\n        )}\n      </div>\n\n      {/* Product Info */}\n      <div className=\"p-4\">\n        <div className=\"mb-2\">\n          <span className=\"text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded\">\n            {product.category}\n          </span>\n        </div>\n        \n        <h3 className=\"text-lg font-semibold text-gray-800 mb-2 line-clamp-2\">\n          {product.name}\n        </h3>\n        \n        <p className=\"text-gray-600 text-sm mb-3 line-clamp-2\">\n          {product.description}\n        </p>\n\n        <div className=\"flex items-center justify-between mb-3\">\n          <span className=\"text-2xl font-bold text-primary-600\">\n            {product.price.toLocaleString('ar-SA')} ر.س\n          </span>\n          <span className=\"text-sm text-gray-500\">\n            متوفر: {product.stock}\n          </span>\n        </div>\n\n        {/* Action Buttons */}\n        <div className=\"flex space-x-2 space-x-reverse\">\n          <button\n            onClick={handleAddToCart}\n            disabled={product.stock === 0}\n            className={`flex-1 py-2 px-4 rounded-lg font-medium transition-colors duration-200 ${\n              product.stock === 0\n                ? 'bg-gray-300 text-gray-500 cursor-not-allowed'\n                : 'bg-primary-600 hover:bg-primary-700 text-white'\n            }`}\n          >\n            {product.stock === 0 ? 'نفد المخزون' : 'أضف للسلة'}\n          </button>\n          \n          <Link\n            href={`/products/${product.id}`}\n            className=\"px-4 py-2 border border-primary-600 text-primary-600 hover:bg-primary-600 hover:text-white rounded-lg font-medium transition-colors duration-200\"\n          >\n            عرض\n          </Link>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAJA;;;;AAWe,SAAS,YAAY,EAAE,OAAO,EAAE,WAAW,EAAoB;IAC5E,MAAM,kBAAkB;QACtB,IAAI,aAAa;YACf,YAAY,QAAQ,EAAE;QACxB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,6HAAA,CAAA,UAAK;wBACJ,KAAK,QAAQ,KAAK;wBAClB,KAAK,QAAQ,IAAI;wBACjB,IAAI;wBACJ,WAAU;wBACV,SAAS,CAAC;4BACR,+CAA+C;4BAC/C,MAAM,SAAS,EAAE,MAAM;4BACvB,OAAO,GAAG,GAAG;wBACf;;;;;;oBAED,QAAQ,QAAQ,kBACf,8OAAC;wBAAI,WAAU;kCAAsF;;;;;;oBAItG,QAAQ,KAAK,KAAK,mBACjB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAK,WAAU;sCAA+B;;;;;;;;;;;;;;;;;0BAMrD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAK,WAAU;sCACb,QAAQ,QAAQ;;;;;;;;;;;kCAIrB,8OAAC;wBAAG,WAAU;kCACX,QAAQ,IAAI;;;;;;kCAGf,8OAAC;wBAAE,WAAU;kCACV,QAAQ,WAAW;;;;;;kCAGtB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,WAAU;;oCACb,QAAQ,KAAK,CAAC,cAAc,CAAC;oCAAS;;;;;;;0CAEzC,8OAAC;gCAAK,WAAU;;oCAAwB;oCAC9B,QAAQ,KAAK;;;;;;;;;;;;;kCAKzB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS;gCACT,UAAU,QAAQ,KAAK,KAAK;gCAC5B,WAAW,CAAC,uEAAuE,EACjF,QAAQ,KAAK,KAAK,IACd,iDACA,kDACJ;0CAED,QAAQ,KAAK,KAAK,IAAI,gBAAgB;;;;;;0CAGzC,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAM,CAAC,UAAU,EAAE,QAAQ,EAAE,EAAE;gCAC/B,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;AAOX"}}, {"offset": {"line": 411, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 417, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/New%20folder%20%288%29/arabic-ecommerce/src/lib/data.ts"], "sourcesContent": ["import { Product, User } from '@/types';\n\n// Sample products data\nexport const sampleProducts: Product[] = [\n  {\n    id: '1',\n    name: 'هاتف ذكي متطور',\n    description: 'هاتف ذكي بمواصفات عالية وكاميرا متقدمة',\n    price: 2500,\n    image: '/images/phone.jpg',\n    category: 'إلكترونيات',\n    stock: 15,\n    featured: true,\n    createdAt: new Date(),\n    updatedAt: new Date(),\n  },\n  {\n    id: '2',\n    name: 'لابتوب للألعاب',\n    description: 'لابتوب قوي مخصص للألعاب والتصميم',\n    price: 4500,\n    image: '/images/laptop.jpg',\n    category: 'إلكترونيات',\n    stock: 8,\n    featured: true,\n    createdAt: new Date(),\n    updatedAt: new Date(),\n  },\n  {\n    id: '3',\n    name: 'ساعة ذكية',\n    description: 'ساعة ذكية لتتبع اللياقة البدنية',\n    price: 800,\n    image: '/images/watch.jpg',\n    category: 'إكسسوارات',\n    stock: 25,\n    featured: false,\n    createdAt: new Date(),\n    updatedAt: new Date(),\n  },\n  {\n    id: '4',\n    name: 'سماعات لاسلكية',\n    description: 'سماعات بلوتوث عالية الجودة',\n    price: 350,\n    image: '/images/headphones.jpg',\n    category: 'إكسسوارات',\n    stock: 30,\n    featured: true,\n    createdAt: new Date(),\n    updatedAt: new Date(),\n  },\n  {\n    id: '5',\n    name: 'كاميرا رقمية',\n    description: 'كاميرا احترافية للتصوير الفوتوغرافي',\n    price: 3200,\n    image: '/images/camera.jpg',\n    category: 'إلكترونيات',\n    stock: 12,\n    featured: false,\n    createdAt: new Date(),\n    updatedAt: new Date(),\n  },\n  {\n    id: '6',\n    name: 'تابلت للرسم',\n    description: 'تابلت مخصص للرسم والتصميم الرقمي',\n    price: 1800,\n    image: '/images/tablet.jpg',\n    category: 'إلكترونيات',\n    stock: 18,\n    featured: true,\n    createdAt: new Date(),\n    updatedAt: new Date(),\n  },\n];\n\n// Sample users data\nexport const sampleUsers: User[] = [\n  {\n    id: '1',\n    name: 'أحمد محمد',\n    email: '<EMAIL>',\n    role: 'admin',\n    createdAt: new Date(),\n    updatedAt: new Date(),\n  },\n  {\n    id: '2',\n    name: 'فاطمة علي',\n    email: '<EMAIL>',\n    role: 'customer',\n    createdAt: new Date(),\n    updatedAt: new Date(),\n  },\n];\n\n// Categories\nexport const categories = [\n  'إلكترونيات',\n  'إكسسوارات',\n  'ملابس',\n  'كتب',\n  'رياضة',\n  'منزل وحديقة',\n];\n"], "names": [], "mappings": ";;;;;AAGO,MAAM,iBAA4B;IACvC;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,OAAO;QACP,UAAU;QACV,OAAO;QACP,UAAU;QACV,WAAW,IAAI;QACf,WAAW,IAAI;IACjB;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,OAAO;QACP,UAAU;QACV,OAAO;QACP,UAAU;QACV,WAAW,IAAI;QACf,WAAW,IAAI;IACjB;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,OAAO;QACP,UAAU;QACV,OAAO;QACP,UAAU;QACV,WAAW,IAAI;QACf,WAAW,IAAI;IACjB;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,OAAO;QACP,UAAU;QACV,OAAO;QACP,UAAU;QACV,WAAW,IAAI;QACf,WAAW,IAAI;IACjB;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,OAAO;QACP,UAAU;QACV,OAAO;QACP,UAAU;QACV,WAAW,IAAI;QACf,WAAW,IAAI;IACjB;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,OAAO;QACP,UAAU;QACV,OAAO;QACP,UAAU;QACV,WAAW,IAAI;QACf,WAAW,IAAI;IACjB;CACD;AAGM,MAAM,cAAsB;IACjC;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,MAAM;QACN,WAAW,IAAI;QACf,WAAW,IAAI;IACjB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,MAAM;QACN,WAAW,IAAI;QACf,WAAW,IAAI;IACjB;CACD;AAGM,MAAM,aAAa;IACxB;IACA;IACA;IACA;IACA;IACA;CACD"}}, {"offset": {"line": 522, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 528, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/New%20folder%20%288%29/arabic-ecommerce/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport Navbar from '@/components/Navbar';\nimport ProductCard from '@/components/ProductCard';\nimport { sampleProducts } from '@/lib/data';\nimport Link from 'next/link';\n\nexport default function Home() {\n  const featuredProducts = sampleProducts.filter(product => product.featured);\n\n  const handleAddToCart = (productId: string) => {\n    // TODO: Implement add to cart functionality\n    console.log('Adding product to cart:', productId);\n    alert('تم إضافة المنتج إلى السلة!');\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Navbar />\n\n      {/* Hero Section */}\n      <section className=\"bg-gradient-to-r from-primary-600 to-secondary-600 text-white py-20\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n          <h1 className=\"text-4xl md:text-6xl font-bold mb-6\">\n            مرحباً بك في المتجر العربي\n          </h1>\n          <p className=\"text-xl md:text-2xl mb-8 opacity-90\">\n            اكتشف أفضل المنتجات بأسعار مميزة وجودة عالية\n          </p>\n          <Link\n            href=\"/products\"\n            className=\"inline-block bg-white text-primary-600 font-bold py-3 px-8 rounded-lg hover:bg-gray-100 transition-colors duration-200 text-lg\"\n          >\n            تصفح المنتجات 🛍️\n          </Link>\n        </div>\n      </section>\n\n      {/* Features Section */}\n      <section className=\"py-16 bg-white\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n            <div className=\"text-center\">\n              <div className=\"text-4xl mb-4\">🚚</div>\n              <h3 className=\"text-xl font-semibold mb-2\">شحن سريع</h3>\n              <p className=\"text-gray-600\">توصيل سريع لجميع أنحاء المملكة</p>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-4xl mb-4\">💳</div>\n              <h3 className=\"text-xl font-semibold mb-2\">دفع آمن</h3>\n              <p className=\"text-gray-600\">طرق دفع متعددة وآمنة</p>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-4xl mb-4\">🔄</div>\n              <h3 className=\"text-xl font-semibold mb-2\">إرجاع مجاني</h3>\n              <p className=\"text-gray-600\">إمكانية الإرجاع خلال 30 يوم</p>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Featured Products */}\n      <section className=\"py-16\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-12\">\n            <h2 className=\"text-3xl font-bold text-gray-800 mb-4\">\n              المنتجات المميزة\n            </h2>\n            <p className=\"text-gray-600 text-lg\">\n              اكتشف أفضل منتجاتنا الأكثر مبيعاً\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\">\n            {featuredProducts.map((product) => (\n              <ProductCard\n                key={product.id}\n                product={product}\n                onAddToCart={handleAddToCart}\n              />\n            ))}\n          </div>\n\n          <div className=\"text-center mt-12\">\n            <Link\n              href=\"/products\"\n              className=\"inline-block bg-primary-600 hover:bg-primary-700 text-white font-bold py-3 px-8 rounded-lg transition-colors duration-200\"\n            >\n              عرض جميع المنتجات\n            </Link>\n          </div>\n        </div>\n      </section>\n\n      {/* Footer */}\n      <footer className=\"bg-gray-800 text-white py-12\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n            <div>\n              <h3 className=\"text-xl font-bold mb-4\">المتجر العربي</h3>\n              <p className=\"text-gray-300\">\n                متجرك الإلكتروني المفضل للحصول على أفضل المنتجات بأسعار مميزة\n              </p>\n            </div>\n            <div>\n              <h4 className=\"text-lg font-semibold mb-4\">روابط سريعة</h4>\n              <ul className=\"space-y-2\">\n                <li><Link href=\"/\" className=\"text-gray-300 hover:text-white\">الصفحة الرئيسية</Link></li>\n                <li><Link href=\"/products\" className=\"text-gray-300 hover:text-white\">المنتجات</Link></li>\n                <li><Link href=\"/about\" className=\"text-gray-300 hover:text-white\">من نحن</Link></li>\n                <li><Link href=\"/contact\" className=\"text-gray-300 hover:text-white\">اتصل بنا</Link></li>\n              </ul>\n            </div>\n            <div>\n              <h4 className=\"text-lg font-semibold mb-4\">تواصل معنا</h4>\n              <p className=\"text-gray-300 mb-2\">📧 <EMAIL></p>\n              <p className=\"text-gray-300 mb-2\">📞 +966 50 123 4567</p>\n              <p className=\"text-gray-300\">📍 الرياض، المملكة العربية السعودية</p>\n            </div>\n          </div>\n          <div className=\"border-t border-gray-700 mt-8 pt-8 text-center\">\n            <p className=\"text-gray-300\">\n              © 2024 المتجر العربي. جميع الحقوق محفوظة.\n            </p>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOe,SAAS;IACtB,MAAM,mBAAmB,kHAAA,CAAA,iBAAc,CAAC,MAAM,CAAC,CAAA,UAAW,QAAQ,QAAQ;IAE1E,MAAM,kBAAkB,CAAC;QACvB,4CAA4C;QAC5C,QAAQ,GAAG,CAAC,2BAA2B;QACvC,MAAM;IACR;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,4HAAA,CAAA,UAAM;;;;;0BAGP,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAsC;;;;;;sCAGpD,8OAAC;4BAAE,WAAU;sCAAsC;;;;;;sCAGnD,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;sCACX;;;;;;;;;;;;;;;;;0BAOL,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAgB;;;;;;kDAC/B,8OAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,8OAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;0CAE/B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAgB;;;;;;kDAC/B,8OAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,8OAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;0CAE/B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAgB;;;;;;kDAC/B,8OAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,8OAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOrC,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CAGtD,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAKvC,8OAAC;4BAAI,WAAU;sCACZ,iBAAiB,GAAG,CAAC,CAAC,wBACrB,8OAAC,iIAAA,CAAA,UAAW;oCAEV,SAAS;oCACT,aAAa;mCAFR,QAAQ,EAAE;;;;;;;;;;sCAOrB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;0BAQP,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAyB;;;;;;sDACvC,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAI/B,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;8DAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAI,WAAU;kEAAiC;;;;;;;;;;;8DAC9D,8OAAC;8DAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAY,WAAU;kEAAiC;;;;;;;;;;;8DACtE,8OAAC;8DAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAS,WAAU;kEAAiC;;;;;;;;;;;8DACnE,8OAAC;8DAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAW,WAAU;kEAAiC;;;;;;;;;;;;;;;;;;;;;;;8CAGzE,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,8OAAC;4CAAE,WAAU;sDAAqB;;;;;;sDAClC,8OAAC;4CAAE,WAAU;sDAAqB;;;;;;sDAClC,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;;;;;;;sCAGjC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQzC"}}, {"offset": {"line": 988, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}