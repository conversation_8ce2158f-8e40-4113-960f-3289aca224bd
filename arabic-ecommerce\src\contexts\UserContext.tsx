'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { User } from '@/types';

interface UserContextType {
  users: User[];
  addUser: (user: Omit<User, 'id' | 'totalOrders' | 'totalSpent' | 'createdAt' | 'updatedAt'>) => void;
  updateUser: (id: string, user: Partial<User>) => void;
  deleteUser: (id: string) => void;
  toggleUserStatus: (id: string) => void;
  getUserById: (id: string) => User | undefined;
  getActiveUsers: () => User[];
  getUsersByRole: (role: 'admin' | 'customer') => User[];
  updateUserStats: (userId: string, orderCount: number, totalSpent: number) => void;
  getTotalUsers: () => number;
  getActiveUsersCount: () => number;
  getRecentUsers: (limit?: number) => User[];
  isLoading: boolean;
}

const UserContext = createContext<UserContextType | undefined>(undefined);

interface UserProviderProps {
  children: ReactNode;
}

// Sample users data
const sampleUsers: User[] = [
  {
    id: '1',
    name: 'أحمد محمد علي',
    email: '<EMAIL>',
    phone: '+966501234567',
    role: 'customer',
    isActive: true,
    lastLogin: new Date('2024-01-16T10:30:00'),
    totalOrders: 3,
    totalSpent: 5780,
    address: {
      street: 'شارع الملك فهد، حي النخيل',
      city: 'الرياض',
      postalCode: '12345',
      country: 'السعودية',
    },
    createdAt: new Date('2024-01-01T08:00:00'),
    updatedAt: new Date('2024-01-16T10:30:00'),
  },
  {
    id: '2',
    name: 'فاطمة أحمد محمد',
    email: '<EMAIL>',
    phone: '+966507654321',
    role: 'customer',
    isActive: true,
    lastLogin: new Date('2024-01-15T14:20:00'),
    totalOrders: 1,
    totalSpent: 1410,
    address: {
      street: 'طريق الأمير محمد بن عبدالعزيز',
      city: 'جدة',
      postalCode: '21589',
      country: 'السعودية',
    },
    createdAt: new Date('2024-01-05T12:00:00'),
    updatedAt: new Date('2024-01-15T14:20:00'),
  },
  {
    id: '3',
    name: 'محمد عبدالله',
    email: '<EMAIL>',
    phone: '+966509876543',
    role: 'customer',
    isActive: false,
    lastLogin: new Date('2024-01-10T09:15:00'),
    totalOrders: 0,
    totalSpent: 0,
    address: {
      street: 'شارع العليا',
      city: 'الرياض',
      postalCode: '11564',
      country: 'السعودية',
    },
    createdAt: new Date('2024-01-08T16:30:00'),
    updatedAt: new Date('2024-01-12T11:00:00'),
  },
  {
    id: '4',
    name: 'سارة خالد',
    email: '<EMAIL>',
    phone: '+966502468135',
    role: 'customer',
    isActive: true,
    lastLogin: new Date('2024-01-14T11:45:00'),
    totalOrders: 2,
    totalSpent: 3200,
    address: {
      street: 'حي الملقا',
      city: 'الرياض',
      postalCode: '13524',
      country: 'السعودية',
    },
    createdAt: new Date('2024-01-03T09:20:00'),
    updatedAt: new Date('2024-01-14T11:45:00'),
  },
  {
    id: '5',
    name: 'عبدالرحمن أحمد',
    email: '<EMAIL>',
    phone: '+966501111111',
    role: 'admin',
    isActive: true,
    lastLogin: new Date('2024-01-16T16:00:00'),
    totalOrders: 0,
    totalSpent: 0,
    createdAt: new Date('2023-12-01T10:00:00'),
    updatedAt: new Date('2024-01-16T16:00:00'),
  },
];

export function UserProvider({ children }: UserProviderProps) {
  const [users, setUsers] = useState<User[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Initialize users from localStorage or use sample data
  useEffect(() => {
    const initializeUsers = () => {
      try {
        const savedUsers = localStorage.getItem('ecommerce-users');
        if (savedUsers) {
          const parsedUsers = JSON.parse(savedUsers);
          // Convert date strings back to Date objects
          const usersWithDates = parsedUsers.map((user: any) => ({
            ...user,
            lastLogin: user.lastLogin ? new Date(user.lastLogin) : undefined,
            createdAt: new Date(user.createdAt),
            updatedAt: new Date(user.updatedAt),
          }));
          setUsers(usersWithDates);
        } else {
          // First time - use sample data
          setUsers(sampleUsers);
          localStorage.setItem('ecommerce-users', JSON.stringify(sampleUsers));
        }
      } catch (error) {
        console.error('Error loading users from localStorage:', error);
        setUsers(sampleUsers);
      } finally {
        setIsLoading(false);
      }
    };

    initializeUsers();
  }, []);

  // Save to localStorage whenever users change
  useEffect(() => {
    if (!isLoading && users.length > 0) {
      try {
        localStorage.setItem('ecommerce-users', JSON.stringify(users));
      } catch (error) {
        console.error('Error saving users to localStorage:', error);
      }
    }
  }, [users, isLoading]);

  const addUser = (userData: Omit<User, 'id' | 'totalOrders' | 'totalSpent' | 'createdAt' | 'updatedAt'>) => {
    const newUser: User = {
      ...userData,
      id: Date.now().toString(),
      totalOrders: 0,
      totalSpent: 0,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    setUsers(prev => [newUser, ...prev]);
  };

  const updateUser = (id: string, userData: Partial<User>) => {
    setUsers(prev => prev.map(user => 
      user.id === id 
        ? { ...user, ...userData, updatedAt: new Date() }
        : user
    ));
  };

  const deleteUser = (id: string) => {
    setUsers(prev => prev.filter(user => user.id !== id));
  };

  const toggleUserStatus = (id: string) => {
    setUsers(prev => prev.map(user => 
      user.id === id 
        ? { ...user, isActive: !user.isActive, updatedAt: new Date() }
        : user
    ));
  };

  const getUserById = (id: string): User | undefined => {
    return users.find(user => user.id === id);
  };

  const getActiveUsers = (): User[] => {
    return users.filter(user => user.isActive);
  };

  const getUsersByRole = (role: 'admin' | 'customer'): User[] => {
    return users.filter(user => user.role === role);
  };

  const updateUserStats = (userId: string, orderCount: number, totalSpent: number) => {
    setUsers(prev => prev.map(user => 
      user.id === userId 
        ? { 
            ...user, 
            totalOrders: orderCount, 
            totalSpent: totalSpent,
            lastLogin: new Date(),
            updatedAt: new Date() 
          }
        : user
    ));
  };

  const getTotalUsers = (): number => {
    return users.length;
  };

  const getActiveUsersCount = (): number => {
    return users.filter(user => user.isActive).length;
  };

  const getRecentUsers = (limit: number = 5): User[] => {
    return users
      .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())
      .slice(0, limit);
  };

  const value: UserContextType = {
    users,
    addUser,
    updateUser,
    deleteUser,
    toggleUserStatus,
    getUserById,
    getActiveUsers,
    getUsersByRole,
    updateUserStats,
    getTotalUsers,
    getActiveUsersCount,
    getRecentUsers,
    isLoading,
  };

  return (
    <UserContext.Provider value={value}>
      {children}
    </UserContext.Provider>
  );
}

export function useUsers() {
  const context = useContext(UserContext);
  if (context === undefined) {
    throw new Error('useUsers must be used within a UserProvider');
  }
  return context;
}
