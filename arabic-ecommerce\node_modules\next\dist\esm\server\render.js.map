{"version": 3, "sources": ["../../src/server/render.tsx"], "sourcesContent": ["import type { IncomingMessage, ServerResponse } from 'http'\nimport type { ParsedUrlQuery } from 'querystring'\nimport type { NextRouter } from '../shared/lib/router/router'\nimport type { HtmlProps } from '../shared/lib/html-context.shared-runtime'\nimport type { DomainLocale } from './config'\nimport type {\n  AppType,\n  DocumentInitialProps,\n  DocumentType,\n  DocumentProps,\n  DocumentContext,\n  NextComponentType,\n  RenderPage,\n  RenderPageResult,\n} from '../shared/lib/utils'\nimport type { ImageConfigComplete } from '../shared/lib/image-config'\nimport type { Redirect } from '../lib/load-custom-routes'\nimport {\n  type NextApiRequestCookies,\n  type __ApiPreviewProps,\n  setLazyProp,\n} from './api-utils'\nimport { getCookieParser } from './api-utils/get-cookie-parser'\nimport type { LoadComponentsReturnType } from './load-components'\nimport type {\n  GetServerSideProps,\n  GetStaticProps,\n  PreviewData,\n  ServerRuntime,\n  SizeLimit,\n} from '../types'\nimport type { UnwrapPromise } from '../lib/coalesced-function'\nimport type { ReactReadableStream } from './stream-utils/node-web-streams-helper'\nimport type { ClientReferenceManifest } from '../build/webpack/plugins/flight-manifest-plugin'\nimport type { NextFontManifest } from '../build/webpack/plugins/next-font-manifest-plugin'\nimport type { PagesModule } from './route-modules/pages/module'\nimport type { ComponentsEnhancer } from '../shared/lib/utils'\nimport type { NextParsedUrlQuery } from './request-meta'\nimport type { Revalidate, ExpireTime } from './lib/revalidate'\nimport type { COMPILER_NAMES } from '../shared/lib/constants'\n\nimport React, { type JSX } from 'react'\nimport ReactDOMServerPages from 'next/dist/server/ReactDOMServerPages'\nimport { StyleRegistry, createStyleRegistry } from 'styled-jsx'\nimport {\n  GSP_NO_RETURNED_VALUE,\n  GSSP_COMPONENT_MEMBER_ERROR,\n  GSSP_NO_RETURNED_VALUE,\n  STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR,\n  SERVER_PROPS_GET_INIT_PROPS_CONFLICT,\n  SERVER_PROPS_SSG_CONFLICT,\n  SSG_GET_INITIAL_PROPS_CONFLICT,\n  UNSTABLE_REVALIDATE_RENAME_ERROR,\n} from '../lib/constants'\nimport {\n  NEXT_BUILTIN_DOCUMENT,\n  SERVER_PROPS_ID,\n  STATIC_PROPS_ID,\n  STATIC_STATUS_PAGES,\n} from '../shared/lib/constants'\nimport { isSerializableProps } from '../lib/is-serializable-props'\nimport { isInAmpMode } from '../shared/lib/amp-mode'\nimport { AmpStateContext } from '../shared/lib/amp-context.shared-runtime'\nimport { defaultHead } from '../shared/lib/head'\nimport { HeadManagerContext } from '../shared/lib/head-manager-context.shared-runtime'\nimport Loadable from '../shared/lib/loadable.shared-runtime'\nimport { LoadableContext } from '../shared/lib/loadable-context.shared-runtime'\nimport { RouterContext } from '../shared/lib/router-context.shared-runtime'\nimport { isDynamicRoute } from '../shared/lib/router/utils/is-dynamic'\nimport {\n  getDisplayName,\n  isResSent,\n  loadGetInitialProps,\n} from '../shared/lib/utils'\nimport { HtmlContext } from '../shared/lib/html-context.shared-runtime'\nimport { normalizePagePath } from '../shared/lib/page-path/normalize-page-path'\nimport { denormalizePagePath } from '../shared/lib/page-path/denormalize-page-path'\nimport { getRequestMeta } from './request-meta'\nimport { allowedStatusCodes, getRedirectStatus } from '../lib/redirect-status'\nimport RenderResult, { type PagesRenderResultMetadata } from './render-result'\nimport isError from '../lib/is-error'\nimport {\n  streamToString,\n  renderToInitialFizzStream,\n} from './stream-utils/node-web-streams-helper'\nimport { ImageConfigContext } from '../shared/lib/image-config-context.shared-runtime'\nimport stripAnsi from 'next/dist/compiled/strip-ansi'\nimport { stripInternalQueries } from './internal-utils'\nimport {\n  adaptForAppRouterInstance,\n  adaptForPathParams,\n  adaptForSearchParams,\n  PathnameContextProviderAdapter,\n} from '../shared/lib/router/adapters'\nimport { AppRouterContext } from '../shared/lib/app-router-context.shared-runtime'\nimport {\n  SearchParamsContext,\n  PathParamsContext,\n} from '../shared/lib/hooks-client-context.shared-runtime'\nimport { getTracer } from './lib/trace/tracer'\nimport { RenderSpan } from './lib/trace/constants'\nimport { ReflectAdapter } from './web/spec-extension/adapters/reflect'\nimport { formatRevalidate } from './lib/revalidate'\nimport { getErrorSource } from '../shared/lib/error-source'\nimport type { DeepReadonly } from '../shared/lib/deep-readonly'\n\nlet tryGetPreviewData: typeof import('./api-utils/node/try-get-preview-data').tryGetPreviewData\nlet warn: typeof import('../build/output/log').warn\nlet postProcessHTML: typeof import('./post-process').postProcessHTML\n\nconst DOCTYPE = '<!DOCTYPE html>'\n\nif (process.env.NEXT_RUNTIME !== 'edge') {\n  tryGetPreviewData =\n    require('./api-utils/node/try-get-preview-data').tryGetPreviewData\n  warn = require('../build/output/log').warn\n  postProcessHTML = require('./post-process').postProcessHTML\n} else {\n  warn = console.warn.bind(console)\n  postProcessHTML = async (_pathname: string, html: string) => html\n}\n\nfunction noRouter() {\n  const message =\n    'No router instance found. you should only use \"next/router\" inside the client side of your app. https://nextjs.org/docs/messages/no-router-instance'\n  throw new Error(message)\n}\n\nasync function renderToString(element: React.ReactElement) {\n  const renderStream = await ReactDOMServerPages.renderToReadableStream(element)\n  await renderStream.allReady\n  return streamToString(renderStream)\n}\n\nclass ServerRouter implements NextRouter {\n  route: string\n  pathname: string\n  query: ParsedUrlQuery\n  asPath: string\n  basePath: string\n  events: any\n  isFallback: boolean\n  locale?: string\n  isReady: boolean\n  locales?: string[]\n  defaultLocale?: string\n  domainLocales?: DomainLocale[]\n  isPreview: boolean\n  isLocaleDomain: boolean\n\n  constructor(\n    pathname: string,\n    query: ParsedUrlQuery,\n    as: string,\n    { isFallback }: { isFallback: boolean },\n    isReady: boolean,\n    basePath: string,\n    locale?: string,\n    locales?: string[],\n    defaultLocale?: string,\n    domainLocales?: DomainLocale[],\n    isPreview?: boolean,\n    isLocaleDomain?: boolean\n  ) {\n    this.route = pathname.replace(/\\/$/, '') || '/'\n    this.pathname = pathname\n    this.query = query\n    this.asPath = as\n    this.isFallback = isFallback\n    this.basePath = basePath\n    this.locale = locale\n    this.locales = locales\n    this.defaultLocale = defaultLocale\n    this.isReady = isReady\n    this.domainLocales = domainLocales\n    this.isPreview = !!isPreview\n    this.isLocaleDomain = !!isLocaleDomain\n  }\n\n  push(): any {\n    noRouter()\n  }\n  replace(): any {\n    noRouter()\n  }\n  reload() {\n    noRouter()\n  }\n  back() {\n    noRouter()\n  }\n  forward(): void {\n    noRouter()\n  }\n  prefetch(): any {\n    noRouter()\n  }\n  beforePopState() {\n    noRouter()\n  }\n}\n\nfunction enhanceComponents(\n  options: ComponentsEnhancer,\n  App: AppType,\n  Component: NextComponentType\n): {\n  App: AppType\n  Component: NextComponentType\n} {\n  // For backwards compatibility\n  if (typeof options === 'function') {\n    return {\n      App,\n      Component: options(Component),\n    }\n  }\n\n  return {\n    App: options.enhanceApp ? options.enhanceApp(App) : App,\n    Component: options.enhanceComponent\n      ? options.enhanceComponent(Component)\n      : Component,\n  }\n}\n\nfunction renderPageTree(\n  App: AppType,\n  Component: NextComponentType,\n  props: any\n) {\n  return <App Component={Component} {...props} />\n}\n\nexport type RenderOptsPartial = {\n  buildId: string\n  canonicalBase: string\n  runtimeConfig?: { [key: string]: any }\n  assetPrefix?: string\n  err?: Error | null\n  nextExport?: boolean\n  dev?: boolean\n  ampPath?: string\n  ErrorDebug?: React.ComponentType<{ error: Error }>\n  ampValidator?: (html: string, pathname: string) => Promise<void>\n  ampSkipValidation?: boolean\n  ampOptimizerConfig?: { [key: string]: any }\n  isNextDataRequest?: boolean\n  params?: ParsedUrlQuery\n  previewProps: __ApiPreviewProps | undefined\n  basePath: string\n  unstable_runtimeJS?: false\n  unstable_JsPreload?: false\n  optimizeCss: any\n  nextConfigOutput?: 'standalone' | 'export'\n  nextScriptWorkers: any\n  assetQueryString?: string\n  resolvedUrl?: string\n  resolvedAsPath?: string\n  clientReferenceManifest?: DeepReadonly<ClientReferenceManifest>\n  nextFontManifest?: DeepReadonly<NextFontManifest>\n  distDir?: string\n  locale?: string\n  locales?: string[]\n  defaultLocale?: string\n  domainLocales?: DomainLocale[]\n  disableOptimizedLoading?: boolean\n  supportsDynamicResponse: boolean\n  isBot?: boolean\n  runtime?: ServerRuntime\n  serverComponents?: boolean\n  serverActions?: {\n    bodySizeLimit?: SizeLimit\n    allowedOrigins?: string[]\n  }\n  customServer?: boolean\n  crossOrigin?: 'anonymous' | 'use-credentials' | '' | undefined\n  images: ImageConfigComplete\n  largePageDataBytes?: number\n  isOnDemandRevalidate?: boolean\n  strictNextHead: boolean\n  isDraftMode?: boolean\n  deploymentId?: string\n  isServerAction?: boolean\n  isExperimentalCompile?: boolean\n  isPrefetch?: boolean\n  expireTime?: ExpireTime\n  experimental: {\n    clientTraceMetadata?: string[]\n  }\n}\n\nexport type RenderOpts = LoadComponentsReturnType<PagesModule> &\n  RenderOptsPartial\n\n/**\n * RenderOptsExtra is being used to split away functionality that's within the\n * renderOpts. Eventually we can have more explicit render options for each\n * route kind.\n */\nexport type RenderOptsExtra = {\n  App: AppType\n  Document: DocumentType\n}\n\nconst invalidKeysMsg = (\n  methodName: 'getServerSideProps' | 'getStaticProps',\n  invalidKeys: string[]\n) => {\n  const docsPathname = `invalid-${methodName.toLocaleLowerCase()}-value`\n\n  return (\n    `Additional keys were returned from \\`${methodName}\\`. Properties intended for your component must be nested under the \\`props\\` key, e.g.:` +\n    `\\n\\n\\treturn { props: { title: 'My Title', content: '...' } }` +\n    `\\n\\nKeys that need to be moved: ${invalidKeys.join(', ')}.` +\n    `\\nRead more: https://nextjs.org/docs/messages/${docsPathname}`\n  )\n}\n\nfunction checkRedirectValues(\n  redirect: Redirect,\n  req: IncomingMessage,\n  method: 'getStaticProps' | 'getServerSideProps'\n) {\n  const { destination, permanent, statusCode, basePath } = redirect\n  let errors: string[] = []\n\n  const hasStatusCode = typeof statusCode !== 'undefined'\n  const hasPermanent = typeof permanent !== 'undefined'\n\n  if (hasPermanent && hasStatusCode) {\n    errors.push(`\\`permanent\\` and \\`statusCode\\` can not both be provided`)\n  } else if (hasPermanent && typeof permanent !== 'boolean') {\n    errors.push(`\\`permanent\\` must be \\`true\\` or \\`false\\``)\n  } else if (hasStatusCode && !allowedStatusCodes.has(statusCode!)) {\n    errors.push(\n      `\\`statusCode\\` must undefined or one of ${[...allowedStatusCodes].join(\n        ', '\n      )}`\n    )\n  }\n  const destinationType = typeof destination\n\n  if (destinationType !== 'string') {\n    errors.push(\n      `\\`destination\\` should be string but received ${destinationType}`\n    )\n  }\n\n  const basePathType = typeof basePath\n\n  if (basePathType !== 'undefined' && basePathType !== 'boolean') {\n    errors.push(\n      `\\`basePath\\` should be undefined or a false, received ${basePathType}`\n    )\n  }\n\n  if (errors.length > 0) {\n    throw new Error(\n      `Invalid redirect object returned from ${method} for ${req.url}\\n` +\n        errors.join(' and ') +\n        '\\n' +\n        `See more info here: https://nextjs.org/docs/messages/invalid-redirect-gssp`\n    )\n  }\n}\n\nexport function errorToJSON(err: Error) {\n  let source: typeof COMPILER_NAMES.server | typeof COMPILER_NAMES.edgeServer =\n    'server'\n\n  if (process.env.NEXT_RUNTIME !== 'edge') {\n    source = getErrorSource(err) || 'server'\n  }\n\n  return {\n    name: err.name,\n    source,\n    message: stripAnsi(err.message),\n    stack: err.stack,\n    digest: (err as any).digest,\n  }\n}\n\nfunction serializeError(\n  dev: boolean | undefined,\n  err: Error\n): Error & {\n  statusCode?: number\n  source?: typeof COMPILER_NAMES.server | typeof COMPILER_NAMES.edgeServer\n} {\n  if (dev) {\n    return errorToJSON(err)\n  }\n\n  return {\n    name: 'Internal Server Error.',\n    message: '500 - Internal Server Error.',\n    statusCode: 500,\n  }\n}\n\nexport async function renderToHTMLImpl(\n  req: IncomingMessage,\n  res: ServerResponse,\n  pathname: string,\n  query: NextParsedUrlQuery,\n  renderOpts: Omit<RenderOpts, keyof RenderOptsExtra>,\n  extra: RenderOptsExtra\n): Promise<RenderResult> {\n  // Adds support for reading `cookies` in `getServerSideProps` when SSR.\n  setLazyProp({ req: req as any }, 'cookies', getCookieParser(req.headers))\n\n  const metadata: PagesRenderResultMetadata = {}\n\n  metadata.assetQueryString =\n    (renderOpts.dev && renderOpts.assetQueryString) || ''\n\n  if (renderOpts.dev && !metadata.assetQueryString) {\n    const userAgent = (req.headers['user-agent'] || '').toLowerCase()\n    if (userAgent.includes('safari') && !userAgent.includes('chrome')) {\n      // In dev we invalidate the cache by appending a timestamp to the resource URL.\n      // This is a workaround to fix https://github.com/vercel/next.js/issues/5860\n      // TODO: remove this workaround when https://bugs.webkit.org/show_bug.cgi?id=187726 is fixed.\n      // Note: The workaround breaks breakpoints on reload since the script url always changes,\n      // so we only apply it to Safari.\n      metadata.assetQueryString = `?ts=${Date.now()}`\n    }\n  }\n\n  // if deploymentId is provided we append it to all asset requests\n  if (renderOpts.deploymentId) {\n    metadata.assetQueryString += `${metadata.assetQueryString ? '&' : '?'}dpl=${\n      renderOpts.deploymentId\n    }`\n  }\n\n  // don't modify original query object\n  query = Object.assign({}, query)\n\n  const {\n    err,\n    dev = false,\n    ampPath = '',\n    pageConfig = {},\n    buildManifest,\n    reactLoadableManifest,\n    ErrorDebug,\n    getStaticProps,\n    getStaticPaths,\n    getServerSideProps,\n    isNextDataRequest,\n    params,\n    previewProps,\n    basePath,\n    images,\n    runtime: globalRuntime,\n    isExperimentalCompile,\n    expireTime,\n  } = renderOpts\n  const { App } = extra\n\n  const assetQueryString = metadata.assetQueryString\n\n  let Document = extra.Document\n\n  let Component: React.ComponentType<{}> | ((props: any) => JSX.Element) =\n    renderOpts.Component\n  const OriginComponent = Component\n\n  const isFallback = !!query.__nextFallback\n  const notFoundSrcPage = query.__nextNotFoundSrcPage\n\n  // next internal queries should be stripped out\n  stripInternalQueries(query)\n\n  const isSSG = !!getStaticProps\n  const isBuildTimeSSG = isSSG && renderOpts.nextExport\n  const defaultAppGetInitialProps =\n    App.getInitialProps === (App as any).origGetInitialProps\n\n  const hasPageGetInitialProps = !!(Component as any)?.getInitialProps\n  const hasPageScripts = (Component as any)?.unstable_scriptLoader\n\n  const pageIsDynamic = isDynamicRoute(pathname)\n\n  const defaultErrorGetInitialProps =\n    pathname === '/_error' &&\n    (Component as any).getInitialProps ===\n      (Component as any).origGetInitialProps\n\n  if (\n    renderOpts.nextExport &&\n    hasPageGetInitialProps &&\n    !defaultErrorGetInitialProps\n  ) {\n    warn(\n      `Detected getInitialProps on page '${pathname}'` +\n        ` while running export. It's recommended to use getStaticProps` +\n        ` which has a more correct behavior for static exporting.` +\n        `\\nRead more: https://nextjs.org/docs/messages/get-initial-props-export`\n    )\n  }\n\n  let isAutoExport =\n    !hasPageGetInitialProps &&\n    defaultAppGetInitialProps &&\n    !isSSG &&\n    !getServerSideProps\n\n  // if we are running from experimental compile and the page\n  // would normally be automatically statically optimized\n  // ensure we set cache header so it's not rendered on-demand\n  // every request\n  if (isAutoExport && !dev && isExperimentalCompile) {\n    res.setHeader(\n      'Cache-Control',\n      formatRevalidate({\n        revalidate: false,\n        expireTime,\n      })\n    )\n    isAutoExport = false\n  }\n\n  if (hasPageGetInitialProps && isSSG) {\n    throw new Error(SSG_GET_INITIAL_PROPS_CONFLICT + ` ${pathname}`)\n  }\n\n  if (hasPageGetInitialProps && getServerSideProps) {\n    throw new Error(SERVER_PROPS_GET_INIT_PROPS_CONFLICT + ` ${pathname}`)\n  }\n\n  if (getServerSideProps && isSSG) {\n    throw new Error(SERVER_PROPS_SSG_CONFLICT + ` ${pathname}`)\n  }\n\n  if (getServerSideProps && renderOpts.nextConfigOutput === 'export') {\n    throw new Error(\n      'getServerSideProps cannot be used with \"output: export\". See more info here: https://nextjs.org/docs/advanced-features/static-html-export'\n    )\n  }\n\n  if (getStaticPaths && !pageIsDynamic) {\n    throw new Error(\n      `getStaticPaths is only allowed for dynamic SSG pages and was found on '${pathname}'.` +\n        `\\nRead more: https://nextjs.org/docs/messages/non-dynamic-getstaticpaths-usage`\n    )\n  }\n\n  if (!!getStaticPaths && !isSSG) {\n    throw new Error(\n      `getStaticPaths was added without a getStaticProps in ${pathname}. Without getStaticProps, getStaticPaths does nothing`\n    )\n  }\n\n  if (isSSG && pageIsDynamic && !getStaticPaths) {\n    throw new Error(\n      `getStaticPaths is required for dynamic SSG pages and is missing for '${pathname}'.` +\n        `\\nRead more: https://nextjs.org/docs/messages/invalid-getstaticpaths-value`\n    )\n  }\n\n  let asPath: string = renderOpts.resolvedAsPath || (req.url as string)\n\n  if (dev) {\n    const { isValidElementType } = require('next/dist/compiled/react-is')\n    if (!isValidElementType(Component)) {\n      throw new Error(\n        `The default export is not a React Component in page: \"${pathname}\"`\n      )\n    }\n\n    if (!isValidElementType(App)) {\n      throw new Error(\n        `The default export is not a React Component in page: \"/_app\"`\n      )\n    }\n\n    if (!isValidElementType(Document)) {\n      throw new Error(\n        `The default export is not a React Component in page: \"/_document\"`\n      )\n    }\n\n    if (isAutoExport || isFallback) {\n      // remove query values except ones that will be set during export\n      query = {\n        ...(query.amp\n          ? {\n              amp: query.amp,\n            }\n          : {}),\n      }\n      asPath = `${pathname}${\n        // ensure trailing slash is present for non-dynamic auto-export pages\n        req.url!.endsWith('/') && pathname !== '/' && !pageIsDynamic ? '/' : ''\n      }`\n      req.url = pathname\n    }\n\n    if (pathname === '/404' && (hasPageGetInitialProps || getServerSideProps)) {\n      throw new Error(\n        `\\`pages/404\\` ${STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR}`\n      )\n    }\n    if (\n      STATIC_STATUS_PAGES.includes(pathname) &&\n      (hasPageGetInitialProps || getServerSideProps)\n    ) {\n      throw new Error(\n        `\\`pages${pathname}\\` ${STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR}`\n      )\n    }\n  }\n\n  for (const methodName of [\n    'getStaticProps',\n    'getServerSideProps',\n    'getStaticPaths',\n  ]) {\n    if ((Component as any)?.[methodName]) {\n      throw new Error(\n        `page ${pathname} ${methodName} ${GSSP_COMPONENT_MEMBER_ERROR}`\n      )\n    }\n  }\n\n  await Loadable.preloadAll() // Make sure all dynamic imports are loaded\n\n  let isPreview: boolean | undefined = undefined\n  let previewData: PreviewData\n\n  if (\n    (isSSG || getServerSideProps) &&\n    !isFallback &&\n    process.env.NEXT_RUNTIME !== 'edge' &&\n    previewProps\n  ) {\n    // Reads of this are cached on the `req` object, so this should resolve\n    // instantly. There's no need to pass this data down from a previous\n    // invoke.\n    previewData = tryGetPreviewData(\n      req,\n      res,\n      previewProps,\n      !!renderOpts.multiZoneDraftMode\n    )\n    isPreview = previewData !== false\n  }\n\n  // url will always be set\n  const routerIsReady = !!(\n    getServerSideProps ||\n    hasPageGetInitialProps ||\n    (!defaultAppGetInitialProps && !isSSG) ||\n    isExperimentalCompile\n  )\n  const router = new ServerRouter(\n    pathname,\n    query,\n    asPath,\n    {\n      isFallback: isFallback,\n    },\n    routerIsReady,\n    basePath,\n    renderOpts.locale,\n    renderOpts.locales,\n    renderOpts.defaultLocale,\n    renderOpts.domainLocales,\n    isPreview,\n    getRequestMeta(req, 'isLocaleDomain')\n  )\n\n  const appRouter = adaptForAppRouterInstance(router)\n\n  let scriptLoader: any = {}\n  const jsxStyleRegistry = createStyleRegistry()\n  const ampState = {\n    ampFirst: pageConfig.amp === true,\n    hasQuery: Boolean(query.amp),\n    hybrid: pageConfig.amp === 'hybrid',\n  }\n\n  // Disable AMP under the web environment\n  const inAmpMode = process.env.NEXT_RUNTIME !== 'edge' && isInAmpMode(ampState)\n  let head: JSX.Element[] = defaultHead(inAmpMode)\n  const reactLoadableModules: string[] = []\n\n  let initialScripts: any = {}\n  if (hasPageScripts) {\n    initialScripts.beforeInteractive = []\n      .concat(hasPageScripts())\n      .filter((script: any) => script.props.strategy === 'beforeInteractive')\n      .map((script: any) => script.props)\n  }\n\n  const AppContainer = ({ children }: { children: JSX.Element }) => (\n    <AppRouterContext.Provider value={appRouter}>\n      <SearchParamsContext.Provider value={adaptForSearchParams(router)}>\n        <PathnameContextProviderAdapter\n          router={router}\n          isAutoExport={isAutoExport}\n        >\n          <PathParamsContext.Provider value={adaptForPathParams(router)}>\n            <RouterContext.Provider value={router}>\n              <AmpStateContext.Provider value={ampState}>\n                <HeadManagerContext.Provider\n                  value={{\n                    updateHead: (state) => {\n                      head = state\n                    },\n                    updateScripts: (scripts) => {\n                      scriptLoader = scripts\n                    },\n                    scripts: initialScripts,\n                    mountedInstances: new Set(),\n                  }}\n                >\n                  <LoadableContext.Provider\n                    value={(moduleName) =>\n                      reactLoadableModules.push(moduleName)\n                    }\n                  >\n                    <StyleRegistry registry={jsxStyleRegistry}>\n                      <ImageConfigContext.Provider value={images}>\n                        {children}\n                      </ImageConfigContext.Provider>\n                    </StyleRegistry>\n                  </LoadableContext.Provider>\n                </HeadManagerContext.Provider>\n              </AmpStateContext.Provider>\n            </RouterContext.Provider>\n          </PathParamsContext.Provider>\n        </PathnameContextProviderAdapter>\n      </SearchParamsContext.Provider>\n    </AppRouterContext.Provider>\n  )\n\n  // The `useId` API uses the path indexes to generate an ID for each node.\n  // To guarantee the match of hydration, we need to ensure that the structure\n  // of wrapper nodes is isomorphic in server and client.\n  // TODO: With `enhanceApp` and `enhanceComponents` options, this approach may\n  // not be useful.\n  // https://github.com/facebook/react/pull/22644\n  const Noop = () => null\n  const AppContainerWithIsomorphicFiberStructure: React.FC<{\n    children: JSX.Element\n  }> = ({ children }) => {\n    return (\n      <>\n        {/* <Head/> */}\n        <Noop />\n        <AppContainer>\n          <>\n            {/* <ReactDevOverlay/> */}\n            {dev ? (\n              <>\n                {children}\n                <Noop />\n              </>\n            ) : (\n              children\n            )}\n            {/* <RouteAnnouncer/> */}\n            <Noop />\n          </>\n        </AppContainer>\n      </>\n    )\n  }\n\n  const ctx = {\n    err,\n    req: isAutoExport ? undefined : req,\n    res: isAutoExport ? undefined : res,\n    pathname,\n    query,\n    asPath,\n    locale: renderOpts.locale,\n    locales: renderOpts.locales,\n    defaultLocale: renderOpts.defaultLocale,\n    AppTree: (props: any) => {\n      return (\n        <AppContainerWithIsomorphicFiberStructure>\n          {renderPageTree(App, OriginComponent, { ...props, router })}\n        </AppContainerWithIsomorphicFiberStructure>\n      )\n    },\n    defaultGetInitialProps: async (\n      docCtx: DocumentContext,\n      options: { nonce?: string } = {}\n    ): Promise<DocumentInitialProps> => {\n      const enhanceApp = (AppComp: any) => {\n        return (props: any) => <AppComp {...props} />\n      }\n\n      const { html, head: renderPageHead } = await docCtx.renderPage({\n        enhanceApp,\n      })\n      const styles = jsxStyleRegistry.styles({ nonce: options.nonce })\n      jsxStyleRegistry.flush()\n      return { html, head: renderPageHead, styles }\n    },\n  }\n  let props: any\n\n  const nextExport =\n    !isSSG && (renderOpts.nextExport || (dev && (isAutoExport || isFallback)))\n\n  const styledJsxInsertedHTML = () => {\n    const styles = jsxStyleRegistry.styles()\n    jsxStyleRegistry.flush()\n    return <>{styles}</>\n  }\n\n  props = await loadGetInitialProps(App, {\n    AppTree: ctx.AppTree,\n    Component,\n    router,\n    ctx,\n  })\n\n  if ((isSSG || getServerSideProps) && isPreview) {\n    props.__N_PREVIEW = true\n  }\n\n  if (isSSG) {\n    props[STATIC_PROPS_ID] = true\n  }\n\n  if (isSSG && !isFallback) {\n    let data: Readonly<UnwrapPromise<ReturnType<GetStaticProps>>>\n\n    try {\n      data = await getTracer().trace(\n        RenderSpan.getStaticProps,\n        {\n          spanName: `getStaticProps ${pathname}`,\n          attributes: {\n            'next.route': pathname,\n          },\n        },\n        () =>\n          getStaticProps({\n            ...(pageIsDynamic ? { params } : undefined),\n            ...(isPreview\n              ? { draftMode: true, preview: true, previewData: previewData }\n              : undefined),\n            locales: renderOpts.locales,\n            locale: renderOpts.locale,\n            defaultLocale: renderOpts.defaultLocale,\n            revalidateReason: renderOpts.isOnDemandRevalidate\n              ? 'on-demand'\n              : isBuildTimeSSG\n                ? 'build'\n                : 'stale',\n          })\n      )\n    } catch (staticPropsError: any) {\n      // remove not found error code to prevent triggering legacy\n      // 404 rendering\n      if (staticPropsError && staticPropsError.code === 'ENOENT') {\n        delete staticPropsError.code\n      }\n      throw staticPropsError\n    }\n\n    if (data == null) {\n      throw new Error(GSP_NO_RETURNED_VALUE)\n    }\n\n    const invalidKeys = Object.keys(data).filter(\n      (key) =>\n        key !== 'revalidate' &&\n        key !== 'props' &&\n        key !== 'redirect' &&\n        key !== 'notFound'\n    )\n\n    if (invalidKeys.includes('unstable_revalidate')) {\n      throw new Error(UNSTABLE_REVALIDATE_RENAME_ERROR)\n    }\n\n    if (invalidKeys.length) {\n      throw new Error(invalidKeysMsg('getStaticProps', invalidKeys))\n    }\n\n    if (process.env.NODE_ENV !== 'production') {\n      if (\n        typeof (data as any).notFound !== 'undefined' &&\n        typeof (data as any).redirect !== 'undefined'\n      ) {\n        throw new Error(\n          `\\`redirect\\` and \\`notFound\\` can not both be returned from ${\n            isSSG ? 'getStaticProps' : 'getServerSideProps'\n          } at the same time. Page: ${pathname}\\nSee more info here: https://nextjs.org/docs/messages/gssp-mixed-not-found-redirect`\n        )\n      }\n    }\n\n    if ('notFound' in data && data.notFound) {\n      if (pathname === '/404') {\n        throw new Error(\n          `The /404 page can not return notFound in \"getStaticProps\", please remove it to continue!`\n        )\n      }\n\n      metadata.isNotFound = true\n    }\n\n    if (\n      'redirect' in data &&\n      data.redirect &&\n      typeof data.redirect === 'object'\n    ) {\n      checkRedirectValues(data.redirect as Redirect, req, 'getStaticProps')\n\n      if (isBuildTimeSSG) {\n        throw new Error(\n          `\\`redirect\\` can not be returned from getStaticProps during prerendering (${req.url})\\n` +\n            `See more info here: https://nextjs.org/docs/messages/gsp-redirect-during-prerender`\n        )\n      }\n\n      ;(data as any).props = {\n        __N_REDIRECT: data.redirect.destination,\n        __N_REDIRECT_STATUS: getRedirectStatus(data.redirect),\n      }\n      if (typeof data.redirect.basePath !== 'undefined') {\n        ;(data as any).props.__N_REDIRECT_BASE_PATH = data.redirect.basePath\n      }\n      metadata.isRedirect = true\n    }\n\n    if (\n      (dev || isBuildTimeSSG) &&\n      !metadata.isNotFound &&\n      !isSerializableProps(pathname, 'getStaticProps', (data as any).props)\n    ) {\n      // this fn should throw an error instead of ever returning `false`\n      throw new Error(\n        'invariant: getStaticProps did not return valid props. Please report this.'\n      )\n    }\n\n    let revalidate: Revalidate\n    if ('revalidate' in data) {\n      if (data.revalidate && renderOpts.nextConfigOutput === 'export') {\n        throw new Error(\n          'ISR cannot be used with \"output: export\". See more info here: https://nextjs.org/docs/advanced-features/static-html-export'\n        )\n      }\n      if (typeof data.revalidate === 'number') {\n        if (!Number.isInteger(data.revalidate)) {\n          throw new Error(\n            `A page's revalidate option must be seconds expressed as a natural number for ${req.url}. Mixed numbers, such as '${data.revalidate}', cannot be used.` +\n              `\\nTry changing the value to '${Math.ceil(\n                data.revalidate\n              )}' or using \\`Math.ceil()\\` if you're computing the value.`\n          )\n        } else if (data.revalidate <= 0) {\n          throw new Error(\n            `A page's revalidate option can not be less than or equal to zero for ${req.url}. A revalidate option of zero means to revalidate after _every_ request, and implies stale data cannot be tolerated.` +\n              `\\n\\nTo never revalidate, you can set revalidate to \\`false\\` (only ran once at build-time).` +\n              `\\nTo revalidate as soon as possible, you can set the value to \\`1\\`.`\n          )\n        } else {\n          if (data.revalidate > 31536000) {\n            // if it's greater than a year for some reason error\n            console.warn(\n              `Warning: A page's revalidate option was set to more than a year for ${req.url}. This may have been done in error.` +\n                `\\nTo only run getStaticProps at build-time and not revalidate at runtime, you can set \\`revalidate\\` to \\`false\\`!`\n            )\n          }\n\n          revalidate = data.revalidate\n        }\n      } else if (data.revalidate === true) {\n        // When enabled, revalidate after 1 second. This value is optimal for\n        // the most up-to-date page possible, but without a 1-to-1\n        // request-refresh ratio.\n        revalidate = 1\n      } else if (\n        data.revalidate === false ||\n        typeof data.revalidate === 'undefined'\n      ) {\n        // By default, we never revalidate.\n        revalidate = false\n      } else {\n        throw new Error(\n          `A page's revalidate option must be seconds expressed as a natural number. Mixed numbers and strings cannot be used. Received '${JSON.stringify(\n            data.revalidate\n          )}' for ${req.url}`\n        )\n      }\n    } else {\n      // By default, we never revalidate.\n      revalidate = false\n    }\n\n    props.pageProps = Object.assign(\n      {},\n      props.pageProps,\n      'props' in data ? data.props : undefined\n    )\n\n    // pass up revalidate and props for export\n    metadata.revalidate = revalidate\n    metadata.pageData = props\n\n    // this must come after revalidate is added to renderResultMeta\n    if (metadata.isNotFound) {\n      return new RenderResult(null, { metadata })\n    }\n  }\n\n  if (getServerSideProps) {\n    props[SERVER_PROPS_ID] = true\n  }\n\n  if (getServerSideProps && !isFallback) {\n    let data: UnwrapPromise<ReturnType<GetServerSideProps>>\n\n    let canAccessRes = true\n    let resOrProxy = res\n    let deferredContent = false\n    if (process.env.NODE_ENV !== 'production') {\n      resOrProxy = new Proxy<ServerResponse>(res, {\n        get: function (obj, prop) {\n          if (!canAccessRes) {\n            const message =\n              `You should not access 'res' after getServerSideProps resolves.` +\n              `\\nRead more: https://nextjs.org/docs/messages/gssp-no-mutating-res`\n\n            if (deferredContent) {\n              throw new Error(message)\n            } else {\n              warn(message)\n            }\n          }\n\n          if (typeof prop === 'symbol') {\n            return ReflectAdapter.get(obj, prop, res)\n          }\n\n          return ReflectAdapter.get(obj, prop, res)\n        },\n      })\n    }\n\n    try {\n      data = await getTracer().trace(\n        RenderSpan.getServerSideProps,\n        {\n          spanName: `getServerSideProps ${pathname}`,\n          attributes: {\n            'next.route': pathname,\n          },\n        },\n        async () =>\n          getServerSideProps({\n            req: req as IncomingMessage & {\n              cookies: NextApiRequestCookies\n            },\n            res: resOrProxy,\n            query,\n            resolvedUrl: renderOpts.resolvedUrl as string,\n            ...(pageIsDynamic ? { params } : undefined),\n            ...(previewData !== false\n              ? { draftMode: true, preview: true, previewData: previewData }\n              : undefined),\n            locales: renderOpts.locales,\n            locale: renderOpts.locale,\n            defaultLocale: renderOpts.defaultLocale,\n          })\n      )\n      canAccessRes = false\n      metadata.revalidate = 0\n    } catch (serverSidePropsError: any) {\n      // remove not found error code to prevent triggering legacy\n      // 404 rendering\n      if (\n        isError(serverSidePropsError) &&\n        serverSidePropsError.code === 'ENOENT'\n      ) {\n        delete serverSidePropsError.code\n      }\n      throw serverSidePropsError\n    }\n\n    if (data == null) {\n      throw new Error(GSSP_NO_RETURNED_VALUE)\n    }\n\n    if ((data as any).props instanceof Promise) {\n      deferredContent = true\n    }\n\n    const invalidKeys = Object.keys(data).filter(\n      (key) => key !== 'props' && key !== 'redirect' && key !== 'notFound'\n    )\n\n    if ((data as any).unstable_notFound) {\n      throw new Error(\n        `unstable_notFound has been renamed to notFound, please update the field to continue. Page: ${pathname}`\n      )\n    }\n    if ((data as any).unstable_redirect) {\n      throw new Error(\n        `unstable_redirect has been renamed to redirect, please update the field to continue. Page: ${pathname}`\n      )\n    }\n\n    if (invalidKeys.length) {\n      throw new Error(invalidKeysMsg('getServerSideProps', invalidKeys))\n    }\n\n    if ('notFound' in data && data.notFound) {\n      if (pathname === '/404') {\n        throw new Error(\n          `The /404 page can not return notFound in \"getStaticProps\", please remove it to continue!`\n        )\n      }\n\n      metadata.isNotFound = true\n      return new RenderResult(null, { metadata })\n    }\n\n    if ('redirect' in data && typeof data.redirect === 'object') {\n      checkRedirectValues(data.redirect as Redirect, req, 'getServerSideProps')\n      ;(data as any).props = {\n        __N_REDIRECT: data.redirect.destination,\n        __N_REDIRECT_STATUS: getRedirectStatus(data.redirect),\n      }\n      if (typeof data.redirect.basePath !== 'undefined') {\n        ;(data as any).props.__N_REDIRECT_BASE_PATH = data.redirect.basePath\n      }\n      metadata.isRedirect = true\n    }\n\n    if (deferredContent) {\n      ;(data as any).props = await (data as any).props\n    }\n\n    if (\n      (dev || isBuildTimeSSG) &&\n      !isSerializableProps(pathname, 'getServerSideProps', (data as any).props)\n    ) {\n      // this fn should throw an error instead of ever returning `false`\n      throw new Error(\n        'invariant: getServerSideProps did not return valid props. Please report this.'\n      )\n    }\n\n    props.pageProps = Object.assign({}, props.pageProps, (data as any).props)\n    metadata.pageData = props\n  }\n\n  if (\n    !isSSG && // we only show this warning for legacy pages\n    !getServerSideProps &&\n    process.env.NODE_ENV !== 'production' &&\n    Object.keys(props?.pageProps || {}).includes('url')\n  ) {\n    console.warn(\n      `The prop \\`url\\` is a reserved prop in Next.js for legacy reasons and will be overridden on page ${pathname}\\n` +\n        `See more info here: https://nextjs.org/docs/messages/reserved-page-prop`\n    )\n  }\n\n  // Avoid rendering page un-necessarily for getServerSideProps data request\n  // and getServerSideProps/getStaticProps redirects\n  if ((isNextDataRequest && !isSSG) || metadata.isRedirect) {\n    return new RenderResult(JSON.stringify(props), {\n      metadata,\n    })\n  }\n\n  // We don't call getStaticProps or getServerSideProps while generating\n  // the fallback so make sure to set pageProps to an empty object\n  if (isFallback) {\n    props.pageProps = {}\n  }\n\n  // the response might be finished on the getInitialProps call\n  if (isResSent(res) && !isSSG) return new RenderResult(null, { metadata })\n\n  // we preload the buildManifest for auto-export dynamic pages\n  // to speed up hydrating query values\n  let filteredBuildManifest = buildManifest\n  if (isAutoExport && pageIsDynamic) {\n    const page = denormalizePagePath(normalizePagePath(pathname))\n    // This code would be much cleaner using `immer` and directly pushing into\n    // the result from `getPageFiles`, we could maybe consider that in the\n    // future.\n    if (page in filteredBuildManifest.pages) {\n      filteredBuildManifest = {\n        ...filteredBuildManifest,\n        pages: {\n          ...filteredBuildManifest.pages,\n          [page]: [\n            ...filteredBuildManifest.pages[page],\n            ...filteredBuildManifest.lowPriorityFiles.filter((f) =>\n              f.includes('_buildManifest')\n            ),\n          ],\n        },\n        lowPriorityFiles: filteredBuildManifest.lowPriorityFiles.filter(\n          (f) => !f.includes('_buildManifest')\n        ),\n      }\n    }\n  }\n\n  const Body = ({ children }: { children: JSX.Element }) => {\n    return inAmpMode ? children : <div id=\"__next\">{children}</div>\n  }\n\n  const renderDocument = async () => {\n    // For `Document`, there are two cases that we don't support:\n    // 1. Using `Document.getInitialProps` in the Edge runtime.\n    // 2. Using the class component `Document` with concurrent features.\n\n    const BuiltinFunctionalDocument: DocumentType | undefined = (\n      Document as any\n    )[NEXT_BUILTIN_DOCUMENT]\n\n    if (process.env.NEXT_RUNTIME === 'edge' && Document.getInitialProps) {\n      // In the Edge runtime, `Document.getInitialProps` isn't supported.\n      // We throw an error here if it's customized.\n      if (BuiltinFunctionalDocument) {\n        Document = BuiltinFunctionalDocument\n      } else {\n        throw new Error(\n          '`getInitialProps` in Document component is not supported with the Edge Runtime.'\n        )\n      }\n    }\n\n    async function loadDocumentInitialProps(\n      renderShell: (\n        _App: AppType,\n        _Component: NextComponentType\n      ) => Promise<ReactReadableStream>\n    ) {\n      const renderPage: RenderPage = async (\n        options: ComponentsEnhancer = {}\n      ): Promise<RenderPageResult> => {\n        if (ctx.err && ErrorDebug) {\n          // Always start rendering the shell even if there's an error.\n          if (renderShell) {\n            renderShell(App, Component)\n          }\n\n          const html = await renderToString(\n            <Body>\n              <ErrorDebug error={ctx.err} />\n            </Body>\n          )\n          return { html, head }\n        }\n\n        if (dev && (props.router || props.Component)) {\n          throw new Error(\n            `'router' and 'Component' can not be returned in getInitialProps from _app.js https://nextjs.org/docs/messages/cant-override-next-props`\n          )\n        }\n\n        const { App: EnhancedApp, Component: EnhancedComponent } =\n          enhanceComponents(options, App, Component)\n\n        const stream = await renderShell(EnhancedApp, EnhancedComponent)\n        await stream.allReady\n        const html = await streamToString(stream)\n\n        return { html, head }\n      }\n      const documentCtx = { ...ctx, renderPage }\n      const docProps: DocumentInitialProps = await loadGetInitialProps(\n        Document,\n        documentCtx\n      )\n      // the response might be finished on the getInitialProps call\n      if (isResSent(res) && !isSSG) return null\n\n      if (!docProps || typeof docProps.html !== 'string') {\n        const message = `\"${getDisplayName(\n          Document\n        )}.getInitialProps()\" should resolve to an object with a \"html\" prop set with a valid html string`\n        throw new Error(message)\n      }\n\n      return { docProps, documentCtx }\n    }\n\n    const renderContent = (_App: AppType, _Component: NextComponentType) => {\n      const EnhancedApp = _App || App\n      const EnhancedComponent = _Component || Component\n\n      return ctx.err && ErrorDebug ? (\n        <Body>\n          <ErrorDebug error={ctx.err} />\n        </Body>\n      ) : (\n        <Body>\n          <AppContainerWithIsomorphicFiberStructure>\n            {renderPageTree(EnhancedApp, EnhancedComponent, {\n              ...props,\n              router,\n            })}\n          </AppContainerWithIsomorphicFiberStructure>\n        </Body>\n      )\n    }\n\n    // Always using react concurrent rendering mode with required react version 18.x\n    const renderShell = async (\n      EnhancedApp: AppType,\n      EnhancedComponent: NextComponentType\n    ) => {\n      const content = renderContent(EnhancedApp, EnhancedComponent)\n      return await renderToInitialFizzStream({\n        ReactDOMServer: ReactDOMServerPages,\n        element: content,\n      })\n    }\n\n    const hasDocumentGetInitialProps =\n      process.env.NEXT_RUNTIME !== 'edge' && !!Document.getInitialProps\n\n    // If it has getInitialProps, we will render the shell in `renderPage`.\n    // Otherwise we do it right now.\n    let documentInitialPropsRes:\n      | {}\n      | Awaited<ReturnType<typeof loadDocumentInitialProps>>\n\n    const [rawStyledJsxInsertedHTML, content] = await Promise.all([\n      renderToString(styledJsxInsertedHTML()),\n      (async () => {\n        if (hasDocumentGetInitialProps) {\n          documentInitialPropsRes = await loadDocumentInitialProps(renderShell)\n          if (documentInitialPropsRes === null) return null\n          const { docProps } = documentInitialPropsRes as any\n          return docProps.html\n        } else {\n          documentInitialPropsRes = {}\n          const stream = await renderShell(App, Component)\n          await stream.allReady\n          return streamToString(stream)\n        }\n      })(),\n    ])\n\n    if (content === null) {\n      return null\n    }\n\n    const contentHTML = rawStyledJsxInsertedHTML + content\n\n    // @ts-ignore: documentInitialPropsRes is set\n    const { docProps } = (documentInitialPropsRes as any) || {}\n    const documentElement = (htmlProps: any) => {\n      if (process.env.NEXT_RUNTIME === 'edge') {\n        return (Document as any)()\n      } else {\n        return <Document {...htmlProps} {...docProps} />\n      }\n    }\n\n    let styles\n    if (hasDocumentGetInitialProps) {\n      styles = docProps.styles\n      head = docProps.head\n    } else {\n      styles = jsxStyleRegistry.styles()\n      jsxStyleRegistry.flush()\n    }\n\n    return {\n      contentHTML,\n      documentElement,\n      head,\n      headTags: [],\n      styles,\n    }\n  }\n\n  getTracer().setRootSpanAttribute('next.route', renderOpts.page)\n  const documentResult = await getTracer().trace(\n    RenderSpan.renderDocument,\n    {\n      spanName: `render route (pages) ${renderOpts.page}`,\n      attributes: {\n        'next.route': renderOpts.page,\n      },\n    },\n    async () => renderDocument()\n  )\n  if (!documentResult) {\n    return new RenderResult(null, { metadata })\n  }\n\n  const dynamicImportsIds = new Set<string | number>()\n  const dynamicImports = new Set<string>()\n\n  for (const mod of reactLoadableModules) {\n    const manifestItem = reactLoadableManifest[mod]\n\n    if (manifestItem) {\n      dynamicImportsIds.add(manifestItem.id)\n      manifestItem.files.forEach((item) => {\n        dynamicImports.add(item)\n      })\n    }\n  }\n\n  const hybridAmp = ampState.hybrid\n  const docComponentsRendered: DocumentProps['docComponentsRendered'] = {}\n\n  const {\n    assetPrefix,\n    buildId,\n    customServer,\n    defaultLocale,\n    disableOptimizedLoading,\n    domainLocales,\n    locale,\n    locales,\n    runtimeConfig,\n  } = renderOpts\n  const htmlProps: HtmlProps = {\n    __NEXT_DATA__: {\n      props, // The result of getInitialProps\n      page: pathname, // The rendered page\n      query, // querystring parsed / passed by the user\n      buildId, // buildId is used to facilitate caching of page bundles, we send it to the client so that pageloader knows where to load bundles\n      assetPrefix: assetPrefix === '' ? undefined : assetPrefix, // send assetPrefix to the client side when configured, otherwise don't sent in the resulting HTML\n      runtimeConfig, // runtimeConfig if provided, otherwise don't sent in the resulting HTML\n      nextExport: nextExport === true ? true : undefined, // If this is a page exported by `next export`\n      autoExport: isAutoExport === true ? true : undefined, // If this is an auto exported page\n      isFallback,\n      isExperimentalCompile,\n      dynamicIds:\n        dynamicImportsIds.size === 0\n          ? undefined\n          : Array.from(dynamicImportsIds),\n      err: renderOpts.err ? serializeError(dev, renderOpts.err) : undefined, // Error if one happened, otherwise don't sent in the resulting HTML\n      gsp: !!getStaticProps ? true : undefined, // whether the page is getStaticProps\n      gssp: !!getServerSideProps ? true : undefined, // whether the page is getServerSideProps\n      customServer, // whether the user is using a custom server\n      gip: hasPageGetInitialProps ? true : undefined, // whether the page has getInitialProps\n      appGip: !defaultAppGetInitialProps ? true : undefined, // whether the _app has getInitialProps\n      locale,\n      locales,\n      defaultLocale,\n      domainLocales,\n      isPreview: isPreview === true ? true : undefined,\n      notFoundSrcPage: notFoundSrcPage && dev ? notFoundSrcPage : undefined,\n    },\n    strictNextHead: renderOpts.strictNextHead,\n    buildManifest: filteredBuildManifest,\n    docComponentsRendered,\n    dangerousAsPath: router.asPath,\n    canonicalBase:\n      !renderOpts.ampPath && getRequestMeta(req, 'didStripLocale')\n        ? `${renderOpts.canonicalBase || ''}/${renderOpts.locale}`\n        : renderOpts.canonicalBase,\n    ampPath,\n    inAmpMode,\n    isDevelopment: !!dev,\n    hybridAmp,\n    dynamicImports: Array.from(dynamicImports),\n    dynamicCssManifest: new Set(renderOpts.dynamicCssManifest || []),\n    assetPrefix,\n    // Only enabled in production as development mode has features relying on HMR (style injection for example)\n    unstable_runtimeJS:\n      process.env.NODE_ENV === 'production'\n        ? pageConfig.unstable_runtimeJS\n        : undefined,\n    unstable_JsPreload: pageConfig.unstable_JsPreload,\n    assetQueryString,\n    scriptLoader,\n    locale,\n    disableOptimizedLoading,\n    head: documentResult.head,\n    headTags: documentResult.headTags,\n    styles: documentResult.styles,\n    crossOrigin: renderOpts.crossOrigin,\n    optimizeCss: renderOpts.optimizeCss,\n    nextConfigOutput: renderOpts.nextConfigOutput,\n    nextScriptWorkers: renderOpts.nextScriptWorkers,\n    runtime: globalRuntime,\n    largePageDataBytes: renderOpts.largePageDataBytes,\n    nextFontManifest: renderOpts.nextFontManifest,\n    experimentalClientTraceMetadata:\n      renderOpts.experimental.clientTraceMetadata,\n  }\n\n  const document = (\n    <AmpStateContext.Provider value={ampState}>\n      <HtmlContext.Provider value={htmlProps}>\n        {documentResult.documentElement(htmlProps)}\n      </HtmlContext.Provider>\n    </AmpStateContext.Provider>\n  )\n\n  const documentHTML = await getTracer().trace(\n    RenderSpan.renderToString,\n    async () => renderToString(document)\n  )\n\n  if (process.env.NODE_ENV !== 'production') {\n    const nonRenderedComponents = []\n    const expectedDocComponents = ['Main', 'Head', 'NextScript', 'Html']\n\n    for (const comp of expectedDocComponents) {\n      if (!(docComponentsRendered as any)[comp]) {\n        nonRenderedComponents.push(comp)\n      }\n    }\n\n    if (nonRenderedComponents.length) {\n      const missingComponentList = nonRenderedComponents\n        .map((e) => `<${e} />`)\n        .join(', ')\n      const plural = nonRenderedComponents.length !== 1 ? 's' : ''\n      console.warn(\n        `Your custom Document (pages/_document) did not render all the required subcomponent${plural}.\\n` +\n          `Missing component${plural}: ${missingComponentList}\\n` +\n          'Read how to fix here: https://nextjs.org/docs/messages/missing-document-component'\n      )\n    }\n  }\n\n  const [renderTargetPrefix, renderTargetSuffix] = documentHTML.split(\n    '<next-js-internal-body-render-target></next-js-internal-body-render-target>',\n    2\n  )\n\n  let prefix = ''\n  if (!documentHTML.startsWith(DOCTYPE)) {\n    prefix += DOCTYPE\n  }\n  prefix += renderTargetPrefix\n  if (inAmpMode) {\n    prefix += '<!-- __NEXT_DATA__ -->'\n  }\n\n  const content = prefix + documentResult.contentHTML + renderTargetSuffix\n\n  const optimizedHtml = await postProcessHTML(pathname, content, renderOpts, {\n    inAmpMode,\n    hybridAmp,\n  })\n\n  return new RenderResult(optimizedHtml, { metadata })\n}\n\nexport type PagesRender = (\n  req: IncomingMessage,\n  res: ServerResponse,\n  pathname: string,\n  query: NextParsedUrlQuery,\n  renderOpts: RenderOpts\n) => Promise<RenderResult>\n\nexport const renderToHTML: PagesRender = (\n  req,\n  res,\n  pathname,\n  query,\n  renderOpts\n) => {\n  return renderToHTMLImpl(req, res, pathname, query, renderOpts, renderOpts)\n}\n"], "names": ["setLazyProp", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "React", "ReactDOMServerPages", "StyleRegistry", "createStyleRegistry", "GSP_NO_RETURNED_VALUE", "GSSP_COMPONENT_MEMBER_ERROR", "GSSP_NO_RETURNED_VALUE", "STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR", "SERVER_PROPS_GET_INIT_PROPS_CONFLICT", "SERVER_PROPS_SSG_CONFLICT", "SSG_GET_INITIAL_PROPS_CONFLICT", "UNSTABLE_REVALIDATE_RENAME_ERROR", "NEXT_BUILTIN_DOCUMENT", "SERVER_PROPS_ID", "STATIC_PROPS_ID", "STATIC_STATUS_PAGES", "isSerializableProps", "isInAmpMode", "AmpStateContext", "defaultHead", "HeadManagerContext", "Loadable", "LoadableContext", "RouterContext", "isDynamicRoute", "getDisplayName", "isResSent", "loadGetInitialProps", "HtmlContext", "normalizePagePath", "denormalizePagePath", "getRequestMeta", "allowedStatusCodes", "getRedirectStatus", "RenderResult", "isError", "streamToString", "renderToInitialFizzStream", "ImageConfigContext", "stripAnsi", "stripInternalQueries", "adaptForAppRouterInstance", "adaptForPathParams", "adaptForSearchParams", "PathnameContextProviderAdapter", "AppRouterContext", "SearchParamsContext", "PathParamsContext", "getTracer", "RenderSpan", "ReflectAdapter", "formatRevalidate", "getErrorSource", "tryGetPreviewData", "warn", "postProcessHTML", "DOCTYPE", "process", "env", "NEXT_RUNTIME", "require", "console", "bind", "_pathname", "html", "noRouter", "message", "Error", "renderToString", "element", "renderStream", "renderToReadableStream", "allReady", "ServerRouter", "constructor", "pathname", "query", "as", "<PERSON><PERSON><PERSON><PERSON>", "isReady", "basePath", "locale", "locales", "defaultLocale", "domainLocales", "isPreview", "isLocaleDomain", "route", "replace", "<PERSON><PERSON><PERSON>", "push", "reload", "back", "forward", "prefetch", "beforePopState", "enhanceComponents", "options", "App", "Component", "enhanceApp", "enhanceComponent", "renderPageTree", "props", "invalidKeysMsg", "methodName", "<PERSON><PERSON><PERSON><PERSON>", "docsPathname", "toLocaleLowerCase", "join", "checkRedirectValues", "redirect", "req", "method", "destination", "permanent", "statusCode", "errors", "hasStatusCode", "hasPermanent", "has", "destinationType", "basePathType", "length", "url", "errorToJSON", "err", "source", "name", "stack", "digest", "serializeError", "dev", "renderToHTMLImpl", "res", "renderOpts", "extra", "headers", "metadata", "assetQueryString", "userAgent", "toLowerCase", "includes", "Date", "now", "deploymentId", "Object", "assign", "ampPath", "pageConfig", "buildManifest", "reactLoadableManifest", "ErrorDebug", "getStaticProps", "getStaticPaths", "getServerSideProps", "isNextDataRequest", "params", "previewProps", "images", "runtime", "globalRuntime", "isExperimentalCompile", "expireTime", "Document", "OriginComponent", "__<PERSON><PERSON><PERSON><PERSON>", "notFoundSrcPage", "__nextNotFoundSrcPage", "isSSG", "isBuildTimeSSG", "nextExport", "defaultAppGetInitialProps", "getInitialProps", "origGetInitialProps", "hasPageGetInitialProps", "hasPageScripts", "unstable_scriptLoader", "pageIsDynamic", "defaultErrorGetInitialProps", "isAutoExport", "<PERSON><PERSON><PERSON><PERSON>", "revalidate", "nextConfigOutput", "resolvedAsPath", "isValidElementType", "amp", "endsWith", "preloadAll", "undefined", "previewData", "multiZoneDraftMode", "routerIsReady", "router", "appRouter", "<PERSON><PERSON><PERSON><PERSON>", "jsxStyleRegistry", "ampState", "ampFirs<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Boolean", "hybrid", "inAmpMode", "head", "reactLoadableModules", "initialScripts", "beforeInteractive", "concat", "filter", "script", "strategy", "map", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children", "Provider", "value", "updateHead", "state", "updateScripts", "scripts", "mountedInstances", "Set", "moduleName", "registry", "Noop", "AppContainerWithIsomorphicFiberStructure", "ctx", "AppTree", "defaultGetInitialProps", "docCtx", "AppComp", "renderPageHead", "renderPage", "styles", "nonce", "flush", "styledJsxInsertedHTML", "__N_PREVIEW", "data", "trace", "spanName", "attributes", "draftMode", "preview", "revalidateReason", "isOnDemandRevalidate", "staticPropsError", "code", "keys", "key", "NODE_ENV", "notFound", "isNotFound", "__N_REDIRECT", "__N_REDIRECT_STATUS", "__N_REDIRECT_BASE_PATH", "isRedirect", "Number", "isInteger", "Math", "ceil", "JSON", "stringify", "pageProps", "pageData", "canAccessRes", "resOrProxy", "deferred<PERSON><PERSON>nt", "Proxy", "get", "obj", "prop", "resolvedUrl", "serverSidePropsError", "Promise", "unstable_notFound", "unstable_redirect", "filteredBuildManifest", "page", "pages", "lowPriorityFiles", "f", "Body", "div", "id", "renderDocument", "BuiltinFunctionalDocument", "loadDocumentInitialProps", "renderShell", "error", "EnhancedApp", "EnhancedComponent", "stream", "documentCtx", "docProps", "renderContent", "_App", "_Component", "content", "ReactDOMServer", "hasDocumentGetInitialProps", "documentInitialPropsRes", "rawStyledJsxInsertedHTML", "all", "contentHTML", "documentElement", "htmlProps", "headTags", "setRootSpanAttribute", "documentResult", "dynamicImportsIds", "dynamicImports", "mod", "manifestItem", "add", "files", "for<PERSON>ach", "item", "hybridAmp", "doc<PERSON><PERSON><PERSON><PERSON><PERSON>ed", "assetPrefix", "buildId", "customServer", "disableOptimizedLoading", "runtimeConfig", "__NEXT_DATA__", "autoExport", "dynamicIds", "size", "Array", "from", "gsp", "gssp", "gip", "appGip", "strictNextHead", "dangerousAsPath", "canonicalBase", "isDevelopment", "dynamicCssManifest", "unstable_runtimeJS", "unstable_JsPreload", "crossOrigin", "optimizeCss", "nextScriptWorkers", "largePageDataBytes", "nextFontManifest", "experimentalClientTraceMetadata", "experimental", "clientTraceMetadata", "document", "documentHTML", "nonRenderedComponents", "expectedDocComponents", "comp", "missingComponentList", "e", "plural", "renderTargetPrefix", "renderTargetSuffix", "split", "prefix", "startsWith", "optimizedHtml", "renderToHTML"], "mappings": ";AAiBA,SAGEA,WAAW,QACN,cAAa;AACpB,SAASC,eAAe,QAAQ,gCAA+B;AAmB/D,OAAOC,WAAyB,QAAO;AACvC,OAAOC,yBAAyB,uCAAsC;AACtE,SAASC,aAAa,EAAEC,mBAAmB,QAAQ,aAAY;AAC/D,SACEC,qBAAqB,EACrBC,2BAA2B,EAC3BC,sBAAsB,EACtBC,0CAA0C,EAC1CC,oCAAoC,EACpCC,yBAAyB,EACzBC,8BAA8B,EAC9BC,gCAAgC,QAC3B,mBAAkB;AACzB,SACEC,qBAAqB,EACrBC,eAAe,EACfC,eAAe,EACfC,mBAAmB,QACd,0BAAyB;AAChC,SAASC,mBAAmB,QAAQ,+BAA8B;AAClE,SAASC,WAAW,QAAQ,yBAAwB;AACpD,SAASC,eAAe,QAAQ,2CAA0C;AAC1E,SAASC,WAAW,QAAQ,qBAAoB;AAChD,SAASC,kBAAkB,QAAQ,oDAAmD;AACtF,OAAOC,cAAc,wCAAuC;AAC5D,SAASC,eAAe,QAAQ,gDAA+C;AAC/E,SAASC,aAAa,QAAQ,8CAA6C;AAC3E,SAASC,cAAc,QAAQ,wCAAuC;AACtE,SACEC,cAAc,EACdC,SAAS,EACTC,mBAAmB,QACd,sBAAqB;AAC5B,SAASC,WAAW,QAAQ,4CAA2C;AACvE,SAASC,iBAAiB,QAAQ,8CAA6C;AAC/E,SAASC,mBAAmB,QAAQ,gDAA+C;AACnF,SAASC,cAAc,QAAQ,iBAAgB;AAC/C,SAASC,kBAAkB,EAAEC,iBAAiB,QAAQ,yBAAwB;AAC9E,OAAOC,kBAAsD,kBAAiB;AAC9E,OAAOC,aAAa,kBAAiB;AACrC,SACEC,cAAc,EACdC,yBAAyB,QACpB,yCAAwC;AAC/C,SAASC,kBAAkB,QAAQ,oDAAmD;AACtF,OAAOC,eAAe,gCAA+B;AACrD,SAASC,oBAAoB,QAAQ,mBAAkB;AACvD,SACEC,yBAAyB,EACzBC,kBAAkB,EAClBC,oBAAoB,EACpBC,8BAA8B,QACzB,gCAA+B;AACtC,SAASC,gBAAgB,QAAQ,kDAAiD;AAClF,SACEC,mBAAmB,EACnBC,iBAAiB,QACZ,oDAAmD;AAC1D,SAASC,SAAS,QAAQ,qBAAoB;AAC9C,SAASC,UAAU,QAAQ,wBAAuB;AAClD,SAASC,cAAc,QAAQ,wCAAuC;AACtE,SAASC,gBAAgB,QAAQ,mBAAkB;AACnD,SAASC,cAAc,QAAQ,6BAA4B;AAG3D,IAAIC;AACJ,IAAIC;AACJ,IAAIC;AAEJ,MAAMC,UAAU;AAEhB,IAAIC,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;IACvCN,oBACEO,QAAQ,yCAAyCP,iBAAiB;IACpEC,OAAOM,QAAQ,uBAAuBN,IAAI;IAC1CC,kBAAkBK,QAAQ,kBAAkBL,eAAe;AAC7D,OAAO;IACLD,OAAOO,QAAQP,IAAI,CAACQ,IAAI,CAACD;IACzBN,kBAAkB,OAAOQ,WAAmBC,OAAiBA;AAC/D;AAEA,SAASC;IACP,MAAMC,UACJ;IACF,MAAM,IAAIC,MAAMD;AAClB;AAEA,eAAeE,eAAeC,OAA2B;IACvD,MAAMC,eAAe,MAAMrE,oBAAoBsE,sBAAsB,CAACF;IACtE,MAAMC,aAAaE,QAAQ;IAC3B,OAAOpC,eAAekC;AACxB;AAEA,MAAMG;IAgBJC,YACEC,QAAgB,EAChBC,KAAqB,EACrBC,EAAU,EACV,EAAEC,UAAU,EAA2B,EACvCC,OAAgB,EAChBC,QAAgB,EAChBC,MAAe,EACfC,OAAkB,EAClBC,aAAsB,EACtBC,aAA8B,EAC9BC,SAAmB,EACnBC,cAAwB,CACxB;QACA,IAAI,CAACC,KAAK,GAAGZ,SAASa,OAAO,CAAC,OAAO,OAAO;QAC5C,IAAI,CAACb,QAAQ,GAAGA;QAChB,IAAI,CAACC,KAAK,GAAGA;QACb,IAAI,CAACa,MAAM,GAAGZ;QACd,IAAI,CAACC,UAAU,GAAGA;QAClB,IAAI,CAACE,QAAQ,GAAGA;QAChB,IAAI,CAACC,MAAM,GAAGA;QACd,IAAI,CAACC,OAAO,GAAGA;QACf,IAAI,CAACC,aAAa,GAAGA;QACrB,IAAI,CAACJ,OAAO,GAAGA;QACf,IAAI,CAACK,aAAa,GAAGA;QACrB,IAAI,CAACC,SAAS,GAAG,CAAC,CAACA;QACnB,IAAI,CAACC,cAAc,GAAG,CAAC,CAACA;IAC1B;IAEAI,OAAY;QACVzB;IACF;IACAuB,UAAe;QACbvB;IACF;IACA0B,SAAS;QACP1B;IACF;IACA2B,OAAO;QACL3B;IACF;IACA4B,UAAgB;QACd5B;IACF;IACA6B,WAAgB;QACd7B;IACF;IACA8B,iBAAiB;QACf9B;IACF;AACF;AAEA,SAAS+B,kBACPC,OAA2B,EAC3BC,GAAY,EACZC,SAA4B;IAK5B,8BAA8B;IAC9B,IAAI,OAAOF,YAAY,YAAY;QACjC,OAAO;YACLC;YACAC,WAAWF,QAAQE;QACrB;IACF;IAEA,OAAO;QACLD,KAAKD,QAAQG,UAAU,GAAGH,QAAQG,UAAU,CAACF,OAAOA;QACpDC,WAAWF,QAAQI,gBAAgB,GAC/BJ,QAAQI,gBAAgB,CAACF,aACzBA;IACN;AACF;AAEA,SAASG,eACPJ,GAAY,EACZC,SAA4B,EAC5BI,KAAU;IAEV,qBAAO,KAACL;QAAIC,WAAWA;QAAY,GAAGI,KAAK;;AAC7C;AAyEA,MAAMC,iBAAiB,CACrBC,YACAC;IAEA,MAAMC,eAAe,CAAC,QAAQ,EAAEF,WAAWG,iBAAiB,GAAG,MAAM,CAAC;IAEtE,OACE,CAAC,qCAAqC,EAAEH,WAAW,wFAAwF,CAAC,GAC5I,CAAC,6DAA6D,CAAC,GAC/D,CAAC,gCAAgC,EAAEC,YAAYG,IAAI,CAAC,MAAM,CAAC,CAAC,GAC5D,CAAC,8CAA8C,EAAEF,cAAc;AAEnE;AAEA,SAASG,oBACPC,QAAkB,EAClBC,GAAoB,EACpBC,MAA+C;IAE/C,MAAM,EAAEC,WAAW,EAAEC,SAAS,EAAEC,UAAU,EAAEpC,QAAQ,EAAE,GAAG+B;IACzD,IAAIM,SAAmB,EAAE;IAEzB,MAAMC,gBAAgB,OAAOF,eAAe;IAC5C,MAAMG,eAAe,OAAOJ,cAAc;IAE1C,IAAII,gBAAgBD,eAAe;QACjCD,OAAO3B,IAAI,CAAC,CAAC,yDAAyD,CAAC;IACzE,OAAO,IAAI6B,gBAAgB,OAAOJ,cAAc,WAAW;QACzDE,OAAO3B,IAAI,CAAC,CAAC,2CAA2C,CAAC;IAC3D,OAAO,IAAI4B,iBAAiB,CAACtF,mBAAmBwF,GAAG,CAACJ,aAAc;QAChEC,OAAO3B,IAAI,CACT,CAAC,wCAAwC,EAAE;eAAI1D;SAAmB,CAAC6E,IAAI,CACrE,OACC;IAEP;IACA,MAAMY,kBAAkB,OAAOP;IAE/B,IAAIO,oBAAoB,UAAU;QAChCJ,OAAO3B,IAAI,CACT,CAAC,8CAA8C,EAAE+B,iBAAiB;IAEtE;IAEA,MAAMC,eAAe,OAAO1C;IAE5B,IAAI0C,iBAAiB,eAAeA,iBAAiB,WAAW;QAC9DL,OAAO3B,IAAI,CACT,CAAC,sDAAsD,EAAEgC,cAAc;IAE3E;IAEA,IAAIL,OAAOM,MAAM,GAAG,GAAG;QACrB,MAAM,IAAIxD,MACR,CAAC,sCAAsC,EAAE8C,OAAO,KAAK,EAAED,IAAIY,GAAG,CAAC,EAAE,CAAC,GAChEP,OAAOR,IAAI,CAAC,WACZ,OACA,CAAC,0EAA0E,CAAC;IAElF;AACF;AAEA,OAAO,SAASgB,YAAYC,GAAU;IACpC,IAAIC,SACF;IAEF,IAAItE,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;QACvCoE,SAAS3E,eAAe0E,QAAQ;IAClC;IAEA,OAAO;QACLE,MAAMF,IAAIE,IAAI;QACdD;QACA7D,SAAS3B,UAAUuF,IAAI5D,OAAO;QAC9B+D,OAAOH,IAAIG,KAAK;QAChBC,QAAQ,AAACJ,IAAYI,MAAM;IAC7B;AACF;AAEA,SAASC,eACPC,GAAwB,EACxBN,GAAU;IAKV,IAAIM,KAAK;QACP,OAAOP,YAAYC;IACrB;IAEA,OAAO;QACLE,MAAM;QACN9D,SAAS;QACTkD,YAAY;IACd;AACF;AAEA,OAAO,eAAeiB,iBACpBrB,GAAoB,EACpBsB,GAAmB,EACnB3D,QAAgB,EAChBC,KAAyB,EACzB2D,UAAmD,EACnDC,KAAsB;IAEtB,uEAAuE;IACvE1I,YAAY;QAAEkH,KAAKA;IAAW,GAAG,WAAWjH,gBAAgBiH,IAAIyB,OAAO;IAEvE,MAAMC,WAAsC,CAAC;IAE7CA,SAASC,gBAAgB,GACvB,AAACJ,WAAWH,GAAG,IAAIG,WAAWI,gBAAgB,IAAK;IAErD,IAAIJ,WAAWH,GAAG,IAAI,CAACM,SAASC,gBAAgB,EAAE;QAChD,MAAMC,YAAY,AAAC5B,CAAAA,IAAIyB,OAAO,CAAC,aAAa,IAAI,EAAC,EAAGI,WAAW;QAC/D,IAAID,UAAUE,QAAQ,CAAC,aAAa,CAACF,UAAUE,QAAQ,CAAC,WAAW;YACjE,+EAA+E;YAC/E,4EAA4E;YAC5E,6FAA6F;YAC7F,yFAAyF;YACzF,iCAAiC;YACjCJ,SAASC,gBAAgB,GAAG,CAAC,IAAI,EAAEI,KAAKC,GAAG,IAAI;QACjD;IACF;IAEA,iEAAiE;IACjE,IAAIT,WAAWU,YAAY,EAAE;QAC3BP,SAASC,gBAAgB,IAAI,GAAGD,SAASC,gBAAgB,GAAG,MAAM,IAAI,IAAI,EACxEJ,WAAWU,YAAY,EACvB;IACJ;IAEA,qCAAqC;IACrCrE,QAAQsE,OAAOC,MAAM,CAAC,CAAC,GAAGvE;IAE1B,MAAM,EACJkD,GAAG,EACHM,MAAM,KAAK,EACXgB,UAAU,EAAE,EACZC,aAAa,CAAC,CAAC,EACfC,aAAa,EACbC,qBAAqB,EACrBC,UAAU,EACVC,cAAc,EACdC,cAAc,EACdC,kBAAkB,EAClBC,iBAAiB,EACjBC,MAAM,EACNC,YAAY,EACZ9E,QAAQ,EACR+E,MAAM,EACNC,SAASC,aAAa,EACtBC,qBAAqB,EACrBC,UAAU,EACX,GAAG5B;IACJ,MAAM,EAAErC,GAAG,EAAE,GAAGsC;IAEhB,MAAMG,mBAAmBD,SAASC,gBAAgB;IAElD,IAAIyB,WAAW5B,MAAM4B,QAAQ;IAE7B,IAAIjE,YACFoC,WAAWpC,SAAS;IACtB,MAAMkE,kBAAkBlE;IAExB,MAAMrB,aAAa,CAAC,CAACF,MAAM0F,cAAc;IACzC,MAAMC,kBAAkB3F,MAAM4F,qBAAqB;IAEnD,+CAA+C;IAC/ChI,qBAAqBoC;IAErB,MAAM6F,QAAQ,CAAC,CAAChB;IAChB,MAAMiB,iBAAiBD,SAASlC,WAAWoC,UAAU;IACrD,MAAMC,4BACJ1E,IAAI2E,eAAe,KAAK,AAAC3E,IAAY4E,mBAAmB;IAE1D,MAAMC,yBAAyB,CAAC,EAAE5E,6BAAD,AAACA,UAAmB0E,eAAe;IACpE,MAAMG,iBAAkB7E,6BAAD,AAACA,UAAmB8E,qBAAqB;IAEhE,MAAMC,gBAAgB1J,eAAemD;IAErC,MAAMwG,8BACJxG,aAAa,aACb,AAACwB,UAAkB0E,eAAe,KAChC,AAAC1E,UAAkB2E,mBAAmB;IAE1C,IACEvC,WAAWoC,UAAU,IACrBI,0BACA,CAACI,6BACD;QACA7H,KACE,CAAC,kCAAkC,EAAEqB,SAAS,CAAC,CAAC,GAC9C,CAAC,6DAA6D,CAAC,GAC/D,CAAC,wDAAwD,CAAC,GAC1D,CAAC,sEAAsE,CAAC;IAE9E;IAEA,IAAIyG,eACF,CAACL,0BACDH,6BACA,CAACH,SACD,CAACd;IAEH,2DAA2D;IAC3D,uDAAuD;IACvD,4DAA4D;IAC5D,gBAAgB;IAChB,IAAIyB,gBAAgB,CAAChD,OAAO8B,uBAAuB;QACjD5B,IAAI+C,SAAS,CACX,iBACAlI,iBAAiB;YACfmI,YAAY;YACZnB;QACF;QAEFiB,eAAe;IACjB;IAEA,IAAIL,0BAA0BN,OAAO;QACnC,MAAM,IAAItG,MAAMzD,iCAAiC,CAAC,CAAC,EAAEiE,UAAU;IACjE;IAEA,IAAIoG,0BAA0BpB,oBAAoB;QAChD,MAAM,IAAIxF,MAAM3D,uCAAuC,CAAC,CAAC,EAAEmE,UAAU;IACvE;IAEA,IAAIgF,sBAAsBc,OAAO;QAC/B,MAAM,IAAItG,MAAM1D,4BAA4B,CAAC,CAAC,EAAEkE,UAAU;IAC5D;IAEA,IAAIgF,sBAAsBpB,WAAWgD,gBAAgB,KAAK,UAAU;QAClE,MAAM,IAAIpH,MACR;IAEJ;IAEA,IAAIuF,kBAAkB,CAACwB,eAAe;QACpC,MAAM,IAAI/G,MACR,CAAC,uEAAuE,EAAEQ,SAAS,EAAE,CAAC,GACpF,CAAC,8EAA8E,CAAC;IAEtF;IAEA,IAAI,CAAC,CAAC+E,kBAAkB,CAACe,OAAO;QAC9B,MAAM,IAAItG,MACR,CAAC,qDAAqD,EAAEQ,SAAS,qDAAqD,CAAC;IAE3H;IAEA,IAAI8F,SAASS,iBAAiB,CAACxB,gBAAgB;QAC7C,MAAM,IAAIvF,MACR,CAAC,qEAAqE,EAAEQ,SAAS,EAAE,CAAC,GAClF,CAAC,0EAA0E,CAAC;IAElF;IAEA,IAAIc,SAAiB8C,WAAWiD,cAAc,IAAKxE,IAAIY,GAAG;IAE1D,IAAIQ,KAAK;QACP,MAAM,EAAEqD,kBAAkB,EAAE,GAAG7H,QAAQ;QACvC,IAAI,CAAC6H,mBAAmBtF,YAAY;YAClC,MAAM,IAAIhC,MACR,CAAC,sDAAsD,EAAEQ,SAAS,CAAC,CAAC;QAExE;QAEA,IAAI,CAAC8G,mBAAmBvF,MAAM;YAC5B,MAAM,IAAI/B,MACR,CAAC,4DAA4D,CAAC;QAElE;QAEA,IAAI,CAACsH,mBAAmBrB,WAAW;YACjC,MAAM,IAAIjG,MACR,CAAC,iEAAiE,CAAC;QAEvE;QAEA,IAAIiH,gBAAgBtG,YAAY;YAC9B,iEAAiE;YACjEF,QAAQ;gBACN,GAAIA,MAAM8G,GAAG,GACT;oBACEA,KAAK9G,MAAM8G,GAAG;gBAChB,IACA,CAAC,CAAC;YACR;YACAjG,SAAS,GAAGd,WACV,qEAAqE;YACrEqC,IAAIY,GAAG,CAAE+D,QAAQ,CAAC,QAAQhH,aAAa,OAAO,CAACuG,gBAAgB,MAAM,IACrE;YACFlE,IAAIY,GAAG,GAAGjD;QACZ;QAEA,IAAIA,aAAa,UAAWoG,CAAAA,0BAA0BpB,kBAAiB,GAAI;YACzE,MAAM,IAAIxF,MACR,CAAC,cAAc,EAAE5D,4CAA4C;QAEjE;QACA,IACEQ,oBAAoB+H,QAAQ,CAACnE,aAC5BoG,CAAAA,0BAA0BpB,kBAAiB,GAC5C;YACA,MAAM,IAAIxF,MACR,CAAC,OAAO,EAAEQ,SAAS,GAAG,EAAEpE,4CAA4C;QAExE;IACF;IAEA,KAAK,MAAMkG,cAAc;QACvB;QACA;QACA;KACD,CAAE;QACD,IAAKN,6BAAD,AAACA,SAAmB,CAACM,WAAW,EAAE;YACpC,MAAM,IAAItC,MACR,CAAC,KAAK,EAAEQ,SAAS,CAAC,EAAE8B,WAAW,CAAC,EAAEpG,6BAA6B;QAEnE;IACF;IAEA,MAAMgB,SAASuK,UAAU,GAAG,2CAA2C;;IAEvE,IAAIvG,YAAiCwG;IACrC,IAAIC;IAEJ,IACE,AAACrB,CAAAA,SAASd,kBAAiB,KAC3B,CAAC7E,cACDrB,QAAQC,GAAG,CAACC,YAAY,KAAK,UAC7BmG,cACA;QACA,uEAAuE;QACvE,oEAAoE;QACpE,UAAU;QACVgC,cAAczI,kBACZ2D,KACAsB,KACAwB,cACA,CAAC,CAACvB,WAAWwD,kBAAkB;QAEjC1G,YAAYyG,gBAAgB;IAC9B;IAEA,yBAAyB;IACzB,MAAME,gBAAgB,CAAC,CACrBrC,CAAAA,sBACAoB,0BACC,CAACH,6BAA6B,CAACH,SAChCP,qBAAoB;IAEtB,MAAM+B,SAAS,IAAIxH,aACjBE,UACAC,OACAa,QACA;QACEX,YAAYA;IACd,GACAkH,eACAhH,UACAuD,WAAWtD,MAAM,EACjBsD,WAAWrD,OAAO,EAClBqD,WAAWpD,aAAa,EACxBoD,WAAWnD,aAAa,EACxBC,WACAtD,eAAeiF,KAAK;IAGtB,MAAMkF,YAAYzJ,0BAA0BwJ;IAE5C,IAAIE,eAAoB,CAAC;IACzB,MAAMC,mBAAmBjM;IACzB,MAAMkM,WAAW;QACfC,UAAUjD,WAAWqC,GAAG,KAAK;QAC7Ba,UAAUC,QAAQ5H,MAAM8G,GAAG;QAC3Be,QAAQpD,WAAWqC,GAAG,KAAK;IAC7B;IAEA,wCAAwC;IACxC,MAAMgB,YAAYjJ,QAAQC,GAAG,CAACC,YAAY,KAAK,UAAU1C,YAAYoL;IACrE,IAAIM,OAAsBxL,YAAYuL;IACtC,MAAME,uBAAiC,EAAE;IAEzC,IAAIC,iBAAsB,CAAC;IAC3B,IAAI7B,gBAAgB;QAClB6B,eAAeC,iBAAiB,GAAG,EAAE,CAClCC,MAAM,CAAC/B,kBACPgC,MAAM,CAAC,CAACC,SAAgBA,OAAO1G,KAAK,CAAC2G,QAAQ,KAAK,qBAClDC,GAAG,CAAC,CAACF,SAAgBA,OAAO1G,KAAK;IACtC;IAEA,MAAM6G,eAAe,CAAC,EAAEC,QAAQ,EAA6B,iBAC3D,KAACxK,iBAAiByK,QAAQ;YAACC,OAAOrB;sBAChC,cAAA,KAACpJ,oBAAoBwK,QAAQ;gBAACC,OAAO5K,qBAAqBsJ;0BACxD,cAAA,KAACrJ;oBACCqJ,QAAQA;oBACRb,cAAcA;8BAEd,cAAA,KAACrI,kBAAkBuK,QAAQ;wBAACC,OAAO7K,mBAAmBuJ;kCACpD,cAAA,KAAC1K,cAAc+L,QAAQ;4BAACC,OAAOtB;sCAC7B,cAAA,KAAC/K,gBAAgBoM,QAAQ;gCAACC,OAAOlB;0CAC/B,cAAA,KAACjL,mBAAmBkM,QAAQ;oCAC1BC,OAAO;wCACLC,YAAY,CAACC;4CACXd,OAAOc;wCACT;wCACAC,eAAe,CAACC;4CACdxB,eAAewB;wCACjB;wCACAA,SAASd;wCACTe,kBAAkB,IAAIC;oCACxB;8CAEA,cAAA,KAACvM,gBAAgBgM,QAAQ;wCACvBC,OAAO,CAACO,aACNlB,qBAAqBlH,IAAI,CAACoI;kDAG5B,cAAA,KAAC5N;4CAAc6N,UAAU3B;sDACvB,cAAA,KAAC9J,mBAAmBgL,QAAQ;gDAACC,OAAOxD;0DACjCsD;;;;;;;;;;;IAavB,yEAAyE;IACzE,4EAA4E;IAC5E,uDAAuD;IACvD,6EAA6E;IAC7E,iBAAiB;IACjB,+CAA+C;IAC/C,MAAMW,OAAO,IAAM;IACnB,MAAMC,2CAED,CAAC,EAAEZ,QAAQ,EAAE;QAChB,qBACE;;8BAEE,KAACW;8BACD,KAACZ;8BACC,cAAA;;4BAEGhF,oBACC;;oCACGiF;kDACD,KAACW;;iCAGHX;0CAGF,KAACW;;;;;;IAKX;IAEA,MAAME,MAAM;QACVpG;QACAd,KAAKoE,eAAeS,YAAY7E;QAChCsB,KAAK8C,eAAeS,YAAYvD;QAChC3D;QACAC;QACAa;QACAR,QAAQsD,WAAWtD,MAAM;QACzBC,SAASqD,WAAWrD,OAAO;QAC3BC,eAAeoD,WAAWpD,aAAa;QACvCgJ,SAAS,CAAC5H;YACR,qBACE,KAAC0H;0BACE3H,eAAeJ,KAAKmE,iBAAiB;oBAAE,GAAG9D,KAAK;oBAAE0F;gBAAO;;QAG/D;QACAmC,wBAAwB,OACtBC,QACApI,UAA8B,CAAC,CAAC;YAEhC,MAAMG,aAAa,CAACkI;gBAClB,OAAO,CAAC/H,sBAAe,KAAC+H;wBAAS,GAAG/H,KAAK;;YAC3C;YAEA,MAAM,EAAEvC,IAAI,EAAE2I,MAAM4B,cAAc,EAAE,GAAG,MAAMF,OAAOG,UAAU,CAAC;gBAC7DpI;YACF;YACA,MAAMqI,SAASrC,iBAAiBqC,MAAM,CAAC;gBAAEC,OAAOzI,QAAQyI,KAAK;YAAC;YAC9DtC,iBAAiBuC,KAAK;YACtB,OAAO;gBAAE3K;gBAAM2I,MAAM4B;gBAAgBE;YAAO;QAC9C;IACF;IACA,IAAIlI;IAEJ,MAAMoE,aACJ,CAACF,SAAUlC,CAAAA,WAAWoC,UAAU,IAAKvC,OAAQgD,CAAAA,gBAAgBtG,UAAS,CAAE;IAE1E,MAAM8J,wBAAwB;QAC5B,MAAMH,SAASrC,iBAAiBqC,MAAM;QACtCrC,iBAAiBuC,KAAK;QACtB,qBAAO;sBAAGF;;IACZ;IAEAlI,QAAQ,MAAM5E,oBAAoBuE,KAAK;QACrCiI,SAASD,IAAIC,OAAO;QACpBhI;QACA8F;QACAiC;IACF;IAEA,IAAI,AAACzD,CAAAA,SAASd,kBAAiB,KAAMtE,WAAW;QAC9CkB,MAAMsI,WAAW,GAAG;IACtB;IAEA,IAAIpE,OAAO;QACTlE,KAAK,CAACzF,gBAAgB,GAAG;IAC3B;IAEA,IAAI2J,SAAS,CAAC3F,YAAY;QACxB,IAAIgK;QAEJ,IAAI;YACFA,OAAO,MAAM9L,YAAY+L,KAAK,CAC5B9L,WAAWwG,cAAc,EACzB;gBACEuF,UAAU,CAAC,eAAe,EAAErK,UAAU;gBACtCsK,YAAY;oBACV,cAActK;gBAChB;YACF,GACA,IACE8E,eAAe;oBACb,GAAIyB,gBAAgB;wBAAErB;oBAAO,IAAIgC,SAAS;oBAC1C,GAAIxG,YACA;wBAAE6J,WAAW;wBAAMC,SAAS;wBAAMrD,aAAaA;oBAAY,IAC3DD,SAAS;oBACb3G,SAASqD,WAAWrD,OAAO;oBAC3BD,QAAQsD,WAAWtD,MAAM;oBACzBE,eAAeoD,WAAWpD,aAAa;oBACvCiK,kBAAkB7G,WAAW8G,oBAAoB,GAC7C,cACA3E,iBACE,UACA;gBACR;QAEN,EAAE,OAAO4E,kBAAuB;YAC9B,2DAA2D;YAC3D,gBAAgB;YAChB,IAAIA,oBAAoBA,iBAAiBC,IAAI,KAAK,UAAU;gBAC1D,OAAOD,iBAAiBC,IAAI;YAC9B;YACA,MAAMD;QACR;QAEA,IAAIR,QAAQ,MAAM;YAChB,MAAM,IAAI3K,MAAM/D;QAClB;QAEA,MAAMsG,cAAcwC,OAAOsG,IAAI,CAACV,MAAM9B,MAAM,CAC1C,CAACyC,MACCA,QAAQ,gBACRA,QAAQ,WACRA,QAAQ,cACRA,QAAQ;QAGZ,IAAI/I,YAAYoC,QAAQ,CAAC,wBAAwB;YAC/C,MAAM,IAAI3E,MAAMxD;QAClB;QAEA,IAAI+F,YAAYiB,MAAM,EAAE;YACtB,MAAM,IAAIxD,MAAMqC,eAAe,kBAAkBE;QACnD;QAEA,IAAIjD,QAAQC,GAAG,CAACgM,QAAQ,KAAK,cAAc;YACzC,IACE,OAAO,AAACZ,KAAaa,QAAQ,KAAK,eAClC,OAAO,AAACb,KAAa/H,QAAQ,KAAK,aAClC;gBACA,MAAM,IAAI5C,MACR,CAAC,4DAA4D,EAC3DsG,QAAQ,mBAAmB,qBAC5B,yBAAyB,EAAE9F,SAAS,oFAAoF,CAAC;YAE9H;QACF;QAEA,IAAI,cAAcmK,QAAQA,KAAKa,QAAQ,EAAE;YACvC,IAAIhL,aAAa,QAAQ;gBACvB,MAAM,IAAIR,MACR,CAAC,wFAAwF,CAAC;YAE9F;YAEAuE,SAASkH,UAAU,GAAG;QACxB;QAEA,IACE,cAAcd,QACdA,KAAK/H,QAAQ,IACb,OAAO+H,KAAK/H,QAAQ,KAAK,UACzB;YACAD,oBAAoBgI,KAAK/H,QAAQ,EAAcC,KAAK;YAEpD,IAAI0D,gBAAgB;gBAClB,MAAM,IAAIvG,MACR,CAAC,0EAA0E,EAAE6C,IAAIY,GAAG,CAAC,GAAG,CAAC,GACvF,CAAC,kFAAkF,CAAC;YAE1F;;YAEEkH,KAAavI,KAAK,GAAG;gBACrBsJ,cAAcf,KAAK/H,QAAQ,CAACG,WAAW;gBACvC4I,qBAAqB7N,kBAAkB6M,KAAK/H,QAAQ;YACtD;YACA,IAAI,OAAO+H,KAAK/H,QAAQ,CAAC/B,QAAQ,KAAK,aAAa;;gBAC/C8J,KAAavI,KAAK,CAACwJ,sBAAsB,GAAGjB,KAAK/H,QAAQ,CAAC/B,QAAQ;YACtE;YACA0D,SAASsH,UAAU,GAAG;QACxB;QAEA,IACE,AAAC5H,CAAAA,OAAOsC,cAAa,KACrB,CAAChC,SAASkH,UAAU,IACpB,CAAC5O,oBAAoB2D,UAAU,kBAAkB,AAACmK,KAAavI,KAAK,GACpE;YACA,kEAAkE;YAClE,MAAM,IAAIpC,MACR;QAEJ;QAEA,IAAImH;QACJ,IAAI,gBAAgBwD,MAAM;YACxB,IAAIA,KAAKxD,UAAU,IAAI/C,WAAWgD,gBAAgB,KAAK,UAAU;gBAC/D,MAAM,IAAIpH,MACR;YAEJ;YACA,IAAI,OAAO2K,KAAKxD,UAAU,KAAK,UAAU;gBACvC,IAAI,CAAC2E,OAAOC,SAAS,CAACpB,KAAKxD,UAAU,GAAG;oBACtC,MAAM,IAAInH,MACR,CAAC,6EAA6E,EAAE6C,IAAIY,GAAG,CAAC,0BAA0B,EAAEkH,KAAKxD,UAAU,CAAC,kBAAkB,CAAC,GACrJ,CAAC,6BAA6B,EAAE6E,KAAKC,IAAI,CACvCtB,KAAKxD,UAAU,EACf,yDAAyD,CAAC;gBAElE,OAAO,IAAIwD,KAAKxD,UAAU,IAAI,GAAG;oBAC/B,MAAM,IAAInH,MACR,CAAC,qEAAqE,EAAE6C,IAAIY,GAAG,CAAC,oHAAoH,CAAC,GACnM,CAAC,2FAA2F,CAAC,GAC7F,CAAC,oEAAoE,CAAC;gBAE5E,OAAO;oBACL,IAAIkH,KAAKxD,UAAU,GAAG,UAAU;wBAC9B,oDAAoD;wBACpDzH,QAAQP,IAAI,CACV,CAAC,oEAAoE,EAAE0D,IAAIY,GAAG,CAAC,mCAAmC,CAAC,GACjH,CAAC,kHAAkH,CAAC;oBAE1H;oBAEA0D,aAAawD,KAAKxD,UAAU;gBAC9B;YACF,OAAO,IAAIwD,KAAKxD,UAAU,KAAK,MAAM;gBACnC,qEAAqE;gBACrE,0DAA0D;gBAC1D,yBAAyB;gBACzBA,aAAa;YACf,OAAO,IACLwD,KAAKxD,UAAU,KAAK,SACpB,OAAOwD,KAAKxD,UAAU,KAAK,aAC3B;gBACA,mCAAmC;gBACnCA,aAAa;YACf,OAAO;gBACL,MAAM,IAAInH,MACR,CAAC,8HAA8H,EAAEkM,KAAKC,SAAS,CAC7IxB,KAAKxD,UAAU,EACf,MAAM,EAAEtE,IAAIY,GAAG,EAAE;YAEvB;QACF,OAAO;YACL,mCAAmC;YACnC0D,aAAa;QACf;QAEA/E,MAAMgK,SAAS,GAAGrH,OAAOC,MAAM,CAC7B,CAAC,GACD5C,MAAMgK,SAAS,EACf,WAAWzB,OAAOA,KAAKvI,KAAK,GAAGsF;QAGjC,0CAA0C;QAC1CnD,SAAS4C,UAAU,GAAGA;QACtB5C,SAAS8H,QAAQ,GAAGjK;QAEpB,+DAA+D;QAC/D,IAAImC,SAASkH,UAAU,EAAE;YACvB,OAAO,IAAI1N,aAAa,MAAM;gBAAEwG;YAAS;QAC3C;IACF;IAEA,IAAIiB,oBAAoB;QACtBpD,KAAK,CAAC1F,gBAAgB,GAAG;IAC3B;IAEA,IAAI8I,sBAAsB,CAAC7E,YAAY;QACrC,IAAIgK;QAEJ,IAAI2B,eAAe;QACnB,IAAIC,aAAapI;QACjB,IAAIqI,kBAAkB;QACtB,IAAIlN,QAAQC,GAAG,CAACgM,QAAQ,KAAK,cAAc;YACzCgB,aAAa,IAAIE,MAAsBtI,KAAK;gBAC1CuI,KAAK,SAAUC,GAAG,EAAEC,IAAI;oBACtB,IAAI,CAACN,cAAc;wBACjB,MAAMvM,UACJ,CAAC,8DAA8D,CAAC,GAChE,CAAC,kEAAkE,CAAC;wBAEtE,IAAIyM,iBAAiB;4BACnB,MAAM,IAAIxM,MAAMD;wBAClB,OAAO;4BACLZ,KAAKY;wBACP;oBACF;oBAEA,IAAI,OAAO6M,SAAS,UAAU;wBAC5B,OAAO7N,eAAe2N,GAAG,CAACC,KAAKC,MAAMzI;oBACvC;oBAEA,OAAOpF,eAAe2N,GAAG,CAACC,KAAKC,MAAMzI;gBACvC;YACF;QACF;QAEA,IAAI;YACFwG,OAAO,MAAM9L,YAAY+L,KAAK,CAC5B9L,WAAW0G,kBAAkB,EAC7B;gBACEqF,UAAU,CAAC,mBAAmB,EAAErK,UAAU;gBAC1CsK,YAAY;oBACV,cAActK;gBAChB;YACF,GACA,UACEgF,mBAAmB;oBACjB3C,KAAKA;oBAGLsB,KAAKoI;oBACL9L;oBACAoM,aAAazI,WAAWyI,WAAW;oBACnC,GAAI9F,gBAAgB;wBAAErB;oBAAO,IAAIgC,SAAS;oBAC1C,GAAIC,gBAAgB,QAChB;wBAAEoD,WAAW;wBAAMC,SAAS;wBAAMrD,aAAaA;oBAAY,IAC3DD,SAAS;oBACb3G,SAASqD,WAAWrD,OAAO;oBAC3BD,QAAQsD,WAAWtD,MAAM;oBACzBE,eAAeoD,WAAWpD,aAAa;gBACzC;YAEJsL,eAAe;YACf/H,SAAS4C,UAAU,GAAG;QACxB,EAAE,OAAO2F,sBAA2B;YAClC,2DAA2D;YAC3D,gBAAgB;YAChB,IACE9O,QAAQ8O,yBACRA,qBAAqB1B,IAAI,KAAK,UAC9B;gBACA,OAAO0B,qBAAqB1B,IAAI;YAClC;YACA,MAAM0B;QACR;QAEA,IAAInC,QAAQ,MAAM;YAChB,MAAM,IAAI3K,MAAM7D;QAClB;QAEA,IAAI,AAACwO,KAAavI,KAAK,YAAY2K,SAAS;YAC1CP,kBAAkB;QACpB;QAEA,MAAMjK,cAAcwC,OAAOsG,IAAI,CAACV,MAAM9B,MAAM,CAC1C,CAACyC,MAAQA,QAAQ,WAAWA,QAAQ,cAAcA,QAAQ;QAG5D,IAAI,AAACX,KAAaqC,iBAAiB,EAAE;YACnC,MAAM,IAAIhN,MACR,CAAC,2FAA2F,EAAEQ,UAAU;QAE5G;QACA,IAAI,AAACmK,KAAasC,iBAAiB,EAAE;YACnC,MAAM,IAAIjN,MACR,CAAC,2FAA2F,EAAEQ,UAAU;QAE5G;QAEA,IAAI+B,YAAYiB,MAAM,EAAE;YACtB,MAAM,IAAIxD,MAAMqC,eAAe,sBAAsBE;QACvD;QAEA,IAAI,cAAcoI,QAAQA,KAAKa,QAAQ,EAAE;YACvC,IAAIhL,aAAa,QAAQ;gBACvB,MAAM,IAAIR,MACR,CAAC,wFAAwF,CAAC;YAE9F;YAEAuE,SAASkH,UAAU,GAAG;YACtB,OAAO,IAAI1N,aAAa,MAAM;gBAAEwG;YAAS;QAC3C;QAEA,IAAI,cAAcoG,QAAQ,OAAOA,KAAK/H,QAAQ,KAAK,UAAU;YAC3DD,oBAAoBgI,KAAK/H,QAAQ,EAAcC,KAAK;YAClD8H,KAAavI,KAAK,GAAG;gBACrBsJ,cAAcf,KAAK/H,QAAQ,CAACG,WAAW;gBACvC4I,qBAAqB7N,kBAAkB6M,KAAK/H,QAAQ;YACtD;YACA,IAAI,OAAO+H,KAAK/H,QAAQ,CAAC/B,QAAQ,KAAK,aAAa;;gBAC/C8J,KAAavI,KAAK,CAACwJ,sBAAsB,GAAGjB,KAAK/H,QAAQ,CAAC/B,QAAQ;YACtE;YACA0D,SAASsH,UAAU,GAAG;QACxB;QAEA,IAAIW,iBAAiB;;YACjB7B,KAAavI,KAAK,GAAG,MAAM,AAACuI,KAAavI,KAAK;QAClD;QAEA,IACE,AAAC6B,CAAAA,OAAOsC,cAAa,KACrB,CAAC1J,oBAAoB2D,UAAU,sBAAsB,AAACmK,KAAavI,KAAK,GACxE;YACA,kEAAkE;YAClE,MAAM,IAAIpC,MACR;QAEJ;QAEAoC,MAAMgK,SAAS,GAAGrH,OAAOC,MAAM,CAAC,CAAC,GAAG5C,MAAMgK,SAAS,EAAE,AAACzB,KAAavI,KAAK;QACxEmC,SAAS8H,QAAQ,GAAGjK;IACtB;IAEA,IACE,CAACkE,SAAS,6CAA6C;IACvD,CAACd,sBACDlG,QAAQC,GAAG,CAACgM,QAAQ,KAAK,gBACzBxG,OAAOsG,IAAI,CAACjJ,CAAAA,yBAAAA,MAAOgK,SAAS,KAAI,CAAC,GAAGzH,QAAQ,CAAC,QAC7C;QACAjF,QAAQP,IAAI,CACV,CAAC,iGAAiG,EAAEqB,SAAS,EAAE,CAAC,GAC9G,CAAC,uEAAuE,CAAC;IAE/E;IAEA,0EAA0E;IAC1E,kDAAkD;IAClD,IAAI,AAACiF,qBAAqB,CAACa,SAAU/B,SAASsH,UAAU,EAAE;QACxD,OAAO,IAAI9N,aAAamO,KAAKC,SAAS,CAAC/J,QAAQ;YAC7CmC;QACF;IACF;IAEA,sEAAsE;IACtE,gEAAgE;IAChE,IAAI5D,YAAY;QACdyB,MAAMgK,SAAS,GAAG,CAAC;IACrB;IAEA,6DAA6D;IAC7D,IAAI7O,UAAU4G,QAAQ,CAACmC,OAAO,OAAO,IAAIvI,aAAa,MAAM;QAAEwG;IAAS;IAEvE,6DAA6D;IAC7D,qCAAqC;IACrC,IAAI2I,wBAAwB/H;IAC5B,IAAI8B,gBAAgBF,eAAe;QACjC,MAAMoG,OAAOxP,oBAAoBD,kBAAkB8C;QACnD,0EAA0E;QAC1E,sEAAsE;QACtE,UAAU;QACV,IAAI2M,QAAQD,sBAAsBE,KAAK,EAAE;YACvCF,wBAAwB;gBACtB,GAAGA,qBAAqB;gBACxBE,OAAO;oBACL,GAAGF,sBAAsBE,KAAK;oBAC9B,CAACD,KAAK,EAAE;2BACHD,sBAAsBE,KAAK,CAACD,KAAK;2BACjCD,sBAAsBG,gBAAgB,CAACxE,MAAM,CAAC,CAACyE,IAChDA,EAAE3I,QAAQ,CAAC;qBAEd;gBACH;gBACA0I,kBAAkBH,sBAAsBG,gBAAgB,CAACxE,MAAM,CAC7D,CAACyE,IAAM,CAACA,EAAE3I,QAAQ,CAAC;YAEvB;QACF;IACF;IAEA,MAAM4I,OAAO,CAAC,EAAErE,QAAQ,EAA6B;QACnD,OAAOX,YAAYW,yBAAW,KAACsE;YAAIC,IAAG;sBAAUvE;;IAClD;IAEA,MAAMwE,iBAAiB;QACrB,6DAA6D;QAC7D,2DAA2D;QAC3D,oEAAoE;QAEpE,MAAMC,4BAAsD,AAC1D1H,QACD,CAACxJ,sBAAsB;QAExB,IAAI6C,QAAQC,GAAG,CAACC,YAAY,KAAK,UAAUyG,SAASS,eAAe,EAAE;YACnE,mEAAmE;YACnE,6CAA6C;YAC7C,IAAIiH,2BAA2B;gBAC7B1H,WAAW0H;YACb,OAAO;gBACL,MAAM,IAAI3N,MACR;YAEJ;QACF;QAEA,eAAe4N,yBACbC,WAGiC;YAEjC,MAAMxD,aAAyB,OAC7BvI,UAA8B,CAAC,CAAC;gBAEhC,IAAIiI,IAAIpG,GAAG,IAAI0B,YAAY;oBACzB,6DAA6D;oBAC7D,IAAIwI,aAAa;wBACfA,YAAY9L,KAAKC;oBACnB;oBAEA,MAAMnC,OAAO,MAAMI,6BACjB,KAACsN;kCACC,cAAA,KAAClI;4BAAWyI,OAAO/D,IAAIpG,GAAG;;;oBAG9B,OAAO;wBAAE9D;wBAAM2I;oBAAK;gBACtB;gBAEA,IAAIvE,OAAQ7B,CAAAA,MAAM0F,MAAM,IAAI1F,MAAMJ,SAAS,AAAD,GAAI;oBAC5C,MAAM,IAAIhC,MACR,CAAC,sIAAsI,CAAC;gBAE5I;gBAEA,MAAM,EAAE+B,KAAKgM,WAAW,EAAE/L,WAAWgM,iBAAiB,EAAE,GACtDnM,kBAAkBC,SAASC,KAAKC;gBAElC,MAAMiM,SAAS,MAAMJ,YAAYE,aAAaC;gBAC9C,MAAMC,OAAO5N,QAAQ;gBACrB,MAAMR,OAAO,MAAM5B,eAAegQ;gBAElC,OAAO;oBAAEpO;oBAAM2I;gBAAK;YACtB;YACA,MAAM0F,cAAc;gBAAE,GAAGnE,GAAG;gBAAEM;YAAW;YACzC,MAAM8D,WAAiC,MAAM3Q,oBAC3CyI,UACAiI;YAEF,6DAA6D;YAC7D,IAAI3Q,UAAU4G,QAAQ,CAACmC,OAAO,OAAO;YAErC,IAAI,CAAC6H,YAAY,OAAOA,SAAStO,IAAI,KAAK,UAAU;gBAClD,MAAME,UAAU,CAAC,CAAC,EAAEzC,eAClB2I,UACA,+FAA+F,CAAC;gBAClG,MAAM,IAAIjG,MAAMD;YAClB;YAEA,OAAO;gBAAEoO;gBAAUD;YAAY;QACjC;QAEA,MAAME,gBAAgB,CAACC,MAAeC;YACpC,MAAMP,cAAcM,QAAQtM;YAC5B,MAAMiM,oBAAoBM,cAActM;YAExC,OAAO+H,IAAIpG,GAAG,IAAI0B,2BAChB,KAACkI;0BACC,cAAA,KAAClI;oBAAWyI,OAAO/D,IAAIpG,GAAG;;+BAG5B,KAAC4J;0BACC,cAAA,KAACzD;8BACE3H,eAAe4L,aAAaC,mBAAmB;wBAC9C,GAAG5L,KAAK;wBACR0F;oBACF;;;QAIR;QAEA,gFAAgF;QAChF,MAAM+F,cAAc,OAClBE,aACAC;YAEA,MAAMO,UAAUH,cAAcL,aAAaC;YAC3C,OAAO,MAAM9P,0BAA0B;gBACrCsQ,gBAAgB1S;gBAChBoE,SAASqO;YACX;QACF;QAEA,MAAME,6BACJnP,QAAQC,GAAG,CAACC,YAAY,KAAK,UAAU,CAAC,CAACyG,SAASS,eAAe;QAEnE,uEAAuE;QACvE,gCAAgC;QAChC,IAAIgI;QAIJ,MAAM,CAACC,0BAA0BJ,QAAQ,GAAG,MAAMxB,QAAQ6B,GAAG,CAAC;YAC5D3O,eAAewK;YACd,CAAA;gBACC,IAAIgE,4BAA4B;oBAC9BC,0BAA0B,MAAMd,yBAAyBC;oBACzD,IAAIa,4BAA4B,MAAM,OAAO;oBAC7C,MAAM,EAAEP,QAAQ,EAAE,GAAGO;oBACrB,OAAOP,SAAStO,IAAI;gBACtB,OAAO;oBACL6O,0BAA0B,CAAC;oBAC3B,MAAMT,SAAS,MAAMJ,YAAY9L,KAAKC;oBACtC,MAAMiM,OAAO5N,QAAQ;oBACrB,OAAOpC,eAAegQ;gBACxB;YACF,CAAA;SACD;QAED,IAAIM,YAAY,MAAM;YACpB,OAAO;QACT;QAEA,MAAMM,cAAcF,2BAA2BJ;QAE/C,6CAA6C;QAC7C,MAAM,EAAEJ,QAAQ,EAAE,GAAG,AAACO,2BAAmC,CAAC;QAC1D,MAAMI,kBAAkB,CAACC;YACvB,IAAIzP,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;gBACvC,OAAO,AAACyG;YACV,OAAO;gBACL,qBAAO,KAACA;oBAAU,GAAG8I,SAAS;oBAAG,GAAGZ,QAAQ;;YAC9C;QACF;QAEA,IAAI7D;QACJ,IAAImE,4BAA4B;YAC9BnE,SAAS6D,SAAS7D,MAAM;YACxB9B,OAAO2F,SAAS3F,IAAI;QACtB,OAAO;YACL8B,SAASrC,iBAAiBqC,MAAM;YAChCrC,iBAAiBuC,KAAK;QACxB;QAEA,OAAO;YACLqE;YACAC;YACAtG;YACAwG,UAAU,EAAE;YACZ1E;QACF;IACF;IAEAzL,YAAYoQ,oBAAoB,CAAC,cAAc7K,WAAW+I,IAAI;IAC9D,MAAM+B,iBAAiB,MAAMrQ,YAAY+L,KAAK,CAC5C9L,WAAW4O,cAAc,EACzB;QACE7C,UAAU,CAAC,qBAAqB,EAAEzG,WAAW+I,IAAI,EAAE;QACnDrC,YAAY;YACV,cAAc1G,WAAW+I,IAAI;QAC/B;IACF,GACA,UAAYO;IAEd,IAAI,CAACwB,gBAAgB;QACnB,OAAO,IAAInR,aAAa,MAAM;YAAEwG;QAAS;IAC3C;IAEA,MAAM4K,oBAAoB,IAAIzF;IAC9B,MAAM0F,iBAAiB,IAAI1F;IAE3B,KAAK,MAAM2F,OAAO5G,qBAAsB;QACtC,MAAM6G,eAAelK,qBAAqB,CAACiK,IAAI;QAE/C,IAAIC,cAAc;YAChBH,kBAAkBI,GAAG,CAACD,aAAa7B,EAAE;YACrC6B,aAAaE,KAAK,CAACC,OAAO,CAAC,CAACC;gBAC1BN,eAAeG,GAAG,CAACG;YACrB;QACF;IACF;IAEA,MAAMC,YAAYzH,SAASI,MAAM;IACjC,MAAMsH,wBAAgE,CAAC;IAEvE,MAAM,EACJC,WAAW,EACXC,OAAO,EACPC,YAAY,EACZ/O,aAAa,EACbgP,uBAAuB,EACvB/O,aAAa,EACbH,MAAM,EACNC,OAAO,EACPkP,aAAa,EACd,GAAG7L;IACJ,MAAM2K,YAAuB;QAC3BmB,eAAe;YACb9N;YACA+K,MAAM3M;YACNC;YACAqP;YACAD,aAAaA,gBAAgB,KAAKnI,YAAYmI;YAC9CI;YACAzJ,YAAYA,eAAe,OAAO,OAAOkB;YACzCyI,YAAYlJ,iBAAiB,OAAO,OAAOS;YAC3C/G;YACAoF;YACAqK,YACEjB,kBAAkBkB,IAAI,KAAK,IACvB3I,YACA4I,MAAMC,IAAI,CAACpB;YACjBxL,KAAKS,WAAWT,GAAG,GAAGK,eAAeC,KAAKG,WAAWT,GAAG,IAAI+D;YAC5D8I,KAAK,CAAC,CAAClL,iBAAiB,OAAOoC;YAC/B+I,MAAM,CAAC,CAACjL,qBAAqB,OAAOkC;YACpCqI;YACAW,KAAK9J,yBAAyB,OAAOc;YACrCiJ,QAAQ,CAAClK,4BAA4B,OAAOiB;YAC5C5G;YACAC;YACAC;YACAC;YACAC,WAAWA,cAAc,OAAO,OAAOwG;YACvCtB,iBAAiBA,mBAAmBnC,MAAMmC,kBAAkBsB;QAC9D;QACAkJ,gBAAgBxM,WAAWwM,cAAc;QACzCzL,eAAe+H;QACf0C;QACAiB,iBAAiB/I,OAAOxG,MAAM;QAC9BwP,eACE,CAAC1M,WAAWa,OAAO,IAAIrH,eAAeiF,KAAK,oBACvC,GAAGuB,WAAW0M,aAAa,IAAI,GAAG,CAAC,EAAE1M,WAAWtD,MAAM,EAAE,GACxDsD,WAAW0M,aAAa;QAC9B7L;QACAsD;QACAwI,eAAe,CAAC,CAAC9M;QACjB0L;QACAP,gBAAgBkB,MAAMC,IAAI,CAACnB;QAC3B4B,oBAAoB,IAAItH,IAAItF,WAAW4M,kBAAkB,IAAI,EAAE;QAC/DnB;QACA,2GAA2G;QAC3GoB,oBACE3R,QAAQC,GAAG,CAACgM,QAAQ,KAAK,eACrBrG,WAAW+L,kBAAkB,GAC7BvJ;QACNwJ,oBAAoBhM,WAAWgM,kBAAkB;QACjD1M;QACAwD;QACAlH;QACAkP;QACAxH,MAAM0G,eAAe1G,IAAI;QACzBwG,UAAUE,eAAeF,QAAQ;QACjC1E,QAAQ4E,eAAe5E,MAAM;QAC7B6G,aAAa/M,WAAW+M,WAAW;QACnCC,aAAahN,WAAWgN,WAAW;QACnChK,kBAAkBhD,WAAWgD,gBAAgB;QAC7CiK,mBAAmBjN,WAAWiN,iBAAiB;QAC/CxL,SAASC;QACTwL,oBAAoBlN,WAAWkN,kBAAkB;QACjDC,kBAAkBnN,WAAWmN,gBAAgB;QAC7CC,iCACEpN,WAAWqN,YAAY,CAACC,mBAAmB;IAC/C;IAEA,MAAMC,yBACJ,KAAC5U,gBAAgBoM,QAAQ;QAACC,OAAOlB;kBAC/B,cAAA,KAACzK,YAAY0L,QAAQ;YAACC,OAAO2F;sBAC1BG,eAAeJ,eAAe,CAACC;;;IAKtC,MAAM6C,eAAe,MAAM/S,YAAY+L,KAAK,CAC1C9L,WAAWmB,cAAc,EACzB,UAAYA,eAAe0R;IAG7B,IAAIrS,QAAQC,GAAG,CAACgM,QAAQ,KAAK,cAAc;QACzC,MAAMsG,wBAAwB,EAAE;QAChC,MAAMC,wBAAwB;YAAC;YAAQ;YAAQ;YAAc;SAAO;QAEpE,KAAK,MAAMC,QAAQD,sBAAuB;YACxC,IAAI,CAAC,AAAClC,qBAA6B,CAACmC,KAAK,EAAE;gBACzCF,sBAAsBtQ,IAAI,CAACwQ;YAC7B;QACF;QAEA,IAAIF,sBAAsBrO,MAAM,EAAE;YAChC,MAAMwO,uBAAuBH,sBAC1B7I,GAAG,CAAC,CAACiJ,IAAM,CAAC,CAAC,EAAEA,EAAE,GAAG,CAAC,EACrBvP,IAAI,CAAC;YACR,MAAMwP,SAASL,sBAAsBrO,MAAM,KAAK,IAAI,MAAM;YAC1D9D,QAAQP,IAAI,CACV,CAAC,mFAAmF,EAAE+S,OAAO,GAAG,CAAC,GAC/F,CAAC,iBAAiB,EAAEA,OAAO,EAAE,EAAEF,qBAAqB,EAAE,CAAC,GACvD;QAEN;IACF;IAEA,MAAM,CAACG,oBAAoBC,mBAAmB,GAAGR,aAAaS,KAAK,CACjE,+EACA;IAGF,IAAIC,SAAS;IACb,IAAI,CAACV,aAAaW,UAAU,CAAClT,UAAU;QACrCiT,UAAUjT;IACZ;IACAiT,UAAUH;IACV,IAAI5J,WAAW;QACb+J,UAAU;IACZ;IAEA,MAAM/D,UAAU+D,SAASpD,eAAeL,WAAW,GAAGuD;IAEtD,MAAMI,gBAAgB,MAAMpT,gBAAgBoB,UAAU+N,SAASnK,YAAY;QACzEmE;QACAoH;IACF;IAEA,OAAO,IAAI5R,aAAayU,eAAe;QAAEjO;IAAS;AACpD;AAUA,OAAO,MAAMkO,eAA4B,CACvC5P,KACAsB,KACA3D,UACAC,OACA2D;IAEA,OAAOF,iBAAiBrB,KAAKsB,KAAK3D,UAAUC,OAAO2D,YAAYA;AACjE,EAAC"}