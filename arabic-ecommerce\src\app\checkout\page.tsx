'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useCart } from '@/contexts/CartContext';
import { useAuth } from '@/contexts/AuthContext';
import { usePayment } from '@/contexts/PaymentContext';
import PaymentMethodSelector from '@/components/payment/PaymentMethodSelector';
import { PaymentMethod, Currency, CreatePaymentRequest } from '@/types/payment';

export default function CheckoutPage() {
  const router = useRouter();
  const { items: cartItems, totalPrice: cartTotal, clearCart } = useCart();
  const { user } = useAuth();
  const { state: paymentState, createPayment, confirmPayment, resetPaymentState } = usePayment();

  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<PaymentMethod | null>(null);
  const [customerInfo, setCustomerInfo] = useState({
    name: user?.name || '',
    email: user?.email || '',
    phone: user?.phone || '',
  });
  const [currentStep, setCurrentStep] = useState<'info' | 'payment' | 'processing'>('info');

  const currency: Currency = 'SAR';
  const subtotal = cartItems.reduce((sum, item) => sum + (item.product.price * item.quantity), 0);
  const shipping = 25;
  const total = subtotal + shipping;

  useEffect(() => {
    if (cartItems.length === 0) {
      router.push('/cart');
    }
  }, [cartItems.length, router]);

  useEffect(() => {
    resetPaymentState();
  }, [resetPaymentState]);

  const handleCustomerInfoSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (customerInfo.name && customerInfo.email) {
      setCurrentStep('payment');
    }
  };

  const handlePaymentMethodSelect = async (method: PaymentMethod) => {
    setSelectedPaymentMethod(method);

    if (method === 'cash_on_delivery') {
      await processPayment(method);
    }
  };

  const processPayment = async (method: PaymentMethod) => {
    setCurrentStep('processing');

    const orderId = `order_${Date.now()}`;
    const paymentRequest: CreatePaymentRequest = {
      orderId,
      amount: total,
      currency,
      method,
      customerInfo,
      shippingAddress: {
        street: '',
        city: '',
        state: '',
        postalCode: '',
        country: 'SA',
      },
      items: cartItems.map(item => ({
        id: item.product.id,
        name: item.product.name,
        quantity: item.quantity,
        price: item.product.price,
      })),
      metadata: {
        subtotal,
        shipping,
        total,
      },
    };

    const result = await createPayment(paymentRequest);

    if (result.success) {
      if (method === 'cash_on_delivery') {
        await confirmPayment({
          paymentId: result.paymentId!,
          method,
        });

        clearCart();
        router.push(`/checkout/success?orderId=${orderId}`);
      }
    }
  };

  if (cartItems.length === 0) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-6xl mb-4">🛒</div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">السلة فارغة</h2>
          <p className="text-gray-600 mb-4">أضف بعض المنتجات للمتابعة</p>
          <button
            onClick={() => router.push('/products')}
            className="bg-primary-600 text-white px-6 py-2 rounded-lg hover:bg-primary-700"
          >
            تصفح المنتجات
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 text-center">إتمام الطلب</h1>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <div className="lg:col-span-2">
            {currentStep === 'info' && (
              <div className="bg-white rounded-lg p-6">
                <h2 className="text-xl font-semibold mb-4">معلومات العميل</h2>
                <form onSubmit={handleCustomerInfoSubmit} className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      الاسم *
                    </label>
                    <input
                      type="text"
                      value={customerInfo.name}
                      onChange={(e) => setCustomerInfo({...customerInfo, name: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      البريد الإلكتروني *
                    </label>
                    <input
                      type="email"
                      value={customerInfo.email}
                      onChange={(e) => setCustomerInfo({...customerInfo, email: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      رقم الهاتف
                    </label>
                    <input
                      type="tel"
                      value={customerInfo.phone}
                      onChange={(e) => setCustomerInfo({...customerInfo, phone: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                    />
                  </div>
                  <button
                    type="submit"
                    className="w-full bg-primary-600 text-white py-2 px-4 rounded-md hover:bg-primary-700"
                  >
                    متابعة للدفع
                  </button>
                </form>
              </div>
            )}

            {currentStep === 'payment' && (
              <div className="space-y-6">
                <PaymentMethodSelector
                  selectedMethod={selectedPaymentMethod}
                  onMethodSelect={handlePaymentMethodSelect}
                  amount={total}
                  currency={currency}
                />
              </div>
            )}

            {currentStep === 'processing' && (
              <div className="bg-white rounded-lg p-8 text-center">
                <div className="w-16 h-16 border-4 border-primary-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
                <h3 className="text-lg font-semibold mb-2">جاري معالجة طلبك</h3>
                <p className="text-gray-600">يرجى الانتظار...</p>
              </div>
            )}
          </div>

          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg p-6">
              <h3 className="text-lg font-semibold mb-4">ملخص الطلب</h3>
              <div className="space-y-3">
                {cartItems.map((item) => (
                  <div key={item.id} className="flex justify-between">
                    <span className="text-sm">{item.product.name} × {item.quantity}</span>
                    <span className="text-sm font-medium">{item.product.price * item.quantity} ريال</span>
                  </div>
                ))}
                <div className="border-t pt-3">
                  <div className="flex justify-between">
                    <span>المجموع الفرعي:</span>
                    <span>{subtotal} ريال</span>
                  </div>
                  <div className="flex justify-between">
                    <span>الشحن:</span>
                    <span>{shipping} ريال</span>
                  </div>
                  <div className="flex justify-between font-semibold text-lg border-t pt-2 mt-2">
                    <span>المجموع:</span>
                    <span>{total} ريال</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
