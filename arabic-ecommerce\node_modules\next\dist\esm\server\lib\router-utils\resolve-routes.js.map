{"version": 3, "sources": ["../../../../src/server/lib/router-utils/resolve-routes.ts"], "sourcesContent": ["import type { FsOutput } from './filesystem'\nimport type { IncomingMessage, ServerResponse } from 'http'\nimport type { NextConfigComplete } from '../../config-shared'\nimport type { RenderServer, initialize } from '../router-server'\nimport type { PatchMatcher } from '../../../shared/lib/router/utils/path-match'\nimport type { Redirect } from '../../../types'\nimport type { Header, Rewrite } from '../../../lib/load-custom-routes'\nimport type { UnwrapPromise } from '../../../lib/coalesced-function'\nimport type { NextUrlWithParsedQuery } from '../../request-meta'\n\nimport url from 'url'\nimport path from 'node:path'\nimport setupDebug from 'next/dist/compiled/debug'\nimport { getCloneableBody } from '../../body-streams'\nimport { filterReqHeaders, ipcForbiddenHeaders } from '../server-ipc/utils'\nimport { stringifyQuery } from '../../server-route-utils'\nimport { formatHostname } from '../format-hostname'\nimport { toNodeOutgoingHttpHeaders } from '../../web/utils'\nimport { isAbortError } from '../../pipe-readable'\nimport { getHostname } from '../../../shared/lib/get-hostname'\nimport { getRedirectStatus } from '../../../lib/redirect-status'\nimport { normalizeRepeatedSlashes } from '../../../shared/lib/utils'\nimport { relativizeURL } from '../../../shared/lib/router/utils/relativize-url'\nimport { addPathPrefix } from '../../../shared/lib/router/utils/add-path-prefix'\nimport { pathHasPrefix } from '../../../shared/lib/router/utils/path-has-prefix'\nimport { detectDomainLocale } from '../../../shared/lib/i18n/detect-domain-locale'\nimport { normalizeLocalePath } from '../../../shared/lib/i18n/normalize-locale-path'\nimport { removePathPrefix } from '../../../shared/lib/router/utils/remove-path-prefix'\nimport { NextDataPathnameNormalizer } from '../../normalizers/request/next-data'\nimport { BasePathPathnameNormalizer } from '../../normalizers/request/base-path'\n\nimport { addRequestMeta } from '../../request-meta'\nimport {\n  compileNonPath,\n  matchHas,\n  prepareDestination,\n} from '../../../shared/lib/router/utils/prepare-destination'\nimport type { TLSSocket } from 'tls'\nimport { NEXT_ROUTER_STATE_TREE_HEADER } from '../../../client/components/app-router-headers'\nimport { getSelectedParams } from '../../../client/components/router-reducer/compute-changed-path'\nimport { isInterceptionRouteRewrite } from '../../../lib/generate-interception-routes-rewrites'\nimport { parseAndValidateFlightRouterState } from '../../app-render/parse-and-validate-flight-router-state'\n\nconst debug = setupDebug('next:router-server:resolve-routes')\n\nexport function getResolveRoutes(\n  fsChecker: UnwrapPromise<\n    ReturnType<typeof import('./filesystem').setupFsCheck>\n  >,\n  config: NextConfigComplete,\n  opts: Parameters<typeof initialize>[0],\n  renderServer: RenderServer,\n  renderServerOpts: Parameters<RenderServer['initialize']>[0],\n  ensureMiddleware?: (url?: string) => Promise<void>\n) {\n  type Route = {\n    /**\n     * The path matcher to check if this route applies to this request.\n     */\n    match: PatchMatcher\n    check?: boolean\n    name?: string\n  } & Partial<Header> &\n    Partial<Redirect>\n\n  const routes: Route[] = [\n    // _next/data with middleware handling\n    { match: () => ({}), name: 'middleware_next_data' },\n\n    ...(opts.minimalMode ? [] : fsChecker.headers),\n    ...(opts.minimalMode ? [] : fsChecker.redirects),\n\n    // check middleware (using matchers)\n    { match: () => ({}), name: 'middleware' },\n\n    ...(opts.minimalMode ? [] : fsChecker.rewrites.beforeFiles),\n\n    // check middleware (using matchers)\n    { match: () => ({}), name: 'before_files_end' },\n\n    // we check exact matches on fs before continuing to\n    // after files rewrites\n    { match: () => ({}), name: 'check_fs' },\n\n    ...(opts.minimalMode ? [] : fsChecker.rewrites.afterFiles),\n\n    // we always do the check: true handling before continuing to\n    // fallback rewrites\n    {\n      check: true,\n      match: () => ({}),\n      name: 'after files check: true',\n    },\n\n    ...(opts.minimalMode ? [] : fsChecker.rewrites.fallback),\n  ]\n\n  async function resolveRoutes({\n    req,\n    res,\n    isUpgradeReq,\n    invokedOutputs,\n  }: {\n    req: IncomingMessage\n    res: ServerResponse\n    isUpgradeReq: boolean\n    signal: AbortSignal\n    invokedOutputs?: Set<string>\n  }): Promise<{\n    finished: boolean\n    statusCode?: number\n    bodyStream?: ReadableStream | null\n    resHeaders: Record<string, string | string[]>\n    parsedUrl: NextUrlWithParsedQuery\n    matchedOutput?: FsOutput | null\n  }> {\n    let finished = false\n    let resHeaders: Record<string, string | string[]> = {}\n    let matchedOutput: FsOutput | null = null\n    let parsedUrl = url.parse(req.url || '', true) as NextUrlWithParsedQuery\n    let didRewrite = false\n\n    const urlParts = (req.url || '').split('?', 1)\n    const urlNoQuery = urlParts[0]\n\n    // this normalizes repeated slashes in the path e.g. hello//world ->\n    // hello/world or backslashes to forward slashes, this does not\n    // handle trailing slash as that is handled the same as a next.config.js\n    // redirect\n    if (urlNoQuery?.match(/(\\\\|\\/\\/)/)) {\n      parsedUrl = url.parse(normalizeRepeatedSlashes(req.url!), true)\n      return {\n        parsedUrl,\n        resHeaders,\n        finished: true,\n        statusCode: 308,\n      }\n    }\n    // TODO: inherit this from higher up\n    const protocol =\n      (req?.socket as TLSSocket)?.encrypted ||\n      req.headers['x-forwarded-proto']?.includes('https')\n        ? 'https'\n        : 'http'\n\n    // When there are hostname and port we build an absolute URL\n    const initUrl = (config.experimental as any).trustHostHeader\n      ? `https://${req.headers.host || 'localhost'}${req.url}`\n      : opts.port\n        ? `${protocol}://${formatHostname(opts.hostname || 'localhost')}:${\n            opts.port\n          }${req.url}`\n        : req.url || ''\n\n    addRequestMeta(req, 'initURL', initUrl)\n    addRequestMeta(req, 'initQuery', { ...parsedUrl.query })\n    addRequestMeta(req, 'initProtocol', protocol)\n\n    if (!isUpgradeReq) {\n      addRequestMeta(req, 'clonableBody', getCloneableBody(req))\n    }\n\n    const maybeAddTrailingSlash = (pathname: string) => {\n      if (\n        config.trailingSlash &&\n        !config.skipMiddlewareUrlNormalize &&\n        !pathname.endsWith('/')\n      ) {\n        return `${pathname}/`\n      }\n      return pathname\n    }\n\n    let domainLocale: ReturnType<typeof detectDomainLocale> | undefined\n    let defaultLocale: string | undefined\n    let initialLocaleResult:\n      | ReturnType<typeof normalizeLocalePath>\n      | undefined = undefined\n\n    if (config.i18n) {\n      const hadTrailingSlash = parsedUrl.pathname?.endsWith('/')\n      const hadBasePath = pathHasPrefix(\n        parsedUrl.pathname || '',\n        config.basePath\n      )\n      initialLocaleResult = normalizeLocalePath(\n        removePathPrefix(parsedUrl.pathname || '/', config.basePath),\n        config.i18n.locales\n      )\n\n      domainLocale = detectDomainLocale(\n        config.i18n.domains,\n        getHostname(parsedUrl, req.headers)\n      )\n      defaultLocale = domainLocale?.defaultLocale || config.i18n.defaultLocale\n\n      parsedUrl.query.__nextDefaultLocale = defaultLocale\n      parsedUrl.query.__nextLocale =\n        initialLocaleResult.detectedLocale || defaultLocale\n\n      // ensure locale is present for resolving routes\n      if (\n        !initialLocaleResult.detectedLocale &&\n        !initialLocaleResult.pathname.startsWith('/_next/')\n      ) {\n        parsedUrl.pathname = addPathPrefix(\n          initialLocaleResult.pathname === '/'\n            ? `/${defaultLocale}`\n            : addPathPrefix(\n                initialLocaleResult.pathname || '',\n                `/${defaultLocale}`\n              ),\n          hadBasePath ? config.basePath : ''\n        )\n\n        if (hadTrailingSlash) {\n          parsedUrl.pathname = maybeAddTrailingSlash(parsedUrl.pathname)\n        }\n      }\n    } else {\n      // As i18n isn't configured we remove the locale related query params.\n      delete parsedUrl.query.__nextLocale\n      delete parsedUrl.query.__nextDefaultLocale\n      delete parsedUrl.query.__nextInferredLocaleFromDefault\n    }\n\n    const checkLocaleApi = (pathname: string) => {\n      if (\n        config.i18n &&\n        pathname === urlNoQuery &&\n        initialLocaleResult?.detectedLocale &&\n        pathHasPrefix(initialLocaleResult.pathname, '/api')\n      ) {\n        return true\n      }\n    }\n\n    async function checkTrue() {\n      const pathname = parsedUrl.pathname || ''\n\n      if (checkLocaleApi(pathname)) {\n        return\n      }\n      if (!invokedOutputs?.has(pathname)) {\n        const output = await fsChecker.getItem(pathname)\n\n        if (output) {\n          if (\n            config.useFileSystemPublicRoutes ||\n            didRewrite ||\n            (output.type !== 'appFile' && output.type !== 'pageFile')\n          ) {\n            return output\n          }\n        }\n      }\n      const dynamicRoutes = fsChecker.getDynamicRoutes()\n      let curPathname = parsedUrl.pathname\n\n      if (config.basePath) {\n        if (!pathHasPrefix(curPathname || '', config.basePath)) {\n          return\n        }\n        curPathname = curPathname?.substring(config.basePath.length) || '/'\n      }\n      const localeResult = fsChecker.handleLocale(curPathname || '')\n\n      for (const route of dynamicRoutes) {\n        // when resolving fallback: false the\n        // render worker may return a no-fallback response\n        // which signals we need to continue resolving.\n        // TODO: optimize this to collect static paths\n        // to use at the routing layer\n        if (invokedOutputs?.has(route.page)) {\n          continue\n        }\n        const params = route.match(localeResult.pathname)\n\n        if (params) {\n          const pageOutput = await fsChecker.getItem(\n            addPathPrefix(route.page, config.basePath || '')\n          )\n\n          // i18n locales aren't matched for app dir\n          if (\n            pageOutput?.type === 'appFile' &&\n            initialLocaleResult?.detectedLocale\n          ) {\n            continue\n          }\n\n          if (pageOutput && curPathname?.startsWith('/_next/data')) {\n            parsedUrl.query.__nextDataReq = '1'\n          }\n\n          if (config.useFileSystemPublicRoutes || didRewrite) {\n            return pageOutput\n          }\n        }\n      }\n    }\n\n    const normalizers = {\n      basePath:\n        config.basePath && config.basePath !== '/'\n          ? new BasePathPathnameNormalizer(config.basePath)\n          : undefined,\n      data: new NextDataPathnameNormalizer(fsChecker.buildId),\n    }\n\n    async function handleRoute(\n      route: (typeof routes)[0]\n    ): Promise<UnwrapPromise<ReturnType<typeof resolveRoutes>> | void> {\n      let curPathname = parsedUrl.pathname || '/'\n\n      if (config.i18n && route.internal) {\n        const hadTrailingSlash = curPathname.endsWith('/')\n\n        if (config.basePath) {\n          curPathname = removePathPrefix(curPathname, config.basePath)\n        }\n        const hadBasePath = curPathname !== parsedUrl.pathname\n\n        const localeResult = normalizeLocalePath(\n          curPathname,\n          config.i18n.locales\n        )\n        const isDefaultLocale = localeResult.detectedLocale === defaultLocale\n\n        if (isDefaultLocale) {\n          curPathname =\n            localeResult.pathname === '/' && hadBasePath\n              ? config.basePath\n              : addPathPrefix(\n                  localeResult.pathname,\n                  hadBasePath ? config.basePath : ''\n                )\n        } else if (hadBasePath) {\n          curPathname =\n            curPathname === '/'\n              ? config.basePath\n              : addPathPrefix(curPathname, config.basePath)\n        }\n\n        if ((isDefaultLocale || hadBasePath) && hadTrailingSlash) {\n          curPathname = maybeAddTrailingSlash(curPathname)\n        }\n      }\n      let params = route.match(curPathname)\n\n      if ((route.has || route.missing) && params) {\n        const hasParams = matchHas(\n          req,\n          parsedUrl.query,\n          route.has,\n          route.missing\n        )\n        if (hasParams) {\n          Object.assign(params, hasParams)\n        } else {\n          params = false\n        }\n      }\n\n      if (params) {\n        if (\n          fsChecker.exportPathMapRoutes &&\n          route.name === 'before_files_end'\n        ) {\n          for (const exportPathMapRoute of fsChecker.exportPathMapRoutes) {\n            const result = await handleRoute(exportPathMapRoute)\n\n            if (result) {\n              return result\n            }\n          }\n        }\n\n        if (route.name === 'middleware_next_data' && parsedUrl.pathname) {\n          if (fsChecker.getMiddlewareMatchers()?.length) {\n            let normalized = parsedUrl.pathname\n\n            // Remove the base path if it exists.\n            const hadBasePath = normalizers.basePath?.match(parsedUrl.pathname)\n            if (hadBasePath && normalizers.basePath) {\n              normalized = normalizers.basePath.normalize(normalized, true)\n            }\n\n            let updated = false\n            if (normalizers.data.match(normalized)) {\n              updated = true\n              parsedUrl.query.__nextDataReq = '1'\n              normalized = normalizers.data.normalize(normalized, true)\n            }\n\n            if (config.i18n) {\n              const curLocaleResult = normalizeLocalePath(\n                normalized,\n                config.i18n.locales\n              )\n\n              if (curLocaleResult.detectedLocale) {\n                parsedUrl.query.__nextLocale = curLocaleResult.detectedLocale\n              }\n            }\n\n            // If we updated the pathname, and it had a base path, re-add the\n            // base path.\n            if (updated) {\n              if (hadBasePath) {\n                normalized = path.posix.join(config.basePath, normalized)\n              }\n\n              // Re-add the trailing slash (if required).\n              normalized = maybeAddTrailingSlash(normalized)\n\n              parsedUrl.pathname = normalized\n            }\n          }\n        }\n\n        if (route.name === 'check_fs') {\n          const pathname = parsedUrl.pathname || ''\n\n          if (invokedOutputs?.has(pathname) || checkLocaleApi(pathname)) {\n            return\n          }\n          const output = await fsChecker.getItem(pathname)\n\n          if (\n            output &&\n            !(\n              config.i18n &&\n              initialLocaleResult?.detectedLocale &&\n              pathHasPrefix(pathname, '/api')\n            )\n          ) {\n            if (\n              config.useFileSystemPublicRoutes ||\n              didRewrite ||\n              (output.type !== 'appFile' && output.type !== 'pageFile')\n            ) {\n              matchedOutput = output\n\n              if (output.locale) {\n                parsedUrl.query.__nextLocale = output.locale\n              }\n              return {\n                parsedUrl,\n                resHeaders,\n                finished: true,\n                matchedOutput,\n              }\n            }\n          }\n        }\n\n        if (!opts.minimalMode && route.name === 'middleware') {\n          const match = fsChecker.getMiddlewareMatchers()\n          if (\n            // @ts-expect-error BaseNextRequest stuff\n            match?.(parsedUrl.pathname, req, parsedUrl.query)\n          ) {\n            if (ensureMiddleware) {\n              await ensureMiddleware(req.url)\n            }\n\n            const serverResult =\n              await renderServer?.initialize(renderServerOpts)\n\n            if (!serverResult) {\n              throw new Error(`Failed to initialize render server \"middleware\"`)\n            }\n\n            addRequestMeta(req, 'invokePath', '')\n            addRequestMeta(req, 'invokeOutput', '')\n            addRequestMeta(req, 'invokeQuery', {})\n            addRequestMeta(req, 'middlewareInvoke', true)\n            debug('invoking middleware', req.url, req.headers)\n\n            let middlewareRes: Response | undefined = undefined\n            let bodyStream: ReadableStream | undefined = undefined\n            try {\n              try {\n                await serverResult.requestHandler(req, res, parsedUrl)\n              } catch (err: any) {\n                if (!('result' in err) || !('response' in err.result)) {\n                  throw err\n                }\n                middlewareRes = err.result.response as Response\n                res.statusCode = middlewareRes.status\n\n                if (middlewareRes.body) {\n                  bodyStream = middlewareRes.body\n                } else if (middlewareRes.status) {\n                  bodyStream = new ReadableStream({\n                    start(controller) {\n                      controller.enqueue('')\n                      controller.close()\n                    },\n                  })\n                }\n              }\n            } catch (e) {\n              // If the client aborts before we can receive a response object\n              // (when the headers are flushed), then we can early exit without\n              // further processing.\n              if (isAbortError(e)) {\n                return {\n                  parsedUrl,\n                  resHeaders,\n                  finished: true,\n                }\n              }\n              throw e\n            }\n\n            if (res.closed || res.finished || !middlewareRes) {\n              return {\n                parsedUrl,\n                resHeaders,\n                finished: true,\n              }\n            }\n\n            const middlewareHeaders = toNodeOutgoingHttpHeaders(\n              middlewareRes.headers\n            ) as Record<string, string | string[] | undefined>\n\n            debug('middleware res', middlewareRes.status, middlewareHeaders)\n\n            if (middlewareHeaders['x-middleware-override-headers']) {\n              const overriddenHeaders: Set<string> = new Set()\n              let overrideHeaders: string | string[] =\n                middlewareHeaders['x-middleware-override-headers']\n\n              if (typeof overrideHeaders === 'string') {\n                overrideHeaders = overrideHeaders.split(',')\n              }\n\n              for (const key of overrideHeaders) {\n                overriddenHeaders.add(key.trim())\n              }\n              delete middlewareHeaders['x-middleware-override-headers']\n\n              // Delete headers.\n              for (const key of Object.keys(req.headers)) {\n                if (!overriddenHeaders.has(key)) {\n                  delete req.headers[key]\n                }\n              }\n\n              // Update or add headers.\n              for (const key of overriddenHeaders.keys()) {\n                const valueKey = 'x-middleware-request-' + key\n                const newValue = middlewareHeaders[valueKey]\n                const oldValue = req.headers[key]\n\n                if (oldValue !== newValue) {\n                  req.headers[key] = newValue === null ? undefined : newValue\n                }\n                delete middlewareHeaders[valueKey]\n              }\n            }\n\n            if (\n              !middlewareHeaders['x-middleware-rewrite'] &&\n              !middlewareHeaders['x-middleware-next'] &&\n              !middlewareHeaders['location']\n            ) {\n              middlewareHeaders['x-middleware-refresh'] = '1'\n            }\n            delete middlewareHeaders['x-middleware-next']\n\n            for (const [key, value] of Object.entries({\n              ...filterReqHeaders(middlewareHeaders, ipcForbiddenHeaders),\n            })) {\n              if (\n                [\n                  'content-length',\n                  'x-middleware-rewrite',\n                  'x-middleware-redirect',\n                  'x-middleware-refresh',\n                ].includes(key)\n              ) {\n                continue\n              }\n\n              // for set-cookie, the header shouldn't be added to the response\n              // as it's only needed for the request to the middleware function.\n              if (key === 'x-middleware-set-cookie') {\n                req.headers[key] = value\n                continue\n              }\n\n              if (value) {\n                resHeaders[key] = value\n                req.headers[key] = value\n              }\n            }\n\n            if (middlewareHeaders['x-middleware-rewrite']) {\n              const value = middlewareHeaders['x-middleware-rewrite'] as string\n              const rel = relativizeURL(value, initUrl)\n              resHeaders['x-middleware-rewrite'] = rel\n\n              const query = parsedUrl.query\n              parsedUrl = url.parse(rel, true)\n\n              if (parsedUrl.protocol) {\n                return {\n                  parsedUrl,\n                  resHeaders,\n                  finished: true,\n                }\n              }\n\n              // keep internal query state\n              for (const key of Object.keys(query)) {\n                if (key.startsWith('_next') || key.startsWith('__next')) {\n                  parsedUrl.query[key] = query[key]\n                }\n              }\n\n              if (config.i18n) {\n                const curLocaleResult = normalizeLocalePath(\n                  parsedUrl.pathname || '',\n                  config.i18n.locales\n                )\n\n                if (curLocaleResult.detectedLocale) {\n                  parsedUrl.query.__nextLocale = curLocaleResult.detectedLocale\n                }\n              }\n            }\n\n            if (middlewareHeaders['location']) {\n              const value = middlewareHeaders['location'] as string\n              const rel = relativizeURL(value, initUrl)\n              resHeaders['location'] = rel\n              parsedUrl = url.parse(rel, true)\n\n              return {\n                parsedUrl,\n                resHeaders,\n                finished: true,\n                statusCode: middlewareRes.status,\n              }\n            }\n\n            if (middlewareHeaders['x-middleware-refresh']) {\n              return {\n                parsedUrl,\n                resHeaders,\n                finished: true,\n                bodyStream,\n                statusCode: middlewareRes.status,\n              }\n            }\n          }\n        }\n\n        // handle redirect\n        if (\n          ('statusCode' in route || 'permanent' in route) &&\n          route.destination\n        ) {\n          const { parsedDestination } = prepareDestination({\n            appendParamsToQuery: false,\n            destination: route.destination,\n            params: params,\n            query: parsedUrl.query,\n          })\n\n          const { query } = parsedDestination\n          delete (parsedDestination as any).query\n\n          parsedDestination.search = stringifyQuery(req as any, query)\n\n          parsedDestination.pathname = normalizeRepeatedSlashes(\n            parsedDestination.pathname\n          )\n\n          return {\n            finished: true,\n            // @ts-expect-error custom ParsedUrl\n            parsedUrl: parsedDestination,\n            statusCode: getRedirectStatus(route),\n          }\n        }\n\n        // handle headers\n        if (route.headers) {\n          const hasParams = Object.keys(params).length > 0\n          for (const header of route.headers) {\n            let { key, value } = header\n            if (hasParams) {\n              key = compileNonPath(key, params)\n              value = compileNonPath(value, params)\n            }\n\n            if (key.toLowerCase() === 'set-cookie') {\n              if (!Array.isArray(resHeaders[key])) {\n                const val = resHeaders[key]\n                resHeaders[key] = typeof val === 'string' ? [val] : []\n              }\n              ;(resHeaders[key] as string[]).push(value)\n            } else {\n              resHeaders[key] = value\n            }\n          }\n        }\n\n        // handle rewrite\n        if (route.destination) {\n          let rewriteParams = params\n\n          try {\n            // An interception rewrite might reference a dynamic param for a route the user\n            // is currently on, which wouldn't be extractable from the matched route params.\n            // This attempts to extract the dynamic params from the provided router state.\n            if (isInterceptionRouteRewrite(route as Rewrite)) {\n              const stateHeader =\n                req.headers[NEXT_ROUTER_STATE_TREE_HEADER.toLowerCase()]\n\n              if (stateHeader) {\n                rewriteParams = {\n                  ...getSelectedParams(\n                    parseAndValidateFlightRouterState(stateHeader)\n                  ),\n                  ...params,\n                }\n              }\n            }\n          } catch (err) {\n            // this is a no-op -- we couldn't extract dynamic params from the provided router state,\n            // so we'll just use the params from the route matcher\n          }\n\n          const { parsedDestination } = prepareDestination({\n            appendParamsToQuery: true,\n            destination: route.destination,\n            params: rewriteParams,\n            query: parsedUrl.query,\n          })\n\n          if (parsedDestination.protocol) {\n            return {\n              // @ts-expect-error custom ParsedUrl\n              parsedUrl: parsedDestination,\n              finished: true,\n            }\n          }\n\n          if (config.i18n) {\n            const curLocaleResult = normalizeLocalePath(\n              removePathPrefix(parsedDestination.pathname, config.basePath),\n              config.i18n.locales\n            )\n\n            if (curLocaleResult.detectedLocale) {\n              parsedUrl.query.__nextLocale = curLocaleResult.detectedLocale\n            }\n          }\n          didRewrite = true\n          parsedUrl.pathname = parsedDestination.pathname\n          Object.assign(parsedUrl.query, parsedDestination.query)\n        }\n\n        // handle check: true\n        if (route.check) {\n          const output = await checkTrue()\n\n          if (output) {\n            return {\n              parsedUrl,\n              resHeaders,\n              finished: true,\n              matchedOutput: output,\n            }\n          }\n        }\n      }\n    }\n\n    for (const route of routes) {\n      const result = await handleRoute(route)\n      if (result) {\n        return result\n      }\n    }\n\n    return {\n      finished,\n      parsedUrl,\n      resHeaders,\n      matchedOutput,\n    }\n  }\n\n  return resolveRoutes\n}\n"], "names": ["url", "path", "setupDebug", "getCloneableBody", "filterReqHeaders", "ipcForbiddenHeaders", "stringifyQuery", "formatHostname", "toNodeOutgoingHttpHeaders", "isAbortError", "getHostname", "getRedirectStatus", "normalizeRepeatedSlashes", "relativizeURL", "addPathPrefix", "pathHasPrefix", "detectDomainLocale", "normalizeLocalePath", "removePathPrefix", "NextDataPathnameNormalizer", "BasePathPathnameNormalizer", "addRequestMeta", "compileNonPath", "matchHas", "prepareDestination", "NEXT_ROUTER_STATE_TREE_HEADER", "getSelectedParams", "isInterceptionRouteRewrite", "parseAndValidateFlightRouterState", "debug", "getResolveRoutes", "fs<PERSON><PERSON><PERSON>", "config", "opts", "renderServer", "renderServerOpts", "ensureMiddleware", "routes", "match", "name", "minimalMode", "headers", "redirects", "rewrites", "beforeFiles", "afterFiles", "check", "fallback", "resolveRoutes", "req", "res", "isUpgradeReq", "invokedOutputs", "finished", "resHeaders", "matchedOutput", "parsedUrl", "parse", "didRewrite", "urlParts", "split", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "statusCode", "protocol", "socket", "encrypted", "includes", "initUrl", "experimental", "trustHostHeader", "host", "port", "hostname", "query", "maybeAddTrailingSlash", "pathname", "trailingSlash", "skipMiddlewareUrlNormalize", "endsWith", "domainLocale", "defaultLocale", "initialLocaleResult", "undefined", "i18n", "hadTrailingSlash", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "basePath", "locales", "domains", "__nextDefaultLocale", "__next<PERSON><PERSON><PERSON>", "detectedLocale", "startsWith", "__nextInferredLocaleFromDefault", "checkLocaleApi", "checkTrue", "has", "output", "getItem", "useFileSystemPublicRoutes", "type", "dynamicRoutes", "getDynamicRoutes", "curPathname", "substring", "length", "localeResult", "handleLocale", "route", "page", "params", "pageOutput", "__nextDataReq", "normalizers", "data", "buildId", "handleRoute", "internal", "isDefaultLocale", "missing", "hasParams", "Object", "assign", "exportPathMapRoutes", "exportPathMapRoute", "result", "getMiddlewareMatchers", "normalized", "normalize", "updated", "curLocaleResult", "posix", "join", "locale", "serverResult", "initialize", "Error", "middlewareRes", "bodyStream", "requestHandler", "err", "response", "status", "body", "ReadableStream", "start", "controller", "enqueue", "close", "e", "closed", "middlewareHeaders", "overriddenHeaders", "Set", "overrideHeaders", "key", "add", "trim", "keys", "valueKey", "newValue", "oldValue", "value", "entries", "rel", "destination", "parsedDestination", "appendParamsToQuery", "search", "header", "toLowerCase", "Array", "isArray", "val", "push", "rewriteParams", "<PERSON><PERSON><PERSON><PERSON>"], "mappings": "AAUA,OAAOA,SAAS,MAAK;AACrB,OAAOC,UAAU,YAAW;AAC5B,OAAOC,gBAAgB,2BAA0B;AACjD,SAASC,gBAAgB,QAAQ,qBAAoB;AACrD,SAASC,gBAAgB,EAAEC,mBAAmB,QAAQ,sBAAqB;AAC3E,SAASC,cAAc,QAAQ,2BAA0B;AACzD,SAASC,cAAc,QAAQ,qBAAoB;AACnD,SAASC,yBAAyB,QAAQ,kBAAiB;AAC3D,SAASC,YAAY,QAAQ,sBAAqB;AAClD,SAASC,WAAW,QAAQ,mCAAkC;AAC9D,SAASC,iBAAiB,QAAQ,+BAA8B;AAChE,SAASC,wBAAwB,QAAQ,4BAA2B;AACpE,SAASC,aAAa,QAAQ,kDAAiD;AAC/E,SAASC,aAAa,QAAQ,mDAAkD;AAChF,SAASC,aAAa,QAAQ,mDAAkD;AAChF,SAASC,kBAAkB,QAAQ,gDAA+C;AAClF,SAASC,mBAAmB,QAAQ,iDAAgD;AACpF,SAASC,gBAAgB,QAAQ,sDAAqD;AACtF,SAASC,0BAA0B,QAAQ,sCAAqC;AAChF,SAASC,0BAA0B,QAAQ,sCAAqC;AAEhF,SAASC,cAAc,QAAQ,qBAAoB;AACnD,SACEC,cAAc,EACdC,QAAQ,EACRC,kBAAkB,QACb,uDAAsD;AAE7D,SAASC,6BAA6B,QAAQ,gDAA+C;AAC7F,SAASC,iBAAiB,QAAQ,iEAAgE;AAClG,SAASC,0BAA0B,QAAQ,qDAAoD;AAC/F,SAASC,iCAAiC,QAAQ,0DAAyD;AAE3G,MAAMC,QAAQ3B,WAAW;AAEzB,OAAO,SAAS4B,iBACdC,SAEC,EACDC,MAA0B,EAC1BC,IAAsC,EACtCC,YAA0B,EAC1BC,gBAA2D,EAC3DC,gBAAkD;IAYlD,MAAMC,SAAkB;QACtB,sCAAsC;QACtC;YAAEC,OAAO,IAAO,CAAA,CAAC,CAAA;YAAIC,MAAM;QAAuB;WAE9CN,KAAKO,WAAW,GAAG,EAAE,GAAGT,UAAUU,OAAO;WACzCR,KAAKO,WAAW,GAAG,EAAE,GAAGT,UAAUW,SAAS;QAE/C,oCAAoC;QACpC;YAAEJ,OAAO,IAAO,CAAA,CAAC,CAAA;YAAIC,MAAM;QAAa;WAEpCN,KAAKO,WAAW,GAAG,EAAE,GAAGT,UAAUY,QAAQ,CAACC,WAAW;QAE1D,oCAAoC;QACpC;YAAEN,OAAO,IAAO,CAAA,CAAC,CAAA;YAAIC,MAAM;QAAmB;QAE9C,oDAAoD;QACpD,uBAAuB;QACvB;YAAED,OAAO,IAAO,CAAA,CAAC,CAAA;YAAIC,MAAM;QAAW;WAElCN,KAAKO,WAAW,GAAG,EAAE,GAAGT,UAAUY,QAAQ,CAACE,UAAU;QAEzD,6DAA6D;QAC7D,oBAAoB;QACpB;YACEC,OAAO;YACPR,OAAO,IAAO,CAAA,CAAC,CAAA;YACfC,MAAM;QACR;WAEIN,KAAKO,WAAW,GAAG,EAAE,GAAGT,UAAUY,QAAQ,CAACI,QAAQ;KACxD;IAED,eAAeC,cAAc,EAC3BC,GAAG,EACHC,GAAG,EACHC,YAAY,EACZC,cAAc,EAOf;YAgCIH,aACDA;QAzBF,IAAII,WAAW;QACf,IAAIC,aAAgD,CAAC;QACrD,IAAIC,gBAAiC;QACrC,IAAIC,YAAYxD,IAAIyD,KAAK,CAACR,IAAIjD,GAAG,IAAI,IAAI;QACzC,IAAI0D,aAAa;QAEjB,MAAMC,WAAW,AAACV,CAAAA,IAAIjD,GAAG,IAAI,EAAC,EAAG4D,KAAK,CAAC,KAAK;QAC5C,MAAMC,aAAaF,QAAQ,CAAC,EAAE;QAE9B,oEAAoE;QACpE,+DAA+D;QAC/D,wEAAwE;QACxE,WAAW;QACX,IAAIE,8BAAAA,WAAYvB,KAAK,CAAC,cAAc;YAClCkB,YAAYxD,IAAIyD,KAAK,CAAC7C,yBAAyBqC,IAAIjD,GAAG,GAAI;YAC1D,OAAO;gBACLwD;gBACAF;gBACAD,UAAU;gBACVS,YAAY;YACd;QACF;QACA,oCAAoC;QACpC,MAAMC,WACJ,CAACd,wBAAAA,cAAAA,IAAKe,MAAM,qBAAZ,AAACf,YAA2BgB,SAAS,OACrChB,+BAAAA,IAAIR,OAAO,CAAC,oBAAoB,qBAAhCQ,6BAAkCiB,QAAQ,CAAC,YACvC,UACA;QAEN,4DAA4D;QAC5D,MAAMC,UAAU,AAACnC,OAAOoC,YAAY,CAASC,eAAe,GACxD,CAAC,QAAQ,EAAEpB,IAAIR,OAAO,CAAC6B,IAAI,IAAI,cAAcrB,IAAIjD,GAAG,EAAE,GACtDiC,KAAKsC,IAAI,GACP,GAAGR,SAAS,GAAG,EAAExD,eAAe0B,KAAKuC,QAAQ,IAAI,aAAa,CAAC,EAC7DvC,KAAKsC,IAAI,GACRtB,IAAIjD,GAAG,EAAE,GACZiD,IAAIjD,GAAG,IAAI;QAEjBqB,eAAe4B,KAAK,WAAWkB;QAC/B9C,eAAe4B,KAAK,aAAa;YAAE,GAAGO,UAAUiB,KAAK;QAAC;QACtDpD,eAAe4B,KAAK,gBAAgBc;QAEpC,IAAI,CAACZ,cAAc;YACjB9B,eAAe4B,KAAK,gBAAgB9C,iBAAiB8C;QACvD;QAEA,MAAMyB,wBAAwB,CAACC;YAC7B,IACE3C,OAAO4C,aAAa,IACpB,CAAC5C,OAAO6C,0BAA0B,IAClC,CAACF,SAASG,QAAQ,CAAC,MACnB;gBACA,OAAO,GAAGH,SAAS,CAAC,CAAC;YACvB;YACA,OAAOA;QACT;QAEA,IAAII;QACJ,IAAIC;QACJ,IAAIC,sBAEYC;QAEhB,IAAIlD,OAAOmD,IAAI,EAAE;gBACU3B;YAAzB,MAAM4B,oBAAmB5B,sBAAAA,UAAUmB,QAAQ,qBAAlBnB,oBAAoBsB,QAAQ,CAAC;YACtD,MAAMO,cAActE,cAClByC,UAAUmB,QAAQ,IAAI,IACtB3C,OAAOsD,QAAQ;YAEjBL,sBAAsBhE,oBACpBC,iBAAiBsC,UAAUmB,QAAQ,IAAI,KAAK3C,OAAOsD,QAAQ,GAC3DtD,OAAOmD,IAAI,CAACI,OAAO;YAGrBR,eAAe/D,mBACbgB,OAAOmD,IAAI,CAACK,OAAO,EACnB9E,YAAY8C,WAAWP,IAAIR,OAAO;YAEpCuC,gBAAgBD,CAAAA,gCAAAA,aAAcC,aAAa,KAAIhD,OAAOmD,IAAI,CAACH,aAAa;YAExExB,UAAUiB,KAAK,CAACgB,mBAAmB,GAAGT;YACtCxB,UAAUiB,KAAK,CAACiB,YAAY,GAC1BT,oBAAoBU,cAAc,IAAIX;YAExC,gDAAgD;YAChD,IACE,CAACC,oBAAoBU,cAAc,IACnC,CAACV,oBAAoBN,QAAQ,CAACiB,UAAU,CAAC,YACzC;gBACApC,UAAUmB,QAAQ,GAAG7D,cACnBmE,oBAAoBN,QAAQ,KAAK,MAC7B,CAAC,CAAC,EAAEK,eAAe,GACnBlE,cACEmE,oBAAoBN,QAAQ,IAAI,IAChC,CAAC,CAAC,EAAEK,eAAe,GAEzBK,cAAcrD,OAAOsD,QAAQ,GAAG;gBAGlC,IAAIF,kBAAkB;oBACpB5B,UAAUmB,QAAQ,GAAGD,sBAAsBlB,UAAUmB,QAAQ;gBAC/D;YACF;QACF,OAAO;YACL,sEAAsE;YACtE,OAAOnB,UAAUiB,KAAK,CAACiB,YAAY;YACnC,OAAOlC,UAAUiB,KAAK,CAACgB,mBAAmB;YAC1C,OAAOjC,UAAUiB,KAAK,CAACoB,+BAA+B;QACxD;QAEA,MAAMC,iBAAiB,CAACnB;YACtB,IACE3C,OAAOmD,IAAI,IACXR,aAAad,eACboB,uCAAAA,oBAAqBU,cAAc,KACnC5E,cAAckE,oBAAoBN,QAAQ,EAAE,SAC5C;gBACA,OAAO;YACT;QACF;QAEA,eAAeoB;YACb,MAAMpB,WAAWnB,UAAUmB,QAAQ,IAAI;YAEvC,IAAImB,eAAenB,WAAW;gBAC5B;YACF;YACA,IAAI,EAACvB,kCAAAA,eAAgB4C,GAAG,CAACrB,YAAW;gBAClC,MAAMsB,SAAS,MAAMlE,UAAUmE,OAAO,CAACvB;gBAEvC,IAAIsB,QAAQ;oBACV,IACEjE,OAAOmE,yBAAyB,IAChCzC,cACCuC,OAAOG,IAAI,KAAK,aAAaH,OAAOG,IAAI,KAAK,YAC9C;wBACA,OAAOH;oBACT;gBACF;YACF;YACA,MAAMI,gBAAgBtE,UAAUuE,gBAAgB;YAChD,IAAIC,cAAc/C,UAAUmB,QAAQ;YAEpC,IAAI3C,OAAOsD,QAAQ,EAAE;gBACnB,IAAI,CAACvE,cAAcwF,eAAe,IAAIvE,OAAOsD,QAAQ,GAAG;oBACtD;gBACF;gBACAiB,cAAcA,CAAAA,+BAAAA,YAAaC,SAAS,CAACxE,OAAOsD,QAAQ,CAACmB,MAAM,MAAK;YAClE;YACA,MAAMC,eAAe3E,UAAU4E,YAAY,CAACJ,eAAe;YAE3D,KAAK,MAAMK,SAASP,cAAe;gBACjC,qCAAqC;gBACrC,kDAAkD;gBAClD,+CAA+C;gBAC/C,8CAA8C;gBAC9C,8BAA8B;gBAC9B,IAAIjD,kCAAAA,eAAgB4C,GAAG,CAACY,MAAMC,IAAI,GAAG;oBACnC;gBACF;gBACA,MAAMC,SAASF,MAAMtE,KAAK,CAACoE,aAAa/B,QAAQ;gBAEhD,IAAImC,QAAQ;oBACV,MAAMC,aAAa,MAAMhF,UAAUmE,OAAO,CACxCpF,cAAc8F,MAAMC,IAAI,EAAE7E,OAAOsD,QAAQ,IAAI;oBAG/C,0CAA0C;oBAC1C,IACEyB,CAAAA,8BAAAA,WAAYX,IAAI,MAAK,cACrBnB,uCAAAA,oBAAqBU,cAAc,GACnC;wBACA;oBACF;oBAEA,IAAIoB,eAAcR,+BAAAA,YAAaX,UAAU,CAAC,iBAAgB;wBACxDpC,UAAUiB,KAAK,CAACuC,aAAa,GAAG;oBAClC;oBAEA,IAAIhF,OAAOmE,yBAAyB,IAAIzC,YAAY;wBAClD,OAAOqD;oBACT;gBACF;YACF;QACF;QAEA,MAAME,cAAc;YAClB3B,UACEtD,OAAOsD,QAAQ,IAAItD,OAAOsD,QAAQ,KAAK,MACnC,IAAIlE,2BAA2BY,OAAOsD,QAAQ,IAC9CJ;YACNgC,MAAM,IAAI/F,2BAA2BY,UAAUoF,OAAO;QACxD;QAEA,eAAeC,YACbR,KAAyB;YAEzB,IAAIL,cAAc/C,UAAUmB,QAAQ,IAAI;YAExC,IAAI3C,OAAOmD,IAAI,IAAIyB,MAAMS,QAAQ,EAAE;gBACjC,MAAMjC,mBAAmBmB,YAAYzB,QAAQ,CAAC;gBAE9C,IAAI9C,OAAOsD,QAAQ,EAAE;oBACnBiB,cAAcrF,iBAAiBqF,aAAavE,OAAOsD,QAAQ;gBAC7D;gBACA,MAAMD,cAAckB,gBAAgB/C,UAAUmB,QAAQ;gBAEtD,MAAM+B,eAAezF,oBACnBsF,aACAvE,OAAOmD,IAAI,CAACI,OAAO;gBAErB,MAAM+B,kBAAkBZ,aAAaf,cAAc,KAAKX;gBAExD,IAAIsC,iBAAiB;oBACnBf,cACEG,aAAa/B,QAAQ,KAAK,OAAOU,cAC7BrD,OAAOsD,QAAQ,GACfxE,cACE4F,aAAa/B,QAAQ,EACrBU,cAAcrD,OAAOsD,QAAQ,GAAG;gBAE1C,OAAO,IAAID,aAAa;oBACtBkB,cACEA,gBAAgB,MACZvE,OAAOsD,QAAQ,GACfxE,cAAcyF,aAAavE,OAAOsD,QAAQ;gBAClD;gBAEA,IAAI,AAACgC,CAAAA,mBAAmBjC,WAAU,KAAMD,kBAAkB;oBACxDmB,cAAc7B,sBAAsB6B;gBACtC;YACF;YACA,IAAIO,SAASF,MAAMtE,KAAK,CAACiE;YAEzB,IAAI,AAACK,CAAAA,MAAMZ,GAAG,IAAIY,MAAMW,OAAO,AAAD,KAAMT,QAAQ;gBAC1C,MAAMU,YAAYjG,SAChB0B,KACAO,UAAUiB,KAAK,EACfmC,MAAMZ,GAAG,EACTY,MAAMW,OAAO;gBAEf,IAAIC,WAAW;oBACbC,OAAOC,MAAM,CAACZ,QAAQU;gBACxB,OAAO;oBACLV,SAAS;gBACX;YACF;YAEA,IAAIA,QAAQ;gBACV,IACE/E,UAAU4F,mBAAmB,IAC7Bf,MAAMrE,IAAI,KAAK,oBACf;oBACA,KAAK,MAAMqF,sBAAsB7F,UAAU4F,mBAAmB,CAAE;wBAC9D,MAAME,SAAS,MAAMT,YAAYQ;wBAEjC,IAAIC,QAAQ;4BACV,OAAOA;wBACT;oBACF;gBACF;gBAEA,IAAIjB,MAAMrE,IAAI,KAAK,0BAA0BiB,UAAUmB,QAAQ,EAAE;wBAC3D5C;oBAAJ,KAAIA,mCAAAA,UAAU+F,qBAAqB,uBAA/B/F,iCAAmC0E,MAAM,EAAE;4BAIzBQ;wBAHpB,IAAIc,aAAavE,UAAUmB,QAAQ;wBAEnC,qCAAqC;wBACrC,MAAMU,eAAc4B,wBAAAA,YAAY3B,QAAQ,qBAApB2B,sBAAsB3E,KAAK,CAACkB,UAAUmB,QAAQ;wBAClE,IAAIU,eAAe4B,YAAY3B,QAAQ,EAAE;4BACvCyC,aAAad,YAAY3B,QAAQ,CAAC0C,SAAS,CAACD,YAAY;wBAC1D;wBAEA,IAAIE,UAAU;wBACd,IAAIhB,YAAYC,IAAI,CAAC5E,KAAK,CAACyF,aAAa;4BACtCE,UAAU;4BACVzE,UAAUiB,KAAK,CAACuC,aAAa,GAAG;4BAChCe,aAAad,YAAYC,IAAI,CAACc,SAAS,CAACD,YAAY;wBACtD;wBAEA,IAAI/F,OAAOmD,IAAI,EAAE;4BACf,MAAM+C,kBAAkBjH,oBACtB8G,YACA/F,OAAOmD,IAAI,CAACI,OAAO;4BAGrB,IAAI2C,gBAAgBvC,cAAc,EAAE;gCAClCnC,UAAUiB,KAAK,CAACiB,YAAY,GAAGwC,gBAAgBvC,cAAc;4BAC/D;wBACF;wBAEA,iEAAiE;wBACjE,aAAa;wBACb,IAAIsC,SAAS;4BACX,IAAI5C,aAAa;gCACf0C,aAAa9H,KAAKkI,KAAK,CAACC,IAAI,CAACpG,OAAOsD,QAAQ,EAAEyC;4BAChD;4BAEA,2CAA2C;4BAC3CA,aAAarD,sBAAsBqD;4BAEnCvE,UAAUmB,QAAQ,GAAGoD;wBACvB;oBACF;gBACF;gBAEA,IAAInB,MAAMrE,IAAI,KAAK,YAAY;oBAC7B,MAAMoC,WAAWnB,UAAUmB,QAAQ,IAAI;oBAEvC,IAAIvB,CAAAA,kCAAAA,eAAgB4C,GAAG,CAACrB,cAAamB,eAAenB,WAAW;wBAC7D;oBACF;oBACA,MAAMsB,SAAS,MAAMlE,UAAUmE,OAAO,CAACvB;oBAEvC,IACEsB,UACA,CACEjE,CAAAA,OAAOmD,IAAI,KACXF,uCAAAA,oBAAqBU,cAAc,KACnC5E,cAAc4D,UAAU,OAAM,GAEhC;wBACA,IACE3C,OAAOmE,yBAAyB,IAChCzC,cACCuC,OAAOG,IAAI,KAAK,aAAaH,OAAOG,IAAI,KAAK,YAC9C;4BACA7C,gBAAgB0C;4BAEhB,IAAIA,OAAOoC,MAAM,EAAE;gCACjB7E,UAAUiB,KAAK,CAACiB,YAAY,GAAGO,OAAOoC,MAAM;4BAC9C;4BACA,OAAO;gCACL7E;gCACAF;gCACAD,UAAU;gCACVE;4BACF;wBACF;oBACF;gBACF;gBAEA,IAAI,CAACtB,KAAKO,WAAW,IAAIoE,MAAMrE,IAAI,KAAK,cAAc;oBACpD,MAAMD,QAAQP,UAAU+F,qBAAqB;oBAC7C,IACE,yCAAyC;oBACzCxF,yBAAAA,MAAQkB,UAAUmB,QAAQ,EAAE1B,KAAKO,UAAUiB,KAAK,GAChD;wBACA,IAAIrC,kBAAkB;4BACpB,MAAMA,iBAAiBa,IAAIjD,GAAG;wBAChC;wBAEA,MAAMsI,eACJ,OAAMpG,gCAAAA,aAAcqG,UAAU,CAACpG;wBAEjC,IAAI,CAACmG,cAAc;4BACjB,MAAM,IAAIE,MAAM,CAAC,+CAA+C,CAAC;wBACnE;wBAEAnH,eAAe4B,KAAK,cAAc;wBAClC5B,eAAe4B,KAAK,gBAAgB;wBACpC5B,eAAe4B,KAAK,eAAe,CAAC;wBACpC5B,eAAe4B,KAAK,oBAAoB;wBACxCpB,MAAM,uBAAuBoB,IAAIjD,GAAG,EAAEiD,IAAIR,OAAO;wBAEjD,IAAIgG,gBAAsCvD;wBAC1C,IAAIwD,aAAyCxD;wBAC7C,IAAI;4BACF,IAAI;gCACF,MAAMoD,aAAaK,cAAc,CAAC1F,KAAKC,KAAKM;4BAC9C,EAAE,OAAOoF,KAAU;gCACjB,IAAI,CAAE,CAAA,YAAYA,GAAE,KAAM,CAAE,CAAA,cAAcA,IAAIf,MAAM,AAAD,GAAI;oCACrD,MAAMe;gCACR;gCACAH,gBAAgBG,IAAIf,MAAM,CAACgB,QAAQ;gCACnC3F,IAAIY,UAAU,GAAG2E,cAAcK,MAAM;gCAErC,IAAIL,cAAcM,IAAI,EAAE;oCACtBL,aAAaD,cAAcM,IAAI;gCACjC,OAAO,IAAIN,cAAcK,MAAM,EAAE;oCAC/BJ,aAAa,IAAIM,eAAe;wCAC9BC,OAAMC,UAAU;4CACdA,WAAWC,OAAO,CAAC;4CACnBD,WAAWE,KAAK;wCAClB;oCACF;gCACF;4BACF;wBACF,EAAE,OAAOC,GAAG;4BACV,+DAA+D;4BAC/D,iEAAiE;4BACjE,sBAAsB;4BACtB,IAAI5I,aAAa4I,IAAI;gCACnB,OAAO;oCACL7F;oCACAF;oCACAD,UAAU;gCACZ;4BACF;4BACA,MAAMgG;wBACR;wBAEA,IAAInG,IAAIoG,MAAM,IAAIpG,IAAIG,QAAQ,IAAI,CAACoF,eAAe;4BAChD,OAAO;gCACLjF;gCACAF;gCACAD,UAAU;4BACZ;wBACF;wBAEA,MAAMkG,oBAAoB/I,0BACxBiI,cAAchG,OAAO;wBAGvBZ,MAAM,kBAAkB4G,cAAcK,MAAM,EAAES;wBAE9C,IAAIA,iBAAiB,CAAC,gCAAgC,EAAE;4BACtD,MAAMC,oBAAiC,IAAIC;4BAC3C,IAAIC,kBACFH,iBAAiB,CAAC,gCAAgC;4BAEpD,IAAI,OAAOG,oBAAoB,UAAU;gCACvCA,kBAAkBA,gBAAgB9F,KAAK,CAAC;4BAC1C;4BAEA,KAAK,MAAM+F,OAAOD,gBAAiB;gCACjCF,kBAAkBI,GAAG,CAACD,IAAIE,IAAI;4BAChC;4BACA,OAAON,iBAAiB,CAAC,gCAAgC;4BAEzD,kBAAkB;4BAClB,KAAK,MAAMI,OAAOlC,OAAOqC,IAAI,CAAC7G,IAAIR,OAAO,EAAG;gCAC1C,IAAI,CAAC+G,kBAAkBxD,GAAG,CAAC2D,MAAM;oCAC/B,OAAO1G,IAAIR,OAAO,CAACkH,IAAI;gCACzB;4BACF;4BAEA,yBAAyB;4BACzB,KAAK,MAAMA,OAAOH,kBAAkBM,IAAI,GAAI;gCAC1C,MAAMC,WAAW,0BAA0BJ;gCAC3C,MAAMK,WAAWT,iBAAiB,CAACQ,SAAS;gCAC5C,MAAME,WAAWhH,IAAIR,OAAO,CAACkH,IAAI;gCAEjC,IAAIM,aAAaD,UAAU;oCACzB/G,IAAIR,OAAO,CAACkH,IAAI,GAAGK,aAAa,OAAO9E,YAAY8E;gCACrD;gCACA,OAAOT,iBAAiB,CAACQ,SAAS;4BACpC;wBACF;wBAEA,IACE,CAACR,iBAAiB,CAAC,uBAAuB,IAC1C,CAACA,iBAAiB,CAAC,oBAAoB,IACvC,CAACA,iBAAiB,CAAC,WAAW,EAC9B;4BACAA,iBAAiB,CAAC,uBAAuB,GAAG;wBAC9C;wBACA,OAAOA,iBAAiB,CAAC,oBAAoB;wBAE7C,KAAK,MAAM,CAACI,KAAKO,MAAM,IAAIzC,OAAO0C,OAAO,CAAC;4BACxC,GAAG/J,iBAAiBmJ,mBAAmBlJ,oBAAoB;wBAC7D,GAAI;4BACF,IACE;gCACE;gCACA;gCACA;gCACA;6BACD,CAAC6D,QAAQ,CAACyF,MACX;gCACA;4BACF;4BAEA,gEAAgE;4BAChE,kEAAkE;4BAClE,IAAIA,QAAQ,2BAA2B;gCACrC1G,IAAIR,OAAO,CAACkH,IAAI,GAAGO;gCACnB;4BACF;4BAEA,IAAIA,OAAO;gCACT5G,UAAU,CAACqG,IAAI,GAAGO;gCAClBjH,IAAIR,OAAO,CAACkH,IAAI,GAAGO;4BACrB;wBACF;wBAEA,IAAIX,iBAAiB,CAAC,uBAAuB,EAAE;4BAC7C,MAAMW,QAAQX,iBAAiB,CAAC,uBAAuB;4BACvD,MAAMa,MAAMvJ,cAAcqJ,OAAO/F;4BACjCb,UAAU,CAAC,uBAAuB,GAAG8G;4BAErC,MAAM3F,QAAQjB,UAAUiB,KAAK;4BAC7BjB,YAAYxD,IAAIyD,KAAK,CAAC2G,KAAK;4BAE3B,IAAI5G,UAAUO,QAAQ,EAAE;gCACtB,OAAO;oCACLP;oCACAF;oCACAD,UAAU;gCACZ;4BACF;4BAEA,4BAA4B;4BAC5B,KAAK,MAAMsG,OAAOlC,OAAOqC,IAAI,CAACrF,OAAQ;gCACpC,IAAIkF,IAAI/D,UAAU,CAAC,YAAY+D,IAAI/D,UAAU,CAAC,WAAW;oCACvDpC,UAAUiB,KAAK,CAACkF,IAAI,GAAGlF,KAAK,CAACkF,IAAI;gCACnC;4BACF;4BAEA,IAAI3H,OAAOmD,IAAI,EAAE;gCACf,MAAM+C,kBAAkBjH,oBACtBuC,UAAUmB,QAAQ,IAAI,IACtB3C,OAAOmD,IAAI,CAACI,OAAO;gCAGrB,IAAI2C,gBAAgBvC,cAAc,EAAE;oCAClCnC,UAAUiB,KAAK,CAACiB,YAAY,GAAGwC,gBAAgBvC,cAAc;gCAC/D;4BACF;wBACF;wBAEA,IAAI4D,iBAAiB,CAAC,WAAW,EAAE;4BACjC,MAAMW,QAAQX,iBAAiB,CAAC,WAAW;4BAC3C,MAAMa,MAAMvJ,cAAcqJ,OAAO/F;4BACjCb,UAAU,CAAC,WAAW,GAAG8G;4BACzB5G,YAAYxD,IAAIyD,KAAK,CAAC2G,KAAK;4BAE3B,OAAO;gCACL5G;gCACAF;gCACAD,UAAU;gCACVS,YAAY2E,cAAcK,MAAM;4BAClC;wBACF;wBAEA,IAAIS,iBAAiB,CAAC,uBAAuB,EAAE;4BAC7C,OAAO;gCACL/F;gCACAF;gCACAD,UAAU;gCACVqF;gCACA5E,YAAY2E,cAAcK,MAAM;4BAClC;wBACF;oBACF;gBACF;gBAEA,kBAAkB;gBAClB,IACE,AAAC,CAAA,gBAAgBlC,SAAS,eAAeA,KAAI,KAC7CA,MAAMyD,WAAW,EACjB;oBACA,MAAM,EAAEC,iBAAiB,EAAE,GAAG9I,mBAAmB;wBAC/C+I,qBAAqB;wBACrBF,aAAazD,MAAMyD,WAAW;wBAC9BvD,QAAQA;wBACRrC,OAAOjB,UAAUiB,KAAK;oBACxB;oBAEA,MAAM,EAAEA,KAAK,EAAE,GAAG6F;oBAClB,OAAO,AAACA,kBAA0B7F,KAAK;oBAEvC6F,kBAAkBE,MAAM,GAAGlK,eAAe2C,KAAYwB;oBAEtD6F,kBAAkB3F,QAAQ,GAAG/D,yBAC3B0J,kBAAkB3F,QAAQ;oBAG5B,OAAO;wBACLtB,UAAU;wBACV,oCAAoC;wBACpCG,WAAW8G;wBACXxG,YAAYnD,kBAAkBiG;oBAChC;gBACF;gBAEA,iBAAiB;gBACjB,IAAIA,MAAMnE,OAAO,EAAE;oBACjB,MAAM+E,YAAYC,OAAOqC,IAAI,CAAChD,QAAQL,MAAM,GAAG;oBAC/C,KAAK,MAAMgE,UAAU7D,MAAMnE,OAAO,CAAE;wBAClC,IAAI,EAAEkH,GAAG,EAAEO,KAAK,EAAE,GAAGO;wBACrB,IAAIjD,WAAW;4BACbmC,MAAMrI,eAAeqI,KAAK7C;4BAC1BoD,QAAQ5I,eAAe4I,OAAOpD;wBAChC;wBAEA,IAAI6C,IAAIe,WAAW,OAAO,cAAc;4BACtC,IAAI,CAACC,MAAMC,OAAO,CAACtH,UAAU,CAACqG,IAAI,GAAG;gCACnC,MAAMkB,MAAMvH,UAAU,CAACqG,IAAI;gCAC3BrG,UAAU,CAACqG,IAAI,GAAG,OAAOkB,QAAQ,WAAW;oCAACA;iCAAI,GAAG,EAAE;4BACxD;;4BACEvH,UAAU,CAACqG,IAAI,CAAcmB,IAAI,CAACZ;wBACtC,OAAO;4BACL5G,UAAU,CAACqG,IAAI,GAAGO;wBACpB;oBACF;gBACF;gBAEA,iBAAiB;gBACjB,IAAItD,MAAMyD,WAAW,EAAE;oBACrB,IAAIU,gBAAgBjE;oBAEpB,IAAI;wBACF,+EAA+E;wBAC/E,gFAAgF;wBAChF,8EAA8E;wBAC9E,IAAInF,2BAA2BiF,QAAmB;4BAChD,MAAMoE,cACJ/H,IAAIR,OAAO,CAAChB,8BAA8BiJ,WAAW,GAAG;4BAE1D,IAAIM,aAAa;gCACfD,gBAAgB;oCACd,GAAGrJ,kBACDE,kCAAkCoJ,aACnC;oCACD,GAAGlE,MAAM;gCACX;4BACF;wBACF;oBACF,EAAE,OAAO8B,KAAK;oBACZ,wFAAwF;oBACxF,sDAAsD;oBACxD;oBAEA,MAAM,EAAE0B,iBAAiB,EAAE,GAAG9I,mBAAmB;wBAC/C+I,qBAAqB;wBACrBF,aAAazD,MAAMyD,WAAW;wBAC9BvD,QAAQiE;wBACRtG,OAAOjB,UAAUiB,KAAK;oBACxB;oBAEA,IAAI6F,kBAAkBvG,QAAQ,EAAE;wBAC9B,OAAO;4BACL,oCAAoC;4BACpCP,WAAW8G;4BACXjH,UAAU;wBACZ;oBACF;oBAEA,IAAIrB,OAAOmD,IAAI,EAAE;wBACf,MAAM+C,kBAAkBjH,oBACtBC,iBAAiBoJ,kBAAkB3F,QAAQ,EAAE3C,OAAOsD,QAAQ,GAC5DtD,OAAOmD,IAAI,CAACI,OAAO;wBAGrB,IAAI2C,gBAAgBvC,cAAc,EAAE;4BAClCnC,UAAUiB,KAAK,CAACiB,YAAY,GAAGwC,gBAAgBvC,cAAc;wBAC/D;oBACF;oBACAjC,aAAa;oBACbF,UAAUmB,QAAQ,GAAG2F,kBAAkB3F,QAAQ;oBAC/C8C,OAAOC,MAAM,CAAClE,UAAUiB,KAAK,EAAE6F,kBAAkB7F,KAAK;gBACxD;gBAEA,qBAAqB;gBACrB,IAAImC,MAAM9D,KAAK,EAAE;oBACf,MAAMmD,SAAS,MAAMF;oBAErB,IAAIE,QAAQ;wBACV,OAAO;4BACLzC;4BACAF;4BACAD,UAAU;4BACVE,eAAe0C;wBACjB;oBACF;gBACF;YACF;QACF;QAEA,KAAK,MAAMW,SAASvE,OAAQ;YAC1B,MAAMwF,SAAS,MAAMT,YAAYR;YACjC,IAAIiB,QAAQ;gBACV,OAAOA;YACT;QACF;QAEA,OAAO;YACLxE;YACAG;YACAF;YACAC;QACF;IACF;IAEA,OAAOP;AACT"}