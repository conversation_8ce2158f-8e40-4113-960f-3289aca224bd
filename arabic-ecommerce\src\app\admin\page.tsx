'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Product, Order, OrderStatus, User } from '@/types';
import { useProducts } from '@/contexts/ProductContext';
import { useCategories, Category } from '@/contexts/CategoryContext';
import { useOrders } from '@/contexts/OrderContext';
import { useUsers } from '@/contexts/UserContext';
import { useReports } from '@/contexts/ReportsContext';
import ProductForm from '@/components/ProductForm';
import CategoryForm from '@/components/CategoryForm';
import UserForm from '@/components/UserForm';
import OrderDetailsModal from '@/components/OrderDetailsModal';
import DeleteConfirmModal from '@/components/DeleteConfirmModal';
import { LineChart, BarChart, PieChart, StatCard } from '@/components/Charts';

export default function AdminDashboard() {
  const [activeTab, setActiveTab] = useState('overview');
  const [isProductFormOpen, setIsProductFormOpen] = useState(false);
  const [editingProduct, setEditingProduct] = useState<Product | null>(null);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [productToDelete, setProductToDelete] = useState<Product | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  // Category management states
  const [isCategoryFormOpen, setIsCategoryFormOpen] = useState(false);
  const [editingCategory, setEditingCategory] = useState<Category | null>(null);
  const [categoryToDelete, setCategoryToDelete] = useState<Category | null>(null);

  // Order management states
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const [isOrderDetailsOpen, setIsOrderDetailsOpen] = useState(false);
  const [orderStatusFilter, setOrderStatusFilter] = useState<OrderStatus | 'all'>('all');

  // User management states
  const [isUserFormOpen, setIsUserFormOpen] = useState(false);
  const [editingUser, setEditingUser] = useState<User | null>(null);
  const [userToDelete, setUserToDelete] = useState<User | null>(null);
  const [userRoleFilter, setUserRoleFilter] = useState<'all' | 'admin' | 'customer'>('all');
  const [userStatusFilter, setUserStatusFilter] = useState<'all' | 'active' | 'inactive'>('all');

  const { products, addProduct, updateProduct, deleteProduct, isLoading } = useProducts();
  const { categories, addCategory, updateCategory, deleteCategory: deleteCategoryFromContext, isLoading: categoriesLoading } = useCategories();
  const { orders, updateOrderStatus, getTotalRevenue, getOrdersCount, getRecentOrders, isLoading: ordersLoading } = useOrders();
  const { users, addUser, updateUser, deleteUser, toggleUserStatus, getTotalUsers, getActiveUsersCount, getRecentUsers, isLoading: usersLoading } = useUsers();
  const {
    getDailySales,
    getWeeklySales,
    getMonthlySales,
    getTopSellingProducts,
    getMostProfitableProducts,
    getTopCustomers,
    getSummaryStats,
    getTotalProfit,
    getRevenueGrowth,
    getOrderGrowth,
    isLoading: reportsLoading
  } = useReports();

  const stats = {
    totalProducts: products.length,
    totalCategories: categories.length,
    totalUsers: getTotalUsers(),
    activeUsers: getActiveUsersCount(),
    totalOrders: getOrdersCount(),
    totalRevenue: getTotalRevenue(),
  };

  // Product management functions
  const handleAddProduct = () => {
    setEditingProduct(null);
    setIsProductFormOpen(true);
  };

  const handleEditProduct = (product: Product) => {
    setEditingProduct(product);
    setIsProductFormOpen(true);
  };

  const handleDeleteProduct = (product: Product) => {
    setProductToDelete(product);
    setIsDeleteModalOpen(true);
  };

  const handleSaveProduct = (productData: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>) => {
    if (editingProduct) {
      // Update existing product
      updateProduct(editingProduct.id, productData);
    } else {
      // Add new product
      addProduct(productData);
    }
  };

  const confirmDeleteProduct = async () => {
    if (!productToDelete) return;

    setIsDeleting(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      deleteProduct(productToDelete.id);
      setIsDeleteModalOpen(false);
      setProductToDelete(null);
    } catch (error) {
      console.error('Error deleting product:', error);
    } finally {
      setIsDeleting(false);
    }
  };

  // Category management functions
  const handleAddCategory = () => {
    setEditingCategory(null);
    setIsCategoryFormOpen(true);
  };

  const handleEditCategory = (category: Category) => {
    setEditingCategory(category);
    setIsCategoryFormOpen(true);
  };

  const handleDeleteCategory = (category: Category) => {
    setCategoryToDelete(category);
    setIsDeleteModalOpen(true);
  };

  const handleSaveCategory = (categoryData: Omit<Category, 'id' | 'productCount' | 'createdAt' | 'updatedAt'>) => {
    if (editingCategory) {
      // Update existing category
      updateCategory(editingCategory.id, categoryData);
    } else {
      // Add new category
      addCategory(categoryData);
    }
  };

  const confirmDeleteCategory = async () => {
    if (!categoryToDelete) return;

    setIsDeleting(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      deleteCategoryFromContext(categoryToDelete.id);
      setIsDeleteModalOpen(false);
      setCategoryToDelete(null);
    } catch (error) {
      console.error('Error deleting category:', error);
    } finally {
      setIsDeleting(false);
    }
  };

  // Order management functions
  const handleViewOrder = (order: Order) => {
    setSelectedOrder(order);
    setIsOrderDetailsOpen(true);
  };

  const handleUpdateOrderStatus = (orderId: string, status: OrderStatus) => {
    updateOrderStatus(orderId, status);
    // Update the selected order if it's the one being updated
    if (selectedOrder && selectedOrder.id === orderId) {
      setSelectedOrder({ ...selectedOrder, status });
    }
  };

  const getFilteredOrders = () => {
    if (orderStatusFilter === 'all') {
      return orders;
    }
    return orders.filter(order => order.status === orderStatusFilter);
  };

  // User management functions
  const handleAddUser = () => {
    setEditingUser(null);
    setIsUserFormOpen(true);
  };

  const handleEditUser = (user: User) => {
    setEditingUser(user);
    setIsUserFormOpen(true);
  };

  const handleDeleteUser = (user: User) => {
    setUserToDelete(user);
    setIsDeleteModalOpen(true);
  };

  const handleToggleUserStatus = (userId: string) => {
    toggleUserStatus(userId);
  };

  const handleSaveUser = (userData: Omit<User, 'id' | 'totalOrders' | 'totalSpent' | 'createdAt' | 'updatedAt'>) => {
    if (editingUser) {
      // Update existing user
      updateUser(editingUser.id, userData);
    } else {
      // Add new user
      addUser(userData);
    }
  };

  const confirmDeleteUser = async () => {
    if (!userToDelete) return;

    setIsDeleting(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      deleteUser(userToDelete.id);
      setIsDeleteModalOpen(false);
      setUserToDelete(null);
    } catch (error) {
      console.error('Error deleting user:', error);
    } finally {
      setIsDeleting(false);
    }
  };

  const getFilteredUsers = () => {
    let filtered = users;

    if (userRoleFilter !== 'all') {
      filtered = filtered.filter(user => user.role === userRoleFilter);
    }

    if (userStatusFilter !== 'all') {
      filtered = filtered.filter(user =>
        userStatusFilter === 'active' ? user.isActive : !user.isActive
      );
    }

    return filtered;
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <Link href="/" className="text-2xl font-bold text-primary-600">
                🛍️ المتجر العربي
              </Link>
              <p className="text-gray-600 mt-1">لوحة التحكم</p>
            </div>
            <div className="flex items-center space-x-4 space-x-reverse">
              <span className="text-gray-700">مرحباً، أحمد</span>
              <Link
                href="/"
                className="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg transition-colors duration-200"
              >
                عرض المتجر
              </Link>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Navigation Tabs */}
        <div className="border-b border-gray-200 mb-8">
          <nav className="-mb-px flex space-x-8 space-x-reverse">
            {[
              { id: 'overview', name: 'نظرة عامة', icon: '📊' },
              { id: 'products', name: 'المنتجات', icon: '📦' },
              { id: 'categories', name: 'التصنيفات', icon: '🏷️' },
              { id: 'orders', name: 'الطلبات', icon: '🛒' },
              { id: 'users', name: 'المستخدمين', icon: '👥' },
              { id: 'reports', name: 'التقارير', icon: '📈' },
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-primary-500 text-primary-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                {tab.icon} {tab.name}
              </button>
            ))}
          </nav>
        </div>

        {/* Overview Tab */}
        {activeTab === 'overview' && (
          <div>
            <h2 className="text-2xl font-bold text-gray-800 mb-6">نظرة عامة</h2>

            {/* Stats Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-5 gap-6 mb-8">
              <div className="bg-white rounded-lg shadow p-6">
                <div className="flex items-center">
                  <div className="text-3xl">📦</div>
                  <div className="mr-4">
                    <p className="text-sm font-medium text-gray-600">إجمالي المنتجات</p>
                    <p className="text-2xl font-bold text-gray-900">{stats.totalProducts}</p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow p-6">
                <div className="flex items-center">
                  <div className="text-3xl">🏷️</div>
                  <div className="mr-4">
                    <p className="text-sm font-medium text-gray-600">إجمالي التصنيفات</p>
                    <p className="text-2xl font-bold text-gray-900">{stats.totalCategories}</p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow p-6">
                <div className="flex items-center">
                  <div className="text-3xl">👥</div>
                  <div className="mr-4">
                    <p className="text-sm font-medium text-gray-600">إجمالي المستخدمين</p>
                    <p className="text-2xl font-bold text-gray-900">{stats.totalUsers}</p>
                    <p className="text-xs text-green-600">{stats.activeUsers} نشط</p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow p-6">
                <div className="flex items-center">
                  <div className="text-3xl">🛒</div>
                  <div className="mr-4">
                    <p className="text-sm font-medium text-gray-600">إجمالي الطلبات</p>
                    <p className="text-2xl font-bold text-gray-900">{stats.totalOrders}</p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow p-6">
                <div className="flex items-center">
                  <div className="text-3xl">💰</div>
                  <div className="mr-4">
                    <p className="text-sm font-medium text-gray-600">إجمالي المبيعات</p>
                    <p className="text-2xl font-bold text-gray-900">{stats.totalRevenue.toLocaleString('ar-SA')} ر.س</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Recent Activity */}
            <div className="bg-white rounded-lg shadow">
              <div className="px-6 py-4 border-b border-gray-200">
                <h3 className="text-lg font-medium text-gray-900">الطلبات الأخيرة</h3>
              </div>
              <div className="p-6">
                {ordersLoading ? (
                  <div className="text-center py-4">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"></div>
                    <p className="text-gray-600 mt-2">جاري التحميل...</p>
                  </div>
                ) : getRecentOrders(3).length === 0 ? (
                  <div className="text-center py-8">
                    <div className="text-4xl mb-2">📋</div>
                    <p className="text-gray-500">لا توجد طلبات حديثة</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {getRecentOrders(3).map((order) => (
                      <div key={order.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div className="flex items-center">
                          <div className="text-2xl ml-3">🛒</div>
                          <div>
                            <p className="text-sm font-medium text-gray-900">
                              طلب {order.orderNumber}
                            </p>
                            <p className="text-sm text-gray-500">
                              {order.customerName} - {order.total.toLocaleString('ar-SA')} ر.س
                            </p>
                          </div>
                        </div>
                        <div className="text-left">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            order.status === 'delivered' ? 'bg-green-100 text-green-800' :
                            order.status === 'shipped' ? 'bg-blue-100 text-blue-800' :
                            order.status === 'processing' ? 'bg-yellow-100 text-yellow-800' :
                            order.status === 'confirmed' ? 'bg-purple-100 text-purple-800' :
                            order.status === 'pending' ? 'bg-gray-100 text-gray-800' :
                            'bg-red-100 text-red-800'
                          }`}>
                            {order.status === 'pending' ? 'في الانتظار' :
                             order.status === 'confirmed' ? 'مؤكد' :
                             order.status === 'processing' ? 'قيد المعالجة' :
                             order.status === 'shipped' ? 'تم الشحن' :
                             order.status === 'delivered' ? 'تم التسليم' :
                             'ملغي'}
                          </span>
                          <p className="text-xs text-gray-500 mt-1">
                            {order.createdAt.toLocaleDateString('ar-SA')}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Products Tab */}
        {activeTab === 'products' && (
          <div>
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-2xl font-bold text-gray-800">إدارة المنتجات</h2>
              <button
                onClick={handleAddProduct}
                className="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg transition-colors duration-200"
              >
                إضافة منتج جديد
              </button>
            </div>

            {isLoading ? (
              <div className="bg-white rounded-lg shadow p-12 text-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
                <p className="text-gray-600">جاري تحميل المنتجات...</p>
              </div>
            ) : products.length === 0 ? (
              <div className="bg-white rounded-lg shadow p-12 text-center">
                <div className="text-6xl mb-4">📦</div>
                <h3 className="text-xl font-semibold text-gray-700 mb-2">
                  لا توجد منتجات
                </h3>
                <p className="text-gray-500 mb-6">
                  لم يتم إضافة أي منتجات بعد. ابدأ بإضافة منتجك الأول!
                </p>
                <button
                  onClick={handleAddProduct}
                  className="bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-lg transition-colors duration-200"
                >
                  إضافة منتج جديد
                </button>
              </div>
            ) : (
              <div className="bg-white rounded-lg shadow overflow-hidden">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        المنتج
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        السعر
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        المخزون
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        الحالة
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        الإجراءات
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {products.map((product) => (
                    <tr key={product.id}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="h-10 w-10 bg-gray-200 rounded-lg flex items-center justify-center">
                            <span className="text-xs text-gray-500">📦</span>
                          </div>
                          <div className="mr-4">
                            <div className="text-sm font-medium text-gray-900">{product.name}</div>
                            <div className="text-sm text-gray-500">{product.category}</div>
                            {product.featured && (
                              <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800">
                                مميز
                              </span>
                            )}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {product.price.toLocaleString('ar-SA')} ر.س
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {product.stock}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          product.stock > 0 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                        }`}>
                          {product.stock > 0 ? 'متوفر' : 'نفد المخزون'}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <button
                          onClick={() => handleEditProduct(product)}
                          className="text-primary-600 hover:text-primary-900 ml-4"
                        >
                          تعديل
                        </button>
                        <button
                          onClick={() => handleDeleteProduct(product)}
                          className="text-red-600 hover:text-red-900"
                        >
                          حذف
                        </button>
                      </td>
                    </tr>
                  ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        )}

        {/* Categories Tab */}
        {activeTab === 'categories' && (
          <div>
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-2xl font-bold text-gray-800">إدارة التصنيفات</h2>
              <button
                onClick={handleAddCategory}
                className="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg transition-colors duration-200"
              >
                إضافة تصنيف جديد
              </button>
            </div>

            {categoriesLoading ? (
              <div className="bg-white rounded-lg shadow p-12 text-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
                <p className="text-gray-600">جاري تحميل التصنيفات...</p>
              </div>
            ) : categories.length === 0 ? (
              <div className="bg-white rounded-lg shadow p-12 text-center">
                <div className="text-6xl mb-4">🏷️</div>
                <h3 className="text-xl font-semibold text-gray-700 mb-2">
                  لا توجد تصنيفات
                </h3>
                <p className="text-gray-500 mb-6">
                  ابدأ بإضافة تصنيف جديد لتنظيم منتجاتك
                </p>
                <button
                  onClick={handleAddCategory}
                  className="bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-lg transition-colors duration-200"
                >
                  إضافة تصنيف جديد
                </button>
              </div>
            ) : (
              <div className="bg-white rounded-lg shadow overflow-hidden">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        اسم التصنيف
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        الوصف
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        عدد المنتجات
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        الحالة
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        تاريخ الإنشاء
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        الإجراءات
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {categories.map((category) => (
                      <tr key={category.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900">{category.name}</div>
                        </td>
                        <td className="px-6 py-4">
                          <div className="text-sm text-gray-500 max-w-xs truncate">{category.description}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">{category.productCount}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            category.isActive
                              ? 'bg-green-100 text-green-800'
                              : 'bg-red-100 text-red-800'
                          }`}>
                            {category.isActive ? 'نشط' : 'غير نشط'}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {category.createdAt.toLocaleDateString('ar-SA')}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <div className="flex space-x-2 space-x-reverse">
                            <button
                              onClick={() => handleEditCategory(category)}
                              className="text-primary-600 hover:text-primary-900"
                            >
                              تعديل
                            </button>
                            <button
                              onClick={() => handleDeleteCategory(category)}
                              className="text-red-600 hover:text-red-900"
                            >
                              حذف
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        )}

        {/* Orders Tab */}
        {activeTab === 'orders' && (
          <div>
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-2xl font-bold text-gray-800">إدارة الطلبات</h2>
              <div className="flex items-center space-x-4 space-x-reverse">
                <select
                  value={orderStatusFilter}
                  onChange={(e) => setOrderStatusFilter(e.target.value as OrderStatus | 'all')}
                  className="border border-gray-300 rounded-md px-3 py-2"
                >
                  <option value="all">جميع الطلبات</option>
                  <option value="pending">في الانتظار</option>
                  <option value="confirmed">مؤكد</option>
                  <option value="processing">قيد المعالجة</option>
                  <option value="shipped">تم الشحن</option>
                  <option value="delivered">تم التسليم</option>
                  <option value="cancelled">ملغي</option>
                </select>
              </div>
            </div>

            {ordersLoading ? (
              <div className="bg-white rounded-lg shadow p-12 text-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
                <p className="text-gray-600">جاري تحميل الطلبات...</p>
              </div>
            ) : getFilteredOrders().length === 0 ? (
              <div className="bg-white rounded-lg shadow p-12 text-center">
                <div className="text-6xl mb-4">📋</div>
                <h3 className="text-xl font-semibold text-gray-700 mb-2">
                  {orderStatusFilter === 'all' ? 'لا توجد طلبات' : `لا توجد طلبات ${
                    orderStatusFilter === 'pending' ? 'في الانتظار' :
                    orderStatusFilter === 'confirmed' ? 'مؤكدة' :
                    orderStatusFilter === 'processing' ? 'قيد المعالجة' :
                    orderStatusFilter === 'shipped' ? 'مشحونة' :
                    orderStatusFilter === 'delivered' ? 'مسلمة' :
                    'ملغية'
                  }`}
                </h3>
                <p className="text-gray-500">
                  {orderStatusFilter === 'all'
                    ? 'لم يتم إنشاء أي طلبات بعد'
                    : 'جرب تغيير فلتر الحالة لعرض طلبات أخرى'
                  }
                </p>
              </div>
            ) : (
              <div className="bg-white rounded-lg shadow overflow-hidden">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        رقم الطلب
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        العميل
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        المبلغ
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        الحالة
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        طريقة الدفع
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        تاريخ الطلب
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        الإجراءات
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {getFilteredOrders().map((order) => (
                      <tr key={order.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900">{order.orderNumber}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div>
                            <div className="text-sm font-medium text-gray-900">{order.customerName}</div>
                            <div className="text-sm text-gray-500">{order.customerEmail}</div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900">
                            {order.total.toLocaleString('ar-SA')} ر.س
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            order.status === 'delivered' ? 'bg-green-100 text-green-800' :
                            order.status === 'shipped' ? 'bg-blue-100 text-blue-800' :
                            order.status === 'processing' ? 'bg-yellow-100 text-yellow-800' :
                            order.status === 'confirmed' ? 'bg-purple-100 text-purple-800' :
                            order.status === 'pending' ? 'bg-gray-100 text-gray-800' :
                            'bg-red-100 text-red-800'
                          }`}>
                            {order.status === 'pending' ? 'في الانتظار' :
                             order.status === 'confirmed' ? 'مؤكد' :
                             order.status === 'processing' ? 'قيد المعالجة' :
                             order.status === 'shipped' ? 'تم الشحن' :
                             order.status === 'delivered' ? 'تم التسليم' :
                             'ملغي'}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {order.paymentMethod === 'cash' ? 'الدفع عند الاستلام' :
                           order.paymentMethod === 'card' ? 'بطاقة ائتمانية' :
                           'تحويل بنكي'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {order.createdAt.toLocaleDateString('ar-SA')}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <button
                            onClick={() => handleViewOrder(order)}
                            className="text-primary-600 hover:text-primary-900"
                          >
                            عرض التفاصيل
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        )}

        {/* Users Tab */}
        {activeTab === 'users' && (
          <div>
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-2xl font-bold text-gray-800">إدارة المستخدمين</h2>
              <button
                onClick={handleAddUser}
                className="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg transition-colors duration-200"
              >
                إضافة مستخدم جديد
              </button>
            </div>

            {/* Filters */}
            <div className="bg-white rounded-lg shadow p-4 mb-6">
              <div className="flex flex-wrap gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">نوع المستخدم</label>
                  <select
                    value={userRoleFilter}
                    onChange={(e) => setUserRoleFilter(e.target.value as 'all' | 'admin' | 'customer')}
                    className="border border-gray-300 rounded-md px-3 py-2"
                  >
                    <option value="all">جميع الأنواع</option>
                    <option value="admin">مدير</option>
                    <option value="customer">عميل</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">حالة الحساب</label>
                  <select
                    value={userStatusFilter}
                    onChange={(e) => setUserStatusFilter(e.target.value as 'all' | 'active' | 'inactive')}
                    className="border border-gray-300 rounded-md px-3 py-2"
                  >
                    <option value="all">جميع الحالات</option>
                    <option value="active">نشط</option>
                    <option value="inactive">غير نشط</option>
                  </select>
                </div>
              </div>
            </div>

            {usersLoading ? (
              <div className="bg-white rounded-lg shadow p-12 text-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
                <p className="text-gray-600">جاري تحميل المستخدمين...</p>
              </div>
            ) : getFilteredUsers().length === 0 ? (
              <div className="bg-white rounded-lg shadow p-12 text-center">
                <div className="text-6xl mb-4">👥</div>
                <h3 className="text-xl font-semibold text-gray-700 mb-2">
                  لا توجد مستخدمين
                </h3>
                <p className="text-gray-500 mb-6">
                  {userRoleFilter !== 'all' || userStatusFilter !== 'all'
                    ? 'جرب تغيير الفلاتر لعرض مستخدمين آخرين'
                    : 'ابدأ بإضافة مستخدم جديد'
                  }
                </p>
                <button
                  onClick={handleAddUser}
                  className="bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-lg transition-colors duration-200"
                >
                  إضافة مستخدم جديد
                </button>
              </div>
            ) : (
              <div className="bg-white rounded-lg shadow overflow-hidden">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        المستخدم
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        النوع
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        الحالة
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        الطلبات
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        إجمالي الإنفاق
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        آخر دخول
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        الإجراءات
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {getFilteredUsers().map((user) => (
                      <tr key={user.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div>
                            <div className="text-sm font-medium text-gray-900">{user.name}</div>
                            <div className="text-sm text-gray-500">{user.email}</div>
                            {user.phone && (
                              <div className="text-sm text-gray-500">{user.phone}</div>
                            )}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            user.role === 'admin'
                              ? 'bg-purple-100 text-purple-800'
                              : 'bg-blue-100 text-blue-800'
                          }`}>
                            {user.role === 'admin' ? 'مدير' : 'عميل'}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <button
                            onClick={() => handleToggleUserStatus(user.id)}
                            className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full transition-colors duration-200 ${
                              user.isActive
                                ? 'bg-green-100 text-green-800 hover:bg-green-200'
                                : 'bg-red-100 text-red-800 hover:bg-red-200'
                            }`}
                          >
                            {user.isActive ? 'نشط' : 'غير نشط'}
                          </button>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {user.totalOrders}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {user.totalSpent.toLocaleString('ar-SA')} ر.س
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {user.lastLogin
                            ? user.lastLogin.toLocaleDateString('ar-SA')
                            : 'لم يسجل دخول'
                          }
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <div className="flex space-x-2 space-x-reverse">
                            <button
                              onClick={() => handleEditUser(user)}
                              className="text-primary-600 hover:text-primary-900"
                            >
                              تعديل
                            </button>
                            <button
                              onClick={() => handleDeleteUser(user)}
                              className="text-red-600 hover:text-red-900"
                            >
                              حذف
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        )}

        {/* Reports Tab */}
        {activeTab === 'reports' && (
          <div>
            <h2 className="text-2xl font-bold text-gray-800 mb-6">تقارير المبيعات والأرباح</h2>

            {reportsLoading ? (
              <div className="bg-white rounded-lg shadow p-12 text-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
                <p className="text-gray-600">جاري تحميل التقارير...</p>
              </div>
            ) : (
              <div className="space-y-8">
                {/* Summary Statistics */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  <StatCard
                    key="total-sales"
                    title="إجمالي المبيعات"
                    value={`${getSummaryStats().totalRevenue.toLocaleString('ar-SA')} ر.س`}
                    change={getRevenueGrowth('monthly')}
                    icon="💰"
                    color="green"
                  />
                  <StatCard
                    key="total-profit"
                    title="إجمالي الأرباح"
                    value={`${getSummaryStats().totalProfit.toLocaleString('ar-SA')} ر.س`}
                    change={getRevenueGrowth('monthly') * 0.3}
                    icon="📈"
                    color="blue"
                  />
                  <StatCard
                    key="total-orders"
                    title="عدد الطلبات"
                    value={getSummaryStats().totalOrders}
                    change={getOrderGrowth('monthly')}
                    icon="🛒"
                    color="purple"
                  />
                  <StatCard
                    key="average-order-value"
                    title="متوسط قيمة الطلب"
                    value={`${getSummaryStats().averageOrderValue.toLocaleString('ar-SA')} ر.س`}
                    icon="📊"
                    color="yellow"
                  />
                </div>

                {/* Charts Row 1 */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <LineChart
                    key="daily-sales-chart"
                    data={getDailySales(30).map(item => ({
                      label: new Date(item.date).toLocaleDateString('ar-SA', { month: 'short', day: 'numeric' }),
                      value: item.sales
                    }))}
                    title="المبيعات اليومية (آخر 30 يوم)"
                    color="#10B981"
                  />
                  <LineChart
                    key="daily-profit-chart"
                    data={getDailySales(30).map(item => ({
                      label: new Date(item.date).toLocaleDateString('ar-SA', { month: 'short', day: 'numeric' }),
                      value: item.profit
                    }))}
                    title="الأرباح اليومية (آخر 30 يوم)"
                    color="#3B82F6"
                  />
                </div>

                {/* Charts Row 2 */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <BarChart
                    key="top-selling-products-chart"
                    data={getTopSellingProducts(10).map(product => ({
                      label: product.productName.length > 15
                        ? product.productName.substring(0, 15) + '...'
                        : product.productName,
                      value: product.quantitySold
                    }))}
                    title="أفضل المنتجات مبيعاً"
                    horizontal={true}
                  />
                  <BarChart
                    key="most-profitable-products-chart"
                    data={getMostProfitableProducts(10).map(product => ({
                      label: product.productName.length > 15
                        ? product.productName.substring(0, 15) + '...'
                        : product.productName,
                      value: product.totalProfit
                    }))}
                    title="أكثر المنتجات ربحاً"
                    horizontal={true}
                  />
                </div>

                {/* Charts Row 3 */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <PieChart
                    key="sales-distribution-chart"
                    data={getTopSellingProducts(5).map((product, index) => ({
                      label: product.productName,
                      value: product.totalRevenue,
                      color: ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6'][index]
                    }))}
                    title="توزيع المبيعات حسب المنتج"
                  />
                  <div key="top-customers-widget" className="bg-white rounded-lg shadow p-6">
                    <h3 className="text-lg font-semibold text-gray-800 mb-4">أفضل العملاء</h3>
                    <div className="space-y-3">
                      {getTopCustomers(5).map((customer, index) => (
                        <div key={customer.userId} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                          <div className="flex items-center">
                            <div className="w-8 h-8 bg-primary-500 text-white rounded-full flex items-center justify-center text-sm font-bold">
                              {index + 1}
                            </div>
                            <div className="mr-3">
                              <div className="font-medium text-gray-900">{customer.userName}</div>
                              <div className="text-sm text-gray-500">{customer.totalOrders} طلب</div>
                            </div>
                          </div>
                          <div className="text-left">
                            <div className="font-bold text-gray-900">
                              {customer.totalSpent.toLocaleString('ar-SA')} ر.س
                            </div>
                            <div className="text-sm text-gray-500">
                              متوسط: {customer.averageOrderValue.toLocaleString('ar-SA')} ر.س
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>

                {/* Monthly Sales Table */}
                <div key="monthly-sales-table" className="bg-white rounded-lg shadow">
                  <div className="px-6 py-4 border-b border-gray-200">
                    <h3 className="text-lg font-medium text-gray-900">المبيعات الشهرية</h3>
                  </div>
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            الشهر
                          </th>
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            المبيعات
                          </th>
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            الأرباح
                          </th>
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            عدد الطلبات
                          </th>
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            متوسط قيمة الطلب
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {getMonthlySales(6).reverse().map((monthData, index) => (
                          <tr key={monthData.date} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                              {new Date(monthData.date + '-01').toLocaleDateString('ar-SA', {
                                year: 'numeric',
                                month: 'long'
                              })}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              {monthData.sales.toLocaleString('ar-SA')} ر.س
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              {monthData.profit.toLocaleString('ar-SA')} ر.س
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              {monthData.orders}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              {monthData.orders > 0
                                ? (monthData.sales / monthData.orders).toLocaleString('ar-SA')
                                : '0'} ر.س
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Product Form Modal */}
      <ProductForm
        product={editingProduct}
        isOpen={isProductFormOpen}
        onClose={() => {
          setIsProductFormOpen(false);
          setEditingProduct(null);
        }}
        onSave={handleSaveProduct}
      />

      {/* Category Form Modal */}
      <CategoryForm
        category={editingCategory}
        isOpen={isCategoryFormOpen}
        onClose={() => {
          setIsCategoryFormOpen(false);
          setEditingCategory(null);
        }}
        onSave={handleSaveCategory}
      />

      {/* User Form Modal */}
      <UserForm
        user={editingUser}
        isOpen={isUserFormOpen}
        onClose={() => {
          setIsUserFormOpen(false);
          setEditingUser(null);
        }}
        onSave={handleSaveUser}
      />

      {/* Order Details Modal */}
      <OrderDetailsModal
        order={selectedOrder}
        isOpen={isOrderDetailsOpen}
        onClose={() => {
          setIsOrderDetailsOpen(false);
          setSelectedOrder(null);
        }}
        onUpdateStatus={handleUpdateOrderStatus}
      />

      {/* Delete Confirmation Modal */}
      <DeleteConfirmModal
        isOpen={isDeleteModalOpen}
        title={
          productToDelete ? "تأكيد حذف المنتج" :
          categoryToDelete ? "تأكيد حذف التصنيف" :
          "تأكيد حذف المستخدم"
        }
        message={
          productToDelete
            ? `هل أنت متأكد من حذف المنتج "${productToDelete?.name}"؟ لا يمكن التراجع عن هذا الإجراء.`
            : categoryToDelete
            ? `هل أنت متأكد من حذف التصنيف "${categoryToDelete?.name}"؟ لا يمكن التراجع عن هذا الإجراء.`
            : `هل أنت متأكد من حذف المستخدم "${userToDelete?.name}"؟ لا يمكن التراجع عن هذا الإجراء.`
        }
        onConfirm={
          productToDelete ? confirmDeleteProduct :
          categoryToDelete ? confirmDeleteCategory :
          confirmDeleteUser
        }
        onCancel={() => {
          setIsDeleteModalOpen(false);
          setProductToDelete(null);
          setCategoryToDelete(null);
          setUserToDelete(null);
        }}
        isLoading={isDeleting}
      />
    </div>
  );
}
