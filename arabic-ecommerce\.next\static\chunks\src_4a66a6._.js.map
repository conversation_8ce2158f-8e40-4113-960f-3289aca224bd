{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/New%20folder%20%288%29/arabic-ecommerce/src/components/Navbar.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { useState } from 'react';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { useCart } from '@/contexts/CartContext';\nimport { LogOut, User, Settings, ShoppingBag } from 'lucide-react';\n\nexport default function Navbar() {\n  const { isAuthenticated, isAdmin, appUser, signOut } = useAuth();\n  const { items } = useCart();\n  const [showUserMenu, setShowUserMenu] = useState(false);\n\n  const cartItemsCount = items.reduce((total, item) => total + item.quantity, 0);\n\n  const handleSignOut = async () => {\n    await signOut();\n    setShowUserMenu(false);\n  };\n\n  return (\n    <nav className=\"bg-white shadow-lg border-b border-gray-200\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center h-16\">\n          {/* Logo */}\n          <div className=\"flex-shrink-0\">\n            <Link href=\"/\" className=\"text-2xl font-bold text-primary-600\">\n              🛍️ المتجر العربي\n            </Link>\n          </div>\n\n          {/* Navigation Links */}\n          <div className=\"hidden md:block\">\n            <div className=\"mr-10 flex items-baseline space-x-4 space-x-reverse\">\n              <Link\n                href=\"/\"\n                className=\"text-gray-700 hover:text-primary-600 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 flex items-center\"\n              >\n                🏠 الصفحة الرئيسية\n              </Link>\n              <Link\n                href=\"/products\"\n                className=\"text-gray-700 hover:text-primary-600 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 flex items-center\"\n              >\n                📦 المنتجات\n              </Link>\n              {isAdmin && (\n                <Link\n                  href=\"/admin\"\n                  className=\"text-gray-700 hover:text-primary-600 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 flex items-center\"\n                >\n                  ⚙️ لوحة التحكم\n                </Link>\n              )}\n            </div>\n          </div>\n\n          {/* Right side - Cart and Auth */}\n          <div className=\"flex items-center space-x-4 space-x-reverse\">\n            {/* Cart */}\n            <Link\n              href=\"/cart\"\n              className=\"relative text-gray-700 hover:text-primary-600 transition-colors duration-200\"\n            >\n              <span className=\"text-2xl\">🛒</span>\n              {cartItemsCount > 0 && (\n                <span className=\"absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center\">\n                  {cartItemsCount}\n                </span>\n              )}\n            </Link>\n\n            {/* Auth Links */}\n            {isAuthenticated ? (\n              <div className=\"relative\">\n                <button\n                  onClick={() => setShowUserMenu(!showUserMenu)}\n                  className=\"flex items-center space-x-2 space-x-reverse text-gray-700 hover:text-primary-600 px-3 py-1 rounded text-sm transition-colors duration-200\"\n                >\n                  <User className=\"h-4 w-4\" />\n                  <span>مرحباً، {appUser?.name || 'المستخدم'}</span>\n                </button>\n\n                {showUserMenu && (\n                  <div className=\"absolute left-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 border border-gray-200\">\n                    <Link\n                      href=\"/profile\"\n                      className=\"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                      onClick={() => setShowUserMenu(false)}\n                    >\n                      <User className=\"h-4 w-4 ml-2\" />\n                      الملف الشخصي\n                    </Link>\n                    <Link\n                      href=\"/orders\"\n                      className=\"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                      onClick={() => setShowUserMenu(false)}\n                    >\n                      <ShoppingBag className=\"h-4 w-4 ml-2\" />\n                      طلباتي\n                    </Link>\n                    {isAdmin && (\n                      <Link\n                        href=\"/admin\"\n                        className=\"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                        onClick={() => setShowUserMenu(false)}\n                      >\n                        <Settings className=\"h-4 w-4 ml-2\" />\n                        لوحة التحكم\n                      </Link>\n                    )}\n                    <hr className=\"my-1\" />\n                    <button\n                      onClick={handleSignOut}\n                      className=\"flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50\"\n                    >\n                      <LogOut className=\"h-4 w-4 ml-2\" />\n                      تسجيل الخروج\n                    </button>\n                  </div>\n                )}\n              </div>\n            ) : (\n              <div className=\"flex items-center space-x-2 space-x-reverse\">\n                <Link\n                  href=\"/auth/login\"\n                  className=\"text-gray-700 hover:text-primary-600 px-3 py-1 rounded text-sm transition-colors duration-200\"\n                >\n                  تسجيل الدخول\n                </Link>\n                <Link\n                  href=\"/auth/register\"\n                  className=\"bg-primary-600 hover:bg-primary-700 text-white px-3 py-1 rounded text-sm transition-colors duration-200\"\n                >\n                  إنشاء حساب\n                </Link>\n              </div>\n            )}\n          </div>\n\n          {/* Mobile menu button */}\n          <div className=\"md:hidden\">\n            <button\n              type=\"button\"\n              className=\"text-gray-700 hover:text-primary-600 focus:outline-none focus:text-primary-600\"\n            >\n              <svg className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\n              </svg>\n            </button>\n          </div>\n        </div>\n      </div>\n    </nav>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;;;AANA;;;;;;AAQe,SAAS;;IACtB,MAAM,EAAE,eAAe,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAC7D,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACxB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,iBAAiB,MAAM,MAAM,CAAC,CAAC,OAAO,OAAS,QAAQ,KAAK,QAAQ,EAAE;IAE5E,MAAM,gBAAgB;QACpB,MAAM;QACN,gBAAgB;IAClB;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;sCAAsC;;;;;;;;;;;kCAMjE,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;gCAGA,yBACC,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;kCAQP,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;kDAEV,6LAAC;wCAAK,WAAU;kDAAW;;;;;;oCAC1B,iBAAiB,mBAChB,6LAAC;wCAAK,WAAU;kDACb;;;;;;;;;;;;4BAMN,gCACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,SAAS,IAAM,gBAAgB,CAAC;wCAChC,WAAU;;0DAEV,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,6LAAC;;oDAAK;oDAAS,SAAS,QAAQ;;;;;;;;;;;;;oCAGjC,8BACC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,gBAAgB;;kEAE/B,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGnC,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,gBAAgB;;kEAE/B,6LAAC,uNAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;4CAGzC,yBACC,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,gBAAgB;;kEAE/B,6LAAC,6MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAIzC,6LAAC;gDAAG,WAAU;;;;;;0DACd,6LAAC;gDACC,SAAS;gDACT,WAAU;;kEAEV,6LAAC,6MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;qDAO3C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;kCAQP,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,MAAK;4BACL,WAAU;sCAEV,cAAA,6LAAC;gCAAI,WAAU;gCAAU,MAAK;gCAAO,SAAQ;gCAAY,QAAO;0CAC9D,cAAA,6LAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQrF;GAnJwB;;QACiC,kIAAA,CAAA,UAAO;QAC5C,kIAAA,CAAA,UAAO;;;KAFH"}}, {"offset": {"line": 355, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 361, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/New%20folder%20%288%29/arabic-ecommerce/src/app/cart/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useRouter } from 'next/navigation';\nimport Link from 'next/link';\nimport Image from 'next/image';\nimport Navbar from '@/components/Navbar';\nimport { useCart } from '@/contexts/CartContext';\n\nexport default function CartPage() {\n  const router = useRouter();\n  const { items: cartItems, updateQuantity, removeFromCart, totalPrice } = useCart();\n\n  const subtotal = cartItems.reduce((sum, item) => sum + (item.product.price * item.quantity), 0);\n  const shipping = 25; // Fixed shipping cost\n  const total = subtotal + shipping;\n\n  const handleCheckout = () => {\n    if (cartItems.length === 0) {\n      alert('السلة فارغة! أضف منتجات أولاً.');\n      return;\n    }\n    router.push('/checkout');\n  };\n\n  if (cartItems.length === 0) {\n    return (\n      <div className=\"min-h-screen bg-gray-50\">\n        <Navbar />\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16\">\n          <div className=\"text-center\">\n            <div className=\"text-6xl mb-4\">🛒</div>\n            <h2 className=\"text-2xl font-bold text-gray-800 mb-4\">\n              سلة التسوق فارغة\n            </h2>\n            <p className=\"text-gray-600 mb-8\">\n              لم تقم بإضافة أي منتجات إلى سلة التسوق بعد\n            </p>\n            <Link\n              href=\"/products\"\n              className=\"inline-block bg-primary-600 hover:bg-primary-700 text-white font-bold py-3 px-8 rounded-lg transition-colors duration-200\"\n            >\n              تصفح المنتجات\n            </Link>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Navbar />\n\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <h1 className=\"text-3xl font-bold text-gray-800 mb-8\">سلة التسوق</h1>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n          {/* Cart Items */}\n          <div className=\"lg:col-span-2\">\n            <div className=\"bg-white rounded-lg shadow-md overflow-hidden\">\n              <div className=\"px-6 py-4 border-b border-gray-200\">\n                <h2 className=\"text-lg font-semibold text-gray-800\">\n                  المنتجات ({cartItems.length})\n                </h2>\n              </div>\n\n              <div className=\"divide-y divide-gray-200\">\n                {cartItems.map((item) => (\n                  <div key={item.id} className=\"p-6\">\n                    <div className=\"flex items-center\">\n                      <div className=\"h-20 w-20 bg-gray-200 rounded-lg flex-shrink-0 overflow-hidden\">\n                        <Image\n                          src={item.product.image}\n                          alt={item.product.name}\n                          width={80}\n                          height={80}\n                          className=\"w-full h-full object-cover\"\n                        />\n                      </div>\n\n                      <div className=\"mr-4 flex-1\">\n                        <h3 className=\"text-lg font-medium text-gray-800\">\n                          {item.product.name}\n                        </h3>\n                        <p className=\"text-gray-600 text-sm mt-1\">\n                          {item.product.category}\n                        </p>\n                        <p className=\"text-primary-600 font-bold mt-2\">\n                          {item.product.price.toLocaleString('ar-SA')} ر.س\n                        </p>\n                      </div>\n\n                      <div className=\"flex items-center space-x-2 space-x-reverse\">\n                        <button\n                          onClick={() => updateQuantity(item.id, item.quantity - 1)}\n                          className=\"w-8 h-8 rounded-full bg-gray-200 hover:bg-gray-300 flex items-center justify-center\"\n                        >\n                          -\n                        </button>\n                        <span className=\"w-12 text-center font-medium\">\n                          {item.quantity}\n                        </span>\n                        <button\n                          onClick={() => updateQuantity(item.id, item.quantity + 1)}\n                          className=\"w-8 h-8 rounded-full bg-gray-200 hover:bg-gray-300 flex items-center justify-center\"\n                        >\n                          +\n                        </button>\n                      </div>\n\n                      <button\n                        onClick={() => removeFromCart(item.id)}\n                        className=\"mr-4 text-red-500 hover:text-red-700\"\n                      >\n                        🗑️\n                      </button>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n          </div>\n\n          {/* Order Summary */}\n          <div className=\"lg:col-span-1\">\n            <div className=\"bg-white rounded-lg shadow-md p-6 sticky top-4\">\n              <h2 className=\"text-lg font-semibold text-gray-800 mb-4\">\n                ملخص الطلب\n              </h2>\n\n              <div className=\"space-y-3\">\n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-600\">المجموع الفرعي</span>\n                  <span className=\"font-medium\">\n                    {subtotal.toLocaleString('ar-SA')} ر.س\n                  </span>\n                </div>\n\n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-600\">الشحن</span>\n                  <span className=\"font-medium\">\n                    {shipping.toLocaleString('ar-SA')} ر.س\n                  </span>\n                </div>\n\n                <div className=\"border-t border-gray-200 pt-3\">\n                  <div className=\"flex justify-between\">\n                    <span className=\"text-lg font-semibold\">المجموع الكلي</span>\n                    <span className=\"text-lg font-bold text-primary-600\">\n                      {total.toLocaleString('ar-SA')} ر.س\n                    </span>\n                  </div>\n                </div>\n              </div>\n\n              <button\n                onClick={handleCheckout}\n                className=\"w-full bg-primary-600 hover:bg-primary-700 text-white font-bold py-3 px-4 rounded-lg mt-6 transition-colors duration-200\"\n              >\n                متابعة للدفع\n              </button>\n\n              <Link\n                href=\"/products\"\n                className=\"block text-center text-primary-600 hover:text-primary-700 mt-4 font-medium\"\n              >\n                متابعة التسوق\n              </Link>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;;;AANA;;;;;;AAQe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,OAAO,SAAS,EAAE,cAAc,EAAE,cAAc,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAE/E,MAAM,WAAW,UAAU,MAAM,CAAC,CAAC,KAAK,OAAS,MAAO,KAAK,OAAO,CAAC,KAAK,GAAG,KAAK,QAAQ,EAAG;IAC7F,MAAM,WAAW,IAAI,sBAAsB;IAC3C,MAAM,QAAQ,WAAW;IAEzB,MAAM,iBAAiB;QACrB,IAAI,UAAU,MAAM,KAAK,GAAG;YAC1B,MAAM;YACN;QACF;QACA,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,UAAU,MAAM,KAAK,GAAG;QAC1B,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC,+HAAA,CAAA,UAAM;;;;;8BACP,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CAAgB;;;;;;0CAC/B,6LAAC;gCAAG,WAAU;0CAAwC;;;;;;0CAGtD,6LAAC;gCAAE,WAAU;0CAAqB;;;;;;0CAGlC,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;IAOX;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,+HAAA,CAAA,UAAM;;;;;0BAEP,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAwC;;;;;;kCAEtD,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAG,WAAU;;oDAAsC;oDACvC,UAAU,MAAM;oDAAC;;;;;;;;;;;;sDAIhC,6LAAC;4CAAI,WAAU;sDACZ,UAAU,GAAG,CAAC,CAAC,qBACd,6LAAC;oDAAkB,WAAU;8DAC3B,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;oEACJ,KAAK,KAAK,OAAO,CAAC,KAAK;oEACvB,KAAK,KAAK,OAAO,CAAC,IAAI;oEACtB,OAAO;oEACP,QAAQ;oEACR,WAAU;;;;;;;;;;;0EAId,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAG,WAAU;kFACX,KAAK,OAAO,CAAC,IAAI;;;;;;kFAEpB,6LAAC;wEAAE,WAAU;kFACV,KAAK,OAAO,CAAC,QAAQ;;;;;;kFAExB,6LAAC;wEAAE,WAAU;;4EACV,KAAK,OAAO,CAAC,KAAK,CAAC,cAAc,CAAC;4EAAS;;;;;;;;;;;;;0EAIhD,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEACC,SAAS,IAAM,eAAe,KAAK,EAAE,EAAE,KAAK,QAAQ,GAAG;wEACvD,WAAU;kFACX;;;;;;kFAGD,6LAAC;wEAAK,WAAU;kFACb,KAAK,QAAQ;;;;;;kFAEhB,6LAAC;wEACC,SAAS,IAAM,eAAe,KAAK,EAAE,EAAE,KAAK,QAAQ,GAAG;wEACvD,WAAU;kFACX;;;;;;;;;;;;0EAKH,6LAAC;gEACC,SAAS,IAAM,eAAe,KAAK,EAAE;gEACrC,WAAU;0EACX;;;;;;;;;;;;mDA7CK,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;0CAwDzB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA2C;;;;;;sDAIzD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,6LAAC;4DAAK,WAAU;;gEACb,SAAS,cAAc,CAAC;gEAAS;;;;;;;;;;;;;8DAItC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,6LAAC;4DAAK,WAAU;;gEACb,SAAS,cAAc,CAAC;gEAAS;;;;;;;;;;;;;8DAItC,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EAAwB;;;;;;0EACxC,6LAAC;gEAAK,WAAU;;oEACb,MAAM,cAAc,CAAC;oEAAS;;;;;;;;;;;;;;;;;;;;;;;;sDAMvC,6LAAC;4CACC,SAAS;4CACT,WAAU;sDACX;;;;;;sDAID,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;GAtKwB;;QACP,qIAAA,CAAA,YAAS;QACiD,kIAAA,CAAA,UAAO;;;KAF1D"}}, {"offset": {"line": 808, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}