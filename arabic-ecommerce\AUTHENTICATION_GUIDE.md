# 🔐 دليل نظام المصادقة - Authentication Guide

## ✅ **تم تطبيق نظام المصادقة الكامل!**

---

## 🎯 **الميزات المطبقة:**

### **1️⃣ صفحات المصادقة:**
- ✅ **تسجيل الدخول**: `/auth/login`
- ✅ **إنشاء حساب**: `/auth/register`
- ✅ **نسيان كلمة المرور**: `/auth/forgot-password`
- ✅ **إعادة تعيين كلمة المرور**: `/auth/reset-password`
- ✅ **الملف الشخصي**: `/profile`

### **2️⃣ حماية الصفحات:**
- ✅ **حماية لوحة التحكم**: صلاحيات مدير فقط
- ✅ **حماية الملف الشخصي**: مستخدمين مسجلين فقط
- ✅ **إعادة توجيه تلقائية**: للمستخدمين غير المصرحين

### **3️⃣ إدارة الجلسات:**
- ✅ **تسجيل دخول آمن**: مع Supabase Auth
- ✅ **تسجيل خروج**: مع مسح الجلسة
- ✅ **تذكر المستخدم**: جلسات دائمة
- ✅ **تحديث تلقائي**: لحالة المصادقة

### **4️⃣ واجهة المستخدم:**
- ✅ **شريط تنقل ديناميكي**: يتغير حسب حالة المصادقة
- ✅ **قائمة المستخدم**: مع خيارات الملف الشخصي والخروج
- ✅ **رسائل خطأ عربية**: واضحة ومفهومة
- ✅ **تصميم متجاوب**: يعمل على جميع الأجهزة

---

## 🚀 **كيفية الاستخدام:**

### **📝 إنشاء حساب جديد:**
1. اذهب إلى `/auth/register`
2. أدخل البيانات المطلوبة:
   - الاسم الكامل (مطلوب)
   - البريد الإلكتروني (مطلوب)
   - رقم الهاتف (اختياري)
   - كلمة المرور (6 أحرف على الأقل)
   - تأكيد كلمة المرور
3. اقبل الشروط والأحكام
4. اضغط "إنشاء حساب"

### **🔑 تسجيل الدخول:**
1. اذهب إلى `/auth/login`
2. أدخل البريد الإلكتروني وكلمة المرور
3. اختر "تذكرني" (اختياري)
4. اضغط "تسجيل الدخول"

### **🔄 إعادة تعيين كلمة المرور:**
1. في صفحة تسجيل الدخول، اضغط "نسيت كلمة المرور؟"
2. أدخل بريدك الإلكتروني
3. تحقق من بريدك الإلكتروني للحصول على الرابط
4. اتبع الرابط وأدخل كلمة المرور الجديدة

### **👤 إدارة الملف الشخصي:**
1. بعد تسجيل الدخول، اضغط على اسمك في شريط التنقل
2. اختر "الملف الشخصي"
3. اضغط "تعديل" لتحديث بياناتك
4. احفظ التغييرات

---

## 🛡️ **مستويات الحماية:**

### **🔓 صفحات عامة (بدون حماية):**
- الصفحة الرئيسية `/`
- صفحة المنتجات `/products`
- صفحات المصادقة `/auth/*`

### **🔒 صفحات محمية (مستخدمين مسجلين):**
- الملف الشخصي `/profile`
- طلباتي `/orders`
- سلة التسوق `/cart` (للشراء)

### **🛡️ صفحات المدراء (صلاحيات خاصة):**
- لوحة التحكم `/admin`
- إدارة المنتجات `/admin/products`
- إدارة المستخدمين `/admin/users`

---

## 🔧 **للمطورين:**

### **استخدام Hook المصادقة:**
```typescript
import { useAuth } from '@/contexts/AuthContext';

function MyComponent() {
  const { 
    user,           // بيانات Supabase
    appUser,        // بيانات التطبيق
    isAuthenticated, // هل المستخدم مسجل دخوله؟
    isAdmin,        // هل المستخدم مدير؟
    loading,        // هل يتم التحميل؟
    signIn,         // دالة تسجيل الدخول
    signOut,        // دالة تسجيل الخروج
    signUp          // دالة إنشاء حساب
  } = useAuth();

  if (loading) return <div>جاري التحميل...</div>;
  
  return (
    <div>
      {isAuthenticated ? (
        <p>مرحباً {appUser?.name}</p>
      ) : (
        <p>يرجى تسجيل الدخول</p>
      )}
    </div>
  );
}
```

### **حماية المكونات:**
```typescript
import { AuthRequired, AdminRequired } from '@/components/ProtectedRoute';

// للمستخدمين المسجلين فقط
function UserOnlyComponent() {
  return (
    <AuthRequired>
      <div>محتوى للمستخدمين المسجلين فقط</div>
    </AuthRequired>
  );
}

// للمدراء فقط
function AdminOnlyComponent() {
  return (
    <AdminRequired>
      <div>محتوى للمدراء فقط</div>
    </AdminRequired>
  );
}
```

### **التحقق من الصلاحيات:**
```typescript
import { useRequireAuth, useRequireAdmin } from '@/contexts/AuthContext';

// إعادة توجيه تلقائية للمستخدمين غير المسجلين
function ProtectedPage() {
  const { isAuthenticated, loading } = useRequireAuth();
  
  if (loading) return <div>جاري التحميل...</div>;
  
  return <div>صفحة محمية</div>;
}

// إعادة توجيه تلقائية لغير المدراء
function AdminPage() {
  const { isAdmin, loading } = useRequireAdmin();
  
  if (loading) return <div>جاري التحميل...</div>;
  
  return <div>صفحة المدراء</div>;
}
```

---

## ⚙️ **الإعدادات:**

### **متغيرات البيئة المطلوبة:**
```bash
# في ملف .env.local
NEXT_PUBLIC_USE_DATABASE=false  # للبيانات الوهمية
# أو
NEXT_PUBLIC_USE_DATABASE=true   # لقاعدة البيانات الحقيقية

# إعدادات Supabase (عند استخدام قاعدة البيانات الحقيقية)
NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here
```

### **إعداد Supabase للمصادقة:**
1. إنشاء مشروع جديد في [Supabase](https://supabase.com)
2. تفعيل المصادقة في لوحة التحكم
3. إعداد موفري المصادقة (Google, Facebook, إلخ)
4. تحديث متغيرات البيئة
5. تشغيل ملفات SQL لإنشاء الجداول

---

## 🐛 **استكشاف الأخطاء:**

### **خطأ: "Missing Supabase environment variables"**
**الحل**: تأكد من إعداد متغيرات البيئة في `.env.local`

### **خطأ: "Invalid login credentials"**
**الحل**: تحقق من صحة البريد الإلكتروني وكلمة المرور

### **خطأ: "Email not confirmed"**
**الحل**: تحقق من بريدك الإلكتروني وأكد الحساب

### **خطأ: "Row Level Security policy violation"**
**الحل**: تأكد من تطبيق سياسات RLS في Supabase

---

## 🎉 **النتيجة:**

الآن لديك نظام مصادقة كامل وآمن يدعم:
- ✅ تسجيل الدخول والخروج
- ✅ إنشاء حسابات جديدة
- ✅ إعادة تعيين كلمة المرور
- ✅ حماية الصفحات والمكونات
- ✅ إدارة الصلاحيات
- ✅ واجهة مستخدم عربية جميلة

**الخطوة التالية: تكامل بوابات الدفع! 💳**
