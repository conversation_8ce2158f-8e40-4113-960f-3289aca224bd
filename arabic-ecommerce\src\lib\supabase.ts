import { createClient } from '@supabase/supabase-js';
import { Database } from '@/types/database';

// التحقق من وجود متغيرات البيئة
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

// إذا لم تكن متغيرات البيئة موجودة، استخدم قيم افتراضية للتطوير
const defaultUrl = 'https://placeholder.supabase.co';
const defaultKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBsYWNlaG9sZGVyIiwicm9sZSI6ImFub24iLCJpYXQiOjE2NDUxOTI3MjAsImV4cCI6MTk2MDc2ODcyMH0.placeholder';

// Create a single supabase client for interacting with your database
export const supabase = createClient<Database>(
  supabaseUrl || defaultUrl,
  supabaseAnonKey || defaultKey
);

// Admin client with service role key (for server-side operations)
export const supabaseAdmin = createClient<Database>(
  supabaseUrl || defaultUrl,
  process.env.SUPABASE_SERVICE_ROLE_KEY || defaultKey,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
);

// Helper function to handle Supabase errors
export function handleSupabaseError(error: any) {
  console.error('Supabase error:', error);

  if (error?.message) {
    return error.message;
  }

  return 'حدث خطأ غير متوقع';
}

// Helper function to format Supabase response
export function formatSupabaseResponse<T>(data: T[] | null, error: any) {
  if (error) {
    throw new Error(handleSupabaseError(error));
  }

  return data || [];
}
