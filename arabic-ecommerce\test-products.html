<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار المنتجات</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            direction: rtl;
        }
        .product {
            border: 1px solid #ddd;
            padding: 20px;
            margin: 10px 0;
            border-radius: 8px;
            background: white;
        }
        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
        .error {
            color: red;
            padding: 20px;
            background: #ffe6e6;
            border-radius: 8px;
        }
        .success {
            color: green;
            padding: 20px;
            background: #e6ffe6;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <h1>اختبار تحميل المنتجات</h1>
    
    <div id="status" class="loading">جاري التحميل...</div>
    <div id="products"></div>

    <script>
        // محاكاة البيانات الوهمية
        const sampleProducts = [
            {
                id: '1',
                name: 'هاتف ذكي متطور',
                description: 'هاتف ذكي بمواصفات عالية وكاميرا متقدمة',
                price: 2500,
                image: '/images/phone.jpg',
                category: 'إلكترونيات',
                stock: 15,
                featured: true,
            },
            {
                id: '2',
                name: 'لابتوب للألعاب',
                description: 'لابتوب قوي مخصص للألعاب والتصميم',
                price: 4500,
                image: '/images/laptop.jpg',
                category: 'إلكترونيات',
                stock: 8,
                featured: true,
            },
            {
                id: '3',
                name: 'ساعة ذكية',
                description: 'ساعة ذكية لتتبع اللياقة البدنية',
                price: 800,
                image: '/images/watch.jpg',
                category: 'إكسسوارات',
                stock: 25,
                featured: false,
            },
            {
                id: '4',
                name: 'سماعات لاسلكية',
                description: 'سماعات بلوتوث عالية الجودة',
                price: 350,
                image: '/images/headphones.jpg',
                category: 'إكسسوارات',
                stock: 30,
                featured: true,
            }
        ];

        function loadProducts() {
            try {
                // محاكاة تحميل من localStorage
                const savedProducts = localStorage.getItem('ecommerce-products');
                let products = [];
                
                if (savedProducts) {
                    products = JSON.parse(savedProducts);
                    document.getElementById('status').innerHTML = '<div class="success">تم تحميل المنتجات من localStorage</div>';
                } else {
                    // استخدام البيانات الوهمية
                    products = sampleProducts;
                    localStorage.setItem('ecommerce-products', JSON.stringify(products));
                    document.getElementById('status').innerHTML = '<div class="success">تم تحميل البيانات الوهمية وحفظها في localStorage</div>';
                }

                displayProducts(products);
            } catch (error) {
                document.getElementById('status').innerHTML = '<div class="error">خطأ في تحميل المنتجات: ' + error.message + '</div>';
            }
        }

        function displayProducts(products) {
            const productsDiv = document.getElementById('products');
            
            if (products.length === 0) {
                productsDiv.innerHTML = '<p>لا توجد منتجات</p>';
                return;
            }

            let html = '<h2>المنتجات المحملة (' + products.length + '):</h2>';
            
            products.forEach(product => {
                html += `
                    <div class="product">
                        <h3>${product.name}</h3>
                        <p><strong>الوصف:</strong> ${product.description}</p>
                        <p><strong>السعر:</strong> ${product.price} ريال</p>
                        <p><strong>التصنيف:</strong> ${product.category}</p>
                        <p><strong>المخزون:</strong> ${product.stock}</p>
                        <p><strong>مميز:</strong> ${product.featured ? 'نعم' : 'لا'}</p>
                    </div>
                `;
            });
            
            productsDiv.innerHTML = html;
        }

        function clearData() {
            localStorage.removeItem('ecommerce-products');
            document.getElementById('status').innerHTML = '<div class="success">تم مسح البيانات من localStorage</div>';
            document.getElementById('products').innerHTML = '';
        }

        function testNextJSAPI() {
            fetch('http://localhost:3001/api/products')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('status').innerHTML = '<div class="success">تم تحميل المنتجات من API</div>';
                    displayProducts(data);
                })
                .catch(error => {
                    document.getElementById('status').innerHTML = '<div class="error">خطأ في API: ' + error.message + '</div>';
                });
        }

        // تحميل المنتجات عند تحميل الصفحة
        loadProducts();
    </script>

    <div style="margin-top: 20px; padding: 20px; background: #f0f0f0; border-radius: 8px;">
        <h3>أدوات الاختبار:</h3>
        <button onclick="loadProducts()" style="margin: 5px; padding: 10px;">إعادة تحميل المنتجات</button>
        <button onclick="clearData()" style="margin: 5px; padding: 10px;">مسح البيانات</button>
        <button onclick="testNextJSAPI()" style="margin: 5px; padding: 10px;">اختبار API</button>
        <button onclick="window.open('http://localhost:3001/products', '_blank')" style="margin: 5px; padding: 10px;">فتح صفحة المنتجات</button>
    </div>
</body>
</html>
