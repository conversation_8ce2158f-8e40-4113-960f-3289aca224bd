{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/New%20folder%20%288%29/arabic-ecommerce/src/components/Navbar.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { useState } from 'react';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { useCart } from '@/contexts/CartContext';\nimport { LogOut, User, Settings, ShoppingBag } from 'lucide-react';\n\nexport default function Navbar() {\n  const { isAuthenticated, isAdmin, appUser, signOut } = useAuth();\n  const { items } = useCart();\n  const [showUserMenu, setShowUserMenu] = useState(false);\n\n  const cartItemsCount = items.reduce((total, item) => total + item.quantity, 0);\n\n  const handleSignOut = async () => {\n    await signOut();\n    setShowUserMenu(false);\n  };\n\n  return (\n    <nav className=\"bg-white shadow-lg border-b border-gray-200\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center h-16\">\n          {/* Logo */}\n          <div className=\"flex-shrink-0\">\n            <Link href=\"/\" className=\"text-2xl font-bold text-primary-600\">\n              🛍️ المتجر العربي\n            </Link>\n          </div>\n\n          {/* Navigation Links */}\n          <div className=\"hidden md:block\">\n            <div className=\"mr-10 flex items-baseline space-x-4 space-x-reverse\">\n              <Link\n                href=\"/\"\n                className=\"text-gray-700 hover:text-primary-600 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 flex items-center\"\n              >\n                🏠 الصفحة الرئيسية\n              </Link>\n              <Link\n                href=\"/products\"\n                className=\"text-gray-700 hover:text-primary-600 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 flex items-center\"\n              >\n                📦 المنتجات\n              </Link>\n              {isAdmin && (\n                <Link\n                  href=\"/admin\"\n                  className=\"text-gray-700 hover:text-primary-600 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 flex items-center\"\n                >\n                  ⚙️ لوحة التحكم\n                </Link>\n              )}\n            </div>\n          </div>\n\n          {/* Right side - Cart and Auth */}\n          <div className=\"flex items-center space-x-4 space-x-reverse\">\n            {/* Cart */}\n            <Link\n              href=\"/cart\"\n              className=\"relative text-gray-700 hover:text-primary-600 transition-colors duration-200\"\n            >\n              <span className=\"text-2xl\">🛒</span>\n              {cartItemsCount > 0 && (\n                <span className=\"absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center\">\n                  {cartItemsCount}\n                </span>\n              )}\n            </Link>\n\n            {/* Auth Links */}\n            {isAuthenticated ? (\n              <div className=\"relative\">\n                <button\n                  onClick={() => setShowUserMenu(!showUserMenu)}\n                  className=\"flex items-center space-x-2 space-x-reverse text-gray-700 hover:text-primary-600 px-3 py-1 rounded text-sm transition-colors duration-200\"\n                >\n                  <User className=\"h-4 w-4\" />\n                  <span>مرحباً، {appUser?.name || 'المستخدم'}</span>\n                </button>\n\n                {showUserMenu && (\n                  <div className=\"absolute left-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 border border-gray-200\">\n                    <Link\n                      href=\"/profile\"\n                      className=\"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                      onClick={() => setShowUserMenu(false)}\n                    >\n                      <User className=\"h-4 w-4 ml-2\" />\n                      الملف الشخصي\n                    </Link>\n                    <Link\n                      href=\"/orders\"\n                      className=\"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                      onClick={() => setShowUserMenu(false)}\n                    >\n                      <ShoppingBag className=\"h-4 w-4 ml-2\" />\n                      طلباتي\n                    </Link>\n                    {isAdmin && (\n                      <Link\n                        href=\"/admin\"\n                        className=\"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                        onClick={() => setShowUserMenu(false)}\n                      >\n                        <Settings className=\"h-4 w-4 ml-2\" />\n                        لوحة التحكم\n                      </Link>\n                    )}\n                    <hr className=\"my-1\" />\n                    <button\n                      onClick={handleSignOut}\n                      className=\"flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50\"\n                    >\n                      <LogOut className=\"h-4 w-4 ml-2\" />\n                      تسجيل الخروج\n                    </button>\n                  </div>\n                )}\n              </div>\n            ) : (\n              <div className=\"flex items-center space-x-2 space-x-reverse\">\n                <Link\n                  href=\"/auth/login\"\n                  className=\"text-gray-700 hover:text-primary-600 px-3 py-1 rounded text-sm transition-colors duration-200\"\n                >\n                  تسجيل الدخول\n                </Link>\n                <Link\n                  href=\"/auth/register\"\n                  className=\"bg-primary-600 hover:bg-primary-700 text-white px-3 py-1 rounded text-sm transition-colors duration-200\"\n                >\n                  إنشاء حساب\n                </Link>\n              </div>\n            )}\n          </div>\n\n          {/* Mobile menu button */}\n          <div className=\"md:hidden\">\n            <button\n              type=\"button\"\n              className=\"text-gray-700 hover:text-primary-600 focus:outline-none focus:text-primary-600\"\n            >\n              <svg className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\n              </svg>\n            </button>\n          </div>\n        </div>\n      </div>\n    </nav>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;;;AANA;;;;;;AAQe,SAAS;;IACtB,MAAM,EAAE,eAAe,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAC7D,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACxB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,iBAAiB,MAAM,MAAM,CAAC,CAAC,OAAO,OAAS,QAAQ,KAAK,QAAQ,EAAE;IAE5E,MAAM,gBAAgB;QACpB,MAAM;QACN,gBAAgB;IAClB;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;sCAAsC;;;;;;;;;;;kCAMjE,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;gCAGA,yBACC,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;kCAQP,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;kDAEV,6LAAC;wCAAK,WAAU;kDAAW;;;;;;oCAC1B,iBAAiB,mBAChB,6LAAC;wCAAK,WAAU;kDACb;;;;;;;;;;;;4BAMN,gCACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,SAAS,IAAM,gBAAgB,CAAC;wCAChC,WAAU;;0DAEV,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,6LAAC;;oDAAK;oDAAS,SAAS,QAAQ;;;;;;;;;;;;;oCAGjC,8BACC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,gBAAgB;;kEAE/B,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGnC,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,gBAAgB;;kEAE/B,6LAAC,uNAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;4CAGzC,yBACC,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,gBAAgB;;kEAE/B,6LAAC,6MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAIzC,6LAAC;gDAAG,WAAU;;;;;;0DACd,6LAAC;gDACC,SAAS;gDACT,WAAU;;kEAEV,6LAAC,6MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;qDAO3C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;kCAQP,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,MAAK;4BACL,WAAU;sCAEV,cAAA,6LAAC;gCAAI,WAAU;gCAAU,MAAK;gCAAO,SAAQ;gCAAY,QAAO;0CAC9D,cAAA,6LAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQrF;GAnJwB;;QACiC,kIAAA,CAAA,UAAO;QAC5C,kIAAA,CAAA,UAAO;;;KAFH"}}, {"offset": {"line": 355, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 361, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/New%20folder%20%288%29/arabic-ecommerce/src/components/ProductCard.tsx"], "sourcesContent": ["'use client';\n\nimport { Product } from '@/types';\nimport Image from 'next/image';\nimport Link from 'next/link';\n\ninterface ProductCardProps {\n  product: Product;\n  onAddToCart?: (productId: string) => void;\n}\n\nexport default function ProductCard({ product, onAddToCart }: ProductCardProps) {\n  const handleAddToCart = () => {\n    if (onAddToCart) {\n      onAddToCart(product.id);\n    }\n  };\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 overflow-hidden\">\n      {/* Product Image */}\n      <div className=\"relative h-48 bg-gray-200\">\n        <Image\n          src={product.image}\n          alt={product.name}\n          fill\n          className=\"object-cover\"\n          onError={(e) => {\n            // Fallback image if the original fails to load\n            const target = e.target as HTMLImageElement;\n            target.src = '/images/placeholder.jpg';\n          }}\n        />\n        {product.featured && (\n          <div className=\"absolute top-2 right-2 bg-yellow-500 text-white px-2 py-1 rounded text-xs font-bold\">\n            مميز\n          </div>\n        )}\n        {product.stock === 0 && (\n          <div className=\"absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center\">\n            <span className=\"text-white font-bold text-lg\">نفد المخزون</span>\n          </div>\n        )}\n      </div>\n\n      {/* Product Info */}\n      <div className=\"p-4\">\n        <div className=\"mb-2\">\n          <span className=\"text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded\">\n            {product.category}\n          </span>\n        </div>\n        \n        <h3 className=\"text-lg font-semibold text-gray-800 mb-2 line-clamp-2\">\n          {product.name}\n        </h3>\n        \n        <p className=\"text-gray-600 text-sm mb-3 line-clamp-2\">\n          {product.description}\n        </p>\n\n        <div className=\"flex items-center justify-between mb-3\">\n          <span className=\"text-2xl font-bold text-primary-600\">\n            {product.price.toLocaleString('ar-SA')} ر.س\n          </span>\n          <span className=\"text-sm text-gray-500\">\n            متوفر: {product.stock}\n          </span>\n        </div>\n\n        {/* Action Buttons */}\n        <div className=\"flex space-x-2 space-x-reverse\">\n          <button\n            onClick={handleAddToCart}\n            disabled={product.stock === 0}\n            className={`flex-1 py-2 px-4 rounded-lg font-medium transition-colors duration-200 ${\n              product.stock === 0\n                ? 'bg-gray-300 text-gray-500 cursor-not-allowed'\n                : 'bg-primary-600 hover:bg-primary-700 text-white'\n            }`}\n          >\n            {product.stock === 0 ? 'نفد المخزون' : 'أضف للسلة'}\n          </button>\n          \n          <Link\n            href={`/products/${product.id}`}\n            className=\"px-4 py-2 border border-primary-600 text-primary-600 hover:bg-primary-600 hover:text-white rounded-lg font-medium transition-colors duration-200\"\n          >\n            عرض\n          </Link>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAJA;;;;AAWe,SAAS,YAAY,EAAE,OAAO,EAAE,WAAW,EAAoB;IAC5E,MAAM,kBAAkB;QACtB,IAAI,aAAa;YACf,YAAY,QAAQ,EAAE;QACxB;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,gIAAA,CAAA,UAAK;wBACJ,KAAK,QAAQ,KAAK;wBAClB,KAAK,QAAQ,IAAI;wBACjB,IAAI;wBACJ,WAAU;wBACV,SAAS,CAAC;4BACR,+CAA+C;4BAC/C,MAAM,SAAS,EAAE,MAAM;4BACvB,OAAO,GAAG,GAAG;wBACf;;;;;;oBAED,QAAQ,QAAQ,kBACf,6LAAC;wBAAI,WAAU;kCAAsF;;;;;;oBAItG,QAAQ,KAAK,KAAK,mBACjB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAK,WAAU;sCAA+B;;;;;;;;;;;;;;;;;0BAMrD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAK,WAAU;sCACb,QAAQ,QAAQ;;;;;;;;;;;kCAIrB,6LAAC;wBAAG,WAAU;kCACX,QAAQ,IAAI;;;;;;kCAGf,6LAAC;wBAAE,WAAU;kCACV,QAAQ,WAAW;;;;;;kCAGtB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;;oCACb,QAAQ,KAAK,CAAC,cAAc,CAAC;oCAAS;;;;;;;0CAEzC,6LAAC;gCAAK,WAAU;;oCAAwB;oCAC9B,QAAQ,KAAK;;;;;;;;;;;;;kCAKzB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS;gCACT,UAAU,QAAQ,KAAK,KAAK;gCAC5B,WAAW,CAAC,uEAAuE,EACjF,QAAQ,KAAK,KAAK,IACd,iDACA,kDACJ;0CAED,QAAQ,KAAK,KAAK,IAAI,gBAAgB;;;;;;0CAGzC,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAM,CAAC,UAAU,EAAE,QAAQ,EAAE,EAAE;gCAC/B,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;AAOX;KAnFwB"}}, {"offset": {"line": 539, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 545, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/New%20folder%20%288%29/arabic-ecommerce/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport Navbar from '@/components/Navbar';\nimport ProductCard from '@/components/ProductCard';\nimport { useProducts } from '@/contexts/ProductContext';\nimport Link from 'next/link';\n\nexport default function Home() {\n  const { getFeaturedProducts, isLoading } = useProducts();\n  const featuredProducts = getFeaturedProducts();\n\n  const handleAddToCart = (productId: string) => {\n    // TODO: Implement add to cart functionality\n    console.log('Adding product to cart:', productId);\n    alert('تم إضافة المنتج إلى السلة!');\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Navbar />\n\n      {/* Hero Section */}\n      <section className=\"bg-gradient-to-r from-primary-600 to-secondary-600 text-white py-20\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n          <h1 className=\"text-4xl md:text-6xl font-bold mb-6\">\n            مرحباً بك في المتجر العربي\n          </h1>\n          <p className=\"text-xl md:text-2xl mb-8 opacity-90\">\n            اكتشف أفضل المنتجات بأسعار مميزة وجودة عالية\n          </p>\n          <Link\n            href=\"/products\"\n            className=\"inline-block bg-white text-primary-600 font-bold py-3 px-8 rounded-lg hover:bg-gray-100 transition-colors duration-200 text-lg\"\n          >\n            تصفح المنتجات 🛍️\n          </Link>\n        </div>\n      </section>\n\n      {/* Features Section */}\n      <section className=\"py-16 bg-white\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n            <div className=\"text-center\">\n              <div className=\"text-4xl mb-4\">🚚</div>\n              <h3 className=\"text-xl font-semibold mb-2\">شحن سريع</h3>\n              <p className=\"text-gray-600\">توصيل سريع لجميع أنحاء المملكة</p>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-4xl mb-4\">💳</div>\n              <h3 className=\"text-xl font-semibold mb-2\">دفع آمن</h3>\n              <p className=\"text-gray-600\">طرق دفع متعددة وآمنة</p>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-4xl mb-4\">🔄</div>\n              <h3 className=\"text-xl font-semibold mb-2\">إرجاع مجاني</h3>\n              <p className=\"text-gray-600\">إمكانية الإرجاع خلال 30 يوم</p>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Featured Products */}\n      <section className=\"py-16\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-12\">\n            <h2 className=\"text-3xl font-bold text-gray-800 mb-4\">\n              المنتجات المميزة\n            </h2>\n            <p className=\"text-gray-600 text-lg\">\n              اكتشف أفضل منتجاتنا الأكثر مبيعاً\n            </p>\n          </div>\n\n          {isLoading ? (\n            <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\">\n              {[...Array(4)].map((_, index) => (\n                <div key={index} className=\"bg-white rounded-lg shadow-md overflow-hidden animate-pulse\">\n                  <div className=\"h-48 bg-gray-200\"></div>\n                  <div className=\"p-4\">\n                    <div className=\"h-4 bg-gray-200 rounded mb-2\"></div>\n                    <div className=\"h-6 bg-gray-200 rounded mb-2\"></div>\n                    <div className=\"h-4 bg-gray-200 rounded mb-3\"></div>\n                    <div className=\"flex justify-between mb-3\">\n                      <div className=\"h-6 bg-gray-200 rounded w-20\"></div>\n                      <div className=\"h-4 bg-gray-200 rounded w-16\"></div>\n                    </div>\n                    <div className=\"flex space-x-2 space-x-reverse\">\n                      <div className=\"flex-1 h-10 bg-gray-200 rounded\"></div>\n                      <div className=\"w-16 h-10 bg-gray-200 rounded\"></div>\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          ) : featuredProducts.length === 0 ? (\n            <div className=\"text-center py-12\">\n              <div className=\"text-6xl mb-4\">📦</div>\n              <h3 className=\"text-xl font-semibold text-gray-700 mb-2\">\n                لا توجد منتجات مميزة\n              </h3>\n              <p className=\"text-gray-500 mb-6\">\n                لم يتم تعيين أي منتجات كمميزة بعد\n              </p>\n              <Link\n                href=\"/products\"\n                className=\"inline-block bg-primary-600 hover:bg-primary-700 text-white font-bold py-3 px-8 rounded-lg transition-colors duration-200\"\n              >\n                تصفح جميع المنتجات\n              </Link>\n            </div>\n          ) : (\n            <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\">\n              {featuredProducts.map((product) => (\n                <ProductCard\n                  key={product.id}\n                  product={product}\n                  onAddToCart={handleAddToCart}\n                />\n              ))}\n            </div>\n          )}\n\n          <div className=\"text-center mt-12\">\n            <Link\n              href=\"/products\"\n              className=\"inline-block bg-primary-600 hover:bg-primary-700 text-white font-bold py-3 px-8 rounded-lg transition-colors duration-200\"\n            >\n              عرض جميع المنتجات\n            </Link>\n          </div>\n        </div>\n      </section>\n\n      {/* Footer */}\n      <footer className=\"bg-gray-800 text-white py-12\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n            <div>\n              <h3 className=\"text-xl font-bold mb-4\">المتجر العربي</h3>\n              <p className=\"text-gray-300\">\n                متجرك الإلكتروني المفضل للحصول على أفضل المنتجات بأسعار مميزة\n              </p>\n            </div>\n            <div>\n              <h4 className=\"text-lg font-semibold mb-4\">روابط سريعة</h4>\n              <ul className=\"space-y-2\">\n                <li><Link href=\"/\" className=\"text-gray-300 hover:text-white\">الصفحة الرئيسية</Link></li>\n                <li><Link href=\"/products\" className=\"text-gray-300 hover:text-white\">المنتجات</Link></li>\n                <li><Link href=\"/about\" className=\"text-gray-300 hover:text-white\">من نحن</Link></li>\n                <li><Link href=\"/contact\" className=\"text-gray-300 hover:text-white\">اتصل بنا</Link></li>\n              </ul>\n            </div>\n            <div>\n              <h4 className=\"text-lg font-semibold mb-4\">تواصل معنا</h4>\n              <p className=\"text-gray-300 mb-2\">📧 <EMAIL></p>\n              <p className=\"text-gray-300 mb-2\">📞 +966 50 123 4567</p>\n              <p className=\"text-gray-300\">📍 الرياض، المملكة العربية السعودية</p>\n            </div>\n          </div>\n          <div className=\"border-t border-gray-700 mt-8 pt-8 text-center\">\n            <p className=\"text-gray-300\">\n              © 2024 المتجر العربي. جميع الحقوق محفوظة.\n            </p>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAOe,SAAS;;IACtB,MAAM,EAAE,mBAAmB,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IACrD,MAAM,mBAAmB;IAEzB,MAAM,kBAAkB,CAAC;QACvB,4CAA4C;QAC5C,QAAQ,GAAG,CAAC,2BAA2B;QACvC,MAAM;IACR;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,+HAAA,CAAA,UAAM;;;;;0BAGP,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAsC;;;;;;sCAGpD,6LAAC;4BAAE,WAAU;sCAAsC;;;;;;sCAGnD,6LAAC,+JAA<PERSON>,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;sCACX;;;;;;;;;;;;;;;;;0BAOL,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAAgB;;;;;;kDAC/B,6LAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,6LAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;0CAE/B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAAgB;;;;;;kDAC/B,6LAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,6LAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;0CAE/B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAAgB;;;;;;kDAC/B,6LAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,6LAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOrC,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAwC;;;;;;8CAGtD,6LAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;wBAKtC,0BACC,6LAAC;4BAAI,WAAU;sCACZ;mCAAI,MAAM;6BAAG,CAAC,GAAG,CAAC,CAAC,GAAG,sBACrB,6LAAC;oCAAgB,WAAU;;sDACzB,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;;;;;sEACf,6LAAC;4DAAI,WAAU;;;;;;;;;;;;8DAEjB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;;;;;sEACf,6LAAC;4DAAI,WAAU;;;;;;;;;;;;;;;;;;;mCAZX;;;;;;;;;mCAkBZ,iBAAiB,MAAM,KAAK,kBAC9B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CAAgB;;;;;;8CAC/B,6LAAC;oCAAG,WAAU;8CAA2C;;;;;;8CAGzD,6LAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAGlC,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;iDAKH,6LAAC;4BAAI,WAAU;sCACZ,iBAAiB,GAAG,CAAC,CAAC,wBACrB,6LAAC,oIAAA,CAAA,UAAW;oCAEV,SAAS;oCACT,aAAa;mCAFR,QAAQ,EAAE;;;;;;;;;;sCAQvB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;0BAQP,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAyB;;;;;;sDACvC,6LAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAI/B,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;8DAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAI,WAAU;kEAAiC;;;;;;;;;;;8DAC9D,6LAAC;8DAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAY,WAAU;kEAAiC;;;;;;;;;;;8DACtE,6LAAC;8DAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAS,WAAU;kEAAiC;;;;;;;;;;;8DACnE,6LAAC;8DAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAW,WAAU;kEAAiC;;;;;;;;;;;;;;;;;;;;;;;8CAGzE,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,6LAAC;4CAAE,WAAU;sDAAqB;;;;;;sDAClC,6LAAC;4CAAE,WAAU;sDAAqB;;;;;;sDAClC,6LAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;;;;;;;sCAGjC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQzC;GAlKwB;;QACqB,qIAAA,CAAA,cAAW;;;KADhC"}}, {"offset": {"line": 1160, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}