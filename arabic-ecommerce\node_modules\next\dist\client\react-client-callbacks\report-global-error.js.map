{"version": 3, "sources": ["../../../src/client/react-client-callbacks/report-global-error.ts"], "sourcesContent": ["export const reportGlobalError =\n  typeof reportError === 'function'\n    ? // In modern browsers, reportError will dispatch an error event,\n      // emulating an uncaught JavaScript error.\n      reportError\n    : (error: unknown) => {\n        window.console.error(error)\n      }\n"], "names": ["reportGlobalError", "reportError", "error", "window", "console"], "mappings": ";;;;+BAAaA;;;eAAAA;;;AAAN,MAAMA,oBACX,OAAOC,gBAAgB,aAEnB,0CAA0C;AAC1CA,cACA,CAACC;IACCC,OAAOC,OAAO,CAACF,KAAK,CAACA;AACvB"}