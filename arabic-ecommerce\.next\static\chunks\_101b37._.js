(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["static/chunks/_101b37._.js", {

"[project]/src/lib/data.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "categories": (()=>categories),
    "sampleProducts": (()=>sampleProducts),
    "sampleUsers": (()=>sampleUsers)
});
const sampleProducts = [
    {
        id: '1',
        name: 'هاتف ذكي متطور',
        description: 'هاتف ذكي بمواصفات عالية وكاميرا متقدمة',
        price: 2500,
        image: '/images/phone.jpg',
        category: 'إلكترونيات',
        stock: 15,
        featured: true,
        createdAt: new Date(),
        updatedAt: new Date()
    },
    {
        id: '2',
        name: 'لابتوب للألعاب',
        description: 'لابتوب قوي مخصص للألعاب والتصميم',
        price: 4500,
        image: '/images/laptop.jpg',
        category: 'إلكترونيات',
        stock: 8,
        featured: true,
        createdAt: new Date(),
        updatedAt: new Date()
    },
    {
        id: '3',
        name: 'ساعة ذكية',
        description: 'ساعة ذكية لتتبع اللياقة البدنية',
        price: 800,
        image: '/images/watch.jpg',
        category: 'إكسسوارات',
        stock: 25,
        featured: false,
        createdAt: new Date(),
        updatedAt: new Date()
    },
    {
        id: '4',
        name: 'سماعات لاسلكية',
        description: 'سماعات بلوتوث عالية الجودة',
        price: 350,
        image: '/images/headphones.jpg',
        category: 'إكسسوارات',
        stock: 30,
        featured: true,
        createdAt: new Date(),
        updatedAt: new Date()
    },
    {
        id: '5',
        name: 'كاميرا رقمية',
        description: 'كاميرا احترافية للتصوير الفوتوغرافي',
        price: 3200,
        image: '/images/camera.jpg',
        category: 'إلكترونيات',
        stock: 12,
        featured: false,
        createdAt: new Date(),
        updatedAt: new Date()
    },
    {
        id: '6',
        name: 'تابلت للرسم',
        description: 'تابلت مخصص للرسم والتصميم الرقمي',
        price: 1800,
        image: '/images/tablet.jpg',
        category: 'إلكترونيات',
        stock: 18,
        featured: true,
        createdAt: new Date(),
        updatedAt: new Date()
    }
];
const sampleUsers = [
    {
        id: '1',
        name: 'أحمد محمد',
        email: '<EMAIL>',
        role: 'admin',
        createdAt: new Date(),
        updatedAt: new Date()
    },
    {
        id: '2',
        name: 'فاطمة علي',
        email: '<EMAIL>',
        role: 'customer',
        createdAt: new Date(),
        updatedAt: new Date()
    }
];
const categories = [
    'إلكترونيات',
    'إكسسوارات',
    'ملابس',
    'كتب',
    'رياضة',
    'منزل وحديقة'
];
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/contexts/ProductContext.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "ProductProvider": (()=>ProductProvider),
    "useFilteredProducts": (()=>useFilteredProducts),
    "useProducts": (()=>useProducts)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/lib/data.ts [app-client] (ecmascript)");
;
var _s = __turbopack_refresh__.signature(), _s1 = __turbopack_refresh__.signature(), _s2 = __turbopack_refresh__.signature();
'use client';
;
;
const ProductContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(undefined);
function ProductProvider({ children }) {
    _s();
    const [products, setProducts] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [isLoading, setIsLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    // Initialize products from localStorage or use sample data
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ProductProvider.useEffect": ()=>{
            const initializeProducts = {
                "ProductProvider.useEffect.initializeProducts": ()=>{
                    try {
                        const savedProducts = localStorage.getItem('ecommerce-products');
                        if (savedProducts) {
                            const parsedProducts = JSON.parse(savedProducts);
                            // Convert date strings back to Date objects
                            const productsWithDates = parsedProducts.map({
                                "ProductProvider.useEffect.initializeProducts.productsWithDates": (product)=>({
                                        ...product,
                                        createdAt: new Date(product.createdAt),
                                        updatedAt: new Date(product.updatedAt)
                                    })
                            }["ProductProvider.useEffect.initializeProducts.productsWithDates"]);
                            setProducts(productsWithDates);
                        } else {
                            // First time - use sample data
                            setProducts(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["sampleProducts"]);
                            localStorage.setItem('ecommerce-products', JSON.stringify(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["sampleProducts"]));
                        }
                    } catch (error) {
                        console.error('Error loading products from localStorage:', error);
                        setProducts(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["sampleProducts"]);
                    } finally{
                        setIsLoading(false);
                    }
                }
            }["ProductProvider.useEffect.initializeProducts"];
            initializeProducts();
        }
    }["ProductProvider.useEffect"], []);
    // Save to localStorage whenever products change
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ProductProvider.useEffect": ()=>{
            if (!isLoading && products.length > 0) {
                try {
                    localStorage.setItem('ecommerce-products', JSON.stringify(products));
                } catch (error) {
                    console.error('Error saving products to localStorage:', error);
                }
            }
        }
    }["ProductProvider.useEffect"], [
        products,
        isLoading
    ]);
    const addProduct = (productData)=>{
        const newProduct = {
            ...productData,
            id: Date.now().toString(),
            createdAt: new Date(),
            updatedAt: new Date()
        };
        setProducts((prev)=>[
                ...prev,
                newProduct
            ]);
    };
    const updateProduct = (id, productData)=>{
        setProducts((prev)=>prev.map((product)=>product.id === id ? {
                    ...product,
                    ...productData,
                    updatedAt: new Date()
                } : product));
    };
    const deleteProduct = (id)=>{
        setProducts((prev)=>prev.filter((product)=>product.id !== id));
    };
    const getProductById = (id)=>{
        return products.find((product)=>product.id === id);
    };
    const getFeaturedProducts = ()=>{
        return products.filter((product)=>product.featured);
    };
    const value = {
        products,
        addProduct,
        updateProduct,
        deleteProduct,
        getProductById,
        getFeaturedProducts,
        isLoading
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(ProductContext.Provider, {
        value: value,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/contexts/ProductContext.tsx",
        lineNumber: 109,
        columnNumber: 5
    }, this);
}
_s(ProductProvider, "Nktqz2QYtAF3DsCL7n5ysh3tZ+Q=");
_c = ProductProvider;
function useProducts() {
    _s1();
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(ProductContext);
    if (context === undefined) {
        throw new Error('useProducts must be used within a ProductProvider');
    }
    return context;
}
_s1(useProducts, "b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=");
function useFilteredProducts(filters) {
    _s2();
    const { products } = useProducts();
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].useMemo({
        "useFilteredProducts.useMemo": ()=>{
            if (!filters) return products;
            return products.filter({
                "useFilteredProducts.useMemo": (product)=>{
                    const matchesCategory = !filters.category || product.category === filters.category;
                    const matchesSearch = !filters.searchTerm || product.name.toLowerCase().includes(filters.searchTerm.toLowerCase()) || product.description.toLowerCase().includes(filters.searchTerm.toLowerCase());
                    let matchesPrice = true;
                    if (filters.priceRange) {
                        switch(filters.priceRange){
                            case 'under-500':
                                matchesPrice = product.price < 500;
                                break;
                            case '500-1000':
                                matchesPrice = product.price >= 500 && product.price <= 1000;
                                break;
                            case '1000-3000':
                                matchesPrice = product.price >= 1000 && product.price <= 3000;
                                break;
                            case 'over-3000':
                                matchesPrice = product.price > 3000;
                                break;
                        }
                    }
                    const matchesFeatured = filters.featured === undefined || product.featured === filters.featured;
                    return matchesCategory && matchesSearch && matchesPrice && matchesFeatured;
                }
            }["useFilteredProducts.useMemo"]);
        }
    }["useFilteredProducts.useMemo"], [
        products,
        filters
    ]);
}
_s2(useFilteredProducts, "0ZuPzGKrlk3QXiGFdUvkpWtzpyQ=", false, function() {
    return [
        useProducts
    ];
});
var _c;
__turbopack_refresh__.register(_c, "ProductProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/contexts/CategoryContext.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "CategoryProvider": (()=>CategoryProvider),
    "useCategories": (()=>useCategories)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
var _s = __turbopack_refresh__.signature(), _s1 = __turbopack_refresh__.signature();
'use client';
;
const CategoryContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(undefined);
// Default categories
const defaultCategories = [
    {
        id: '1',
        name: 'إلكترونيات',
        description: 'أجهزة إلكترونية وتقنية حديثة',
        isActive: true,
        productCount: 0,
        createdAt: new Date(),
        updatedAt: new Date()
    },
    {
        id: '2',
        name: 'إكسسوارات',
        description: 'إكسسوارات متنوعة وعملية',
        isActive: true,
        productCount: 0,
        createdAt: new Date(),
        updatedAt: new Date()
    },
    {
        id: '3',
        name: 'ملابس',
        description: 'ملابس عصرية للرجال والنساء',
        isActive: true,
        productCount: 0,
        createdAt: new Date(),
        updatedAt: new Date()
    },
    {
        id: '4',
        name: 'كتب',
        description: 'كتب ومراجع في مختلف المجالات',
        isActive: true,
        productCount: 0,
        createdAt: new Date(),
        updatedAt: new Date()
    },
    {
        id: '5',
        name: 'رياضة',
        description: 'معدات وأدوات رياضية',
        isActive: true,
        productCount: 0,
        createdAt: new Date(),
        updatedAt: new Date()
    },
    {
        id: '6',
        name: 'منزل وحديقة',
        description: 'أدوات ومستلزمات المنزل والحديقة',
        isActive: true,
        productCount: 0,
        createdAt: new Date(),
        updatedAt: new Date()
    }
];
function CategoryProvider({ children }) {
    _s();
    const [categories, setCategories] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [isLoading, setIsLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    // Initialize categories from localStorage or use default data
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "CategoryProvider.useEffect": ()=>{
            const initializeCategories = {
                "CategoryProvider.useEffect.initializeCategories": ()=>{
                    try {
                        const savedCategories = localStorage.getItem('ecommerce-categories');
                        if (savedCategories) {
                            const parsedCategories = JSON.parse(savedCategories);
                            // Convert date strings back to Date objects
                            const categoriesWithDates = parsedCategories.map({
                                "CategoryProvider.useEffect.initializeCategories.categoriesWithDates": (category)=>({
                                        ...category,
                                        createdAt: new Date(category.createdAt),
                                        updatedAt: new Date(category.updatedAt)
                                    })
                            }["CategoryProvider.useEffect.initializeCategories.categoriesWithDates"]);
                            setCategories(categoriesWithDates);
                        } else {
                            // First time - use default data
                            setCategories(defaultCategories);
                            localStorage.setItem('ecommerce-categories', JSON.stringify(defaultCategories));
                        }
                    } catch (error) {
                        console.error('Error loading categories from localStorage:', error);
                        setCategories(defaultCategories);
                    } finally{
                        setIsLoading(false);
                    }
                }
            }["CategoryProvider.useEffect.initializeCategories"];
            initializeCategories();
        }
    }["CategoryProvider.useEffect"], []);
    // Save to localStorage whenever categories change
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "CategoryProvider.useEffect": ()=>{
            if (!isLoading && categories.length > 0) {
                try {
                    localStorage.setItem('ecommerce-categories', JSON.stringify(categories));
                } catch (error) {
                    console.error('Error saving categories to localStorage:', error);
                }
            }
        }
    }["CategoryProvider.useEffect"], [
        categories,
        isLoading
    ]);
    const addCategory = (categoryData)=>{
        const newCategory = {
            ...categoryData,
            id: Date.now().toString(),
            productCount: 0,
            createdAt: new Date(),
            updatedAt: new Date()
        };
        setCategories((prev)=>[
                ...prev,
                newCategory
            ]);
    };
    const updateCategory = (id, categoryData)=>{
        setCategories((prev)=>prev.map((category)=>category.id === id ? {
                    ...category,
                    ...categoryData,
                    updatedAt: new Date()
                } : category));
    };
    const deleteCategory = (id)=>{
        setCategories((prev)=>prev.filter((category)=>category.id !== id));
    };
    const getCategoryById = (id)=>{
        return categories.find((category)=>category.id === id);
    };
    const getActiveCategories = ()=>{
        return categories.filter((category)=>category.isActive);
    };
    const updateProductCount = (categoryName, count)=>{
        setCategories((prev)=>prev.map((category)=>category.name === categoryName ? {
                    ...category,
                    productCount: count
                } : category));
    };
    const value = {
        categories,
        addCategory,
        updateCategory,
        deleteCategory,
        getCategoryById,
        getActiveCategories,
        updateProductCount,
        isLoading
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(CategoryContext.Provider, {
        value: value,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/contexts/CategoryContext.tsx",
        lineNumber: 186,
        columnNumber: 5
    }, this);
}
_s(CategoryProvider, "DM1SLmkBSBujHBEgj8SCyAgZFVw=");
_c = CategoryProvider;
function useCategories() {
    _s1();
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(CategoryContext);
    if (context === undefined) {
        throw new Error('useCategories must be used within a CategoryProvider');
    }
    return context;
}
_s1(useCategories, "b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=");
var _c;
__turbopack_refresh__.register(_c, "CategoryProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/contexts/OrderContext.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "OrderProvider": (()=>OrderProvider),
    "useOrders": (()=>useOrders)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
var _s = __turbopack_refresh__.signature(), _s1 = __turbopack_refresh__.signature();
'use client';
;
const OrderContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(undefined);
// Sample orders data
const sampleOrders = [
    {
        id: '1',
        orderNumber: 'ORD-2024-001',
        customerId: '1',
        customerName: 'أحمد محمد',
        customerEmail: '<EMAIL>',
        items: [
            {
                id: '1',
                productId: '1',
                productName: 'لابتوب Dell XPS 13',
                productImage: '/images/laptop.jpg',
                price: 2500,
                quantity: 1,
                total: 2500
            },
            {
                id: '2',
                productId: '2',
                productName: 'سماعات لاسلكية',
                productImage: '/images/headphones.jpg',
                price: 150,
                quantity: 2,
                total: 300
            }
        ],
        subtotal: 2800,
        shipping: 50,
        tax: 420,
        total: 3270,
        status: 'confirmed',
        paymentMethod: 'card',
        paymentStatus: 'paid',
        shippingAddress: {
            fullName: 'أحمد محمد علي',
            phone: '+************',
            address: 'شارع الملك فهد، حي النخيل',
            city: 'الرياض',
            postalCode: '12345',
            country: 'السعودية'
        },
        notes: 'يرجى التسليم في المساء',
        createdAt: new Date('2024-01-15T10:30:00'),
        updatedAt: new Date('2024-01-15T11:00:00')
    },
    {
        id: '2',
        orderNumber: 'ORD-2024-002',
        customerId: '2',
        customerName: 'فاطمة أحمد',
        customerEmail: '<EMAIL>',
        items: [
            {
                id: '3',
                productId: '3',
                productName: 'هاتف ذكي Samsung Galaxy',
                productImage: '/images/phone.jpg',
                price: 1200,
                quantity: 1,
                total: 1200
            }
        ],
        subtotal: 1200,
        shipping: 30,
        tax: 180,
        total: 1410,
        status: 'processing',
        paymentMethod: 'cash',
        paymentStatus: 'pending',
        shippingAddress: {
            fullName: 'فاطمة أحمد محمد',
            phone: '+966507654321',
            address: 'طريق الأمير محمد بن عبدالعزيز',
            city: 'جدة',
            postalCode: '21589',
            country: 'السعودية'
        },
        createdAt: new Date('2024-01-16T14:20:00'),
        updatedAt: new Date('2024-01-16T15:45:00')
    },
    {
        id: '3',
        orderNumber: 'ORD-2024-003',
        customerId: '1',
        customerName: 'أحمد محمد',
        customerEmail: '<EMAIL>',
        items: [
            {
                id: '4',
                productId: '4',
                productName: 'تابلت iPad Pro',
                productImage: '/images/tablet.jpg',
                price: 1800,
                quantity: 1,
                total: 1800
            }
        ],
        subtotal: 1800,
        shipping: 40,
        tax: 270,
        total: 2110,
        status: 'delivered',
        paymentMethod: 'bank_transfer',
        paymentStatus: 'paid',
        shippingAddress: {
            fullName: 'أحمد محمد علي',
            phone: '+************',
            address: 'شارع الملك فهد، حي النخيل',
            city: 'الرياض',
            postalCode: '12345',
            country: 'السعودية'
        },
        createdAt: new Date('2024-01-10T09:15:00'),
        updatedAt: new Date('2024-01-12T16:30:00'),
        deliveredAt: new Date('2024-01-12T16:30:00')
    }
];
function OrderProvider({ children }) {
    _s();
    const [orders, setOrders] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [isLoading, setIsLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    // Initialize orders from localStorage or use sample data
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "OrderProvider.useEffect": ()=>{
            const initializeOrders = {
                "OrderProvider.useEffect.initializeOrders": ()=>{
                    try {
                        const savedOrders = localStorage.getItem('ecommerce-orders');
                        if (savedOrders) {
                            const parsedOrders = JSON.parse(savedOrders);
                            // Convert date strings back to Date objects
                            const ordersWithDates = parsedOrders.map({
                                "OrderProvider.useEffect.initializeOrders.ordersWithDates": (order)=>({
                                        ...order,
                                        createdAt: new Date(order.createdAt),
                                        updatedAt: new Date(order.updatedAt),
                                        deliveredAt: order.deliveredAt ? new Date(order.deliveredAt) : undefined
                                    })
                            }["OrderProvider.useEffect.initializeOrders.ordersWithDates"]);
                            setOrders(ordersWithDates);
                        } else {
                            // First time - use sample data
                            setOrders(sampleOrders);
                            localStorage.setItem('ecommerce-orders', JSON.stringify(sampleOrders));
                        }
                    } catch (error) {
                        console.error('Error loading orders from localStorage:', error);
                        setOrders(sampleOrders);
                    } finally{
                        setIsLoading(false);
                    }
                }
            }["OrderProvider.useEffect.initializeOrders"];
            initializeOrders();
        }
    }["OrderProvider.useEffect"], []);
    // Save to localStorage whenever orders change
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "OrderProvider.useEffect": ()=>{
            if (!isLoading && orders.length > 0) {
                try {
                    localStorage.setItem('ecommerce-orders', JSON.stringify(orders));
                } catch (error) {
                    console.error('Error saving orders to localStorage:', error);
                }
            }
        }
    }["OrderProvider.useEffect"], [
        orders,
        isLoading
    ]);
    const generateOrderNumber = ()=>{
        const year = new Date().getFullYear();
        const orderCount = orders.length + 1;
        return `ORD-${year}-${orderCount.toString().padStart(3, '0')}`;
    };
    const addOrder = (orderData)=>{
        const newOrder = {
            ...orderData,
            id: Date.now().toString(),
            orderNumber: generateOrderNumber(),
            createdAt: new Date(),
            updatedAt: new Date()
        };
        setOrders((prev)=>[
                newOrder,
                ...prev
            ]);
    };
    const updateOrderStatus = (id, status)=>{
        setOrders((prev)=>prev.map((order)=>{
                if (order.id === id) {
                    const updatedOrder = {
                        ...order,
                        status,
                        updatedAt: new Date()
                    };
                    // Set deliveredAt when status changes to delivered
                    if (status === 'delivered' && !order.deliveredAt) {
                        updatedOrder.deliveredAt = new Date();
                    }
                    return updatedOrder;
                }
                return order;
            }));
    };
    const updateOrder = (id, updates)=>{
        setOrders((prev)=>prev.map((order)=>order.id === id ? {
                    ...order,
                    ...updates,
                    updatedAt: new Date()
                } : order));
    };
    const deleteOrder = (id)=>{
        setOrders((prev)=>prev.filter((order)=>order.id !== id));
    };
    const getOrderById = (id)=>{
        return orders.find((order)=>order.id === id);
    };
    const getOrdersByStatus = (status)=>{
        return orders.filter((order)=>order.status === status);
    };
    const getOrdersByCustomer = (customerId)=>{
        return orders.filter((order)=>order.customerId === customerId);
    };
    const getTotalRevenue = ()=>{
        return orders.filter((order)=>order.paymentStatus === 'paid').reduce((total, order)=>total + order.total, 0);
    };
    const getOrdersCount = ()=>{
        return orders.length;
    };
    const getRecentOrders = (limit = 5)=>{
        return orders.sort((a, b)=>b.createdAt.getTime() - a.createdAt.getTime()).slice(0, limit);
    };
    const value = {
        orders,
        addOrder,
        updateOrderStatus,
        updateOrder,
        deleteOrder,
        getOrderById,
        getOrdersByStatus,
        getOrdersByCustomer,
        getTotalRevenue,
        getOrdersCount,
        getRecentOrders,
        isLoading
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(OrderContext.Provider, {
        value: value,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/contexts/OrderContext.tsx",
        lineNumber: 286,
        columnNumber: 5
    }, this);
}
_s(OrderProvider, "6FPSA1fF3eWXO1yjZQ/963tNc/k=");
_c = OrderProvider;
function useOrders() {
    _s1();
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(OrderContext);
    if (context === undefined) {
        throw new Error('useOrders must be used within an OrderProvider');
    }
    return context;
}
_s1(useOrders, "b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=");
var _c;
__turbopack_refresh__.register(_c, "OrderProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/contexts/UserContext.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "UserProvider": (()=>UserProvider),
    "useUsers": (()=>useUsers)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
var _s = __turbopack_refresh__.signature(), _s1 = __turbopack_refresh__.signature();
'use client';
;
const UserContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(undefined);
// Sample users data
const sampleUsers = [
    {
        id: '1',
        name: 'أحمد محمد علي',
        email: '<EMAIL>',
        phone: '+************',
        role: 'customer',
        isActive: true,
        lastLogin: new Date('2024-01-16T10:30:00'),
        totalOrders: 3,
        totalSpent: 5780,
        address: {
            street: 'شارع الملك فهد، حي النخيل',
            city: 'الرياض',
            postalCode: '12345',
            country: 'السعودية'
        },
        createdAt: new Date('2024-01-01T08:00:00'),
        updatedAt: new Date('2024-01-16T10:30:00')
    },
    {
        id: '2',
        name: 'فاطمة أحمد محمد',
        email: '<EMAIL>',
        phone: '+966507654321',
        role: 'customer',
        isActive: true,
        lastLogin: new Date('2024-01-15T14:20:00'),
        totalOrders: 1,
        totalSpent: 1410,
        address: {
            street: 'طريق الأمير محمد بن عبدالعزيز',
            city: 'جدة',
            postalCode: '21589',
            country: 'السعودية'
        },
        createdAt: new Date('2024-01-05T12:00:00'),
        updatedAt: new Date('2024-01-15T14:20:00')
    },
    {
        id: '3',
        name: 'محمد عبدالله',
        email: '<EMAIL>',
        phone: '+966509876543',
        role: 'customer',
        isActive: false,
        lastLogin: new Date('2024-01-10T09:15:00'),
        totalOrders: 0,
        totalSpent: 0,
        address: {
            street: 'شارع العليا',
            city: 'الرياض',
            postalCode: '11564',
            country: 'السعودية'
        },
        createdAt: new Date('2024-01-08T16:30:00'),
        updatedAt: new Date('2024-01-12T11:00:00')
    },
    {
        id: '4',
        name: 'سارة خالد',
        email: '<EMAIL>',
        phone: '+966502468135',
        role: 'customer',
        isActive: true,
        lastLogin: new Date('2024-01-14T11:45:00'),
        totalOrders: 2,
        totalSpent: 3200,
        address: {
            street: 'حي الملقا',
            city: 'الرياض',
            postalCode: '13524',
            country: 'السعودية'
        },
        createdAt: new Date('2024-01-03T09:20:00'),
        updatedAt: new Date('2024-01-14T11:45:00')
    },
    {
        id: '5',
        name: 'عبدالرحمن أحمد',
        email: '<EMAIL>',
        phone: '+966501111111',
        role: 'admin',
        isActive: true,
        lastLogin: new Date('2024-01-16T16:00:00'),
        totalOrders: 0,
        totalSpent: 0,
        createdAt: new Date('2023-12-01T10:00:00'),
        updatedAt: new Date('2024-01-16T16:00:00')
    }
];
function UserProvider({ children }) {
    _s();
    const [users, setUsers] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [isLoading, setIsLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    // Initialize users from localStorage or use sample data
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "UserProvider.useEffect": ()=>{
            const initializeUsers = {
                "UserProvider.useEffect.initializeUsers": ()=>{
                    try {
                        const savedUsers = localStorage.getItem('ecommerce-users');
                        if (savedUsers) {
                            const parsedUsers = JSON.parse(savedUsers);
                            // Convert date strings back to Date objects
                            const usersWithDates = parsedUsers.map({
                                "UserProvider.useEffect.initializeUsers.usersWithDates": (user)=>({
                                        ...user,
                                        lastLogin: user.lastLogin ? new Date(user.lastLogin) : undefined,
                                        createdAt: new Date(user.createdAt),
                                        updatedAt: new Date(user.updatedAt)
                                    })
                            }["UserProvider.useEffect.initializeUsers.usersWithDates"]);
                            setUsers(usersWithDates);
                        } else {
                            // First time - use sample data
                            setUsers(sampleUsers);
                            localStorage.setItem('ecommerce-users', JSON.stringify(sampleUsers));
                        }
                    } catch (error) {
                        console.error('Error loading users from localStorage:', error);
                        setUsers(sampleUsers);
                    } finally{
                        setIsLoading(false);
                    }
                }
            }["UserProvider.useEffect.initializeUsers"];
            initializeUsers();
        }
    }["UserProvider.useEffect"], []);
    // Save to localStorage whenever users change
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "UserProvider.useEffect": ()=>{
            if (!isLoading && users.length > 0) {
                try {
                    localStorage.setItem('ecommerce-users', JSON.stringify(users));
                } catch (error) {
                    console.error('Error saving users to localStorage:', error);
                }
            }
        }
    }["UserProvider.useEffect"], [
        users,
        isLoading
    ]);
    const addUser = (userData)=>{
        const newUser = {
            ...userData,
            id: Date.now().toString(),
            totalOrders: 0,
            totalSpent: 0,
            createdAt: new Date(),
            updatedAt: new Date()
        };
        setUsers((prev)=>[
                newUser,
                ...prev
            ]);
    };
    const updateUser = (id, userData)=>{
        setUsers((prev)=>prev.map((user)=>user.id === id ? {
                    ...user,
                    ...userData,
                    updatedAt: new Date()
                } : user));
    };
    const deleteUser = (id)=>{
        setUsers((prev)=>prev.filter((user)=>user.id !== id));
    };
    const toggleUserStatus = (id)=>{
        setUsers((prev)=>prev.map((user)=>user.id === id ? {
                    ...user,
                    isActive: !user.isActive,
                    updatedAt: new Date()
                } : user));
    };
    const getUserById = (id)=>{
        return users.find((user)=>user.id === id);
    };
    const getActiveUsers = ()=>{
        return users.filter((user)=>user.isActive);
    };
    const getUsersByRole = (role)=>{
        return users.filter((user)=>user.role === role);
    };
    const updateUserStats = (userId, orderCount, totalSpent)=>{
        setUsers((prev)=>prev.map((user)=>user.id === userId ? {
                    ...user,
                    totalOrders: orderCount,
                    totalSpent: totalSpent,
                    lastLogin: new Date(),
                    updatedAt: new Date()
                } : user));
    };
    const getTotalUsers = ()=>{
        return users.length;
    };
    const getActiveUsersCount = ()=>{
        return users.filter((user)=>user.isActive).length;
    };
    const getRecentUsers = (limit = 5)=>{
        return users.sort((a, b)=>b.createdAt.getTime() - a.createdAt.getTime()).slice(0, limit);
    };
    const value = {
        users,
        addUser,
        updateUser,
        deleteUser,
        toggleUserStatus,
        getUserById,
        getActiveUsers,
        getUsersByRole,
        updateUserStats,
        getTotalUsers,
        getActiveUsersCount,
        getRecentUsers,
        isLoading
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(UserContext.Provider, {
        value: value,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/contexts/UserContext.tsx",
        lineNumber: 256,
        columnNumber: 5
    }, this);
}
_s(UserProvider, "ysOX+ftzAG74veWw5QS71aORh2k=");
_c = UserProvider;
function useUsers() {
    _s1();
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(UserContext);
    if (context === undefined) {
        throw new Error('useUsers must be used within a UserProvider');
    }
    return context;
}
_s1(useUsers, "b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=");
var _c;
__turbopack_refresh__.register(_c, "UserProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/layout.tsx [app-rsc] (ecmascript, Next.js server component, client modules)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
}}),
"[project]/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
/**
 * @license React
 * react-jsx-dev-runtime.development.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */ var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
"use strict";
"production" !== ("TURBOPACK compile-time value", "development") && function() {
    function getComponentNameFromType(type) {
        if (null == type) return null;
        if ("function" === typeof type) return type.$$typeof === REACT_CLIENT_REFERENCE$2 ? null : type.displayName || type.name || null;
        if ("string" === typeof type) return type;
        switch(type){
            case REACT_FRAGMENT_TYPE:
                return "Fragment";
            case REACT_PORTAL_TYPE:
                return "Portal";
            case REACT_PROFILER_TYPE:
                return "Profiler";
            case REACT_STRICT_MODE_TYPE:
                return "StrictMode";
            case REACT_SUSPENSE_TYPE:
                return "Suspense";
            case REACT_SUSPENSE_LIST_TYPE:
                return "SuspenseList";
        }
        if ("object" === typeof type) switch("number" === typeof type.tag && console.error("Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."), type.$$typeof){
            case REACT_CONTEXT_TYPE:
                return (type.displayName || "Context") + ".Provider";
            case REACT_CONSUMER_TYPE:
                return (type._context.displayName || "Context") + ".Consumer";
            case REACT_FORWARD_REF_TYPE:
                var innerType = type.render;
                type = type.displayName;
                type || (type = innerType.displayName || innerType.name || "", type = "" !== type ? "ForwardRef(" + type + ")" : "ForwardRef");
                return type;
            case REACT_MEMO_TYPE:
                return innerType = type.displayName || null, null !== innerType ? innerType : getComponentNameFromType(type.type) || "Memo";
            case REACT_LAZY_TYPE:
                innerType = type._payload;
                type = type._init;
                try {
                    return getComponentNameFromType(type(innerType));
                } catch (x) {}
        }
        return null;
    }
    function testStringCoercion(value) {
        return "" + value;
    }
    function checkKeyStringCoercion(value) {
        try {
            testStringCoercion(value);
            var JSCompiler_inline_result = !1;
        } catch (e) {
            JSCompiler_inline_result = !0;
        }
        if (JSCompiler_inline_result) {
            JSCompiler_inline_result = console;
            var JSCompiler_temp_const = JSCompiler_inline_result.error;
            var JSCompiler_inline_result$jscomp$0 = "function" === typeof Symbol && Symbol.toStringTag && value[Symbol.toStringTag] || value.constructor.name || "Object";
            JSCompiler_temp_const.call(JSCompiler_inline_result, "The provided key is an unsupported type %s. This value must be coerced to a string before using it here.", JSCompiler_inline_result$jscomp$0);
            return testStringCoercion(value);
        }
    }
    function disabledLog() {}
    function disableLogs() {
        if (0 === disabledDepth) {
            prevLog = console.log;
            prevInfo = console.info;
            prevWarn = console.warn;
            prevError = console.error;
            prevGroup = console.group;
            prevGroupCollapsed = console.groupCollapsed;
            prevGroupEnd = console.groupEnd;
            var props = {
                configurable: !0,
                enumerable: !0,
                value: disabledLog,
                writable: !0
            };
            Object.defineProperties(console, {
                info: props,
                log: props,
                warn: props,
                error: props,
                group: props,
                groupCollapsed: props,
                groupEnd: props
            });
        }
        disabledDepth++;
    }
    function reenableLogs() {
        disabledDepth--;
        if (0 === disabledDepth) {
            var props = {
                configurable: !0,
                enumerable: !0,
                writable: !0
            };
            Object.defineProperties(console, {
                log: assign({}, props, {
                    value: prevLog
                }),
                info: assign({}, props, {
                    value: prevInfo
                }),
                warn: assign({}, props, {
                    value: prevWarn
                }),
                error: assign({}, props, {
                    value: prevError
                }),
                group: assign({}, props, {
                    value: prevGroup
                }),
                groupCollapsed: assign({}, props, {
                    value: prevGroupCollapsed
                }),
                groupEnd: assign({}, props, {
                    value: prevGroupEnd
                })
            });
        }
        0 > disabledDepth && console.error("disabledDepth fell below zero. This is a bug in React. Please file an issue.");
    }
    function describeBuiltInComponentFrame(name) {
        if (void 0 === prefix) try {
            throw Error();
        } catch (x) {
            var match = x.stack.trim().match(/\n( *(at )?)/);
            prefix = match && match[1] || "";
            suffix = -1 < x.stack.indexOf("\n    at") ? " (<anonymous>)" : -1 < x.stack.indexOf("@") ? "@unknown:0:0" : "";
        }
        return "\n" + prefix + name + suffix;
    }
    function describeNativeComponentFrame(fn, construct) {
        if (!fn || reentry) return "";
        var frame = componentFrameCache.get(fn);
        if (void 0 !== frame) return frame;
        reentry = !0;
        frame = Error.prepareStackTrace;
        Error.prepareStackTrace = void 0;
        var previousDispatcher = null;
        previousDispatcher = ReactSharedInternals.H;
        ReactSharedInternals.H = null;
        disableLogs();
        try {
            var RunInRootFrame = {
                DetermineComponentFrameRoot: function() {
                    try {
                        if (construct) {
                            var Fake = function() {
                                throw Error();
                            };
                            Object.defineProperty(Fake.prototype, "props", {
                                set: function() {
                                    throw Error();
                                }
                            });
                            if ("object" === typeof Reflect && Reflect.construct) {
                                try {
                                    Reflect.construct(Fake, []);
                                } catch (x) {
                                    var control = x;
                                }
                                Reflect.construct(fn, [], Fake);
                            } else {
                                try {
                                    Fake.call();
                                } catch (x$0) {
                                    control = x$0;
                                }
                                fn.call(Fake.prototype);
                            }
                        } else {
                            try {
                                throw Error();
                            } catch (x$1) {
                                control = x$1;
                            }
                            (Fake = fn()) && "function" === typeof Fake.catch && Fake.catch(function() {});
                        }
                    } catch (sample) {
                        if (sample && control && "string" === typeof sample.stack) return [
                            sample.stack,
                            control.stack
                        ];
                    }
                    return [
                        null,
                        null
                    ];
                }
            };
            RunInRootFrame.DetermineComponentFrameRoot.displayName = "DetermineComponentFrameRoot";
            var namePropDescriptor = Object.getOwnPropertyDescriptor(RunInRootFrame.DetermineComponentFrameRoot, "name");
            namePropDescriptor && namePropDescriptor.configurable && Object.defineProperty(RunInRootFrame.DetermineComponentFrameRoot, "name", {
                value: "DetermineComponentFrameRoot"
            });
            var _RunInRootFrame$Deter = RunInRootFrame.DetermineComponentFrameRoot(), sampleStack = _RunInRootFrame$Deter[0], controlStack = _RunInRootFrame$Deter[1];
            if (sampleStack && controlStack) {
                var sampleLines = sampleStack.split("\n"), controlLines = controlStack.split("\n");
                for(_RunInRootFrame$Deter = namePropDescriptor = 0; namePropDescriptor < sampleLines.length && !sampleLines[namePropDescriptor].includes("DetermineComponentFrameRoot");)namePropDescriptor++;
                for(; _RunInRootFrame$Deter < controlLines.length && !controlLines[_RunInRootFrame$Deter].includes("DetermineComponentFrameRoot");)_RunInRootFrame$Deter++;
                if (namePropDescriptor === sampleLines.length || _RunInRootFrame$Deter === controlLines.length) for(namePropDescriptor = sampleLines.length - 1, _RunInRootFrame$Deter = controlLines.length - 1; 1 <= namePropDescriptor && 0 <= _RunInRootFrame$Deter && sampleLines[namePropDescriptor] !== controlLines[_RunInRootFrame$Deter];)_RunInRootFrame$Deter--;
                for(; 1 <= namePropDescriptor && 0 <= _RunInRootFrame$Deter; namePropDescriptor--, _RunInRootFrame$Deter--)if (sampleLines[namePropDescriptor] !== controlLines[_RunInRootFrame$Deter]) {
                    if (1 !== namePropDescriptor || 1 !== _RunInRootFrame$Deter) {
                        do if (namePropDescriptor--, _RunInRootFrame$Deter--, 0 > _RunInRootFrame$Deter || sampleLines[namePropDescriptor] !== controlLines[_RunInRootFrame$Deter]) {
                            var _frame = "\n" + sampleLines[namePropDescriptor].replace(" at new ", " at ");
                            fn.displayName && _frame.includes("<anonymous>") && (_frame = _frame.replace("<anonymous>", fn.displayName));
                            "function" === typeof fn && componentFrameCache.set(fn, _frame);
                            return _frame;
                        }
                        while (1 <= namePropDescriptor && 0 <= _RunInRootFrame$Deter)
                    }
                    break;
                }
            }
        } finally{
            reentry = !1, ReactSharedInternals.H = previousDispatcher, reenableLogs(), Error.prepareStackTrace = frame;
        }
        sampleLines = (sampleLines = fn ? fn.displayName || fn.name : "") ? describeBuiltInComponentFrame(sampleLines) : "";
        "function" === typeof fn && componentFrameCache.set(fn, sampleLines);
        return sampleLines;
    }
    function describeUnknownElementTypeFrameInDEV(type) {
        if (null == type) return "";
        if ("function" === typeof type) {
            var prototype = type.prototype;
            return describeNativeComponentFrame(type, !(!prototype || !prototype.isReactComponent));
        }
        if ("string" === typeof type) return describeBuiltInComponentFrame(type);
        switch(type){
            case REACT_SUSPENSE_TYPE:
                return describeBuiltInComponentFrame("Suspense");
            case REACT_SUSPENSE_LIST_TYPE:
                return describeBuiltInComponentFrame("SuspenseList");
        }
        if ("object" === typeof type) switch(type.$$typeof){
            case REACT_FORWARD_REF_TYPE:
                return type = describeNativeComponentFrame(type.render, !1), type;
            case REACT_MEMO_TYPE:
                return describeUnknownElementTypeFrameInDEV(type.type);
            case REACT_LAZY_TYPE:
                prototype = type._payload;
                type = type._init;
                try {
                    return describeUnknownElementTypeFrameInDEV(type(prototype));
                } catch (x) {}
        }
        return "";
    }
    function getOwner() {
        var dispatcher = ReactSharedInternals.A;
        return null === dispatcher ? null : dispatcher.getOwner();
    }
    function hasValidKey(config) {
        if (hasOwnProperty.call(config, "key")) {
            var getter = Object.getOwnPropertyDescriptor(config, "key").get;
            if (getter && getter.isReactWarning) return !1;
        }
        return void 0 !== config.key;
    }
    function defineKeyPropWarningGetter(props, displayName) {
        function warnAboutAccessingKey() {
            specialPropKeyWarningShown || (specialPropKeyWarningShown = !0, console.error("%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)", displayName));
        }
        warnAboutAccessingKey.isReactWarning = !0;
        Object.defineProperty(props, "key", {
            get: warnAboutAccessingKey,
            configurable: !0
        });
    }
    function elementRefGetterWithDeprecationWarning() {
        var componentName = getComponentNameFromType(this.type);
        didWarnAboutElementRef[componentName] || (didWarnAboutElementRef[componentName] = !0, console.error("Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release."));
        componentName = this.props.ref;
        return void 0 !== componentName ? componentName : null;
    }
    function ReactElement(type, key, self, source, owner, props) {
        self = props.ref;
        type = {
            $$typeof: REACT_ELEMENT_TYPE,
            type: type,
            key: key,
            props: props,
            _owner: owner
        };
        null !== (void 0 !== self ? self : null) ? Object.defineProperty(type, "ref", {
            enumerable: !1,
            get: elementRefGetterWithDeprecationWarning
        }) : Object.defineProperty(type, "ref", {
            enumerable: !1,
            value: null
        });
        type._store = {};
        Object.defineProperty(type._store, "validated", {
            configurable: !1,
            enumerable: !1,
            writable: !0,
            value: 0
        });
        Object.defineProperty(type, "_debugInfo", {
            configurable: !1,
            enumerable: !1,
            writable: !0,
            value: null
        });
        Object.freeze && (Object.freeze(type.props), Object.freeze(type));
        return type;
    }
    function jsxDEVImpl(type, config, maybeKey, isStaticChildren, source, self) {
        if ("string" === typeof type || "function" === typeof type || type === REACT_FRAGMENT_TYPE || type === REACT_PROFILER_TYPE || type === REACT_STRICT_MODE_TYPE || type === REACT_SUSPENSE_TYPE || type === REACT_SUSPENSE_LIST_TYPE || type === REACT_OFFSCREEN_TYPE || "object" === typeof type && null !== type && (type.$$typeof === REACT_LAZY_TYPE || type.$$typeof === REACT_MEMO_TYPE || type.$$typeof === REACT_CONTEXT_TYPE || type.$$typeof === REACT_CONSUMER_TYPE || type.$$typeof === REACT_FORWARD_REF_TYPE || type.$$typeof === REACT_CLIENT_REFERENCE$1 || void 0 !== type.getModuleId)) {
            var children = config.children;
            if (void 0 !== children) if (isStaticChildren) if (isArrayImpl(children)) {
                for(isStaticChildren = 0; isStaticChildren < children.length; isStaticChildren++)validateChildKeys(children[isStaticChildren], type);
                Object.freeze && Object.freeze(children);
            } else console.error("React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.");
            else validateChildKeys(children, type);
        } else {
            children = "";
            if (void 0 === type || "object" === typeof type && null !== type && 0 === Object.keys(type).length) children += " You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.";
            null === type ? isStaticChildren = "null" : isArrayImpl(type) ? isStaticChildren = "array" : void 0 !== type && type.$$typeof === REACT_ELEMENT_TYPE ? (isStaticChildren = "<" + (getComponentNameFromType(type.type) || "Unknown") + " />", children = " Did you accidentally export a JSX literal instead of a component?") : isStaticChildren = typeof type;
            console.error("React.jsx: type is invalid -- expected a string (for built-in components) or a class/function (for composite components) but got: %s.%s", isStaticChildren, children);
        }
        if (hasOwnProperty.call(config, "key")) {
            children = getComponentNameFromType(type);
            var keys = Object.keys(config).filter(function(k) {
                return "key" !== k;
            });
            isStaticChildren = 0 < keys.length ? "{key: someKey, " + keys.join(": ..., ") + ": ...}" : "{key: someKey}";
            didWarnAboutKeySpread[children + isStaticChildren] || (keys = 0 < keys.length ? "{" + keys.join(": ..., ") + ": ...}" : "{}", console.error('A props object containing a "key" prop is being spread into JSX:\n  let props = %s;\n  <%s {...props} />\nReact keys must be passed directly to JSX without using spread:\n  let props = %s;\n  <%s key={someKey} {...props} />', isStaticChildren, children, keys, children), didWarnAboutKeySpread[children + isStaticChildren] = !0);
        }
        children = null;
        void 0 !== maybeKey && (checkKeyStringCoercion(maybeKey), children = "" + maybeKey);
        hasValidKey(config) && (checkKeyStringCoercion(config.key), children = "" + config.key);
        if ("key" in config) {
            maybeKey = {};
            for(var propName in config)"key" !== propName && (maybeKey[propName] = config[propName]);
        } else maybeKey = config;
        children && defineKeyPropWarningGetter(maybeKey, "function" === typeof type ? type.displayName || type.name || "Unknown" : type);
        return ReactElement(type, children, self, source, getOwner(), maybeKey);
    }
    function validateChildKeys(node, parentType) {
        if ("object" === typeof node && node && node.$$typeof !== REACT_CLIENT_REFERENCE) {
            if (isArrayImpl(node)) for(var i = 0; i < node.length; i++){
                var child = node[i];
                isValidElement(child) && validateExplicitKey(child, parentType);
            }
            else if (isValidElement(node)) node._store && (node._store.validated = 1);
            else if (null === node || "object" !== typeof node ? i = null : (i = MAYBE_ITERATOR_SYMBOL && node[MAYBE_ITERATOR_SYMBOL] || node["@@iterator"], i = "function" === typeof i ? i : null), "function" === typeof i && i !== node.entries && (i = i.call(node), i !== node)) for(; !(node = i.next()).done;)isValidElement(node.value) && validateExplicitKey(node.value, parentType);
        }
    }
    function isValidElement(object) {
        return "object" === typeof object && null !== object && object.$$typeof === REACT_ELEMENT_TYPE;
    }
    function validateExplicitKey(element, parentType) {
        if (element._store && !element._store.validated && null == element.key && (element._store.validated = 1, parentType = getCurrentComponentErrorInfo(parentType), !ownerHasKeyUseWarning[parentType])) {
            ownerHasKeyUseWarning[parentType] = !0;
            var childOwner = "";
            element && null != element._owner && element._owner !== getOwner() && (childOwner = null, "number" === typeof element._owner.tag ? childOwner = getComponentNameFromType(element._owner.type) : "string" === typeof element._owner.name && (childOwner = element._owner.name), childOwner = " It was passed a child from " + childOwner + ".");
            var prevGetCurrentStack = ReactSharedInternals.getCurrentStack;
            ReactSharedInternals.getCurrentStack = function() {
                var stack = describeUnknownElementTypeFrameInDEV(element.type);
                prevGetCurrentStack && (stack += prevGetCurrentStack() || "");
                return stack;
            };
            console.error('Each child in a list should have a unique "key" prop.%s%s See https://react.dev/link/warning-keys for more information.', parentType, childOwner);
            ReactSharedInternals.getCurrentStack = prevGetCurrentStack;
        }
    }
    function getCurrentComponentErrorInfo(parentType) {
        var info = "", owner = getOwner();
        owner && (owner = getComponentNameFromType(owner.type)) && (info = "\n\nCheck the render method of `" + owner + "`.");
        info || (parentType = getComponentNameFromType(parentType)) && (info = "\n\nCheck the top-level render call using <" + parentType + ">.");
        return info;
    }
    var React = __turbopack_require__("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)"), REACT_ELEMENT_TYPE = Symbol.for("react.transitional.element"), REACT_PORTAL_TYPE = Symbol.for("react.portal"), REACT_FRAGMENT_TYPE = Symbol.for("react.fragment"), REACT_STRICT_MODE_TYPE = Symbol.for("react.strict_mode"), REACT_PROFILER_TYPE = Symbol.for("react.profiler");
    Symbol.for("react.provider");
    var REACT_CONSUMER_TYPE = Symbol.for("react.consumer"), REACT_CONTEXT_TYPE = Symbol.for("react.context"), REACT_FORWARD_REF_TYPE = Symbol.for("react.forward_ref"), REACT_SUSPENSE_TYPE = Symbol.for("react.suspense"), REACT_SUSPENSE_LIST_TYPE = Symbol.for("react.suspense_list"), REACT_MEMO_TYPE = Symbol.for("react.memo"), REACT_LAZY_TYPE = Symbol.for("react.lazy"), REACT_OFFSCREEN_TYPE = Symbol.for("react.offscreen"), MAYBE_ITERATOR_SYMBOL = Symbol.iterator, REACT_CLIENT_REFERENCE$2 = Symbol.for("react.client.reference"), ReactSharedInternals = React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE, hasOwnProperty = Object.prototype.hasOwnProperty, assign = Object.assign, REACT_CLIENT_REFERENCE$1 = Symbol.for("react.client.reference"), isArrayImpl = Array.isArray, disabledDepth = 0, prevLog, prevInfo, prevWarn, prevError, prevGroup, prevGroupCollapsed, prevGroupEnd;
    disabledLog.__reactDisabledLog = !0;
    var prefix, suffix, reentry = !1;
    var componentFrameCache = new ("function" === typeof WeakMap ? WeakMap : Map)();
    var REACT_CLIENT_REFERENCE = Symbol.for("react.client.reference"), specialPropKeyWarningShown;
    var didWarnAboutElementRef = {};
    var didWarnAboutKeySpread = {}, ownerHasKeyUseWarning = {};
    exports.Fragment = REACT_FRAGMENT_TYPE;
    exports.jsxDEV = function(type, config, maybeKey, isStaticChildren, source, self) {
        return jsxDEVImpl(type, config, maybeKey, isStaticChildren, source, self);
    };
}();
}}),
"[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
'use strict';
if ("TURBOPACK compile-time falsy", 0) {
    "TURBOPACK unreachable";
} else {
    module.exports = __turbopack_require__("[project]/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js [app-client] (ecmascript)");
}
}}),
}]);

//# sourceMappingURL=_101b37._.js.map