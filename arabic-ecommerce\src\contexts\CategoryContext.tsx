'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

export interface Category {
  id: string;
  name: string;
  description: string;
  isActive: boolean;
  productCount: number;
  createdAt: Date;
  updatedAt: Date;
}

interface CategoryContextType {
  categories: Category[];
  addCategory: (category: Omit<Category, 'id' | 'productCount' | 'createdAt' | 'updatedAt'>) => void;
  updateCategory: (id: string, category: Omit<Category, 'id' | 'productCount' | 'createdAt' | 'updatedAt'>) => void;
  deleteCategory: (id: string) => void;
  getCategoryById: (id: string) => Category | undefined;
  getActiveCategories: () => Category[];
  updateProductCount: (categoryName: string, count: number) => void;
  isLoading: boolean;
}

const CategoryContext = createContext<CategoryContextType | undefined>(undefined);

interface CategoryProviderProps {
  children: ReactNode;
}

// Default categories
const defaultCategories: Category[] = [
  {
    id: '1',
    name: 'إلكترونيات',
    description: 'أجهزة إلكترونية وتقنية حديثة',
    isActive: true,
    productCount: 0,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: '2',
    name: 'إكسسوارات',
    description: 'إكسسوارات متنوعة وعملية',
    isActive: true,
    productCount: 0,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: '3',
    name: 'ملابس',
    description: 'ملابس عصرية للرجال والنساء',
    isActive: true,
    productCount: 0,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: '4',
    name: 'كتب',
    description: 'كتب ومراجع في مختلف المجالات',
    isActive: true,
    productCount: 0,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: '5',
    name: 'رياضة',
    description: 'معدات وأدوات رياضية',
    isActive: true,
    productCount: 0,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: '6',
    name: 'منزل وحديقة',
    description: 'أدوات ومستلزمات المنزل والحديقة',
    isActive: true,
    productCount: 0,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
];

export function CategoryProvider({ children }: CategoryProviderProps) {
  const [categories, setCategories] = useState<Category[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Initialize categories from localStorage or use default data
  useEffect(() => {
    const initializeCategories = () => {
      try {
        const savedCategories = localStorage.getItem('ecommerce-categories');
        if (savedCategories) {
          const parsedCategories = JSON.parse(savedCategories);
          // Convert date strings back to Date objects
          const categoriesWithDates = parsedCategories.map((category: any) => ({
            ...category,
            createdAt: new Date(category.createdAt),
            updatedAt: new Date(category.updatedAt),
          }));
          setCategories(categoriesWithDates);
        } else {
          // First time - use default data
          setCategories(defaultCategories);
          localStorage.setItem('ecommerce-categories', JSON.stringify(defaultCategories));
        }
      } catch (error) {
        console.error('Error loading categories from localStorage:', error);
        setCategories(defaultCategories);
      } finally {
        setIsLoading(false);
      }
    };

    initializeCategories();
  }, []);

  // Save to localStorage whenever categories change
  useEffect(() => {
    if (!isLoading && categories.length > 0) {
      try {
        localStorage.setItem('ecommerce-categories', JSON.stringify(categories));
      } catch (error) {
        console.error('Error saving categories to localStorage:', error);
      }
    }
  }, [categories, isLoading]);

  const addCategory = (categoryData: Omit<Category, 'id' | 'productCount' | 'createdAt' | 'updatedAt'>) => {
    const newCategory: Category = {
      ...categoryData,
      id: Date.now().toString(),
      productCount: 0,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    setCategories(prev => [...prev, newCategory]);
  };

  const updateCategory = (id: string, categoryData: Omit<Category, 'id' | 'productCount' | 'createdAt' | 'updatedAt'>) => {
    setCategories(prev => prev.map(category => 
      category.id === id 
        ? { ...category, ...categoryData, updatedAt: new Date() }
        : category
    ));
  };

  const deleteCategory = (id: string) => {
    setCategories(prev => prev.filter(category => category.id !== id));
  };

  const getCategoryById = (id: string): Category | undefined => {
    return categories.find(category => category.id === id);
  };

  const getActiveCategories = (): Category[] => {
    return categories.filter(category => category.isActive);
  };

  const updateProductCount = (categoryName: string, count: number) => {
    setCategories(prev => prev.map(category => 
      category.name === categoryName 
        ? { ...category, productCount: count }
        : category
    ));
  };

  const value: CategoryContextType = {
    categories,
    addCategory,
    updateCategory,
    deleteCategory,
    getCategoryById,
    getActiveCategories,
    updateProductCount,
    isLoading,
  };

  return (
    <CategoryContext.Provider value={value}>
      {children}
    </CategoryContext.Provider>
  );
}

export function useCategories() {
  const context = useContext(CategoryContext);
  if (context === undefined) {
    throw new Error('useCategories must be used within a CategoryProvider');
  }
  return context;
}
