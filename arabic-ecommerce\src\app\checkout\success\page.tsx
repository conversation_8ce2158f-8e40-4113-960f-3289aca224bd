'use client';

import { useEffect, useState } from 'react';
import { useSearchParams } from 'next/navigation';
import Link from 'next/link';
import Navbar from '@/components/Navbar';

export default function CheckoutSuccessPage() {
  const searchParams = useSearchParams();
  const orderNumber = searchParams.get('order');
  const [showConfetti, setShowConfetti] = useState(true);

  useEffect(() => {
    // Hide confetti after 3 seconds
    const timer = setTimeout(() => {
      setShowConfetti(false);
    }, 3000);

    return () => clearTimeout(timer);
  }, []);

  if (!orderNumber) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navbar />
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center">
            <div className="text-6xl mb-4">❌</div>
            <h2 className="text-2xl font-bold text-gray-800 mb-4">
              خطأ في الطلب
            </h2>
            <p className="text-gray-600 mb-8">
              لم يتم العثور على معلومات الطلب
            </p>
            <Link
              href="/"
              className="inline-block bg-primary-600 hover:bg-primary-700 text-white font-bold py-3 px-8 rounded-lg transition-colors duration-200"
            >
              العودة للرئيسية
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 relative">
      <Navbar />
      
      {/* Confetti Animation */}
      {showConfetti && (
        <div className="fixed inset-0 pointer-events-none z-50">
          <div className="absolute inset-0 overflow-hidden">
            {[...Array(50)].map((_, i) => (
              <div
                key={i}
                className="absolute animate-bounce"
                style={{
                  left: `${Math.random() * 100}%`,
                  top: `${Math.random() * 100}%`,
                  animationDelay: `${Math.random() * 2}s`,
                  animationDuration: `${2 + Math.random() * 2}s`,
                }}
              >
                <span className="text-2xl">
                  {['🎉', '🎊', '✨', '🌟', '💫'][Math.floor(Math.random() * 5)]}
                </span>
              </div>
            ))}
          </div>
        </div>
      )}

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="bg-white rounded-lg shadow-lg overflow-hidden">
          {/* Success Header */}
          <div className="bg-gradient-to-r from-green-500 to-green-600 px-8 py-12 text-center">
            <div className="text-6xl mb-4">✅</div>
            <h1 className="text-3xl font-bold text-white mb-2">
              تم تأكيد طلبك بنجاح!
            </h1>
            <p className="text-green-100 text-lg">
              شكراً لك على ثقتك في متجرنا
            </p>
          </div>

          {/* Order Details */}
          <div className="px-8 py-8">
            <div className="text-center mb-8">
              <h2 className="text-2xl font-bold text-gray-800 mb-2">
                رقم الطلب
              </h2>
              <div className="inline-block bg-gray-100 px-6 py-3 rounded-lg">
                <span className="text-xl font-mono font-bold text-primary-600">
                  {orderNumber}
                </span>
              </div>
            </div>

            {/* Order Status Timeline */}
            <div className="mb-8">
              <h3 className="text-lg font-semibold text-gray-800 mb-4">حالة الطلب</h3>
              <div className="flex items-center justify-between">
                <div className="flex flex-col items-center">
                  <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                    <span className="text-white text-sm">✓</span>
                  </div>
                  <span className="text-sm text-gray-600 mt-2">تم الطلب</span>
                </div>
                <div className="flex-1 h-1 bg-gray-200 mx-2">
                  <div className="h-full bg-yellow-400 w-1/3"></div>
                </div>
                <div className="flex flex-col items-center">
                  <div className="w-8 h-8 bg-yellow-400 rounded-full flex items-center justify-center">
                    <span className="text-white text-sm">⏳</span>
                  </div>
                  <span className="text-sm text-gray-600 mt-2">قيد التحضير</span>
                </div>
                <div className="flex-1 h-1 bg-gray-200 mx-2"></div>
                <div className="flex flex-col items-center">
                  <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                    <span className="text-gray-500 text-sm">📦</span>
                  </div>
                  <span className="text-sm text-gray-600 mt-2">الشحن</span>
                </div>
                <div className="flex-1 h-1 bg-gray-200 mx-2"></div>
                <div className="flex flex-col items-center">
                  <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                    <span className="text-gray-500 text-sm">🏠</span>
                  </div>
                  <span className="text-sm text-gray-600 mt-2">التسليم</span>
                </div>
              </div>
            </div>

            {/* What's Next */}
            <div className="bg-blue-50 rounded-lg p-6 mb-8">
              <h3 className="text-lg font-semibold text-blue-800 mb-3">ما التالي؟</h3>
              <ul className="space-y-2 text-blue-700">
                <li className="flex items-center">
                  <span className="text-blue-500 ml-2">📧</span>
                  سيتم إرسال تأكيد الطلب إلى بريدك الإلكتروني
                </li>
                <li className="flex items-center">
                  <span className="text-blue-500 ml-2">📱</span>
                  ستتلقى رسالة نصية عند شحن الطلب
                </li>
                <li className="flex items-center">
                  <span className="text-blue-500 ml-2">🚚</span>
                  سيتم التسليم خلال 2-3 أيام عمل
                </li>
                <li className="flex items-center">
                  <span className="text-blue-500 ml-2">💬</span>
                  يمكنك التواصل معنا في أي وقت للاستفسار
                </li>
              </ul>
            </div>

            {/* Order Summary */}
            <div className="bg-gray-50 rounded-lg p-6 mb-8">
              <h3 className="text-lg font-semibold text-gray-800 mb-4">ملخص الطلب</h3>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-600">تاريخ الطلب</span>
                  <span className="font-medium">
                    {new Date().toLocaleDateString('ar-SA', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric',
                    })}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">طريقة الدفع</span>
                  <span className="font-medium">الدفع عند الاستلام</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">وقت التسليم المتوقع</span>
                  <span className="font-medium">2-3 أيام عمل</span>
                </div>
              </div>
            </div>

            {/* Contact Information */}
            <div className="bg-yellow-50 rounded-lg p-6 mb-8">
              <h3 className="text-lg font-semibold text-yellow-800 mb-3">تحتاج مساعدة؟</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-yellow-700">
                <div className="flex items-center">
                  <span className="text-yellow-600 ml-2">📞</span>
                  <span>الهاتف: 920000000</span>
                </div>
                <div className="flex items-center">
                  <span className="text-yellow-600 ml-2">📧</span>
                  <span>البريد: <EMAIL></span>
                </div>
                <div className="flex items-center">
                  <span className="text-yellow-600 ml-2">💬</span>
                  <span>الدردشة المباشرة متاحة 24/7</span>
                </div>
                <div className="flex items-center">
                  <span className="text-yellow-600 ml-2">🕒</span>
                  <span>ساعات العمل: 8 ص - 10 م</span>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/products"
                className="bg-primary-600 hover:bg-primary-700 text-white font-bold py-3 px-8 rounded-lg transition-colors duration-200 text-center"
              >
                متابعة التسوق
              </Link>
              <Link
                href="/"
                className="bg-gray-200 hover:bg-gray-300 text-gray-800 font-bold py-3 px-8 rounded-lg transition-colors duration-200 text-center"
              >
                العودة للرئيسية
              </Link>
              <button
                onClick={() => window.print()}
                className="bg-green-600 hover:bg-green-700 text-white font-bold py-3 px-8 rounded-lg transition-colors duration-200"
              >
                طباعة التأكيد
              </button>
            </div>
          </div>
        </div>

        {/* Additional Information */}
        <div className="mt-8 text-center text-gray-600">
          <p className="mb-2">
            💡 <strong>نصيحة:</strong> احتفظ برقم الطلب للمراجعة المستقبلية
          </p>
          <p>
            🔒 جميع معاملاتك محمية بأعلى معايير الأمان
          </p>
        </div>
      </div>
    </div>
  );
}
