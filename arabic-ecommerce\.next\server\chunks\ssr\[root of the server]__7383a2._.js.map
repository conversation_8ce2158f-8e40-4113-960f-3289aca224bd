{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/New%20folder%20%288%29/arabic-ecommerce/src/lib/data.ts"], "sourcesContent": ["import { Product, User } from '@/types';\n\n// Sample products data\nexport const sampleProducts: Product[] = [\n  {\n    id: '1',\n    name: 'هاتف ذكي متطور',\n    description: 'هاتف ذكي بمواصفات عالية وكاميرا متقدمة',\n    price: 2500,\n    image: '/images/phone.jpg',\n    category: 'إلكترونيات',\n    stock: 15,\n    featured: true,\n    createdAt: new Date(),\n    updatedAt: new Date(),\n  },\n  {\n    id: '2',\n    name: 'لابتوب للألعاب',\n    description: 'لابتوب قوي مخصص للألعاب والتصميم',\n    price: 4500,\n    image: '/images/laptop.jpg',\n    category: 'إلكترونيات',\n    stock: 8,\n    featured: true,\n    createdAt: new Date(),\n    updatedAt: new Date(),\n  },\n  {\n    id: '3',\n    name: 'ساعة ذكية',\n    description: 'ساعة ذكية لتتبع اللياقة البدنية',\n    price: 800,\n    image: '/images/watch.jpg',\n    category: 'إكسسوارات',\n    stock: 25,\n    featured: false,\n    createdAt: new Date(),\n    updatedAt: new Date(),\n  },\n  {\n    id: '4',\n    name: 'سماعات لاسلكية',\n    description: 'سماعات بلوتوث عالية الجودة',\n    price: 350,\n    image: '/images/headphones.jpg',\n    category: 'إكسسوارات',\n    stock: 30,\n    featured: true,\n    createdAt: new Date(),\n    updatedAt: new Date(),\n  },\n  {\n    id: '5',\n    name: 'كاميرا رقمية',\n    description: 'كاميرا احترافية للتصوير الفوتوغرافي',\n    price: 3200,\n    image: '/images/camera.jpg',\n    category: 'إلكترونيات',\n    stock: 12,\n    featured: false,\n    createdAt: new Date(),\n    updatedAt: new Date(),\n  },\n  {\n    id: '6',\n    name: 'تابلت للرسم',\n    description: 'تابلت مخصص للرسم والتصميم الرقمي',\n    price: 1800,\n    image: '/images/tablet.jpg',\n    category: 'إلكترونيات',\n    stock: 18,\n    featured: true,\n    createdAt: new Date(),\n    updatedAt: new Date(),\n  },\n];\n\n// Sample users data\nexport const sampleUsers: User[] = [\n  {\n    id: '1',\n    name: 'أحمد محمد',\n    email: '<EMAIL>',\n    role: 'admin',\n    createdAt: new Date(),\n    updatedAt: new Date(),\n  },\n  {\n    id: '2',\n    name: 'فاطمة علي',\n    email: '<EMAIL>',\n    role: 'customer',\n    createdAt: new Date(),\n    updatedAt: new Date(),\n  },\n];\n\n// Categories (deprecated - use CategoryContext instead)\nexport const categories = [\n  'إلكترونيات',\n  'إكسسوارات',\n  'ملابس',\n  'كتب',\n  'رياضة',\n  'منزل وحديقة',\n];\n"], "names": [], "mappings": ";;;;;AAGO,MAAM,iBAA4B;IACvC;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,OAAO;QACP,UAAU;QACV,OAAO;QACP,UAAU;QACV,WAAW,IAAI;QACf,WAAW,IAAI;IACjB;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,OAAO;QACP,UAAU;QACV,OAAO;QACP,UAAU;QACV,WAAW,IAAI;QACf,WAAW,IAAI;IACjB;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,OAAO;QACP,UAAU;QACV,OAAO;QACP,UAAU;QACV,WAAW,IAAI;QACf,WAAW,IAAI;IACjB;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,OAAO;QACP,UAAU;QACV,OAAO;QACP,UAAU;QACV,WAAW,IAAI;QACf,WAAW,IAAI;IACjB;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,OAAO;QACP,UAAU;QACV,OAAO;QACP,UAAU;QACV,WAAW,IAAI;QACf,WAAW,IAAI;IACjB;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,OAAO;QACP,UAAU;QACV,OAAO;QACP,UAAU;QACV,WAAW,IAAI;QACf,WAAW,IAAI;IACjB;CACD;AAGM,MAAM,cAAsB;IACjC;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,MAAM;QACN,WAAW,IAAI;QACf,WAAW,IAAI;IACjB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,MAAM;QACN,WAAW,IAAI;QACf,WAAW,IAAI;IACjB;CACD;AAGM,MAAM,aAAa;IACxB;IACA;IACA;IACA;IACA;IACA;CACD"}}, {"offset": {"line": 120, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 126, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/New%20folder%20%288%29/arabic-ecommerce/src/contexts/ProductContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';\nimport { Product } from '@/types';\nimport { sampleProducts } from '@/lib/data';\n\ninterface ProductContextType {\n  products: Product[];\n  addProduct: (product: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>) => void;\n  updateProduct: (id: string, product: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>) => void;\n  deleteProduct: (id: string) => void;\n  getProductById: (id: string) => Product | undefined;\n  getFeaturedProducts: () => Product[];\n  isLoading: boolean;\n}\n\nconst ProductContext = createContext<ProductContextType | undefined>(undefined);\n\ninterface ProductProviderProps {\n  children: ReactNode;\n}\n\nexport function ProductProvider({ children }: ProductProviderProps) {\n  const [products, setProducts] = useState<Product[]>([]);\n  const [isLoading, setIsLoading] = useState(true);\n\n  // Initialize products from localStorage or use sample data\n  useEffect(() => {\n    const initializeProducts = () => {\n      try {\n        const savedProducts = localStorage.getItem('ecommerce-products');\n        if (savedProducts) {\n          const parsedProducts = JSON.parse(savedProducts);\n          // Convert date strings back to Date objects\n          const productsWithDates = parsedProducts.map((product: any) => ({\n            ...product,\n            createdAt: new Date(product.createdAt),\n            updatedAt: new Date(product.updatedAt),\n          }));\n          setProducts(productsWithDates);\n        } else {\n          // First time - use sample data\n          setProducts(sampleProducts);\n          localStorage.setItem('ecommerce-products', JSON.stringify(sampleProducts));\n        }\n      } catch (error) {\n        console.error('Error loading products from localStorage:', error);\n        setProducts(sampleProducts);\n      } finally {\n        setIsLoading(false);\n      }\n    };\n\n    initializeProducts();\n  }, []);\n\n  // Save to localStorage whenever products change\n  useEffect(() => {\n    if (!isLoading && products.length > 0) {\n      try {\n        localStorage.setItem('ecommerce-products', JSON.stringify(products));\n      } catch (error) {\n        console.error('Error saving products to localStorage:', error);\n      }\n    }\n  }, [products, isLoading]);\n\n  const addProduct = (productData: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>) => {\n    const newProduct: Product = {\n      ...productData,\n      id: Date.now().toString(),\n      createdAt: new Date(),\n      updatedAt: new Date(),\n    };\n    setProducts(prev => [...prev, newProduct]);\n  };\n\n  const updateProduct = (id: string, productData: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>) => {\n    setProducts(prev => prev.map(product => \n      product.id === id \n        ? { ...product, ...productData, updatedAt: new Date() }\n        : product\n    ));\n  };\n\n  const deleteProduct = (id: string) => {\n    setProducts(prev => prev.filter(product => product.id !== id));\n  };\n\n  const getProductById = (id: string): Product | undefined => {\n    return products.find(product => product.id === id);\n  };\n\n  const getFeaturedProducts = (): Product[] => {\n    return products.filter(product => product.featured);\n  };\n\n  const value: ProductContextType = {\n    products,\n    addProduct,\n    updateProduct,\n    deleteProduct,\n    getProductById,\n    getFeaturedProducts,\n    isLoading,\n  };\n\n  return (\n    <ProductContext.Provider value={value}>\n      {children}\n    </ProductContext.Provider>\n  );\n}\n\nexport function useProducts() {\n  const context = useContext(ProductContext);\n  if (context === undefined) {\n    throw new Error('useProducts must be used within a ProductProvider');\n  }\n  return context;\n}\n\n// Hook for getting products with optional filtering\nexport function useFilteredProducts(filters?: {\n  category?: string;\n  priceRange?: string;\n  searchTerm?: string;\n  featured?: boolean;\n}) {\n  const { products } = useProducts();\n\n  return React.useMemo(() => {\n    if (!filters) return products;\n\n    return products.filter(product => {\n      const matchesCategory = !filters.category || product.category === filters.category;\n      const matchesSearch = !filters.searchTerm || \n        product.name.toLowerCase().includes(filters.searchTerm.toLowerCase()) ||\n        product.description.toLowerCase().includes(filters.searchTerm.toLowerCase());\n      \n      let matchesPrice = true;\n      if (filters.priceRange) {\n        switch (filters.priceRange) {\n          case 'under-500':\n            matchesPrice = product.price < 500;\n            break;\n          case '500-1000':\n            matchesPrice = product.price >= 500 && product.price <= 1000;\n            break;\n          case '1000-3000':\n            matchesPrice = product.price >= 1000 && product.price <= 3000;\n            break;\n          case 'over-3000':\n            matchesPrice = product.price > 3000;\n            break;\n        }\n      }\n\n      const matchesFeatured = filters.featured === undefined || product.featured === filters.featured;\n\n      return matchesCategory && matchesSearch && matchesPrice && matchesFeatured;\n    });\n  }, [products, filters]);\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AAEA;AAJA;;;;AAgBA,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAkC;AAM9D,SAAS,gBAAgB,EAAE,QAAQ,EAAwB;IAChE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,2DAA2D;IAC3D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,qBAAqB;YACzB,IAAI;gBACF,MAAM,gBAAgB,aAAa,OAAO,CAAC;gBAC3C,IAAI,eAAe;oBACjB,MAAM,iBAAiB,KAAK,KAAK,CAAC;oBAClC,4CAA4C;oBAC5C,MAAM,oBAAoB,eAAe,GAAG,CAAC,CAAC,UAAiB,CAAC;4BAC9D,GAAG,OAAO;4BACV,WAAW,IAAI,KAAK,QAAQ,SAAS;4BACrC,WAAW,IAAI,KAAK,QAAQ,SAAS;wBACvC,CAAC;oBACD,YAAY;gBACd,OAAO;oBACL,+BAA+B;oBAC/B,YAAY,kHAAA,CAAA,iBAAc;oBAC1B,aAAa,OAAO,CAAC,sBAAsB,KAAK,SAAS,CAAC,kHAAA,CAAA,iBAAc;gBAC1E;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,6CAA6C;gBAC3D,YAAY,kHAAA,CAAA,iBAAc;YAC5B,SAAU;gBACR,aAAa;YACf;QACF;QAEA;IACF,GAAG,EAAE;IAEL,gDAAgD;IAChD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,aAAa,SAAS,MAAM,GAAG,GAAG;YACrC,IAAI;gBACF,aAAa,OAAO,CAAC,sBAAsB,KAAK,SAAS,CAAC;YAC5D,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,0CAA0C;YAC1D;QACF;IACF,GAAG;QAAC;QAAU;KAAU;IAExB,MAAM,aAAa,CAAC;QAClB,MAAM,aAAsB;YAC1B,GAAG,WAAW;YACd,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,WAAW,IAAI;YACf,WAAW,IAAI;QACjB;QACA,YAAY,CAAA,OAAQ;mBAAI;gBAAM;aAAW;IAC3C;IAEA,MAAM,gBAAgB,CAAC,IAAY;QACjC,YAAY,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,UAC3B,QAAQ,EAAE,KAAK,KACX;oBAAE,GAAG,OAAO;oBAAE,GAAG,WAAW;oBAAE,WAAW,IAAI;gBAAO,IACpD;IAER;IAEA,MAAM,gBAAgB,CAAC;QACrB,YAAY,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK;IAC5D;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAO,SAAS,IAAI,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK;IACjD;IAEA,MAAM,sBAAsB;QAC1B,OAAO,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,QAAQ;IACpD;IAEA,MAAM,QAA4B;QAChC;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,8OAAC,eAAe,QAAQ;QAAC,OAAO;kBAC7B;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAGO,SAAS,oBAAoB,OAKnC;IACC,MAAM,EAAE,QAAQ,EAAE,GAAG;IAErB,OAAO,qMAAA,CAAA,UAAK,CAAC,OAAO,CAAC;QACnB,IAAI,CAAC,SAAS,OAAO;QAErB,OAAO,SAAS,MAAM,CAAC,CAAA;YACrB,MAAM,kBAAkB,CAAC,QAAQ,QAAQ,IAAI,QAAQ,QAAQ,KAAK,QAAQ,QAAQ;YAClF,MAAM,gBAAgB,CAAC,QAAQ,UAAU,IACvC,QAAQ,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,QAAQ,UAAU,CAAC,WAAW,OAClE,QAAQ,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,QAAQ,UAAU,CAAC,WAAW;YAE3E,IAAI,eAAe;YACnB,IAAI,QAAQ,UAAU,EAAE;gBACtB,OAAQ,QAAQ,UAAU;oBACxB,KAAK;wBACH,eAAe,QAAQ,KAAK,GAAG;wBAC/B;oBACF,KAAK;wBACH,eAAe,QAAQ,KAAK,IAAI,OAAO,QAAQ,KAAK,IAAI;wBACxD;oBACF,KAAK;wBACH,eAAe,QAAQ,KAAK,IAAI,QAAQ,QAAQ,KAAK,IAAI;wBACzD;oBACF,KAAK;wBACH,eAAe,QAAQ,KAAK,GAAG;wBAC/B;gBACJ;YACF;YAEA,MAAM,kBAAkB,QAAQ,QAAQ,KAAK,aAAa,QAAQ,QAAQ,KAAK,QAAQ,QAAQ;YAE/F,OAAO,mBAAmB,iBAAiB,gBAAgB;QAC7D;IACF,GAAG;QAAC;QAAU;KAAQ;AACxB"}}, {"offset": {"line": 268, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 274, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/New%20folder%20%288%29/arabic-ecommerce/src/contexts/CategoryContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';\n\nexport interface Category {\n  id: string;\n  name: string;\n  description: string;\n  isActive: boolean;\n  productCount: number;\n  createdAt: Date;\n  updatedAt: Date;\n}\n\ninterface CategoryContextType {\n  categories: Category[];\n  addCategory: (category: Omit<Category, 'id' | 'productCount' | 'createdAt' | 'updatedAt'>) => void;\n  updateCategory: (id: string, category: Omit<Category, 'id' | 'productCount' | 'createdAt' | 'updatedAt'>) => void;\n  deleteCategory: (id: string) => void;\n  getCategoryById: (id: string) => Category | undefined;\n  getActiveCategories: () => Category[];\n  updateProductCount: (categoryName: string, count: number) => void;\n  isLoading: boolean;\n}\n\nconst CategoryContext = createContext<CategoryContextType | undefined>(undefined);\n\ninterface CategoryProviderProps {\n  children: ReactNode;\n}\n\n// Default categories\nconst defaultCategories: Category[] = [\n  {\n    id: '1',\n    name: 'إلكترونيات',\n    description: 'أجهزة إلكترونية وتقنية حديثة',\n    isActive: true,\n    productCount: 0,\n    createdAt: new Date(),\n    updatedAt: new Date(),\n  },\n  {\n    id: '2',\n    name: 'إكسسوارات',\n    description: 'إكسسوارات متنوعة وعملية',\n    isActive: true,\n    productCount: 0,\n    createdAt: new Date(),\n    updatedAt: new Date(),\n  },\n  {\n    id: '3',\n    name: 'ملابس',\n    description: 'ملابس عصرية للرجال والنساء',\n    isActive: true,\n    productCount: 0,\n    createdAt: new Date(),\n    updatedAt: new Date(),\n  },\n  {\n    id: '4',\n    name: 'كتب',\n    description: 'كتب ومراجع في مختلف المجالات',\n    isActive: true,\n    productCount: 0,\n    createdAt: new Date(),\n    updatedAt: new Date(),\n  },\n  {\n    id: '5',\n    name: 'رياضة',\n    description: 'معدات وأدوات رياضية',\n    isActive: true,\n    productCount: 0,\n    createdAt: new Date(),\n    updatedAt: new Date(),\n  },\n  {\n    id: '6',\n    name: 'منزل وحديقة',\n    description: 'أدوات ومستلزمات المنزل والحديقة',\n    isActive: true,\n    productCount: 0,\n    createdAt: new Date(),\n    updatedAt: new Date(),\n  },\n];\n\nexport function CategoryProvider({ children }: CategoryProviderProps) {\n  const [categories, setCategories] = useState<Category[]>([]);\n  const [isLoading, setIsLoading] = useState(true);\n\n  // Initialize categories from localStorage or use default data\n  useEffect(() => {\n    const initializeCategories = () => {\n      try {\n        const savedCategories = localStorage.getItem('ecommerce-categories');\n        if (savedCategories) {\n          const parsedCategories = JSON.parse(savedCategories);\n          // Convert date strings back to Date objects\n          const categoriesWithDates = parsedCategories.map((category: any) => ({\n            ...category,\n            createdAt: new Date(category.createdAt),\n            updatedAt: new Date(category.updatedAt),\n          }));\n          setCategories(categoriesWithDates);\n        } else {\n          // First time - use default data\n          setCategories(defaultCategories);\n          localStorage.setItem('ecommerce-categories', JSON.stringify(defaultCategories));\n        }\n      } catch (error) {\n        console.error('Error loading categories from localStorage:', error);\n        setCategories(defaultCategories);\n      } finally {\n        setIsLoading(false);\n      }\n    };\n\n    initializeCategories();\n  }, []);\n\n  // Save to localStorage whenever categories change\n  useEffect(() => {\n    if (!isLoading && categories.length > 0) {\n      try {\n        localStorage.setItem('ecommerce-categories', JSON.stringify(categories));\n      } catch (error) {\n        console.error('Error saving categories to localStorage:', error);\n      }\n    }\n  }, [categories, isLoading]);\n\n  const addCategory = (categoryData: Omit<Category, 'id' | 'productCount' | 'createdAt' | 'updatedAt'>) => {\n    const newCategory: Category = {\n      ...categoryData,\n      id: Date.now().toString(),\n      productCount: 0,\n      createdAt: new Date(),\n      updatedAt: new Date(),\n    };\n    setCategories(prev => [...prev, newCategory]);\n  };\n\n  const updateCategory = (id: string, categoryData: Omit<Category, 'id' | 'productCount' | 'createdAt' | 'updatedAt'>) => {\n    setCategories(prev => prev.map(category => \n      category.id === id \n        ? { ...category, ...categoryData, updatedAt: new Date() }\n        : category\n    ));\n  };\n\n  const deleteCategory = (id: string) => {\n    setCategories(prev => prev.filter(category => category.id !== id));\n  };\n\n  const getCategoryById = (id: string): Category | undefined => {\n    return categories.find(category => category.id === id);\n  };\n\n  const getActiveCategories = (): Category[] => {\n    return categories.filter(category => category.isActive);\n  };\n\n  const updateProductCount = (categoryName: string, count: number) => {\n    setCategories(prev => prev.map(category => \n      category.name === categoryName \n        ? { ...category, productCount: count }\n        : category\n    ));\n  };\n\n  const value: CategoryContextType = {\n    categories,\n    addCategory,\n    updateCategory,\n    deleteCategory,\n    getCategoryById,\n    getActiveCategories,\n    updateProductCount,\n    isLoading,\n  };\n\n  return (\n    <CategoryContext.Provider value={value}>\n      {children}\n    </CategoryContext.Provider>\n  );\n}\n\nexport function useCategories() {\n  const context = useContext(CategoryContext);\n  if (context === undefined) {\n    throw new Error('useCategories must be used within a CategoryProvider');\n  }\n  return context;\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAyBA,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAmC;AAMvE,qBAAqB;AACrB,MAAM,oBAAgC;IACpC;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,cAAc;QACd,WAAW,IAAI;QACf,WAAW,IAAI;IACjB;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,cAAc;QACd,WAAW,IAAI;QACf,WAAW,IAAI;IACjB;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,cAAc;QACd,WAAW,IAAI;QACf,WAAW,IAAI;IACjB;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,cAAc;QACd,WAAW,IAAI;QACf,WAAW,IAAI;IACjB;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,cAAc;QACd,WAAW,IAAI;QACf,WAAW,IAAI;IACjB;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,cAAc;QACd,WAAW,IAAI;QACf,WAAW,IAAI;IACjB;CACD;AAEM,SAAS,iBAAiB,EAAE,QAAQ,EAAyB;IAClE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IAC3D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,8DAA8D;IAC9D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,uBAAuB;YAC3B,IAAI;gBACF,MAAM,kBAAkB,aAAa,OAAO,CAAC;gBAC7C,IAAI,iBAAiB;oBACnB,MAAM,mBAAmB,KAAK,KAAK,CAAC;oBACpC,4CAA4C;oBAC5C,MAAM,sBAAsB,iBAAiB,GAAG,CAAC,CAAC,WAAkB,CAAC;4BACnE,GAAG,QAAQ;4BACX,WAAW,IAAI,KAAK,SAAS,SAAS;4BACtC,WAAW,IAAI,KAAK,SAAS,SAAS;wBACxC,CAAC;oBACD,cAAc;gBAChB,OAAO;oBACL,gCAAgC;oBAChC,cAAc;oBACd,aAAa,OAAO,CAAC,wBAAwB,KAAK,SAAS,CAAC;gBAC9D;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,+CAA+C;gBAC7D,cAAc;YAChB,SAAU;gBACR,aAAa;YACf;QACF;QAEA;IACF,GAAG,EAAE;IAEL,kDAAkD;IAClD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,aAAa,WAAW,MAAM,GAAG,GAAG;YACvC,IAAI;gBACF,aAAa,OAAO,CAAC,wBAAwB,KAAK,SAAS,CAAC;YAC9D,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,4CAA4C;YAC5D;QACF;IACF,GAAG;QAAC;QAAY;KAAU;IAE1B,MAAM,cAAc,CAAC;QACnB,MAAM,cAAwB;YAC5B,GAAG,YAAY;YACf,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,cAAc;YACd,WAAW,IAAI;YACf,WAAW,IAAI;QACjB;QACA,cAAc,CAAA,OAAQ;mBAAI;gBAAM;aAAY;IAC9C;IAEA,MAAM,iBAAiB,CAAC,IAAY;QAClC,cAAc,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,WAC7B,SAAS,EAAE,KAAK,KACZ;oBAAE,GAAG,QAAQ;oBAAE,GAAG,YAAY;oBAAE,WAAW,IAAI;gBAAO,IACtD;IAER;IAEA,MAAM,iBAAiB,CAAC;QACtB,cAAc,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,WAAY,SAAS,EAAE,KAAK;IAChE;IAEA,MAAM,kBAAkB,CAAC;QACvB,OAAO,WAAW,IAAI,CAAC,CAAA,WAAY,SAAS,EAAE,KAAK;IACrD;IAEA,MAAM,sBAAsB;QAC1B,OAAO,WAAW,MAAM,CAAC,CAAA,WAAY,SAAS,QAAQ;IACxD;IAEA,MAAM,qBAAqB,CAAC,cAAsB;QAChD,cAAc,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,WAC7B,SAAS,IAAI,KAAK,eACd;oBAAE,GAAG,QAAQ;oBAAE,cAAc;gBAAM,IACnC;IAER;IAEA,MAAM,QAA6B;QACjC;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,8OAAC,gBAAgB,QAAQ;QAAC,OAAO;kBAC9B;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT"}}, {"offset": {"line": 446, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 452, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/New%20folder%20%288%29/arabic-ecommerce/src/contexts/OrderContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';\nimport { Order, OrderStatus } from '@/types';\n\ninterface OrderContextType {\n  orders: Order[];\n  addOrder: (order: Omit<Order, 'id' | 'orderNumber' | 'createdAt' | 'updatedAt'>) => void;\n  updateOrderStatus: (id: string, status: OrderStatus) => void;\n  updateOrder: (id: string, updates: Partial<Order>) => void;\n  deleteOrder: (id: string) => void;\n  getOrderById: (id: string) => Order | undefined;\n  getOrdersByStatus: (status: OrderStatus) => Order[];\n  getOrdersByCustomer: (customerId: string) => Order[];\n  getTotalRevenue: () => number;\n  getOrdersCount: () => number;\n  getRecentOrders: (limit?: number) => Order[];\n  isLoading: boolean;\n}\n\nconst OrderContext = createContext<OrderContextType | undefined>(undefined);\n\ninterface OrderProviderProps {\n  children: ReactNode;\n}\n\n// Sample orders data\nconst sampleOrders: Order[] = [\n  {\n    id: '1',\n    orderNumber: 'ORD-2024-001',\n    customerId: '1',\n    customerName: 'أحمد محمد',\n    customerEmail: '<EMAIL>',\n    items: [\n      {\n        id: '1',\n        productId: '1',\n        productName: 'لابتوب Dell XPS 13',\n        productImage: '/images/laptop.jpg',\n        price: 2500,\n        quantity: 1,\n        total: 2500,\n      },\n      {\n        id: '2',\n        productId: '2',\n        productName: 'سماعات لاسلكية',\n        productImage: '/images/headphones.jpg',\n        price: 150,\n        quantity: 2,\n        total: 300,\n      },\n    ],\n    subtotal: 2800,\n    shipping: 50,\n    tax: 420,\n    total: 3270,\n    status: 'confirmed',\n    paymentMethod: 'card',\n    paymentStatus: 'paid',\n    shippingAddress: {\n      fullName: 'أحمد محمد علي',\n      phone: '+************',\n      address: 'شارع الملك فهد، حي النخيل',\n      city: 'الرياض',\n      postalCode: '12345',\n      country: 'السعودية',\n    },\n    notes: 'يرجى التسليم في المساء',\n    createdAt: new Date('2024-01-15T10:30:00'),\n    updatedAt: new Date('2024-01-15T11:00:00'),\n  },\n  {\n    id: '2',\n    orderNumber: 'ORD-2024-002',\n    customerId: '2',\n    customerName: 'فاطمة أحمد',\n    customerEmail: '<EMAIL>',\n    items: [\n      {\n        id: '3',\n        productId: '3',\n        productName: 'هاتف ذكي Samsung Galaxy',\n        productImage: '/images/phone.jpg',\n        price: 1200,\n        quantity: 1,\n        total: 1200,\n      },\n    ],\n    subtotal: 1200,\n    shipping: 30,\n    tax: 180,\n    total: 1410,\n    status: 'processing',\n    paymentMethod: 'cash',\n    paymentStatus: 'pending',\n    shippingAddress: {\n      fullName: 'فاطمة أحمد محمد',\n      phone: '+966507654321',\n      address: 'طريق الأمير محمد بن عبدالعزيز',\n      city: 'جدة',\n      postalCode: '21589',\n      country: 'السعودية',\n    },\n    createdAt: new Date('2024-01-16T14:20:00'),\n    updatedAt: new Date('2024-01-16T15:45:00'),\n  },\n  {\n    id: '3',\n    orderNumber: 'ORD-2024-003',\n    customerId: '1',\n    customerName: 'أحمد محمد',\n    customerEmail: '<EMAIL>',\n    items: [\n      {\n        id: '4',\n        productId: '4',\n        productName: 'تابلت iPad Pro',\n        productImage: '/images/tablet.jpg',\n        price: 1800,\n        quantity: 1,\n        total: 1800,\n      },\n    ],\n    subtotal: 1800,\n    shipping: 40,\n    tax: 270,\n    total: 2110,\n    status: 'delivered',\n    paymentMethod: 'bank_transfer',\n    paymentStatus: 'paid',\n    shippingAddress: {\n      fullName: 'أحمد محمد علي',\n      phone: '+************',\n      address: 'شارع الملك فهد، حي النخيل',\n      city: 'الرياض',\n      postalCode: '12345',\n      country: 'السعودية',\n    },\n    createdAt: new Date('2024-01-10T09:15:00'),\n    updatedAt: new Date('2024-01-12T16:30:00'),\n    deliveredAt: new Date('2024-01-12T16:30:00'),\n  },\n];\n\nexport function OrderProvider({ children }: OrderProviderProps) {\n  const [orders, setOrders] = useState<Order[]>([]);\n  const [isLoading, setIsLoading] = useState(true);\n\n  // Initialize orders from localStorage or use sample data\n  useEffect(() => {\n    const initializeOrders = () => {\n      try {\n        const savedOrders = localStorage.getItem('ecommerce-orders');\n        if (savedOrders) {\n          const parsedOrders = JSON.parse(savedOrders);\n          // Convert date strings back to Date objects\n          const ordersWithDates = parsedOrders.map((order: any) => ({\n            ...order,\n            createdAt: new Date(order.createdAt),\n            updatedAt: new Date(order.updatedAt),\n            deliveredAt: order.deliveredAt ? new Date(order.deliveredAt) : undefined,\n          }));\n          setOrders(ordersWithDates);\n        } else {\n          // First time - use sample data\n          setOrders(sampleOrders);\n          localStorage.setItem('ecommerce-orders', JSON.stringify(sampleOrders));\n        }\n      } catch (error) {\n        console.error('Error loading orders from localStorage:', error);\n        setOrders(sampleOrders);\n      } finally {\n        setIsLoading(false);\n      }\n    };\n\n    initializeOrders();\n  }, []);\n\n  // Save to localStorage whenever orders change\n  useEffect(() => {\n    if (!isLoading && orders.length > 0) {\n      try {\n        localStorage.setItem('ecommerce-orders', JSON.stringify(orders));\n      } catch (error) {\n        console.error('Error saving orders to localStorage:', error);\n      }\n    }\n  }, [orders, isLoading]);\n\n  const generateOrderNumber = (): string => {\n    const year = new Date().getFullYear();\n    const orderCount = orders.length + 1;\n    return `ORD-${year}-${orderCount.toString().padStart(3, '0')}`;\n  };\n\n  const addOrder = (orderData: Omit<Order, 'id' | 'orderNumber' | 'createdAt' | 'updatedAt'>) => {\n    const newOrder: Order = {\n      ...orderData,\n      id: Date.now().toString(),\n      orderNumber: generateOrderNumber(),\n      createdAt: new Date(),\n      updatedAt: new Date(),\n    };\n    setOrders(prev => [newOrder, ...prev]);\n  };\n\n  const updateOrderStatus = (id: string, status: OrderStatus) => {\n    setOrders(prev => prev.map(order => {\n      if (order.id === id) {\n        const updatedOrder = { \n          ...order, \n          status, \n          updatedAt: new Date() \n        };\n        \n        // Set deliveredAt when status changes to delivered\n        if (status === 'delivered' && !order.deliveredAt) {\n          updatedOrder.deliveredAt = new Date();\n        }\n        \n        return updatedOrder;\n      }\n      return order;\n    }));\n  };\n\n  const updateOrder = (id: string, updates: Partial<Order>) => {\n    setOrders(prev => prev.map(order => \n      order.id === id \n        ? { ...order, ...updates, updatedAt: new Date() }\n        : order\n    ));\n  };\n\n  const deleteOrder = (id: string) => {\n    setOrders(prev => prev.filter(order => order.id !== id));\n  };\n\n  const getOrderById = (id: string): Order | undefined => {\n    return orders.find(order => order.id === id);\n  };\n\n  const getOrdersByStatus = (status: OrderStatus): Order[] => {\n    return orders.filter(order => order.status === status);\n  };\n\n  const getOrdersByCustomer = (customerId: string): Order[] => {\n    return orders.filter(order => order.customerId === customerId);\n  };\n\n  const getTotalRevenue = (): number => {\n    return orders\n      .filter(order => order.paymentStatus === 'paid')\n      .reduce((total, order) => total + order.total, 0);\n  };\n\n  const getOrdersCount = (): number => {\n    return orders.length;\n  };\n\n  const getRecentOrders = (limit: number = 5): Order[] => {\n    return orders\n      .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())\n      .slice(0, limit);\n  };\n\n  const value: OrderContextType = {\n    orders,\n    addOrder,\n    updateOrderStatus,\n    updateOrder,\n    deleteOrder,\n    getOrderById,\n    getOrdersByStatus,\n    getOrdersByCustomer,\n    getTotalRevenue,\n    getOrdersCount,\n    getRecentOrders,\n    isLoading,\n  };\n\n  return (\n    <OrderContext.Provider value={value}>\n      {children}\n    </OrderContext.Provider>\n  );\n}\n\nexport function useOrders() {\n  const context = useContext(OrderContext);\n  if (context === undefined) {\n    throw new Error('useOrders must be used within an OrderProvider');\n  }\n  return context;\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAoBA,MAAM,6BAAe,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAgC;AAMjE,qBAAqB;AACrB,MAAM,eAAwB;IAC5B;QACE,IAAI;QACJ,aAAa;QACb,YAAY;QACZ,cAAc;QACd,eAAe;QACf,OAAO;YACL;gBACE,IAAI;gBACJ,WAAW;gBACX,aAAa;gBACb,cAAc;gBACd,OAAO;gBACP,UAAU;gBACV,OAAO;YACT;YACA;gBACE,IAAI;gBACJ,WAAW;gBACX,aAAa;gBACb,cAAc;gBACd,OAAO;gBACP,UAAU;gBACV,OAAO;YACT;SACD;QACD,UAAU;QACV,UAAU;QACV,KAAK;QACL,OAAO;QACP,QAAQ;QACR,eAAe;QACf,eAAe;QACf,iBAAiB;YACf,UAAU;YACV,OAAO;YACP,SAAS;YACT,MAAM;YACN,YAAY;YACZ,SAAS;QACX;QACA,OAAO;QACP,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,aAAa;QACb,YAAY;QACZ,cAAc;QACd,eAAe;QACf,OAAO;YACL;gBACE,IAAI;gBACJ,WAAW;gBACX,aAAa;gBACb,cAAc;gBACd,OAAO;gBACP,UAAU;gBACV,OAAO;YACT;SACD;QACD,UAAU;QACV,UAAU;QACV,KAAK;QACL,OAAO;QACP,QAAQ;QACR,eAAe;QACf,eAAe;QACf,iBAAiB;YACf,UAAU;YACV,OAAO;YACP,SAAS;YACT,MAAM;YACN,YAAY;YACZ,SAAS;QACX;QACA,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,aAAa;QACb,YAAY;QACZ,cAAc;QACd,eAAe;QACf,OAAO;YACL;gBACE,IAAI;gBACJ,WAAW;gBACX,aAAa;gBACb,cAAc;gBACd,OAAO;gBACP,UAAU;gBACV,OAAO;YACT;SACD;QACD,UAAU;QACV,UAAU;QACV,KAAK;QACL,OAAO;QACP,QAAQ;QACR,eAAe;QACf,eAAe;QACf,iBAAiB;YACf,UAAU;YACV,OAAO;YACP,SAAS;YACT,MAAM;YACN,YAAY;YACZ,SAAS;QACX;QACA,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;QACpB,aAAa,IAAI,KAAK;IACxB;CACD;AAEM,SAAS,cAAc,EAAE,QAAQ,EAAsB;IAC5D,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW,EAAE;IAChD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,yDAAyD;IACzD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,mBAAmB;YACvB,IAAI;gBACF,MAAM,cAAc,aAAa,OAAO,CAAC;gBACzC,IAAI,aAAa;oBACf,MAAM,eAAe,KAAK,KAAK,CAAC;oBAChC,4CAA4C;oBAC5C,MAAM,kBAAkB,aAAa,GAAG,CAAC,CAAC,QAAe,CAAC;4BACxD,GAAG,KAAK;4BACR,WAAW,IAAI,KAAK,MAAM,SAAS;4BACnC,WAAW,IAAI,KAAK,MAAM,SAAS;4BACnC,aAAa,MAAM,WAAW,GAAG,IAAI,KAAK,MAAM,WAAW,IAAI;wBACjE,CAAC;oBACD,UAAU;gBACZ,OAAO;oBACL,+BAA+B;oBAC/B,UAAU;oBACV,aAAa,OAAO,CAAC,oBAAoB,KAAK,SAAS,CAAC;gBAC1D;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,2CAA2C;gBACzD,UAAU;YACZ,SAAU;gBACR,aAAa;YACf;QACF;QAEA;IACF,GAAG,EAAE;IAEL,8CAA8C;IAC9C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,aAAa,OAAO,MAAM,GAAG,GAAG;YACnC,IAAI;gBACF,aAAa,OAAO,CAAC,oBAAoB,KAAK,SAAS,CAAC;YAC1D,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,wCAAwC;YACxD;QACF;IACF,GAAG;QAAC;QAAQ;KAAU;IAEtB,MAAM,sBAAsB;QAC1B,MAAM,OAAO,IAAI,OAAO,WAAW;QACnC,MAAM,aAAa,OAAO,MAAM,GAAG;QACnC,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,WAAW,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;IAChE;IAEA,MAAM,WAAW,CAAC;QAChB,MAAM,WAAkB;YACtB,GAAG,SAAS;YACZ,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,aAAa;YACb,WAAW,IAAI;YACf,WAAW,IAAI;QACjB;QACA,UAAU,CAAA,OAAQ;gBAAC;mBAAa;aAAK;IACvC;IAEA,MAAM,oBAAoB,CAAC,IAAY;QACrC,UAAU,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA;gBACzB,IAAI,MAAM,EAAE,KAAK,IAAI;oBACnB,MAAM,eAAe;wBACnB,GAAG,KAAK;wBACR;wBACA,WAAW,IAAI;oBACjB;oBAEA,mDAAmD;oBACnD,IAAI,WAAW,eAAe,CAAC,MAAM,WAAW,EAAE;wBAChD,aAAa,WAAW,GAAG,IAAI;oBACjC;oBAEA,OAAO;gBACT;gBACA,OAAO;YACT;IACF;IAEA,MAAM,cAAc,CAAC,IAAY;QAC/B,UAAU,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,QACzB,MAAM,EAAE,KAAK,KACT;oBAAE,GAAG,KAAK;oBAAE,GAAG,OAAO;oBAAE,WAAW,IAAI;gBAAO,IAC9C;IAER;IAEA,MAAM,cAAc,CAAC;QACnB,UAAU,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;IACtD;IAEA,MAAM,eAAe,CAAC;QACpB,OAAO,OAAO,IAAI,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;IAC3C;IAEA,MAAM,oBAAoB,CAAC;QACzB,OAAO,OAAO,MAAM,CAAC,CAAA,QAAS,MAAM,MAAM,KAAK;IACjD;IAEA,MAAM,sBAAsB,CAAC;QAC3B,OAAO,OAAO,MAAM,CAAC,CAAA,QAAS,MAAM,UAAU,KAAK;IACrD;IAEA,MAAM,kBAAkB;QACtB,OAAO,OACJ,MAAM,CAAC,CAAA,QAAS,MAAM,aAAa,KAAK,QACxC,MAAM,CAAC,CAAC,OAAO,QAAU,QAAQ,MAAM,KAAK,EAAE;IACnD;IAEA,MAAM,iBAAiB;QACrB,OAAO,OAAO,MAAM;IACtB;IAEA,MAAM,kBAAkB,CAAC,QAAgB,CAAC;QACxC,OAAO,OACJ,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,SAAS,CAAC,OAAO,KAAK,EAAE,SAAS,CAAC,OAAO,IAC1D,KAAK,CAAC,GAAG;IACd;IAEA,MAAM,QAA0B;QAC9B;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,8OAAC,aAAa,QAAQ;QAAC,OAAO;kBAC3B;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT"}}, {"offset": {"line": 719, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 725, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/New%20folder%20%288%29/arabic-ecommerce/src/contexts/UserContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';\nimport { User } from '@/types';\n\ninterface UserContextType {\n  users: User[];\n  addUser: (user: Omit<User, 'id' | 'totalOrders' | 'totalSpent' | 'createdAt' | 'updatedAt'>) => void;\n  updateUser: (id: string, user: Partial<User>) => void;\n  deleteUser: (id: string) => void;\n  toggleUserStatus: (id: string) => void;\n  getUserById: (id: string) => User | undefined;\n  getActiveUsers: () => User[];\n  getUsersByRole: (role: 'admin' | 'customer') => User[];\n  updateUserStats: (userId: string, orderCount: number, totalSpent: number) => void;\n  getTotalUsers: () => number;\n  getActiveUsersCount: () => number;\n  getRecentUsers: (limit?: number) => User[];\n  isLoading: boolean;\n}\n\nconst UserContext = createContext<UserContextType | undefined>(undefined);\n\ninterface UserProviderProps {\n  children: ReactNode;\n}\n\n// Sample users data\nconst sampleUsers: User[] = [\n  {\n    id: '1',\n    name: 'أحمد محمد علي',\n    email: '<EMAIL>',\n    phone: '+************',\n    role: 'customer',\n    isActive: true,\n    lastLogin: new Date('2024-01-16T10:30:00'),\n    totalOrders: 3,\n    totalSpent: 5780,\n    address: {\n      street: 'شارع الملك فهد، حي النخيل',\n      city: 'الرياض',\n      postalCode: '12345',\n      country: 'السعودية',\n    },\n    createdAt: new Date('2024-01-01T08:00:00'),\n    updatedAt: new Date('2024-01-16T10:30:00'),\n  },\n  {\n    id: '2',\n    name: 'فاطمة أحمد محمد',\n    email: '<EMAIL>',\n    phone: '+966507654321',\n    role: 'customer',\n    isActive: true,\n    lastLogin: new Date('2024-01-15T14:20:00'),\n    totalOrders: 1,\n    totalSpent: 1410,\n    address: {\n      street: 'طريق الأمير محمد بن عبدالعزيز',\n      city: 'جدة',\n      postalCode: '21589',\n      country: 'السعودية',\n    },\n    createdAt: new Date('2024-01-05T12:00:00'),\n    updatedAt: new Date('2024-01-15T14:20:00'),\n  },\n  {\n    id: '3',\n    name: 'محمد عبدالله',\n    email: '<EMAIL>',\n    phone: '+966509876543',\n    role: 'customer',\n    isActive: false,\n    lastLogin: new Date('2024-01-10T09:15:00'),\n    totalOrders: 0,\n    totalSpent: 0,\n    address: {\n      street: 'شارع العليا',\n      city: 'الرياض',\n      postalCode: '11564',\n      country: 'السعودية',\n    },\n    createdAt: new Date('2024-01-08T16:30:00'),\n    updatedAt: new Date('2024-01-12T11:00:00'),\n  },\n  {\n    id: '4',\n    name: 'سارة خالد',\n    email: '<EMAIL>',\n    phone: '+966502468135',\n    role: 'customer',\n    isActive: true,\n    lastLogin: new Date('2024-01-14T11:45:00'),\n    totalOrders: 2,\n    totalSpent: 3200,\n    address: {\n      street: 'حي الملقا',\n      city: 'الرياض',\n      postalCode: '13524',\n      country: 'السعودية',\n    },\n    createdAt: new Date('2024-01-03T09:20:00'),\n    updatedAt: new Date('2024-01-14T11:45:00'),\n  },\n  {\n    id: '5',\n    name: 'عبدالرحمن أحمد',\n    email: '<EMAIL>',\n    phone: '+966501111111',\n    role: 'admin',\n    isActive: true,\n    lastLogin: new Date('2024-01-16T16:00:00'),\n    totalOrders: 0,\n    totalSpent: 0,\n    createdAt: new Date('2023-12-01T10:00:00'),\n    updatedAt: new Date('2024-01-16T16:00:00'),\n  },\n];\n\nexport function UserProvider({ children }: UserProviderProps) {\n  const [users, setUsers] = useState<User[]>([]);\n  const [isLoading, setIsLoading] = useState(true);\n\n  // Initialize users from localStorage or use sample data\n  useEffect(() => {\n    const initializeUsers = () => {\n      try {\n        const savedUsers = localStorage.getItem('ecommerce-users');\n        if (savedUsers) {\n          const parsedUsers = JSON.parse(savedUsers);\n          // Convert date strings back to Date objects\n          const usersWithDates = parsedUsers.map((user: any) => ({\n            ...user,\n            lastLogin: user.lastLogin ? new Date(user.lastLogin) : undefined,\n            createdAt: new Date(user.createdAt),\n            updatedAt: new Date(user.updatedAt),\n          }));\n          setUsers(usersWithDates);\n        } else {\n          // First time - use sample data\n          setUsers(sampleUsers);\n          localStorage.setItem('ecommerce-users', JSON.stringify(sampleUsers));\n        }\n      } catch (error) {\n        console.error('Error loading users from localStorage:', error);\n        setUsers(sampleUsers);\n      } finally {\n        setIsLoading(false);\n      }\n    };\n\n    initializeUsers();\n  }, []);\n\n  // Save to localStorage whenever users change\n  useEffect(() => {\n    if (!isLoading && users.length > 0) {\n      try {\n        localStorage.setItem('ecommerce-users', JSON.stringify(users));\n      } catch (error) {\n        console.error('Error saving users to localStorage:', error);\n      }\n    }\n  }, [users, isLoading]);\n\n  const addUser = (userData: Omit<User, 'id' | 'totalOrders' | 'totalSpent' | 'createdAt' | 'updatedAt'>) => {\n    const newUser: User = {\n      ...userData,\n      id: Date.now().toString(),\n      totalOrders: 0,\n      totalSpent: 0,\n      createdAt: new Date(),\n      updatedAt: new Date(),\n    };\n    setUsers(prev => [newUser, ...prev]);\n  };\n\n  const updateUser = (id: string, userData: Partial<User>) => {\n    setUsers(prev => prev.map(user => \n      user.id === id \n        ? { ...user, ...userData, updatedAt: new Date() }\n        : user\n    ));\n  };\n\n  const deleteUser = (id: string) => {\n    setUsers(prev => prev.filter(user => user.id !== id));\n  };\n\n  const toggleUserStatus = (id: string) => {\n    setUsers(prev => prev.map(user => \n      user.id === id \n        ? { ...user, isActive: !user.isActive, updatedAt: new Date() }\n        : user\n    ));\n  };\n\n  const getUserById = (id: string): User | undefined => {\n    return users.find(user => user.id === id);\n  };\n\n  const getActiveUsers = (): User[] => {\n    return users.filter(user => user.isActive);\n  };\n\n  const getUsersByRole = (role: 'admin' | 'customer'): User[] => {\n    return users.filter(user => user.role === role);\n  };\n\n  const updateUserStats = (userId: string, orderCount: number, totalSpent: number) => {\n    setUsers(prev => prev.map(user => \n      user.id === userId \n        ? { \n            ...user, \n            totalOrders: orderCount, \n            totalSpent: totalSpent,\n            lastLogin: new Date(),\n            updatedAt: new Date() \n          }\n        : user\n    ));\n  };\n\n  const getTotalUsers = (): number => {\n    return users.length;\n  };\n\n  const getActiveUsersCount = (): number => {\n    return users.filter(user => user.isActive).length;\n  };\n\n  const getRecentUsers = (limit: number = 5): User[] => {\n    return users\n      .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())\n      .slice(0, limit);\n  };\n\n  const value: UserContextType = {\n    users,\n    addUser,\n    updateUser,\n    deleteUser,\n    toggleUserStatus,\n    getUserById,\n    getActiveUsers,\n    getUsersByRole,\n    updateUserStats,\n    getTotalUsers,\n    getActiveUsersCount,\n    getRecentUsers,\n    isLoading,\n  };\n\n  return (\n    <UserContext.Provider value={value}>\n      {children}\n    </UserContext.Provider>\n  );\n}\n\nexport function useUsers() {\n  const context = useContext(UserContext);\n  if (context === undefined) {\n    throw new Error('useUsers must be used within a UserProvider');\n  }\n  return context;\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAqBA,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAA+B;AAM/D,oBAAoB;AACpB,MAAM,cAAsB;IAC1B;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,OAAO;QACP,MAAM;QACN,UAAU;QACV,WAAW,IAAI,KAAK;QACpB,aAAa;QACb,YAAY;QACZ,SAAS;YACP,QAAQ;YACR,MAAM;YACN,YAAY;YACZ,SAAS;QACX;QACA,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,OAAO;QACP,MAAM;QACN,UAAU;QACV,WAAW,IAAI,KAAK;QACpB,aAAa;QACb,YAAY;QACZ,SAAS;YACP,QAAQ;YACR,MAAM;YACN,YAAY;YACZ,SAAS;QACX;QACA,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,OAAO;QACP,MAAM;QACN,UAAU;QACV,WAAW,IAAI,KAAK;QACpB,aAAa;QACb,YAAY;QACZ,SAAS;YACP,QAAQ;YACR,MAAM;YACN,YAAY;YACZ,SAAS;QACX;QACA,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,OAAO;QACP,MAAM;QACN,UAAU;QACV,WAAW,IAAI,KAAK;QACpB,aAAa;QACb,YAAY;QACZ,SAAS;YACP,QAAQ;YACR,MAAM;YACN,YAAY;YACZ,SAAS;QACX;QACA,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,OAAO;QACP,MAAM;QACN,UAAU;QACV,WAAW,IAAI,KAAK;QACpB,aAAa;QACb,YAAY;QACZ,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;CACD;AAEM,SAAS,aAAa,EAAE,QAAQ,EAAqB;IAC1D,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,wDAAwD;IACxD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,kBAAkB;YACtB,IAAI;gBACF,MAAM,aAAa,aAAa,OAAO,CAAC;gBACxC,IAAI,YAAY;oBACd,MAAM,cAAc,KAAK,KAAK,CAAC;oBAC/B,4CAA4C;oBAC5C,MAAM,iBAAiB,YAAY,GAAG,CAAC,CAAC,OAAc,CAAC;4BACrD,GAAG,IAAI;4BACP,WAAW,KAAK,SAAS,GAAG,IAAI,KAAK,KAAK,SAAS,IAAI;4BACvD,WAAW,IAAI,KAAK,KAAK,SAAS;4BAClC,WAAW,IAAI,KAAK,KAAK,SAAS;wBACpC,CAAC;oBACD,SAAS;gBACX,OAAO;oBACL,+BAA+B;oBAC/B,SAAS;oBACT,aAAa,OAAO,CAAC,mBAAmB,KAAK,SAAS,CAAC;gBACzD;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,0CAA0C;gBACxD,SAAS;YACX,SAAU;gBACR,aAAa;YACf;QACF;QAEA;IACF,GAAG,EAAE;IAEL,6CAA6C;IAC7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,aAAa,MAAM,MAAM,GAAG,GAAG;YAClC,IAAI;gBACF,aAAa,OAAO,CAAC,mBAAmB,KAAK,SAAS,CAAC;YACzD,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,uCAAuC;YACvD;QACF;IACF,GAAG;QAAC;QAAO;KAAU;IAErB,MAAM,UAAU,CAAC;QACf,MAAM,UAAgB;YACpB,GAAG,QAAQ;YACX,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,aAAa;YACb,YAAY;YACZ,WAAW,IAAI;YACf,WAAW,IAAI;QACjB;QACA,SAAS,CAAA,OAAQ;gBAAC;mBAAY;aAAK;IACrC;IAEA,MAAM,aAAa,CAAC,IAAY;QAC9B,SAAS,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,OACxB,KAAK,EAAE,KAAK,KACR;oBAAE,GAAG,IAAI;oBAAE,GAAG,QAAQ;oBAAE,WAAW,IAAI;gBAAO,IAC9C;IAER;IAEA,MAAM,aAAa,CAAC;QAClB,SAAS,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;IACnD;IAEA,MAAM,mBAAmB,CAAC;QACxB,SAAS,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,OACxB,KAAK,EAAE,KAAK,KACR;oBAAE,GAAG,IAAI;oBAAE,UAAU,CAAC,KAAK,QAAQ;oBAAE,WAAW,IAAI;gBAAO,IAC3D;IAER;IAEA,MAAM,cAAc,CAAC;QACnB,OAAO,MAAM,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;IACxC;IAEA,MAAM,iBAAiB;QACrB,OAAO,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ;IAC3C;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAO,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK;IAC5C;IAEA,MAAM,kBAAkB,CAAC,QAAgB,YAAoB;QAC3D,SAAS,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,OACxB,KAAK,EAAE,KAAK,SACR;oBACE,GAAG,IAAI;oBACP,aAAa;oBACb,YAAY;oBACZ,WAAW,IAAI;oBACf,WAAW,IAAI;gBACjB,IACA;IAER;IAEA,MAAM,gBAAgB;QACpB,OAAO,MAAM,MAAM;IACrB;IAEA,MAAM,sBAAsB;QAC1B,OAAO,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,EAAE,MAAM;IACnD;IAEA,MAAM,iBAAiB,CAAC,QAAgB,CAAC;QACvC,OAAO,MACJ,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,SAAS,CAAC,OAAO,KAAK,EAAE,SAAS,CAAC,OAAO,IAC1D,KAAK,CAAC,GAAG;IACd;IAEA,MAAM,QAAyB;QAC7B;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,8OAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT"}}, {"offset": {"line": 961, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 967, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/New%20folder%20%288%29/arabic-ecommerce/src/contexts/ReportsContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';\nimport { useOrders } from './OrderContext';\nimport { useProducts } from './ProductContext';\nimport { useUsers } from './UserContext';\n\ninterface SalesData {\n  date: string;\n  sales: number;\n  orders: number;\n  profit: number;\n}\n\ninterface ProductSales {\n  productId: string;\n  productName: string;\n  quantitySold: number;\n  totalRevenue: number;\n  totalProfit: number;\n  averagePrice: number;\n}\n\ninterface CustomerAnalysis {\n  userId: string;\n  userName: string;\n  totalOrders: number;\n  totalSpent: number;\n  averageOrderValue: number;\n  lastOrderDate: Date;\n}\n\ninterface ReportsContextType {\n  // Sales Reports\n  getDailySales: (days?: number) => SalesData[];\n  getWeeklySales: (weeks?: number) => SalesData[];\n  getMonthlySales: (months?: number) => SalesData[];\n  \n  // Product Analytics\n  getTopSellingProducts: (limit?: number) => ProductSales[];\n  getMostProfitableProducts: (limit?: number) => ProductSales[];\n  getProductPerformance: (productId: string) => ProductSales | null;\n  \n  // Customer Analytics\n  getTopCustomers: (limit?: number) => CustomerAnalysis[];\n  getCustomerAnalysis: (userId: string) => CustomerAnalysis | null;\n  \n  // Financial Reports\n  getTotalRevenue: (startDate?: Date, endDate?: Date) => number;\n  getTotalProfit: (startDate?: Date, endDate?: Date) => number;\n  getAverageOrderValue: (startDate?: Date, endDate?: Date) => number;\n  \n  // Growth Analytics\n  getRevenueGrowth: (period: 'daily' | 'weekly' | 'monthly') => number;\n  getOrderGrowth: (period: 'daily' | 'weekly' | 'monthly') => number;\n  \n  // Summary Stats\n  getSummaryStats: () => {\n    totalRevenue: number;\n    totalOrders: number;\n    totalProfit: number;\n    averageOrderValue: number;\n    topProduct: string;\n    topCustomer: string;\n    revenueGrowth: number;\n    orderGrowth: number;\n  };\n  \n  isLoading: boolean;\n}\n\nconst ReportsContext = createContext<ReportsContextType | undefined>(undefined);\n\ninterface ReportsProviderProps {\n  children: ReactNode;\n}\n\nexport function ReportsProvider({ children }: ReportsProviderProps) {\n  const { orders } = useOrders();\n  const { products } = useProducts();\n  const { users } = useUsers();\n  const [isLoading, setIsLoading] = useState(false);\n\n  // Helper function to get date range\n  const getDateRange = (startDate?: Date, endDate?: Date) => {\n    const end = endDate || new Date();\n    const start = startDate || new Date(end.getTime() - 30 * 24 * 60 * 60 * 1000); // 30 days ago\n    return { start, end };\n  };\n\n  // Helper function to filter orders by date\n  const filterOrdersByDate = (startDate?: Date, endDate?: Date) => {\n    const { start, end } = getDateRange(startDate, endDate);\n    return orders.filter(order => {\n      const orderDate = new Date(order.createdAt);\n      return orderDate >= start && orderDate <= end && order.status !== 'cancelled';\n    });\n  };\n\n  // Daily Sales Report\n  const getDailySales = (days: number = 30): SalesData[] => {\n    const salesMap = new Map<string, { sales: number; orders: number; profit: number }>();\n    \n    // Initialize last N days\n    for (let i = 0; i < days; i++) {\n      const date = new Date();\n      date.setDate(date.getDate() - i);\n      const dateStr = date.toISOString().split('T')[0];\n      salesMap.set(dateStr, { sales: 0, orders: 0, profit: 0 });\n    }\n\n    // Aggregate sales data\n    orders.forEach(order => {\n      if (order.status === 'cancelled') return;\n      \n      const dateStr = new Date(order.createdAt).toISOString().split('T')[0];\n      if (salesMap.has(dateStr)) {\n        const current = salesMap.get(dateStr)!;\n        const profit = order.total * 0.3; // Assume 30% profit margin\n        salesMap.set(dateStr, {\n          sales: current.sales + order.total,\n          orders: current.orders + 1,\n          profit: current.profit + profit\n        });\n      }\n    });\n\n    return Array.from(salesMap.entries())\n      .map(([date, data]) => ({\n        date,\n        sales: data.sales,\n        orders: data.orders,\n        profit: data.profit\n      }))\n      .sort((a, b) => a.date.localeCompare(b.date));\n  };\n\n  // Weekly Sales Report\n  const getWeeklySales = (weeks: number = 12): SalesData[] => {\n    const salesMap = new Map<string, { sales: number; orders: number; profit: number }>();\n    \n    // Initialize last N weeks\n    for (let i = 0; i < weeks; i++) {\n      const date = new Date();\n      date.setDate(date.getDate() - (i * 7));\n      const weekStart = new Date(date.setDate(date.getDate() - date.getDay()));\n      const weekStr = weekStart.toISOString().split('T')[0];\n      salesMap.set(weekStr, { sales: 0, orders: 0, profit: 0 });\n    }\n\n    // Aggregate weekly data\n    orders.forEach(order => {\n      if (order.status === 'cancelled') return;\n      \n      const orderDate = new Date(order.createdAt);\n      const weekStart = new Date(orderDate.setDate(orderDate.getDate() - orderDate.getDay()));\n      const weekStr = weekStart.toISOString().split('T')[0];\n      \n      if (salesMap.has(weekStr)) {\n        const current = salesMap.get(weekStr)!;\n        const profit = order.total * 0.3;\n        salesMap.set(weekStr, {\n          sales: current.sales + order.total,\n          orders: current.orders + 1,\n          profit: current.profit + profit\n        });\n      }\n    });\n\n    return Array.from(salesMap.entries())\n      .map(([date, data]) => ({\n        date,\n        sales: data.sales,\n        orders: data.orders,\n        profit: data.profit\n      }))\n      .sort((a, b) => a.date.localeCompare(b.date));\n  };\n\n  // Monthly Sales Report\n  const getMonthlySales = (months: number = 12): SalesData[] => {\n    const salesMap = new Map<string, { sales: number; orders: number; profit: number }>();\n    \n    // Initialize last N months\n    for (let i = 0; i < months; i++) {\n      const date = new Date();\n      date.setMonth(date.getMonth() - i);\n      const monthStr = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;\n      salesMap.set(monthStr, { sales: 0, orders: 0, profit: 0 });\n    }\n\n    // Aggregate monthly data\n    orders.forEach(order => {\n      if (order.status === 'cancelled') return;\n      \n      const orderDate = new Date(order.createdAt);\n      const monthStr = `${orderDate.getFullYear()}-${String(orderDate.getMonth() + 1).padStart(2, '0')}`;\n      \n      if (salesMap.has(monthStr)) {\n        const current = salesMap.get(monthStr)!;\n        const profit = order.total * 0.3;\n        salesMap.set(monthStr, {\n          sales: current.sales + order.total,\n          orders: current.orders + 1,\n          profit: current.profit + profit\n        });\n      }\n    });\n\n    return Array.from(salesMap.entries())\n      .map(([date, data]) => ({\n        date,\n        sales: data.sales,\n        orders: data.orders,\n        profit: data.profit\n      }))\n      .sort((a, b) => a.date.localeCompare(b.date));\n  };\n\n  // Top Selling Products\n  const getTopSellingProducts = (limit: number = 10): ProductSales[] => {\n    const productSales = new Map<string, { quantity: number; revenue: number; profit: number }>();\n\n    orders.forEach(order => {\n      if (order.status === 'cancelled') return;\n      \n      order.items.forEach(item => {\n        const current = productSales.get(item.id) || { quantity: 0, revenue: 0, profit: 0 };\n        const itemRevenue = item.price * item.quantity;\n        const itemProfit = itemRevenue * 0.3; // 30% profit margin\n        \n        productSales.set(item.id, {\n          quantity: current.quantity + item.quantity,\n          revenue: current.revenue + itemRevenue,\n          profit: current.profit + itemProfit\n        });\n      });\n    });\n\n    return Array.from(productSales.entries())\n      .map(([productId, data]) => {\n        const product = products.find(p => p.id === productId);\n        return {\n          productId,\n          productName: product?.name || 'منتج محذوف',\n          quantitySold: data.quantity,\n          totalRevenue: data.revenue,\n          totalProfit: data.profit,\n          averagePrice: data.revenue / data.quantity\n        };\n      })\n      .sort((a, b) => b.quantitySold - a.quantitySold)\n      .slice(0, limit);\n  };\n\n  // Most Profitable Products\n  const getMostProfitableProducts = (limit: number = 10): ProductSales[] => {\n    return getTopSellingProducts(products.length)\n      .sort((a, b) => b.totalProfit - a.totalProfit)\n      .slice(0, limit);\n  };\n\n  // Product Performance\n  const getProductPerformance = (productId: string): ProductSales | null => {\n    const allProducts = getTopSellingProducts(products.length);\n    return allProducts.find(p => p.productId === productId) || null;\n  };\n\n  // Top Customers\n  const getTopCustomers = (limit: number = 10): CustomerAnalysis[] => {\n    const customerData = new Map<string, { orders: number; spent: number; lastOrder: Date }>();\n\n    orders.forEach(order => {\n      if (order.status === 'cancelled') return;\n      \n      const current = customerData.get(order.userId) || { orders: 0, spent: 0, lastOrder: new Date(0) };\n      customerData.set(order.userId, {\n        orders: current.orders + 1,\n        spent: current.spent + order.total,\n        lastOrder: new Date(Math.max(current.lastOrder.getTime(), new Date(order.createdAt).getTime()))\n      });\n    });\n\n    return Array.from(customerData.entries())\n      .map(([userId, data]) => {\n        const user = users.find(u => u.id === userId);\n        return {\n          userId,\n          userName: user?.name || 'مستخدم محذوف',\n          totalOrders: data.orders,\n          totalSpent: data.spent,\n          averageOrderValue: data.spent / data.orders,\n          lastOrderDate: data.lastOrder\n        };\n      })\n      .sort((a, b) => b.totalSpent - a.totalSpent)\n      .slice(0, limit);\n  };\n\n  // Customer Analysis\n  const getCustomerAnalysis = (userId: string): CustomerAnalysis | null => {\n    const allCustomers = getTopCustomers(users.length);\n    return allCustomers.find(c => c.userId === userId) || null;\n  };\n\n  // Financial Reports\n  const getTotalRevenue = (startDate?: Date, endDate?: Date): number => {\n    const filteredOrders = filterOrdersByDate(startDate, endDate);\n    return filteredOrders.reduce((total, order) => total + order.total, 0);\n  };\n\n  const getTotalProfit = (startDate?: Date, endDate?: Date): number => {\n    const revenue = getTotalRevenue(startDate, endDate);\n    return revenue * 0.3; // 30% profit margin\n  };\n\n  const getAverageOrderValue = (startDate?: Date, endDate?: Date): number => {\n    const filteredOrders = filterOrdersByDate(startDate, endDate);\n    if (filteredOrders.length === 0) return 0;\n    return getTotalRevenue(startDate, endDate) / filteredOrders.length;\n  };\n\n  // Growth Analytics\n  const getRevenueGrowth = (period: 'daily' | 'weekly' | 'monthly'): number => {\n    const salesData = period === 'daily' ? getDailySales(60) : \n                     period === 'weekly' ? getWeeklySales(24) : \n                     getMonthlySales(24);\n    \n    if (salesData.length < 2) return 0;\n    \n    const midPoint = Math.floor(salesData.length / 2);\n    const firstHalf = salesData.slice(0, midPoint);\n    const secondHalf = salesData.slice(midPoint);\n    \n    const firstHalfRevenue = firstHalf.reduce((sum, data) => sum + data.sales, 0);\n    const secondHalfRevenue = secondHalf.reduce((sum, data) => sum + data.sales, 0);\n    \n    if (firstHalfRevenue === 0) return 0;\n    return ((secondHalfRevenue - firstHalfRevenue) / firstHalfRevenue) * 100;\n  };\n\n  const getOrderGrowth = (period: 'daily' | 'weekly' | 'monthly'): number => {\n    const salesData = period === 'daily' ? getDailySales(60) : \n                     period === 'weekly' ? getWeeklySales(24) : \n                     getMonthlySales(24);\n    \n    if (salesData.length < 2) return 0;\n    \n    const midPoint = Math.floor(salesData.length / 2);\n    const firstHalf = salesData.slice(0, midPoint);\n    const secondHalf = salesData.slice(midPoint);\n    \n    const firstHalfOrders = firstHalf.reduce((sum, data) => sum + data.orders, 0);\n    const secondHalfOrders = secondHalf.reduce((sum, data) => sum + data.orders, 0);\n    \n    if (firstHalfOrders === 0) return 0;\n    return ((secondHalfOrders - firstHalfOrders) / firstHalfOrders) * 100;\n  };\n\n  // Summary Statistics\n  const getSummaryStats = () => {\n    const totalRevenue = getTotalRevenue();\n    const totalOrders = orders.filter(o => o.status !== 'cancelled').length;\n    const totalProfit = getTotalProfit();\n    const averageOrderValue = getAverageOrderValue();\n    \n    const topProducts = getTopSellingProducts(1);\n    const topCustomers = getTopCustomers(1);\n    \n    return {\n      totalRevenue,\n      totalOrders,\n      totalProfit,\n      averageOrderValue,\n      topProduct: topProducts[0]?.productName || 'لا يوجد',\n      topCustomer: topCustomers[0]?.userName || 'لا يوجد',\n      revenueGrowth: getRevenueGrowth('monthly'),\n      orderGrowth: getOrderGrowth('monthly')\n    };\n  };\n\n  const value: ReportsContextType = {\n    getDailySales,\n    getWeeklySales,\n    getMonthlySales,\n    getTopSellingProducts,\n    getMostProfitableProducts,\n    getProductPerformance,\n    getTopCustomers,\n    getCustomerAnalysis,\n    getTotalRevenue,\n    getTotalProfit,\n    getAverageOrderValue,\n    getRevenueGrowth,\n    getOrderGrowth,\n    getSummaryStats,\n    isLoading,\n  };\n\n  return (\n    <ReportsContext.Provider value={value}>\n      {children}\n    </ReportsContext.Provider>\n  );\n}\n\nexport function useReports() {\n  const context = useContext(ReportsContext);\n  if (context === undefined) {\n    throw new Error('useReports must be used within a ReportsProvider');\n  }\n  return context;\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAuEA,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAkC;AAM9D,SAAS,gBAAgB,EAAE,QAAQ,EAAwB;IAChE,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,gIAAA,CAAA,YAAS,AAAD;IAC3B,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC/B,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,oCAAoC;IACpC,MAAM,eAAe,CAAC,WAAkB;QACtC,MAAM,MAAM,WAAW,IAAI;QAC3B,MAAM,QAAQ,aAAa,IAAI,KAAK,IAAI,OAAO,KAAK,KAAK,KAAK,KAAK,KAAK,OAAO,cAAc;QAC7F,OAAO;YAAE;YAAO;QAAI;IACtB;IAEA,2CAA2C;IAC3C,MAAM,qBAAqB,CAAC,WAAkB;QAC5C,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,aAAa,WAAW;QAC/C,OAAO,OAAO,MAAM,CAAC,CAAA;YACnB,MAAM,YAAY,IAAI,KAAK,MAAM,SAAS;YAC1C,OAAO,aAAa,SAAS,aAAa,OAAO,MAAM,MAAM,KAAK;QACpE;IACF;IAEA,qBAAqB;IACrB,MAAM,gBAAgB,CAAC,OAAe,EAAE;QACtC,MAAM,WAAW,IAAI;QAErB,yBAAyB;QACzB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,IAAK;YAC7B,MAAM,OAAO,IAAI;YACjB,KAAK,OAAO,CAAC,KAAK,OAAO,KAAK;YAC9B,MAAM,UAAU,KAAK,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;YAChD,SAAS,GAAG,CAAC,SAAS;gBAAE,OAAO;gBAAG,QAAQ;gBAAG,QAAQ;YAAE;QACzD;QAEA,uBAAuB;QACvB,OAAO,OAAO,CAAC,CAAA;YACb,IAAI,MAAM,MAAM,KAAK,aAAa;YAElC,MAAM,UAAU,IAAI,KAAK,MAAM,SAAS,EAAE,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;YACrE,IAAI,SAAS,GAAG,CAAC,UAAU;gBACzB,MAAM,UAAU,SAAS,GAAG,CAAC;gBAC7B,MAAM,SAAS,MAAM,KAAK,GAAG,KAAK,2BAA2B;gBAC7D,SAAS,GAAG,CAAC,SAAS;oBACpB,OAAO,QAAQ,KAAK,GAAG,MAAM,KAAK;oBAClC,QAAQ,QAAQ,MAAM,GAAG;oBACzB,QAAQ,QAAQ,MAAM,GAAG;gBAC3B;YACF;QACF;QAEA,OAAO,MAAM,IAAI,CAAC,SAAS,OAAO,IAC/B,GAAG,CAAC,CAAC,CAAC,MAAM,KAAK,GAAK,CAAC;gBACtB;gBACA,OAAO,KAAK,KAAK;gBACjB,QAAQ,KAAK,MAAM;gBACnB,QAAQ,KAAK,MAAM;YACrB,CAAC,GACA,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,IAAI,CAAC,aAAa,CAAC,EAAE,IAAI;IAC/C;IAEA,sBAAsB;IACtB,MAAM,iBAAiB,CAAC,QAAgB,EAAE;QACxC,MAAM,WAAW,IAAI;QAErB,0BAA0B;QAC1B,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,IAAK;YAC9B,MAAM,OAAO,IAAI;YACjB,KAAK,OAAO,CAAC,KAAK,OAAO,KAAM,IAAI;YACnC,MAAM,YAAY,IAAI,KAAK,KAAK,OAAO,CAAC,KAAK,OAAO,KAAK,KAAK,MAAM;YACpE,MAAM,UAAU,UAAU,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;YACrD,SAAS,GAAG,CAAC,SAAS;gBAAE,OAAO;gBAAG,QAAQ;gBAAG,QAAQ;YAAE;QACzD;QAEA,wBAAwB;QACxB,OAAO,OAAO,CAAC,CAAA;YACb,IAAI,MAAM,MAAM,KAAK,aAAa;YAElC,MAAM,YAAY,IAAI,KAAK,MAAM,SAAS;YAC1C,MAAM,YAAY,IAAI,KAAK,UAAU,OAAO,CAAC,UAAU,OAAO,KAAK,UAAU,MAAM;YACnF,MAAM,UAAU,UAAU,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;YAErD,IAAI,SAAS,GAAG,CAAC,UAAU;gBACzB,MAAM,UAAU,SAAS,GAAG,CAAC;gBAC7B,MAAM,SAAS,MAAM,KAAK,GAAG;gBAC7B,SAAS,GAAG,CAAC,SAAS;oBACpB,OAAO,QAAQ,KAAK,GAAG,MAAM,KAAK;oBAClC,QAAQ,QAAQ,MAAM,GAAG;oBACzB,QAAQ,QAAQ,MAAM,GAAG;gBAC3B;YACF;QACF;QAEA,OAAO,MAAM,IAAI,CAAC,SAAS,OAAO,IAC/B,GAAG,CAAC,CAAC,CAAC,MAAM,KAAK,GAAK,CAAC;gBACtB;gBACA,OAAO,KAAK,KAAK;gBACjB,QAAQ,KAAK,MAAM;gBACnB,QAAQ,KAAK,MAAM;YACrB,CAAC,GACA,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,IAAI,CAAC,aAAa,CAAC,EAAE,IAAI;IAC/C;IAEA,uBAAuB;IACvB,MAAM,kBAAkB,CAAC,SAAiB,EAAE;QAC1C,MAAM,WAAW,IAAI;QAErB,2BAA2B;QAC3B,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,IAAK;YAC/B,MAAM,OAAO,IAAI;YACjB,KAAK,QAAQ,CAAC,KAAK,QAAQ,KAAK;YAChC,MAAM,WAAW,GAAG,KAAK,WAAW,GAAG,CAAC,EAAE,OAAO,KAAK,QAAQ,KAAK,GAAG,QAAQ,CAAC,GAAG,MAAM;YACxF,SAAS,GAAG,CAAC,UAAU;gBAAE,OAAO;gBAAG,QAAQ;gBAAG,QAAQ;YAAE;QAC1D;QAEA,yBAAyB;QACzB,OAAO,OAAO,CAAC,CAAA;YACb,IAAI,MAAM,MAAM,KAAK,aAAa;YAElC,MAAM,YAAY,IAAI,KAAK,MAAM,SAAS;YAC1C,MAAM,WAAW,GAAG,UAAU,WAAW,GAAG,CAAC,EAAE,OAAO,UAAU,QAAQ,KAAK,GAAG,QAAQ,CAAC,GAAG,MAAM;YAElG,IAAI,SAAS,GAAG,CAAC,WAAW;gBAC1B,MAAM,UAAU,SAAS,GAAG,CAAC;gBAC7B,MAAM,SAAS,MAAM,KAAK,GAAG;gBAC7B,SAAS,GAAG,CAAC,UAAU;oBACrB,OAAO,QAAQ,KAAK,GAAG,MAAM,KAAK;oBAClC,QAAQ,QAAQ,MAAM,GAAG;oBACzB,QAAQ,QAAQ,MAAM,GAAG;gBAC3B;YACF;QACF;QAEA,OAAO,MAAM,IAAI,CAAC,SAAS,OAAO,IAC/B,GAAG,CAAC,CAAC,CAAC,MAAM,KAAK,GAAK,CAAC;gBACtB;gBACA,OAAO,KAAK,KAAK;gBACjB,QAAQ,KAAK,MAAM;gBACnB,QAAQ,KAAK,MAAM;YACrB,CAAC,GACA,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,IAAI,CAAC,aAAa,CAAC,EAAE,IAAI;IAC/C;IAEA,uBAAuB;IACvB,MAAM,wBAAwB,CAAC,QAAgB,EAAE;QAC/C,MAAM,eAAe,IAAI;QAEzB,OAAO,OAAO,CAAC,CAAA;YACb,IAAI,MAAM,MAAM,KAAK,aAAa;YAElC,MAAM,KAAK,CAAC,OAAO,CAAC,CAAA;gBAClB,MAAM,UAAU,aAAa,GAAG,CAAC,KAAK,EAAE,KAAK;oBAAE,UAAU;oBAAG,SAAS;oBAAG,QAAQ;gBAAE;gBAClF,MAAM,cAAc,KAAK,KAAK,GAAG,KAAK,QAAQ;gBAC9C,MAAM,aAAa,cAAc,KAAK,oBAAoB;gBAE1D,aAAa,GAAG,CAAC,KAAK,EAAE,EAAE;oBACxB,UAAU,QAAQ,QAAQ,GAAG,KAAK,QAAQ;oBAC1C,SAAS,QAAQ,OAAO,GAAG;oBAC3B,QAAQ,QAAQ,MAAM,GAAG;gBAC3B;YACF;QACF;QAEA,OAAO,MAAM,IAAI,CAAC,aAAa,OAAO,IACnC,GAAG,CAAC,CAAC,CAAC,WAAW,KAAK;YACrB,MAAM,UAAU,SAAS,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;YAC5C,OAAO;gBACL;gBACA,aAAa,SAAS,QAAQ;gBAC9B,cAAc,KAAK,QAAQ;gBAC3B,cAAc,KAAK,OAAO;gBAC1B,aAAa,KAAK,MAAM;gBACxB,cAAc,KAAK,OAAO,GAAG,KAAK,QAAQ;YAC5C;QACF,GACC,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,YAAY,GAAG,EAAE,YAAY,EAC9C,KAAK,CAAC,GAAG;IACd;IAEA,2BAA2B;IAC3B,MAAM,4BAA4B,CAAC,QAAgB,EAAE;QACnD,OAAO,sBAAsB,SAAS,MAAM,EACzC,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,WAAW,GAAG,EAAE,WAAW,EAC5C,KAAK,CAAC,GAAG;IACd;IAEA,sBAAsB;IACtB,MAAM,wBAAwB,CAAC;QAC7B,MAAM,cAAc,sBAAsB,SAAS,MAAM;QACzD,OAAO,YAAY,IAAI,CAAC,CAAA,IAAK,EAAE,SAAS,KAAK,cAAc;IAC7D;IAEA,gBAAgB;IAChB,MAAM,kBAAkB,CAAC,QAAgB,EAAE;QACzC,MAAM,eAAe,IAAI;QAEzB,OAAO,OAAO,CAAC,CAAA;YACb,IAAI,MAAM,MAAM,KAAK,aAAa;YAElC,MAAM,UAAU,aAAa,GAAG,CAAC,MAAM,MAAM,KAAK;gBAAE,QAAQ;gBAAG,OAAO;gBAAG,WAAW,IAAI,KAAK;YAAG;YAChG,aAAa,GAAG,CAAC,MAAM,MAAM,EAAE;gBAC7B,QAAQ,QAAQ,MAAM,GAAG;gBACzB,OAAO,QAAQ,KAAK,GAAG,MAAM,KAAK;gBAClC,WAAW,IAAI,KAAK,KAAK,GAAG,CAAC,QAAQ,SAAS,CAAC,OAAO,IAAI,IAAI,KAAK,MAAM,SAAS,EAAE,OAAO;YAC7F;QACF;QAEA,OAAO,MAAM,IAAI,CAAC,aAAa,OAAO,IACnC,GAAG,CAAC,CAAC,CAAC,QAAQ,KAAK;YAClB,MAAM,OAAO,MAAM,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;YACtC,OAAO;gBACL;gBACA,UAAU,MAAM,QAAQ;gBACxB,aAAa,KAAK,MAAM;gBACxB,YAAY,KAAK,KAAK;gBACtB,mBAAmB,KAAK,KAAK,GAAG,KAAK,MAAM;gBAC3C,eAAe,KAAK,SAAS;YAC/B;QACF,GACC,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,UAAU,GAAG,EAAE,UAAU,EAC1C,KAAK,CAAC,GAAG;IACd;IAEA,oBAAoB;IACpB,MAAM,sBAAsB,CAAC;QAC3B,MAAM,eAAe,gBAAgB,MAAM,MAAM;QACjD,OAAO,aAAa,IAAI,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,WAAW;IACxD;IAEA,oBAAoB;IACpB,MAAM,kBAAkB,CAAC,WAAkB;QACzC,MAAM,iBAAiB,mBAAmB,WAAW;QACrD,OAAO,eAAe,MAAM,CAAC,CAAC,OAAO,QAAU,QAAQ,MAAM,KAAK,EAAE;IACtE;IAEA,MAAM,iBAAiB,CAAC,WAAkB;QACxC,MAAM,UAAU,gBAAgB,WAAW;QAC3C,OAAO,UAAU,KAAK,oBAAoB;IAC5C;IAEA,MAAM,uBAAuB,CAAC,WAAkB;QAC9C,MAAM,iBAAiB,mBAAmB,WAAW;QACrD,IAAI,eAAe,MAAM,KAAK,GAAG,OAAO;QACxC,OAAO,gBAAgB,WAAW,WAAW,eAAe,MAAM;IACpE;IAEA,mBAAmB;IACnB,MAAM,mBAAmB,CAAC;QACxB,MAAM,YAAY,WAAW,UAAU,cAAc,MACpC,WAAW,WAAW,eAAe,MACrC,gBAAgB;QAEjC,IAAI,UAAU,MAAM,GAAG,GAAG,OAAO;QAEjC,MAAM,WAAW,KAAK,KAAK,CAAC,UAAU,MAAM,GAAG;QAC/C,MAAM,YAAY,UAAU,KAAK,CAAC,GAAG;QACrC,MAAM,aAAa,UAAU,KAAK,CAAC;QAEnC,MAAM,mBAAmB,UAAU,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,KAAK,EAAE;QAC3E,MAAM,oBAAoB,WAAW,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,KAAK,EAAE;QAE7E,IAAI,qBAAqB,GAAG,OAAO;QACnC,OAAO,AAAC,CAAC,oBAAoB,gBAAgB,IAAI,mBAAoB;IACvE;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,YAAY,WAAW,UAAU,cAAc,MACpC,WAAW,WAAW,eAAe,MACrC,gBAAgB;QAEjC,IAAI,UAAU,MAAM,GAAG,GAAG,OAAO;QAEjC,MAAM,WAAW,KAAK,KAAK,CAAC,UAAU,MAAM,GAAG;QAC/C,MAAM,YAAY,UAAU,KAAK,CAAC,GAAG;QACrC,MAAM,aAAa,UAAU,KAAK,CAAC;QAEnC,MAAM,kBAAkB,UAAU,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,MAAM,EAAE;QAC3E,MAAM,mBAAmB,WAAW,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,MAAM,EAAE;QAE7E,IAAI,oBAAoB,GAAG,OAAO;QAClC,OAAO,AAAC,CAAC,mBAAmB,eAAe,IAAI,kBAAmB;IACpE;IAEA,qBAAqB;IACrB,MAAM,kBAAkB;QACtB,MAAM,eAAe;QACrB,MAAM,cAAc,OAAO,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,aAAa,MAAM;QACvE,MAAM,cAAc;QACpB,MAAM,oBAAoB;QAE1B,MAAM,cAAc,sBAAsB;QAC1C,MAAM,eAAe,gBAAgB;QAErC,OAAO;YACL;YACA;YACA;YACA;YACA,YAAY,WAAW,CAAC,EAAE,EAAE,eAAe;YAC3C,aAAa,YAAY,CAAC,EAAE,EAAE,YAAY;YAC1C,eAAe,iBAAiB;YAChC,aAAa,eAAe;QAC9B;IACF;IAEA,MAAM,QAA4B;QAChC;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,8OAAC,eAAe,QAAQ;QAAC,OAAO;kBAC7B;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT"}}, {"offset": {"line": 1277, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1331, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/New%20folder%20%288%29/arabic-ecommerce/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js';\nimport { Database } from '@/types/database';\n\n// التحقق من وجود متغيرات البيئة\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;\n\n// إذا لم تكن متغيرات البيئة موجودة، استخدم قيم افتراضية للتطوير\nconst defaultUrl = 'https://placeholder.supabase.co';\nconst defaultKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBsYWNlaG9sZGVyIiwicm9sZSI6ImFub24iLCJpYXQiOjE2NDUxOTI3MjAsImV4cCI6MTk2MDc2ODcyMH0.placeholder';\n\n// Create a single supabase client for interacting with your database\nexport const supabase = createClient<Database>(\n  supabaseUrl || defaultUrl,\n  supabaseAnonKey || defaultKey\n);\n\n// Admin client with service role key (for server-side operations)\nexport const supabaseAdmin = createClient<Database>(\n  supabaseUrl || defaultUrl,\n  process.env.SUPABASE_SERVICE_ROLE_KEY || defaultKey,\n  {\n    auth: {\n      autoRefreshToken: false,\n      persistSession: false\n    }\n  }\n);\n\n// Helper function to handle Supabase errors\nexport function handleSupabaseError(error: any) {\n  console.error('Supabase error:', error);\n\n  if (error?.message) {\n    return error.message;\n  }\n\n  return 'حدث خطأ غير متوقع';\n}\n\n// Helper function to format Supabase response\nexport function formatSupabaseResponse<T>(data: T[] | null, error: any) {\n  if (error) {\n    throw new Error(handleSupabaseError(error));\n  }\n\n  return data || [];\n}\n"], "names": [], "mappings": ";;;;;;AAAA;;AAGA,gCAAgC;AAChC,MAAM;AACN,MAAM;AAEN,gEAAgE;AAChE,MAAM,aAAa;AACnB,MAAM,aAAa;AAGZ,MAAM,WAAW,CAAA,GAAA,uLAAA,CAAA,eAAY,AAAD,EACjC,eAAe,YACf,mBAAmB;AAId,MAAM,gBAAgB,CAAA,GAAA,uLAAA,CAAA,eAAY,AAAD,EACtC,eAAe,YACf,QAAQ,GAAG,CAAC,yBAAyB,IAAI,YACzC;IACE,MAAM;QACJ,kBAAkB;QAClB,gBAAgB;IAClB;AACF;AAIK,SAAS,oBAAoB,KAAU;IAC5C,QAAQ,KAAK,CAAC,mBAAmB;IAEjC,IAAI,OAAO,SAAS;QAClB,OAAO,MAAM,OAAO;IACtB;IAEA,OAAO;AACT;AAGO,SAAS,uBAA0B,IAAgB,EAAE,KAAU;IACpE,IAAI,OAAO;QACT,MAAM,IAAI,MAAM,oBAAoB;IACtC;IAEA,OAAO,QAAQ,EAAE;AACnB"}}, {"offset": {"line": 1365, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1371, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/New%20folder%20%288%29/arabic-ecommerce/src/services/database.ts"], "sourcesContent": ["import { supabase, formatSupabaseResponse } from '@/lib/supabase';\nimport { Product, Category, Order, User, CartItem } from '@/types';\nimport { Database } from '@/types/database';\n\n// Type aliases for database types\ntype DbProduct = Database['public']['Tables']['products']['Row'];\ntype DbCategory = Database['public']['Tables']['categories']['Row'];\ntype DbOrder = Database['public']['Tables']['orders']['Row'];\ntype DbUser = Database['public']['Tables']['users']['Row'];\ntype DbCartItem = Database['public']['Tables']['cart_items']['Row'];\n\n// Helper functions to convert database types to app types\nfunction dbProductToProduct(dbProduct: DbProduct): Product {\n  return {\n    id: dbProduct.id,\n    name: dbProduct.name,\n    description: dbProduct.description,\n    price: dbProduct.price,\n    image: dbProduct.image,\n    category: dbProduct.category_id, // We'll need to join with categories to get the name\n    stock: dbProduct.stock,\n    featured: dbProduct.featured,\n    createdAt: new Date(dbProduct.created_at),\n    updatedAt: new Date(dbProduct.updated_at),\n  };\n}\n\nfunction dbCategoryToCategory(dbCategory: DbCategory): Category {\n  return {\n    id: dbCategory.id,\n    name: dbCategory.name,\n    description: dbCategory.description || '',\n    isActive: dbCategory.is_active,\n    productCount: dbCategory.product_count,\n    createdAt: new Date(dbCategory.created_at),\n    updatedAt: new Date(dbCategory.updated_at),\n  };\n}\n\nfunction dbUserToUser(dbUser: DbUser): User {\n  return {\n    id: dbUser.id,\n    name: dbUser.name,\n    email: dbUser.email,\n    phone: dbUser.phone || undefined,\n    role: dbUser.role as 'admin' | 'customer',\n    isActive: dbUser.is_active,\n    lastLogin: dbUser.last_login ? new Date(dbUser.last_login) : undefined,\n    totalOrders: dbUser.total_orders,\n    totalSpent: dbUser.total_spent,\n    address: dbUser.address as any,\n    createdAt: new Date(dbUser.created_at),\n    updatedAt: new Date(dbUser.updated_at),\n  };\n}\n\nfunction dbOrderToOrder(dbOrder: DbOrder): Order {\n  return {\n    id: dbOrder.id,\n    orderNumber: dbOrder.order_number,\n    customerId: dbOrder.customer_id,\n    customerName: dbOrder.customer_name,\n    customerEmail: dbOrder.customer_email,\n    items: dbOrder.items as any,\n    subtotal: dbOrder.subtotal,\n    shipping: dbOrder.shipping,\n    tax: dbOrder.tax,\n    total: dbOrder.total,\n    status: dbOrder.status as any,\n    paymentMethod: dbOrder.payment_method as any,\n    paymentStatus: dbOrder.payment_status as any,\n    shippingAddress: dbOrder.shipping_address as any,\n    notes: dbOrder.notes || undefined,\n    createdAt: new Date(dbOrder.created_at),\n    updatedAt: new Date(dbOrder.updated_at),\n    deliveredAt: dbOrder.delivered_at ? new Date(dbOrder.delivered_at) : undefined,\n  };\n}\n\n// Products Service\nexport const productsService = {\n  async getAll(): Promise<Product[]> {\n    const { data, error } = await supabase\n      .from('products')\n      .select('*')\n      .order('created_at', { ascending: false });\n\n    const products = formatSupabaseResponse(data, error);\n    return products.map(dbProductToProduct);\n  },\n\n  async getById(id: string): Promise<Product | null> {\n    const { data, error } = await supabase\n      .from('products')\n      .select('*')\n      .eq('id', id)\n      .single();\n\n    if (error || !data) return null;\n    return dbProductToProduct(data);\n  },\n\n  async getFeatured(): Promise<Product[]> {\n    const { data, error } = await supabase\n      .from('products')\n      .select('*')\n      .eq('featured', true)\n      .order('created_at', { ascending: false });\n\n    const products = formatSupabaseResponse(data, error);\n    return products.map(dbProductToProduct);\n  },\n\n  async getByCategory(categoryId: string): Promise<Product[]> {\n    const { data, error } = await supabase\n      .from('products')\n      .select('*')\n      .eq('category_id', categoryId)\n      .order('created_at', { ascending: false });\n\n    const products = formatSupabaseResponse(data, error);\n    return products.map(dbProductToProduct);\n  },\n\n  async create(product: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>): Promise<Product> {\n    const { data, error } = await supabase\n      .from('products')\n      .insert({\n        name: product.name,\n        description: product.description,\n        price: product.price,\n        image: product.image,\n        category_id: product.category,\n        stock: product.stock,\n        featured: product.featured,\n      })\n      .select()\n      .single();\n\n    if (error || !data) {\n      throw new Error(error?.message || 'Failed to create product');\n    }\n\n    return dbProductToProduct(data);\n  },\n\n  async update(id: string, updates: Partial<Product>): Promise<Product> {\n    const updateData: any = {};\n\n    if (updates.name) updateData.name = updates.name;\n    if (updates.description) updateData.description = updates.description;\n    if (updates.price !== undefined) updateData.price = updates.price;\n    if (updates.image) updateData.image = updates.image;\n    if (updates.category) updateData.category_id = updates.category;\n    if (updates.stock !== undefined) updateData.stock = updates.stock;\n    if (updates.featured !== undefined) updateData.featured = updates.featured;\n\n    const { data, error } = await supabase\n      .from('products')\n      .update(updateData)\n      .eq('id', id)\n      .select()\n      .single();\n\n    if (error || !data) {\n      throw new Error(error?.message || 'Failed to update product');\n    }\n\n    return dbProductToProduct(data);\n  },\n\n  async delete(id: string): Promise<void> {\n    const { error } = await supabase\n      .from('products')\n      .delete()\n      .eq('id', id);\n\n    if (error) {\n      throw new Error(error.message);\n    }\n  },\n};\n\n// Categories Service\nexport const categoriesService = {\n  async getAll(): Promise<Category[]> {\n    const { data, error } = await supabase\n      .from('categories')\n      .select('*')\n      .order('name');\n\n    const categories = formatSupabaseResponse(data, error);\n    return categories.map(dbCategoryToCategory);\n  },\n\n  async getById(id: string): Promise<Category | null> {\n    const { data, error } = await supabase\n      .from('categories')\n      .select('*')\n      .eq('id', id)\n      .single();\n\n    if (error || !data) return null;\n    return dbCategoryToCategory(data);\n  },\n\n  async create(category: Omit<Category, 'id' | 'createdAt' | 'updatedAt' | 'productCount'>): Promise<Category> {\n    const { data, error } = await supabase\n      .from('categories')\n      .insert({\n        name: category.name,\n        description: category.description,\n        is_active: category.isActive,\n      })\n      .select()\n      .single();\n\n    if (error || !data) {\n      throw new Error(error?.message || 'Failed to create category');\n    }\n\n    return dbCategoryToCategory(data);\n  },\n\n  async update(id: string, updates: Partial<Category>): Promise<Category> {\n    const updateData: any = {};\n\n    if (updates.name) updateData.name = updates.name;\n    if (updates.description !== undefined) updateData.description = updates.description;\n    if (updates.isActive !== undefined) updateData.is_active = updates.isActive;\n\n    const { data, error } = await supabase\n      .from('categories')\n      .update(updateData)\n      .eq('id', id)\n      .select()\n      .single();\n\n    if (error || !data) {\n      throw new Error(error?.message || 'Failed to update category');\n    }\n\n    return dbCategoryToCategory(data);\n  },\n\n  async delete(id: string): Promise<void> {\n    const { error } = await supabase\n      .from('categories')\n      .delete()\n      .eq('id', id);\n\n    if (error) {\n      throw new Error(error.message);\n    }\n  },\n};\n\n// Orders Service\nexport const ordersService = {\n  async getAll(): Promise<Order[]> {\n    const { data, error } = await supabase\n      .from('orders')\n      .select('*')\n      .order('created_at', { ascending: false });\n\n    const orders = formatSupabaseResponse(data, error);\n    return orders.map(dbOrderToOrder);\n  },\n\n  async getById(id: string): Promise<Order | null> {\n    const { data, error } = await supabase\n      .from('orders')\n      .select('*')\n      .eq('id', id)\n      .single();\n\n    if (error || !data) return null;\n    return dbOrderToOrder(data);\n  },\n\n  async getByCustomerId(customerId: string): Promise<Order[]> {\n    const { data, error } = await supabase\n      .from('orders')\n      .select('*')\n      .eq('customer_id', customerId)\n      .order('created_at', { ascending: false });\n\n    const orders = formatSupabaseResponse(data, error);\n    return orders.map(dbOrderToOrder);\n  },\n\n  async create(order: Omit<Order, 'id' | 'createdAt' | 'updatedAt'>): Promise<Order> {\n    const { data, error } = await supabase\n      .from('orders')\n      .insert({\n        order_number: order.orderNumber,\n        customer_id: order.customerId,\n        customer_name: order.customerName,\n        customer_email: order.customerEmail,\n        items: order.items as any,\n        subtotal: order.subtotal,\n        shipping: order.shipping,\n        tax: order.tax,\n        total: order.total,\n        status: order.status,\n        payment_method: order.paymentMethod,\n        payment_status: order.paymentStatus,\n        shipping_address: order.shippingAddress as any,\n        notes: order.notes,\n      })\n      .select()\n      .single();\n\n    if (error || !data) {\n      throw new Error(error?.message || 'Failed to create order');\n    }\n\n    return dbOrderToOrder(data);\n  },\n\n  async updateStatus(id: string, status: Order['status']): Promise<Order> {\n    const updateData: any = { status };\n\n    if (status === 'delivered') {\n      updateData.delivered_at = new Date().toISOString();\n    }\n\n    const { data, error } = await supabase\n      .from('orders')\n      .update(updateData)\n      .eq('id', id)\n      .select()\n      .single();\n\n    if (error || !data) {\n      throw new Error(error?.message || 'Failed to update order status');\n    }\n\n    return dbOrderToOrder(data);\n  },\n\n  async updatePaymentStatus(id: string, paymentStatus: Order['paymentStatus']): Promise<Order> {\n    const { data, error } = await supabase\n      .from('orders')\n      .update({ payment_status: paymentStatus })\n      .eq('id', id)\n      .select()\n      .single();\n\n    if (error || !data) {\n      throw new Error(error?.message || 'Failed to update payment status');\n    }\n\n    return dbOrderToOrder(data);\n  },\n};\n\n// Users Service\nexport const usersService = {\n  async getAll(): Promise<User[]> {\n    const { data, error } = await supabase\n      .from('users')\n      .select('*')\n      .order('created_at', { ascending: false });\n\n    const users = formatSupabaseResponse(data, error);\n    return users.map(dbUserToUser);\n  },\n\n  async getById(id: string): Promise<User | null> {\n    const { data, error } = await supabase\n      .from('users')\n      .select('*')\n      .eq('id', id)\n      .single();\n\n    if (error || !data) return null;\n    return dbUserToUser(data);\n  },\n\n  async create(user: Omit<User, 'id' | 'createdAt' | 'updatedAt' | 'totalOrders' | 'totalSpent'>): Promise<User> {\n    const { data, error } = await supabase\n      .from('users')\n      .insert({\n        name: user.name,\n        email: user.email,\n        phone: user.phone,\n        role: user.role,\n        is_active: user.isActive,\n        address: user.address as any,\n      })\n      .select()\n      .single();\n\n    if (error || !data) {\n      throw new Error(error?.message || 'Failed to create user');\n    }\n\n    return dbUserToUser(data);\n  },\n\n  async update(id: string, updates: Partial<User>): Promise<User> {\n    const updateData: any = {};\n\n    if (updates.name) updateData.name = updates.name;\n    if (updates.email) updateData.email = updates.email;\n    if (updates.phone !== undefined) updateData.phone = updates.phone;\n    if (updates.role) updateData.role = updates.role;\n    if (updates.isActive !== undefined) updateData.is_active = updates.isActive;\n    if (updates.address !== undefined) updateData.address = updates.address;\n    if (updates.totalOrders !== undefined) updateData.total_orders = updates.totalOrders;\n    if (updates.totalSpent !== undefined) updateData.total_spent = updates.totalSpent;\n\n    const { data, error } = await supabase\n      .from('users')\n      .update(updateData)\n      .eq('id', id)\n      .select()\n      .single();\n\n    if (error || !data) {\n      throw new Error(error?.message || 'Failed to update user');\n    }\n\n    return dbUserToUser(data);\n  },\n\n  async delete(id: string): Promise<void> {\n    const { error } = await supabase\n      .from('users')\n      .delete()\n      .eq('id', id);\n\n    if (error) {\n      throw new Error(error.message);\n    }\n  },\n\n  async updateLastLogin(id: string): Promise<void> {\n    const { error } = await supabase\n      .from('users')\n      .update({ last_login: new Date().toISOString() })\n      .eq('id', id);\n\n    if (error) {\n      throw new Error(error.message);\n    }\n  },\n\n  async getByEmail(email: string): Promise<User | null> {\n    const { data, error } = await supabase\n      .from('users')\n      .select('*')\n      .eq('email', email)\n      .single();\n\n    if (error) {\n      if (error.code === 'PGRST116') {\n        return null; // User not found\n      }\n      throw new Error(`Failed to get user by email: ${error.message}`);\n    }\n\n    return dbUserToUser(data);\n  },\n};"], "names": [], "mappings": ";;;;;;AAAA;;AAWA,0DAA0D;AAC1D,SAAS,mBAAmB,SAAoB;IAC9C,OAAO;QACL,IAAI,UAAU,EAAE;QAChB,MAAM,UAAU,IAAI;QACpB,aAAa,UAAU,WAAW;QAClC,OAAO,UAAU,KAAK;QACtB,OAAO,UAAU,KAAK;QACtB,UAAU,UAAU,WAAW;QAC/B,OAAO,UAAU,KAAK;QACtB,UAAU,UAAU,QAAQ;QAC5B,WAAW,IAAI,KAAK,UAAU,UAAU;QACxC,WAAW,IAAI,KAAK,UAAU,UAAU;IAC1C;AACF;AAEA,SAAS,qBAAqB,UAAsB;IAClD,OAAO;QACL,IAAI,WAAW,EAAE;QACjB,MAAM,WAAW,IAAI;QACrB,aAAa,WAAW,WAAW,IAAI;QACvC,UAAU,WAAW,SAAS;QAC9B,cAAc,WAAW,aAAa;QACtC,WAAW,IAAI,KAAK,WAAW,UAAU;QACzC,WAAW,IAAI,KAAK,WAAW,UAAU;IAC3C;AACF;AAEA,SAAS,aAAa,MAAc;IAClC,OAAO;QACL,IAAI,OAAO,EAAE;QACb,MAAM,OAAO,IAAI;QACjB,OAAO,OAAO,KAAK;QACnB,OAAO,OAAO,KAAK,IAAI;QACvB,MAAM,OAAO,IAAI;QACjB,UAAU,OAAO,SAAS;QAC1B,WAAW,OAAO,UAAU,GAAG,IAAI,KAAK,OAAO,UAAU,IAAI;QAC7D,aAAa,OAAO,YAAY;QAChC,YAAY,OAAO,WAAW;QAC9B,SAAS,OAAO,OAAO;QACvB,WAAW,IAAI,KAAK,OAAO,UAAU;QACrC,WAAW,IAAI,KAAK,OAAO,UAAU;IACvC;AACF;AAEA,SAAS,eAAe,OAAgB;IACtC,OAAO;QACL,IAAI,QAAQ,EAAE;QACd,aAAa,QAAQ,YAAY;QACjC,YAAY,QAAQ,WAAW;QAC/B,cAAc,QAAQ,aAAa;QACnC,eAAe,QAAQ,cAAc;QACrC,OAAO,QAAQ,KAAK;QACpB,UAAU,QAAQ,QAAQ;QAC1B,UAAU,QAAQ,QAAQ;QAC1B,KAAK,QAAQ,GAAG;QAChB,OAAO,QAAQ,KAAK;QACpB,QAAQ,QAAQ,MAAM;QACtB,eAAe,QAAQ,cAAc;QACrC,eAAe,QAAQ,cAAc;QACrC,iBAAiB,QAAQ,gBAAgB;QACzC,OAAO,QAAQ,KAAK,IAAI;QACxB,WAAW,IAAI,KAAK,QAAQ,UAAU;QACtC,WAAW,IAAI,KAAK,QAAQ,UAAU;QACtC,aAAa,QAAQ,YAAY,GAAG,IAAI,KAAK,QAAQ,YAAY,IAAI;IACvE;AACF;AAGO,MAAM,kBAAkB;IAC7B,MAAM;QACJ,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,YACL,MAAM,CAAC,KACP,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,MAAM,WAAW,CAAA,GAAA,sHAAA,CAAA,yBAAsB,AAAD,EAAE,MAAM;QAC9C,OAAO,SAAS,GAAG,CAAC;IACtB;IAEA,MAAM,SAAQ,EAAU;QACtB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,YACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,IACT,MAAM;QAET,IAAI,SAAS,CAAC,MAAM,OAAO;QAC3B,OAAO,mBAAmB;IAC5B;IAEA,MAAM;QACJ,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,YACL,MAAM,CAAC,KACP,EAAE,CAAC,YAAY,MACf,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,MAAM,WAAW,CAAA,GAAA,sHAAA,CAAA,yBAAsB,AAAD,EAAE,MAAM;QAC9C,OAAO,SAAS,GAAG,CAAC;IACtB;IAEA,MAAM,eAAc,UAAkB;QACpC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,YACL,MAAM,CAAC,KACP,EAAE,CAAC,eAAe,YAClB,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,MAAM,WAAW,CAAA,GAAA,sHAAA,CAAA,yBAAsB,AAAD,EAAE,MAAM;QAC9C,OAAO,SAAS,GAAG,CAAC;IACtB;IAEA,MAAM,QAAO,OAAwD;QACnE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,YACL,MAAM,CAAC;YACN,MAAM,QAAQ,IAAI;YAClB,aAAa,QAAQ,WAAW;YAChC,OAAO,QAAQ,KAAK;YACpB,OAAO,QAAQ,KAAK;YACpB,aAAa,QAAQ,QAAQ;YAC7B,OAAO,QAAQ,KAAK;YACpB,UAAU,QAAQ,QAAQ;QAC5B,GACC,MAAM,GACN,MAAM;QAET,IAAI,SAAS,CAAC,MAAM;YAClB,MAAM,IAAI,MAAM,OAAO,WAAW;QACpC;QAEA,OAAO,mBAAmB;IAC5B;IAEA,MAAM,QAAO,EAAU,EAAE,OAAyB;QAChD,MAAM,aAAkB,CAAC;QAEzB,IAAI,QAAQ,IAAI,EAAE,WAAW,IAAI,GAAG,QAAQ,IAAI;QAChD,IAAI,QAAQ,WAAW,EAAE,WAAW,WAAW,GAAG,QAAQ,WAAW;QACrE,IAAI,QAAQ,KAAK,KAAK,WAAW,WAAW,KAAK,GAAG,QAAQ,KAAK;QACjE,IAAI,QAAQ,KAAK,EAAE,WAAW,KAAK,GAAG,QAAQ,KAAK;QACnD,IAAI,QAAQ,QAAQ,EAAE,WAAW,WAAW,GAAG,QAAQ,QAAQ;QAC/D,IAAI,QAAQ,KAAK,KAAK,WAAW,WAAW,KAAK,GAAG,QAAQ,KAAK;QACjE,IAAI,QAAQ,QAAQ,KAAK,WAAW,WAAW,QAAQ,GAAG,QAAQ,QAAQ;QAE1E,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,YACL,MAAM,CAAC,YACP,EAAE,CAAC,MAAM,IACT,MAAM,GACN,MAAM;QAET,IAAI,SAAS,CAAC,MAAM;YAClB,MAAM,IAAI,MAAM,OAAO,WAAW;QACpC;QAEA,OAAO,mBAAmB;IAC5B;IAEA,MAAM,QAAO,EAAU;QACrB,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAC7B,IAAI,CAAC,YACL,MAAM,GACN,EAAE,CAAC,MAAM;QAEZ,IAAI,OAAO;YACT,MAAM,IAAI,MAAM,MAAM,OAAO;QAC/B;IACF;AACF;AAGO,MAAM,oBAAoB;IAC/B,MAAM;QACJ,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,cACL,MAAM,CAAC,KACP,KAAK,CAAC;QAET,MAAM,aAAa,CAAA,GAAA,sHAAA,CAAA,yBAAsB,AAAD,EAAE,MAAM;QAChD,OAAO,WAAW,GAAG,CAAC;IACxB;IAEA,MAAM,SAAQ,EAAU;QACtB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,cACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,IACT,MAAM;QAET,IAAI,SAAS,CAAC,MAAM,OAAO;QAC3B,OAAO,qBAAqB;IAC9B;IAEA,MAAM,QAAO,QAA2E;QACtF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,cACL,MAAM,CAAC;YACN,MAAM,SAAS,IAAI;YACnB,aAAa,SAAS,WAAW;YACjC,WAAW,SAAS,QAAQ;QAC9B,GACC,MAAM,GACN,MAAM;QAET,IAAI,SAAS,CAAC,MAAM;YAClB,MAAM,IAAI,MAAM,OAAO,WAAW;QACpC;QAEA,OAAO,qBAAqB;IAC9B;IAEA,MAAM,QAAO,EAAU,EAAE,OAA0B;QACjD,MAAM,aAAkB,CAAC;QAEzB,IAAI,QAAQ,IAAI,EAAE,WAAW,IAAI,GAAG,QAAQ,IAAI;QAChD,IAAI,QAAQ,WAAW,KAAK,WAAW,WAAW,WAAW,GAAG,QAAQ,WAAW;QACnF,IAAI,QAAQ,QAAQ,KAAK,WAAW,WAAW,SAAS,GAAG,QAAQ,QAAQ;QAE3E,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,cACL,MAAM,CAAC,YACP,EAAE,CAAC,MAAM,IACT,MAAM,GACN,MAAM;QAET,IAAI,SAAS,CAAC,MAAM;YAClB,MAAM,IAAI,MAAM,OAAO,WAAW;QACpC;QAEA,OAAO,qBAAqB;IAC9B;IAEA,MAAM,QAAO,EAAU;QACrB,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAC7B,IAAI,CAAC,cACL,MAAM,GACN,EAAE,CAAC,MAAM;QAEZ,IAAI,OAAO;YACT,MAAM,IAAI,MAAM,MAAM,OAAO;QAC/B;IACF;AACF;AAGO,MAAM,gBAAgB;IAC3B,MAAM;QACJ,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,UACL,MAAM,CAAC,KACP,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,MAAM,SAAS,CAAA,GAAA,sHAAA,CAAA,yBAAsB,AAAD,EAAE,MAAM;QAC5C,OAAO,OAAO,GAAG,CAAC;IACpB;IAEA,MAAM,SAAQ,EAAU;QACtB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,UACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,IACT,MAAM;QAET,IAAI,SAAS,CAAC,MAAM,OAAO;QAC3B,OAAO,eAAe;IACxB;IAEA,MAAM,iBAAgB,UAAkB;QACtC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,UACL,MAAM,CAAC,KACP,EAAE,CAAC,eAAe,YAClB,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,MAAM,SAAS,CAAA,GAAA,sHAAA,CAAA,yBAAsB,AAAD,EAAE,MAAM;QAC5C,OAAO,OAAO,GAAG,CAAC;IACpB;IAEA,MAAM,QAAO,KAAoD;QAC/D,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,UACL,MAAM,CAAC;YACN,cAAc,MAAM,WAAW;YAC/B,aAAa,MAAM,UAAU;YAC7B,eAAe,MAAM,YAAY;YACjC,gBAAgB,MAAM,aAAa;YACnC,OAAO,MAAM,KAAK;YAClB,UAAU,MAAM,QAAQ;YACxB,UAAU,MAAM,QAAQ;YACxB,KAAK,MAAM,GAAG;YACd,OAAO,MAAM,KAAK;YAClB,QAAQ,MAAM,MAAM;YACpB,gBAAgB,MAAM,aAAa;YACnC,gBAAgB,MAAM,aAAa;YACnC,kBAAkB,MAAM,eAAe;YACvC,OAAO,MAAM,KAAK;QACpB,GACC,MAAM,GACN,MAAM;QAET,IAAI,SAAS,CAAC,MAAM;YAClB,MAAM,IAAI,MAAM,OAAO,WAAW;QACpC;QAEA,OAAO,eAAe;IACxB;IAEA,MAAM,cAAa,EAAU,EAAE,MAAuB;QACpD,MAAM,aAAkB;YAAE;QAAO;QAEjC,IAAI,WAAW,aAAa;YAC1B,WAAW,YAAY,GAAG,IAAI,OAAO,WAAW;QAClD;QAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,UACL,MAAM,CAAC,YACP,EAAE,CAAC,MAAM,IACT,MAAM,GACN,MAAM;QAET,IAAI,SAAS,CAAC,MAAM;YAClB,MAAM,IAAI,MAAM,OAAO,WAAW;QACpC;QAEA,OAAO,eAAe;IACxB;IAEA,MAAM,qBAAoB,EAAU,EAAE,aAAqC;QACzE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,UACL,MAAM,CAAC;YAAE,gBAAgB;QAAc,GACvC,EAAE,CAAC,MAAM,IACT,MAAM,GACN,MAAM;QAET,IAAI,SAAS,CAAC,MAAM;YAClB,MAAM,IAAI,MAAM,OAAO,WAAW;QACpC;QAEA,OAAO,eAAe;IACxB;AACF;AAGO,MAAM,eAAe;IAC1B,MAAM;QACJ,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,SACL,MAAM,CAAC,KACP,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,MAAM,QAAQ,CAAA,GAAA,sHAAA,CAAA,yBAAsB,AAAD,EAAE,MAAM;QAC3C,OAAO,MAAM,GAAG,CAAC;IACnB;IAEA,MAAM,SAAQ,EAAU;QACtB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,SACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,IACT,MAAM;QAET,IAAI,SAAS,CAAC,MAAM,OAAO;QAC3B,OAAO,aAAa;IACtB;IAEA,MAAM,QAAO,IAAiF;QAC5F,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,SACL,MAAM,CAAC;YACN,MAAM,KAAK,IAAI;YACf,OAAO,KAAK,KAAK;YACjB,OAAO,KAAK,KAAK;YACjB,MAAM,KAAK,IAAI;YACf,WAAW,KAAK,QAAQ;YACxB,SAAS,KAAK,OAAO;QACvB,GACC,MAAM,GACN,MAAM;QAET,IAAI,SAAS,CAAC,MAAM;YAClB,MAAM,IAAI,MAAM,OAAO,WAAW;QACpC;QAEA,OAAO,aAAa;IACtB;IAEA,MAAM,QAAO,EAAU,EAAE,OAAsB;QAC7C,MAAM,aAAkB,CAAC;QAEzB,IAAI,QAAQ,IAAI,EAAE,WAAW,IAAI,GAAG,QAAQ,IAAI;QAChD,IAAI,QAAQ,KAAK,EAAE,WAAW,KAAK,GAAG,QAAQ,KAAK;QACnD,IAAI,QAAQ,KAAK,KAAK,WAAW,WAAW,KAAK,GAAG,QAAQ,KAAK;QACjE,IAAI,QAAQ,IAAI,EAAE,WAAW,IAAI,GAAG,QAAQ,IAAI;QAChD,IAAI,QAAQ,QAAQ,KAAK,WAAW,WAAW,SAAS,GAAG,QAAQ,QAAQ;QAC3E,IAAI,QAAQ,OAAO,KAAK,WAAW,WAAW,OAAO,GAAG,QAAQ,OAAO;QACvE,IAAI,QAAQ,WAAW,KAAK,WAAW,WAAW,YAAY,GAAG,QAAQ,WAAW;QACpF,IAAI,QAAQ,UAAU,KAAK,WAAW,WAAW,WAAW,GAAG,QAAQ,UAAU;QAEjF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,SACL,MAAM,CAAC,YACP,EAAE,CAAC,MAAM,IACT,MAAM,GACN,MAAM;QAET,IAAI,SAAS,CAAC,MAAM;YAClB,MAAM,IAAI,MAAM,OAAO,WAAW;QACpC;QAEA,OAAO,aAAa;IACtB;IAEA,MAAM,QAAO,EAAU;QACrB,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAC7B,IAAI,CAAC,SACL,MAAM,GACN,EAAE,CAAC,MAAM;QAEZ,IAAI,OAAO;YACT,MAAM,IAAI,MAAM,MAAM,OAAO;QAC/B;IACF;IAEA,MAAM,iBAAgB,EAAU;QAC9B,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAC7B,IAAI,CAAC,SACL,MAAM,CAAC;YAAE,YAAY,IAAI,OAAO,WAAW;QAAG,GAC9C,EAAE,CAAC,MAAM;QAEZ,IAAI,OAAO;YACT,MAAM,IAAI,MAAM,MAAM,OAAO;QAC/B;IACF;IAEA,MAAM,YAAW,KAAa;QAC5B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,SACL,MAAM,CAAC,KACP,EAAE,CAAC,SAAS,OACZ,MAAM;QAET,IAAI,OAAO;YACT,IAAI,MAAM,IAAI,KAAK,YAAY;gBAC7B,OAAO,MAAM,iBAAiB;YAChC;YACA,MAAM,IAAI,MAAM,CAAC,6BAA6B,EAAE,MAAM,OAAO,EAAE;QACjE;QAEA,OAAO,aAAa;IACtB;AACF"}}, {"offset": {"line": 1680, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1686, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/New%20folder%20%288%29/arabic-ecommerce/src/contexts/ProductContextDB.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';\nimport { Product } from '@/types';\nimport { productsService } from '@/services/database';\n\ninterface ProductContextType {\n  products: Product[];\n  addProduct: (product: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>) => Promise<void>;\n  updateProduct: (id: string, product: Partial<Product>) => Promise<void>;\n  deleteProduct: (id: string) => Promise<void>;\n  getProductById: (id: string) => Product | undefined;\n  getFeaturedProducts: () => Product[];\n  getProductsByCategory: (categoryId: string) => Product[];\n  refreshProducts: () => Promise<void>;\n  isLoading: boolean;\n  error: string | null;\n}\n\nconst ProductContext = createContext<ProductContextType | undefined>(undefined);\n\ninterface ProductProviderProps {\n  children: ReactNode;\n}\n\nexport function ProductProvider({ children }: ProductProviderProps) {\n  const [products, setProducts] = useState<Product[]>([]);\n  const [isLoading, setIsLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  // Load products from database\n  const loadProducts = async () => {\n    try {\n      setIsLoading(true);\n      setError(null);\n      const data = await productsService.getAll();\n      setProducts(data);\n    } catch (err) {\n      console.error('Error loading products:', err);\n      setError(err instanceof Error ? err.message : 'حدث خطأ في تحميل المنتجات');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // Initialize products on mount\n  useEffect(() => {\n    loadProducts();\n  }, []);\n\n  const addProduct = async (productData: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>) => {\n    try {\n      setError(null);\n      const newProduct = await productsService.create(productData);\n      setProducts(prev => [newProduct, ...prev]);\n    } catch (err) {\n      console.error('Error adding product:', err);\n      setError(err instanceof Error ? err.message : 'حدث خطأ في إضافة المنتج');\n      throw err;\n    }\n  };\n\n  const updateProduct = async (id: string, updates: Partial<Product>) => {\n    try {\n      setError(null);\n      const updatedProduct = await productsService.update(id, updates);\n      setProducts(prev => prev.map(product => \n        product.id === id ? updatedProduct : product\n      ));\n    } catch (err) {\n      console.error('Error updating product:', err);\n      setError(err instanceof Error ? err.message : 'حدث خطأ في تحديث المنتج');\n      throw err;\n    }\n  };\n\n  const deleteProduct = async (id: string) => {\n    try {\n      setError(null);\n      await productsService.delete(id);\n      setProducts(prev => prev.filter(product => product.id !== id));\n    } catch (err) {\n      console.error('Error deleting product:', err);\n      setError(err instanceof Error ? err.message : 'حدث خطأ في حذف المنتج');\n      throw err;\n    }\n  };\n\n  const getProductById = (id: string): Product | undefined => {\n    return products.find(product => product.id === id);\n  };\n\n  const getFeaturedProducts = (): Product[] => {\n    return products.filter(product => product.featured);\n  };\n\n  const getProductsByCategory = (categoryId: string): Product[] => {\n    return products.filter(product => product.category === categoryId);\n  };\n\n  const refreshProducts = async () => {\n    await loadProducts();\n  };\n\n  const value: ProductContextType = {\n    products,\n    addProduct,\n    updateProduct,\n    deleteProduct,\n    getProductById,\n    getFeaturedProducts,\n    getProductsByCategory,\n    refreshProducts,\n    isLoading,\n    error,\n  };\n\n  return (\n    <ProductContext.Provider value={value}>\n      {children}\n    </ProductContext.Provider>\n  );\n}\n\nexport function useProducts() {\n  const context = useContext(ProductContext);\n  if (context === undefined) {\n    throw new Error('useProducts must be used within a ProductProvider');\n  }\n  return context;\n}\n\n// Hook for getting products with optional filtering\nexport function useFilteredProducts(filters?: {\n  category?: string;\n  priceRange?: string;\n  searchTerm?: string;\n  featured?: boolean;\n}) {\n  const { products } = useProducts();\n\n  return React.useMemo(() => {\n    if (!filters) return products;\n\n    return products.filter(product => {\n      const matchesCategory = !filters.category || product.category === filters.category;\n      const matchesSearch = !filters.searchTerm || \n        product.name.toLowerCase().includes(filters.searchTerm.toLowerCase()) ||\n        product.description.toLowerCase().includes(filters.searchTerm.toLowerCase());\n      \n      let matchesPrice = true;\n      if (filters.priceRange) {\n        switch (filters.priceRange) {\n          case 'under-500':\n            matchesPrice = product.price < 500;\n            break;\n          case '500-1000':\n            matchesPrice = product.price >= 500 && product.price <= 1000;\n            break;\n          case '1000-3000':\n            matchesPrice = product.price >= 1000 && product.price <= 3000;\n            break;\n          case 'over-3000':\n            matchesPrice = product.price > 3000;\n            break;\n        }\n      }\n\n      const matchesFeatured = filters.featured === undefined || product.featured === filters.featured;\n\n      return matchesCategory && matchesSearch && matchesPrice && matchesFeatured;\n    });\n  }, [products, filters]);\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AAEA;AAJA;;;;AAmBA,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAkC;AAM9D,SAAS,gBAAgB,EAAE,QAAQ,EAAwB;IAChE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,8BAA8B;IAC9B,MAAM,eAAe;QACnB,IAAI;YACF,aAAa;YACb,SAAS;YACT,MAAM,OAAO,MAAM,2HAAA,CAAA,kBAAe,CAAC,MAAM;YACzC,YAAY;QACd,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,2BAA2B;YACzC,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD,SAAU;YACR,aAAa;QACf;IACF;IAEA,+BAA+B;IAC/B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,aAAa,OAAO;QACxB,IAAI;YACF,SAAS;YACT,MAAM,aAAa,MAAM,2HAAA,CAAA,kBAAe,CAAC,MAAM,CAAC;YAChD,YAAY,CAAA,OAAQ;oBAAC;uBAAe;iBAAK;QAC3C,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,yBAAyB;YACvC,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC9C,MAAM;QACR;IACF;IAEA,MAAM,gBAAgB,OAAO,IAAY;QACvC,IAAI;YACF,SAAS;YACT,MAAM,iBAAiB,MAAM,2HAAA,CAAA,kBAAe,CAAC,MAAM,CAAC,IAAI;YACxD,YAAY,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,UAC3B,QAAQ,EAAE,KAAK,KAAK,iBAAiB;QAEzC,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,2BAA2B;YACzC,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC9C,MAAM;QACR;IACF;IAEA,MAAM,gBAAgB,OAAO;QAC3B,IAAI;YACF,SAAS;YACT,MAAM,2HAAA,CAAA,kBAAe,CAAC,MAAM,CAAC;YAC7B,YAAY,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK;QAC5D,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,2BAA2B;YACzC,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC9C,MAAM;QACR;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAO,SAAS,IAAI,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK;IACjD;IAEA,MAAM,sBAAsB;QAC1B,OAAO,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,QAAQ;IACpD;IAEA,MAAM,wBAAwB,CAAC;QAC7B,OAAO,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,QAAQ,KAAK;IACzD;IAEA,MAAM,kBAAkB;QACtB,MAAM;IACR;IAEA,MAAM,QAA4B;QAChC;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,8OAAC,eAAe,QAAQ;QAAC,OAAO;kBAC7B;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAGO,SAAS,oBAAoB,OAKnC;IACC,MAAM,EAAE,QAAQ,EAAE,GAAG;IAErB,OAAO,qMAAA,CAAA,UAAK,CAAC,OAAO,CAAC;QACnB,IAAI,CAAC,SAAS,OAAO;QAErB,OAAO,SAAS,MAAM,CAAC,CAAA;YACrB,MAAM,kBAAkB,CAAC,QAAQ,QAAQ,IAAI,QAAQ,QAAQ,KAAK,QAAQ,QAAQ;YAClF,MAAM,gBAAgB,CAAC,QAAQ,UAAU,IACvC,QAAQ,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,QAAQ,UAAU,CAAC,WAAW,OAClE,QAAQ,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,QAAQ,UAAU,CAAC,WAAW;YAE3E,IAAI,eAAe;YACnB,IAAI,QAAQ,UAAU,EAAE;gBACtB,OAAQ,QAAQ,UAAU;oBACxB,KAAK;wBACH,eAAe,QAAQ,KAAK,GAAG;wBAC/B;oBACF,KAAK;wBACH,eAAe,QAAQ,KAAK,IAAI,OAAO,QAAQ,KAAK,IAAI;wBACxD;oBACF,KAAK;wBACH,eAAe,QAAQ,KAAK,IAAI,QAAQ,QAAQ,KAAK,IAAI;wBACzD;oBACF,KAAK;wBACH,eAAe,QAAQ,KAAK,GAAG;wBAC/B;gBACJ;YACF;YAEA,MAAM,kBAAkB,QAAQ,QAAQ,KAAK,aAAa,QAAQ,QAAQ,KAAK,QAAQ,QAAQ;YAE/F,OAAO,mBAAmB,iBAAiB,gBAAgB;QAC7D;IACF,GAAG;QAAC;QAAU;KAAQ;AACxB"}}, {"offset": {"line": 1829, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1835, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/New%20folder%20%288%29/arabic-ecommerce/src/contexts/CategoryContextDB.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';\nimport { categoriesService } from '@/services/database';\n\nexport interface Category {\n  id: string;\n  name: string;\n  description: string;\n  isActive: boolean;\n  productCount: number;\n  createdAt: Date;\n  updatedAt: Date;\n}\n\ninterface CategoryContextType {\n  categories: Category[];\n  addCategory: (category: Omit<Category, 'id' | 'createdAt' | 'updatedAt' | 'productCount'>) => Promise<void>;\n  updateCategory: (id: string, category: Partial<Category>) => Promise<void>;\n  deleteCategory: (id: string) => Promise<void>;\n  getCategoryById: (id: string) => Category | undefined;\n  getActiveCategories: () => Category[];\n  refreshCategories: () => Promise<void>;\n  isLoading: boolean;\n  error: string | null;\n}\n\nconst CategoryContext = createContext<CategoryContextType | undefined>(undefined);\n\ninterface CategoryProviderProps {\n  children: ReactNode;\n}\n\nexport function CategoryProvider({ children }: CategoryProviderProps) {\n  const [categories, setCategories] = useState<Category[]>([]);\n  const [isLoading, setIsLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  // Load categories from database\n  const loadCategories = async () => {\n    try {\n      setIsLoading(true);\n      setError(null);\n      const data = await categoriesService.getAll();\n      setCategories(data);\n    } catch (err) {\n      console.error('Error loading categories:', err);\n      setError(err instanceof Error ? err.message : 'حدث خطأ في تحميل التصنيفات');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // Initialize categories on mount\n  useEffect(() => {\n    loadCategories();\n  }, []);\n\n  const addCategory = async (categoryData: Omit<Category, 'id' | 'createdAt' | 'updatedAt' | 'productCount'>) => {\n    try {\n      setError(null);\n      const newCategory = await categoriesService.create(categoryData);\n      setCategories(prev => [newCategory, ...prev]);\n    } catch (err) {\n      console.error('Error adding category:', err);\n      setError(err instanceof Error ? err.message : 'حدث خطأ في إضافة التصنيف');\n      throw err;\n    }\n  };\n\n  const updateCategory = async (id: string, updates: Partial<Category>) => {\n    try {\n      setError(null);\n      const updatedCategory = await categoriesService.update(id, updates);\n      setCategories(prev => prev.map(category => \n        category.id === id ? updatedCategory : category\n      ));\n    } catch (err) {\n      console.error('Error updating category:', err);\n      setError(err instanceof Error ? err.message : 'حدث خطأ في تحديث التصنيف');\n      throw err;\n    }\n  };\n\n  const deleteCategory = async (id: string) => {\n    try {\n      setError(null);\n      await categoriesService.delete(id);\n      setCategories(prev => prev.filter(category => category.id !== id));\n    } catch (err) {\n      console.error('Error deleting category:', err);\n      setError(err instanceof Error ? err.message : 'حدث خطأ في حذف التصنيف');\n      throw err;\n    }\n  };\n\n  const getCategoryById = (id: string): Category | undefined => {\n    return categories.find(category => category.id === id);\n  };\n\n  const getActiveCategories = (): Category[] => {\n    return categories.filter(category => category.isActive);\n  };\n\n  const refreshCategories = async () => {\n    await loadCategories();\n  };\n\n  const value: CategoryContextType = {\n    categories,\n    addCategory,\n    updateCategory,\n    deleteCategory,\n    getCategoryById,\n    getActiveCategories,\n    refreshCategories,\n    isLoading,\n    error,\n  };\n\n  return (\n    <CategoryContext.Provider value={value}>\n      {children}\n    </CategoryContext.Provider>\n  );\n}\n\nexport function useCategories() {\n  const context = useContext(CategoryContext);\n  if (context === undefined) {\n    throw new Error('useCategories must be used within a CategoryProvider');\n  }\n  return context;\n}\n\n// Hook for getting category name by ID\nexport function useCategoryName(categoryId: string): string {\n  const { categories } = useCategories();\n  const category = categories.find(cat => cat.id === categoryId);\n  return category?.name || 'غير محدد';\n}\n\n// Hook for getting categories with product counts\nexport function useCategoriesWithCounts() {\n  const { categories } = useCategories();\n  \n  return React.useMemo(() => {\n    return categories.map(category => ({\n      ...category,\n      displayName: `${category.name} (${category.productCount})`,\n    }));\n  }, [categories]);\n}\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAHA;;;;AA2BA,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAmC;AAMhE,SAAS,iBAAiB,EAAE,QAAQ,EAAyB;IAClE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IAC3D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,gCAAgC;IAChC,MAAM,iBAAiB;QACrB,IAAI;YACF,aAAa;YACb,SAAS;YACT,MAAM,OAAO,MAAM,2HAAA,CAAA,oBAAiB,CAAC,MAAM;YAC3C,cAAc;QAChB,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD,SAAU;YACR,aAAa;QACf;IACF;IAEA,iCAAiC;IACjC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,cAAc,OAAO;QACzB,IAAI;YACF,SAAS;YACT,MAAM,cAAc,MAAM,2HAAA,CAAA,oBAAiB,CAAC,MAAM,CAAC;YACnD,cAAc,CAAA,OAAQ;oBAAC;uBAAgB;iBAAK;QAC9C,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,0BAA0B;YACxC,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC9C,MAAM;QACR;IACF;IAEA,MAAM,iBAAiB,OAAO,IAAY;QACxC,IAAI;YACF,SAAS;YACT,MAAM,kBAAkB,MAAM,2HAAA,CAAA,oBAAiB,CAAC,MAAM,CAAC,IAAI;YAC3D,cAAc,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,WAC7B,SAAS,EAAE,KAAK,KAAK,kBAAkB;QAE3C,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC9C,MAAM;QACR;IACF;IAEA,MAAM,iBAAiB,OAAO;QAC5B,IAAI;YACF,SAAS;YACT,MAAM,2HAAA,CAAA,oBAAiB,CAAC,MAAM,CAAC;YAC/B,cAAc,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,WAAY,SAAS,EAAE,KAAK;QAChE,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC9C,MAAM;QACR;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,OAAO,WAAW,IAAI,CAAC,CAAA,WAAY,SAAS,EAAE,KAAK;IACrD;IAEA,MAAM,sBAAsB;QAC1B,OAAO,WAAW,MAAM,CAAC,CAAA,WAAY,SAAS,QAAQ;IACxD;IAEA,MAAM,oBAAoB;QACxB,MAAM;IACR;IAEA,MAAM,QAA6B;QACjC;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,8OAAC,gBAAgB,QAAQ;QAAC,OAAO;kBAC9B;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAGO,SAAS,gBAAgB,UAAkB;IAChD,MAAM,EAAE,UAAU,EAAE,GAAG;IACvB,MAAM,WAAW,WAAW,IAAI,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;IACnD,OAAO,UAAU,QAAQ;AAC3B;AAGO,SAAS;IACd,MAAM,EAAE,UAAU,EAAE,GAAG;IAEvB,OAAO,qMAAA,CAAA,UAAK,CAAC,OAAO,CAAC;QACnB,OAAO,WAAW,GAAG,CAAC,CAAA,WAAY,CAAC;gBACjC,GAAG,QAAQ;gBACX,aAAa,GAAG,SAAS,IAAI,CAAC,EAAE,EAAE,SAAS,YAAY,CAAC,CAAC,CAAC;YAC5D,CAAC;IACH,GAAG;QAAC;KAAW;AACjB"}}, {"offset": {"line": 1959, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1965, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/New%20folder%20%288%29/arabic-ecommerce/src/lib/config.ts"], "sourcesContent": ["// Configuration for switching between mock data and real database\nexport const APP_CONFIG = {\n  // Set to true to use Supabase database, false to use mock data\n  USE_DATABASE: process.env.NEXT_PUBLIC_USE_DATABASE === 'true',\n\n  // Database configuration\n  DATABASE: {\n    SUPABASE_URL: process.env.NEXT_PUBLIC_SUPABASE_URL,\n    SUPABASE_ANON_KEY: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,\n    SUPABASE_SERVICE_ROLE_KEY: process.env.SUPABASE_SERVICE_ROLE_KEY,\n  },\n\n  // App configuration\n  APP: {\n    NAME: process.env.NEXT_PUBLIC_APP_NAME || 'المتجر العربي',\n    URL: process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000',\n  },\n\n  // Feature flags\n  FEATURES: {\n    ENABLE_PAYMENTS: process.env.NEXT_PUBLIC_ENABLE_PAYMENTS === 'true',\n    ENABLE_AUTH: process.env.NEXT_PUBLIC_ENABLE_AUTH === 'true',\n    ENABLE_ANALYTICS: process.env.NEXT_PUBLIC_ENABLE_ANALYTICS === 'true',\n  },\n\n  // Payment configuration\n  PAYMENTS: {\n    STRIPE_PUBLISHABLE_KEY: process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY,\n    STRIPE_SECRET_KEY: process.env.STRIPE_SECRET_KEY,\n  },\n};\n\n// Helper function to check if database is configured\nexport function isDatabaseConfigured(): boolean {\n  const url = APP_CONFIG.DATABASE.SUPABASE_URL;\n  const key = APP_CONFIG.DATABASE.SUPABASE_ANON_KEY;\n\n  return !!(\n    url &&\n    key &&\n    url !== 'https://your-project-id.supabase.co' &&\n    key !== 'your_anon_key_here'\n  );\n}\n\n// Helper function to check if we should use database\nexport function shouldUseDatabase(): boolean {\n  return APP_CONFIG.USE_DATABASE && isDatabaseConfigured();\n}\n\n// Helper function to get the appropriate context providers\nexport function getContextProviders() {\n  if (shouldUseDatabase()) {\n    return {\n      ProductProvider: require('@/contexts/ProductContextDB').ProductProvider,\n      CategoryProvider: require('@/contexts/CategoryContextDB').CategoryProvider,\n      useProducts: require('@/contexts/ProductContextDB').useProducts,\n      useCategories: require('@/contexts/CategoryContextDB').useCategories,\n    };\n  } else {\n    return {\n      ProductProvider: require('@/contexts/ProductContext').ProductProvider,\n      CategoryProvider: require('@/contexts/CategoryContext').CategoryProvider,\n      useProducts: require('@/contexts/ProductContext').useProducts,\n      useCategories: require('@/contexts/CategoryContext').useCategories,\n    };\n  }\n}\n"], "names": [], "mappings": "AAAA,kEAAkE;;;;;;;AAC3D,MAAM,aAAa;IACxB,+DAA+D;IAC/D,cAAc,8CAAyC;IAEvD,yBAAyB;IACzB,UAAU;QACR,YAAY;QACZ,iBAAiB;QACjB,2BAA2B,QAAQ,GAAG,CAAC,yBAAyB;IAClE;IAEA,oBAAoB;IACpB,KAAK;QACH,MAAM,qDAAoC;QAC1C,KAAK,6DAAmC;IAC1C;IAEA,gBAAgB;IAChB,UAAU;QACR,iBAAiB,8CAA4C;QAC7D,aAAa,8CAAwC;QACrD,kBAAkB,8CAA6C;IACjE;IAEA,wBAAwB;IACxB,UAAU;QACR,sBAAsB;QACtB,mBAAmB,QAAQ,GAAG,CAAC,iBAAiB;IAClD;AACF;AAGO,SAAS;IACd,MAAM,MAAM,WAAW,QAAQ,CAAC,YAAY;IAC5C,MAAM,MAAM,WAAW,QAAQ,CAAC,iBAAiB;IAEjD,OAAO,CAAC,CAAC,CACP,OACA,OACA,QAAQ,yCACR,QAAQ,oBACV;AACF;AAGO,SAAS;IACd,OAAO,WAAW,YAAY,IAAI;AACpC;AAGO,SAAS;IACd,IAAI,qBAAqB;QACvB,OAAO;YACL,iBAAiB,4FAAuC,eAAe;YACvE,kBAAkB,6FAAwC,gBAAgB;YAC1E,aAAa,4FAAuC,WAAW;YAC/D,eAAe,6FAAwC,aAAa;QACtE;IACF,OAAO;QACL,OAAO;YACL,iBAAiB,0FAAqC,eAAe;YACrE,kBAAkB,2FAAsC,gBAAgB;YACxE,aAAa,0FAAqC,WAAW;YAC7D,eAAe,2FAAsC,aAAa;QACpE;IACF;AACF"}}, {"offset": {"line": 2023, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2029, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/New%20folder%20%288%29/arabic-ecommerce/src/contexts/AuthContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';\nimport { User, Session, AuthError } from '@supabase/supabase-js';\nimport { supabase } from '@/lib/supabase';\nimport { usersService } from '@/services/database';\nimport { User as AppUser } from '@/types';\nimport { isDatabaseConfigured } from '@/lib/config';\n\ninterface AuthContextType {\n  user: User | null;\n  appUser: AppUser | null;\n  session: Session | null;\n  loading: boolean;\n  signUp: (email: string, password: string, userData: { name: string; phone?: string }) => Promise<{ error: AuthError | null }>;\n  signIn: (email: string, password: string) => Promise<{ error: AuthError | null }>;\n  signOut: () => Promise<{ error: AuthError | null }>;\n  resetPassword: (email: string) => Promise<{ error: AuthError | null }>;\n  updateProfile: (updates: Partial<AppUser>) => Promise<{ error: Error | null }>;\n  isAdmin: boolean;\n  isAuthenticated: boolean;\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\n\ninterface AuthProviderProps {\n  children: ReactNode;\n}\n\nexport function AuthProvider({ children }: AuthProviderProps) {\n  const [user, setUser] = useState<User | null>(null);\n  const [appUser, setAppUser] = useState<AppUser | null>(null);\n  const [session, setSession] = useState<Session | null>(null);\n  const [loading, setLoading] = useState(true);\n\n  // تحميل بيانات المستخدم من قاعدة البيانات\n  const loadAppUser = async (userId: string) => {\n    // إذا لم تكن قاعدة البيانات مُعدة، إنشاء مستخدم وهمي\n    if (!isDatabaseConfigured()) {\n      setAppUser({\n        id: userId,\n        name: 'مستخدم تجريبي',\n        email: '<EMAIL>',\n        role: 'customer',\n        isActive: true,\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString(),\n      });\n      return;\n    }\n\n    try {\n      const userData = await usersService.getById(userId);\n      setAppUser(userData);\n\n      // تحديث آخر تسجيل دخول\n      if (userData) {\n        await usersService.updateLastLogin(userId);\n      }\n    } catch (error) {\n      console.error('خطأ في تحميل بيانات المستخدم:', error);\n      // في حالة الخطأ، إنشاء مستخدم وهمي\n      setAppUser({\n        id: userId,\n        name: 'مستخدم',\n        email: '<EMAIL>',\n        role: 'customer',\n        isActive: true,\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString(),\n      });\n    }\n  };\n\n  // مراقبة تغييرات المصادقة\n  useEffect(() => {\n    // إذا لم تكن قاعدة البيانات مُعدة، استخدم نظام وهمي\n    if (!isDatabaseConfigured()) {\n      setLoading(false);\n      return;\n    }\n\n    // الحصول على الجلسة الحالية\n    supabase.auth.getSession().then(({ data: { session } }) => {\n      setSession(session);\n      setUser(session?.user ?? null);\n\n      if (session?.user) {\n        loadAppUser(session.user.id);\n      }\n\n      setLoading(false);\n    }).catch((error) => {\n      console.error('خطأ في الحصول على الجلسة:', error);\n      setLoading(false);\n    });\n\n    // الاستماع لتغييرات المصادقة\n    const { data: { subscription } } = supabase.auth.onAuthStateChange(\n      async (event, session) => {\n        setSession(session);\n        setUser(session?.user ?? null);\n\n        if (session?.user) {\n          await loadAppUser(session.user.id);\n        } else {\n          setAppUser(null);\n        }\n\n        setLoading(false);\n      }\n    );\n\n    return () => subscription.unsubscribe();\n  }, []);\n\n  // تسجيل حساب جديد\n  const signUp = async (\n    email: string,\n    password: string,\n    userData: { name: string; phone?: string }\n  ) => {\n    // إذا لم تكن قاعدة البيانات مُعدة، إرجاع خطأ\n    if (!isDatabaseConfigured()) {\n      return { error: { message: 'نظام المصادقة غير متاح حالياً. يرجى المحاولة لاحقاً.' } as AuthError };\n    }\n\n    try {\n      const { data, error } = await supabase.auth.signUp({\n        email,\n        password,\n        options: {\n          data: {\n            name: userData.name,\n            phone: userData.phone,\n          }\n        }\n      });\n\n      if (error) return { error };\n\n      // إنشاء سجل المستخدم في قاعدة البيانات\n      if (data.user) {\n        try {\n          await usersService.create({\n            id: data.user.id,\n            name: userData.name,\n            email: email,\n            phone: userData.phone,\n            role: 'customer',\n            isActive: true,\n          });\n        } catch (dbError) {\n          console.error('خطأ في إنشاء سجل المستخدم:', dbError);\n        }\n      }\n\n      return { error: null };\n    } catch (error) {\n      return { error: error as AuthError };\n    }\n  };\n\n  // تسجيل الدخول\n  const signIn = async (email: string, password: string) => {\n    // إذا لم تكن قاعدة البيانات مُعدة، إرجاع خطأ\n    if (!isDatabaseConfigured()) {\n      return { error: { message: 'نظام المصادقة غير متاح حالياً. يرجى المحاولة لاحقاً.' } as AuthError };\n    }\n\n    try {\n      const { error } = await supabase.auth.signInWithPassword({\n        email,\n        password,\n      });\n\n      return { error };\n    } catch (error) {\n      return { error: error as AuthError };\n    }\n  };\n\n  // تسجيل الخروج\n  const signOut = async () => {\n    try {\n      const { error } = await supabase.auth.signOut();\n\n      if (!error) {\n        setUser(null);\n        setAppUser(null);\n        setSession(null);\n      }\n\n      return { error };\n    } catch (error) {\n      return { error: error as AuthError };\n    }\n  };\n\n  // إعادة تعيين كلمة المرور\n  const resetPassword = async (email: string) => {\n    try {\n      const { error } = await supabase.auth.resetPasswordForEmail(email, {\n        redirectTo: `${window.location.origin}/auth/reset-password`,\n      });\n\n      return { error };\n    } catch (error) {\n      return { error: error as AuthError };\n    }\n  };\n\n  // تحديث الملف الشخصي\n  const updateProfile = async (updates: Partial<AppUser>) => {\n    if (!user || !appUser) {\n      return { error: new Error('المستخدم غير مسجل الدخول') };\n    }\n\n    try {\n      // تحديث بيانات المصادقة إذا لزم الأمر\n      if (updates.email && updates.email !== user.email) {\n        const { error: authError } = await supabase.auth.updateUser({\n          email: updates.email\n        });\n\n        if (authError) {\n          return { error: new Error(authError.message) };\n        }\n      }\n\n      // تحديث بيانات التطبيق\n      const updatedUser = await usersService.update(user.id, updates);\n      setAppUser(updatedUser);\n\n      return { error: null };\n    } catch (error) {\n      return { error: error as Error };\n    }\n  };\n\n  const isAdmin = appUser?.role === 'admin';\n  const isAuthenticated = !!user;\n\n  const value: AuthContextType = {\n    user,\n    appUser,\n    session,\n    loading,\n    signUp,\n    signIn,\n    signOut,\n    resetPassword,\n    updateProfile,\n    isAdmin,\n    isAuthenticated,\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n}\n\nexport function useAuth() {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n}\n\n// Hook للتحقق من الصلاحيات\nexport function useRequireAuth(redirectTo = '/auth/login') {\n  const { isAuthenticated, loading } = useAuth();\n\n  useEffect(() => {\n    if (!loading && !isAuthenticated) {\n      window.location.href = redirectTo;\n    }\n  }, [isAuthenticated, loading, redirectTo]);\n\n  return { isAuthenticated, loading };\n}\n\n// Hook للتحقق من صلاحيات المدير\nexport function useRequireAdmin(redirectTo = '/') {\n  const { isAdmin, loading, isAuthenticated } = useAuth();\n\n  useEffect(() => {\n    if (!loading && (!isAuthenticated || !isAdmin)) {\n      window.location.href = redirectTo;\n    }\n  }, [isAdmin, loading, isAuthenticated, redirectTo]);\n\n  return { isAdmin, loading };\n}\n"], "names": [], "mappings": ";;;;;;;AAEA;AAEA;AACA;AAEA;AAPA;;;;;;AAuBA,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAA+B;AAMxD,SAAS,aAAa,EAAE,QAAQ,EAAqB;IAC1D,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IACvD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IACvD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,0CAA0C;IAC1C,MAAM,cAAc,OAAO;QACzB,qDAAqD;QACrD,IAAI,CAAC,CAAA,GAAA,oHAAA,CAAA,uBAAoB,AAAD,KAAK;YAC3B,WAAW;gBACT,IAAI;gBACJ,MAAM;gBACN,OAAO;gBACP,MAAM;gBACN,UAAU;gBACV,WAAW,IAAI,OAAO,WAAW;gBACjC,WAAW,IAAI,OAAO,WAAW;YACnC;YACA;QACF;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,2HAAA,CAAA,eAAY,CAAC,OAAO,CAAC;YAC5C,WAAW;YAEX,uBAAuB;YACvB,IAAI,UAAU;gBACZ,MAAM,2HAAA,CAAA,eAAY,CAAC,eAAe,CAAC;YACrC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,mCAAmC;YACnC,WAAW;gBACT,IAAI;gBACJ,MAAM;gBACN,OAAO;gBACP,MAAM;gBACN,UAAU;gBACV,WAAW,IAAI,OAAO,WAAW;gBACjC,WAAW,IAAI,OAAO,WAAW;YACnC;QACF;IACF;IAEA,0BAA0B;IAC1B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,oDAAoD;QACpD,IAAI,CAAC,CAAA,GAAA,oHAAA,CAAA,uBAAoB,AAAD,KAAK;YAC3B,WAAW;YACX;QACF;QAEA,4BAA4B;QAC5B,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE;YACpD,WAAW;YACX,QAAQ,SAAS,QAAQ;YAEzB,IAAI,SAAS,MAAM;gBACjB,YAAY,QAAQ,IAAI,CAAC,EAAE;YAC7B;YAEA,WAAW;QACb,GAAG,KAAK,CAAC,CAAC;YACR,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,WAAW;QACb;QAEA,6BAA6B;QAC7B,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,EAAE,GAAG,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,iBAAiB,CAChE,OAAO,OAAO;YACZ,WAAW;YACX,QAAQ,SAAS,QAAQ;YAEzB,IAAI,SAAS,MAAM;gBACjB,MAAM,YAAY,QAAQ,IAAI,CAAC,EAAE;YACnC,OAAO;gBACL,WAAW;YACb;YAEA,WAAW;QACb;QAGF,OAAO,IAAM,aAAa,WAAW;IACvC,GAAG,EAAE;IAEL,kBAAkB;IAClB,MAAM,SAAS,OACb,OACA,UACA;QAEA,6CAA6C;QAC7C,IAAI,CAAC,CAAA,GAAA,oHAAA,CAAA,uBAAoB,AAAD,KAAK;YAC3B,OAAO;gBAAE,OAAO;oBAAE,SAAS;gBAAuD;YAAe;QACnG;QAEA,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,MAAM,CAAC;gBACjD;gBACA;gBACA,SAAS;oBACP,MAAM;wBACJ,MAAM,SAAS,IAAI;wBACnB,OAAO,SAAS,KAAK;oBACvB;gBACF;YACF;YAEA,IAAI,OAAO,OAAO;gBAAE;YAAM;YAE1B,uCAAuC;YACvC,IAAI,KAAK,IAAI,EAAE;gBACb,IAAI;oBACF,MAAM,2HAAA,CAAA,eAAY,CAAC,MAAM,CAAC;wBACxB,IAAI,KAAK,IAAI,CAAC,EAAE;wBAChB,MAAM,SAAS,IAAI;wBACnB,OAAO;wBACP,OAAO,SAAS,KAAK;wBACrB,MAAM;wBACN,UAAU;oBACZ;gBACF,EAAE,OAAO,SAAS;oBAChB,QAAQ,KAAK,CAAC,8BAA8B;gBAC9C;YACF;YAEA,OAAO;gBAAE,OAAO;YAAK;QACvB,EAAE,OAAO,OAAO;YACd,OAAO;gBAAE,OAAO;YAAmB;QACrC;IACF;IAEA,eAAe;IACf,MAAM,SAAS,OAAO,OAAe;QACnC,6CAA6C;QAC7C,IAAI,CAAC,CAAA,GAAA,oHAAA,CAAA,uBAAoB,AAAD,KAAK;YAC3B,OAAO;gBAAE,OAAO;oBAAE,SAAS;gBAAuD;YAAe;QACnG;QAEA,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC;gBACvD;gBACA;YACF;YAEA,OAAO;gBAAE;YAAM;QACjB,EAAE,OAAO,OAAO;YACd,OAAO;gBAAE,OAAO;YAAmB;QACrC;IACF;IAEA,eAAe;IACf,MAAM,UAAU;QACd,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,OAAO;YAE7C,IAAI,CAAC,OAAO;gBACV,QAAQ;gBACR,WAAW;gBACX,WAAW;YACb;YAEA,OAAO;gBAAE;YAAM;QACjB,EAAE,OAAO,OAAO;YACd,OAAO;gBAAE,OAAO;YAAmB;QACrC;IACF;IAEA,0BAA0B;IAC1B,MAAM,gBAAgB,OAAO;QAC3B,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,qBAAqB,CAAC,OAAO;gBACjE,YAAY,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,oBAAoB,CAAC;YAC7D;YAEA,OAAO;gBAAE;YAAM;QACjB,EAAE,OAAO,OAAO;YACd,OAAO;gBAAE,OAAO;YAAmB;QACrC;IACF;IAEA,qBAAqB;IACrB,MAAM,gBAAgB,OAAO;QAC3B,IAAI,CAAC,QAAQ,CAAC,SAAS;YACrB,OAAO;gBAAE,OAAO,IAAI,MAAM;YAA4B;QACxD;QAEA,IAAI;YACF,sCAAsC;YACtC,IAAI,QAAQ,KAAK,IAAI,QAAQ,KAAK,KAAK,KAAK,KAAK,EAAE;gBACjD,MAAM,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,UAAU,CAAC;oBAC1D,OAAO,QAAQ,KAAK;gBACtB;gBAEA,IAAI,WAAW;oBACb,OAAO;wBAAE,OAAO,IAAI,MAAM,UAAU,OAAO;oBAAE;gBAC/C;YACF;YAEA,uBAAuB;YACvB,MAAM,cAAc,MAAM,2HAAA,CAAA,eAAY,CAAC,MAAM,CAAC,KAAK,EAAE,EAAE;YACvD,WAAW;YAEX,OAAO;gBAAE,OAAO;YAAK;QACvB,EAAE,OAAO,OAAO;YACd,OAAO;gBAAE,OAAO;YAAe;QACjC;IACF;IAEA,MAAM,UAAU,SAAS,SAAS;IAClC,MAAM,kBAAkB,CAAC,CAAC;IAE1B,MAAM,QAAyB;QAC7B;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,8OAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAGO,SAAS,eAAe,aAAa,aAAa;IACvD,MAAM,EAAE,eAAe,EAAE,OAAO,EAAE,GAAG;IAErC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,WAAW,CAAC,iBAAiB;YAChC,OAAO,QAAQ,CAAC,IAAI,GAAG;QACzB;IACF,GAAG;QAAC;QAAiB;QAAS;KAAW;IAEzC,OAAO;QAAE;QAAiB;IAAQ;AACpC;AAGO,SAAS,gBAAgB,aAAa,GAAG;IAC9C,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,eAAe,EAAE,GAAG;IAE9C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,WAAW,CAAC,CAAC,mBAAmB,CAAC,OAAO,GAAG;YAC9C,OAAO,QAAQ,CAAC,IAAI,GAAG;QACzB;IACF,GAAG;QAAC;QAAS;QAAS;QAAiB;KAAW;IAElD,OAAO;QAAE;QAAS;IAAQ;AAC5B"}}, {"offset": {"line": 2320, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2326, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/New%20folder%20%288%29/arabic-ecommerce/src/contexts/CartContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';\nimport { Product } from '@/types';\n\nexport interface CartItem {\n  id: string;\n  product: Product;\n  quantity: number;\n  selectedSize?: string;\n  selectedColor?: string;\n}\n\ninterface CartContextType {\n  items: CartItem[];\n  totalItems: number;\n  totalPrice: number;\n  addToCart: (product: Product, quantity?: number, options?: { size?: string; color?: string }) => void;\n  removeFromCart: (itemId: string) => void;\n  updateQuantity: (itemId: string, quantity: number) => void;\n  clearCart: () => void;\n  isInCart: (productId: string) => boolean;\n  getCartItem: (productId: string) => CartItem | undefined;\n}\n\nconst CartContext = createContext<CartContextType | undefined>(undefined);\n\ninterface CartProviderProps {\n  children: ReactNode;\n}\n\nexport function CartProvider({ children }: CartProviderProps) {\n  const [items, setItems] = useState<CartItem[]>([]);\n\n  // تحميل السلة من localStorage عند بدء التطبيق\n  useEffect(() => {\n    const savedCart = localStorage.getItem('cart');\n    if (savedCart) {\n      try {\n        setItems(JSON.parse(savedCart));\n      } catch (error) {\n        console.error('Error loading cart from localStorage:', error);\n      }\n    }\n  }, []);\n\n  // حفظ السلة في localStorage عند تغييرها\n  useEffect(() => {\n    localStorage.setItem('cart', JSON.stringify(items));\n  }, [items]);\n\n  // حساب إجمالي العناصر\n  const totalItems = items.reduce((total, item) => total + item.quantity, 0);\n\n  // حساب إجمالي السعر\n  const totalPrice = items.reduce((total, item) => total + (item.product.price * item.quantity), 0);\n\n  // إضافة منتج للسلة\n  const addToCart = (\n    product: Product, \n    quantity: number = 1, \n    options?: { size?: string; color?: string }\n  ) => {\n    setItems(currentItems => {\n      const existingItemIndex = currentItems.findIndex(\n        item => item.product.id === product.id && \n                item.selectedSize === options?.size && \n                item.selectedColor === options?.color\n      );\n\n      if (existingItemIndex > -1) {\n        // إذا كان المنتج موجود، زيادة الكمية\n        const updatedItems = [...currentItems];\n        updatedItems[existingItemIndex].quantity += quantity;\n        return updatedItems;\n      } else {\n        // إضافة منتج جديد\n        const newItem: CartItem = {\n          id: `${product.id}-${options?.size || ''}-${options?.color || ''}-${Date.now()}`,\n          product,\n          quantity,\n          selectedSize: options?.size,\n          selectedColor: options?.color,\n        };\n        return [...currentItems, newItem];\n      }\n    });\n  };\n\n  // إزالة منتج من السلة\n  const removeFromCart = (itemId: string) => {\n    setItems(currentItems => currentItems.filter(item => item.id !== itemId));\n  };\n\n  // تحديث كمية منتج\n  const updateQuantity = (itemId: string, quantity: number) => {\n    if (quantity <= 0) {\n      removeFromCart(itemId);\n      return;\n    }\n\n    setItems(currentItems =>\n      currentItems.map(item =>\n        item.id === itemId ? { ...item, quantity } : item\n      )\n    );\n  };\n\n  // مسح السلة\n  const clearCart = () => {\n    setItems([]);\n  };\n\n  // التحقق من وجود منتج في السلة\n  const isInCart = (productId: string) => {\n    return items.some(item => item.product.id === productId);\n  };\n\n  // الحصول على عنصر من السلة\n  const getCartItem = (productId: string) => {\n    return items.find(item => item.product.id === productId);\n  };\n\n  const value: CartContextType = {\n    items,\n    totalItems,\n    totalPrice,\n    addToCart,\n    removeFromCart,\n    updateQuantity,\n    clearCart,\n    isInCart,\n    getCartItem,\n  };\n\n  return (\n    <CartContext.Provider value={value}>\n      {children}\n    </CartContext.Provider>\n  );\n}\n\nexport function useCart() {\n  const context = useContext(CartContext);\n  if (context === undefined) {\n    throw new Error('useCart must be used within a CartProvider');\n  }\n  return context;\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAyBA,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAA+B;AAMxD,SAAS,aAAa,EAAE,QAAQ,EAAqB;IAC1D,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IAEjD,8CAA8C;IAC9C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,YAAY,aAAa,OAAO,CAAC;QACvC,IAAI,WAAW;YACb,IAAI;gBACF,SAAS,KAAK,KAAK,CAAC;YACtB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,yCAAyC;YACzD;QACF;IACF,GAAG,EAAE;IAEL,wCAAwC;IACxC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,aAAa,OAAO,CAAC,QAAQ,KAAK,SAAS,CAAC;IAC9C,GAAG;QAAC;KAAM;IAEV,sBAAsB;IACtB,MAAM,aAAa,MAAM,MAAM,CAAC,CAAC,OAAO,OAAS,QAAQ,KAAK,QAAQ,EAAE;IAExE,oBAAoB;IACpB,MAAM,aAAa,MAAM,MAAM,CAAC,CAAC,OAAO,OAAS,QAAS,KAAK,OAAO,CAAC,KAAK,GAAG,KAAK,QAAQ,EAAG;IAE/F,mBAAmB;IACnB,MAAM,YAAY,CAChB,SACA,WAAmB,CAAC,EACpB;QAEA,SAAS,CAAA;YACP,MAAM,oBAAoB,aAAa,SAAS,CAC9C,CAAA,OAAQ,KAAK,OAAO,CAAC,EAAE,KAAK,QAAQ,EAAE,IAC9B,KAAK,YAAY,KAAK,SAAS,QAC/B,KAAK,aAAa,KAAK,SAAS;YAG1C,IAAI,oBAAoB,CAAC,GAAG;gBAC1B,qCAAqC;gBACrC,MAAM,eAAe;uBAAI;iBAAa;gBACtC,YAAY,CAAC,kBAAkB,CAAC,QAAQ,IAAI;gBAC5C,OAAO;YACT,OAAO;gBACL,kBAAkB;gBAClB,MAAM,UAAoB;oBACxB,IAAI,GAAG,QAAQ,EAAE,CAAC,CAAC,EAAE,SAAS,QAAQ,GAAG,CAAC,EAAE,SAAS,SAAS,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI;oBAChF;oBACA;oBACA,cAAc,SAAS;oBACvB,eAAe,SAAS;gBAC1B;gBACA,OAAO;uBAAI;oBAAc;iBAAQ;YACnC;QACF;IACF;IAEA,sBAAsB;IACtB,MAAM,iBAAiB,CAAC;QACtB,SAAS,CAAA,eAAgB,aAAa,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;IACnE;IAEA,kBAAkB;IAClB,MAAM,iBAAiB,CAAC,QAAgB;QACtC,IAAI,YAAY,GAAG;YACjB,eAAe;YACf;QACF;QAEA,SAAS,CAAA,eACP,aAAa,GAAG,CAAC,CAAA,OACf,KAAK,EAAE,KAAK,SAAS;oBAAE,GAAG,IAAI;oBAAE;gBAAS,IAAI;IAGnD;IAEA,YAAY;IACZ,MAAM,YAAY;QAChB,SAAS,EAAE;IACb;IAEA,+BAA+B;IAC/B,MAAM,WAAW,CAAC;QAChB,OAAO,MAAM,IAAI,CAAC,CAAA,OAAQ,KAAK,OAAO,CAAC,EAAE,KAAK;IAChD;IAEA,2BAA2B;IAC3B,MAAM,cAAc,CAAC;QACnB,OAAO,MAAM,IAAI,CAAC,CAAA,OAAQ,KAAK,OAAO,CAAC,EAAE,KAAK;IAChD;IAEA,MAAM,QAAyB;QAC7B;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,8OAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT"}}, {"offset": {"line": 2440, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}