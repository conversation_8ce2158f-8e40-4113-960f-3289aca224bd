module.exports = {

"[externals]/next/dist/compiled/next-server/app-page.runtime.dev.js [external] (next/dist/compiled/next-server/app-page.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
const mod = __turbopack_external_require__("next/dist/compiled/next-server/app-page.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page.runtime.dev.js"));

module.exports = mod;
}}),
"[project]/src/lib/data.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "categories": (()=>categories),
    "sampleProducts": (()=>sampleProducts),
    "sampleUsers": (()=>sampleUsers)
});
const sampleProducts = [
    {
        id: '1',
        name: 'هاتف ذكي متطور',
        description: 'هاتف ذكي بمواصفات عالية وكاميرا متقدمة',
        price: 2500,
        image: '/images/phone.jpg',
        category: 'إلكترونيات',
        stock: 15,
        featured: true,
        createdAt: new Date(),
        updatedAt: new Date()
    },
    {
        id: '2',
        name: 'لابتوب للألعاب',
        description: 'لابتوب قوي مخصص للألعاب والتصميم',
        price: 4500,
        image: '/images/laptop.jpg',
        category: 'إلكترونيات',
        stock: 8,
        featured: true,
        createdAt: new Date(),
        updatedAt: new Date()
    },
    {
        id: '3',
        name: 'ساعة ذكية',
        description: 'ساعة ذكية لتتبع اللياقة البدنية',
        price: 800,
        image: '/images/watch.jpg',
        category: 'إكسسوارات',
        stock: 25,
        featured: false,
        createdAt: new Date(),
        updatedAt: new Date()
    },
    {
        id: '4',
        name: 'سماعات لاسلكية',
        description: 'سماعات بلوتوث عالية الجودة',
        price: 350,
        image: '/images/headphones.jpg',
        category: 'إكسسوارات',
        stock: 30,
        featured: true,
        createdAt: new Date(),
        updatedAt: new Date()
    },
    {
        id: '5',
        name: 'كاميرا رقمية',
        description: 'كاميرا احترافية للتصوير الفوتوغرافي',
        price: 3200,
        image: '/images/camera.jpg',
        category: 'إلكترونيات',
        stock: 12,
        featured: false,
        createdAt: new Date(),
        updatedAt: new Date()
    },
    {
        id: '6',
        name: 'تابلت للرسم',
        description: 'تابلت مخصص للرسم والتصميم الرقمي',
        price: 1800,
        image: '/images/tablet.jpg',
        category: 'إلكترونيات',
        stock: 18,
        featured: true,
        createdAt: new Date(),
        updatedAt: new Date()
    }
];
const sampleUsers = [
    {
        id: '1',
        name: 'أحمد محمد',
        email: '<EMAIL>',
        role: 'admin',
        createdAt: new Date(),
        updatedAt: new Date()
    },
    {
        id: '2',
        name: 'فاطمة علي',
        email: '<EMAIL>',
        role: 'customer',
        createdAt: new Date(),
        updatedAt: new Date()
    }
];
const categories = [
    'إلكترونيات',
    'إكسسوارات',
    'ملابس',
    'كتب',
    'رياضة',
    'منزل وحديقة'
];
}}),
"[project]/src/contexts/ProductContext.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "ProductProvider": (()=>ProductProvider),
    "useFilteredProducts": (()=>useFilteredProducts),
    "useProducts": (()=>useProducts)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/lib/data.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
const ProductContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createContext"])(undefined);
function ProductProvider({ children }) {
    const [products, setProducts] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([]);
    const [isLoading, setIsLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(true);
    // Initialize products from localStorage or use sample data
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const initializeProducts = ()=>{
            try {
                const savedProducts = localStorage.getItem('ecommerce-products');
                if (savedProducts) {
                    const parsedProducts = JSON.parse(savedProducts);
                    // Convert date strings back to Date objects
                    const productsWithDates = parsedProducts.map((product)=>({
                            ...product,
                            createdAt: new Date(product.createdAt),
                            updatedAt: new Date(product.updatedAt)
                        }));
                    setProducts(productsWithDates);
                } else {
                    // First time - use sample data
                    setProducts(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["sampleProducts"]);
                    localStorage.setItem('ecommerce-products', JSON.stringify(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["sampleProducts"]));
                }
            } catch (error) {
                console.error('Error loading products from localStorage:', error);
                setProducts(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["sampleProducts"]);
            } finally{
                setIsLoading(false);
            }
        };
        initializeProducts();
    }, []);
    // Save to localStorage whenever products change
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (!isLoading && products.length > 0) {
            try {
                localStorage.setItem('ecommerce-products', JSON.stringify(products));
            } catch (error) {
                console.error('Error saving products to localStorage:', error);
            }
        }
    }, [
        products,
        isLoading
    ]);
    const addProduct = (productData)=>{
        const newProduct = {
            ...productData,
            id: Date.now().toString(),
            createdAt: new Date(),
            updatedAt: new Date()
        };
        setProducts((prev)=>[
                ...prev,
                newProduct
            ]);
    };
    const updateProduct = (id, productData)=>{
        setProducts((prev)=>prev.map((product)=>product.id === id ? {
                    ...product,
                    ...productData,
                    updatedAt: new Date()
                } : product));
    };
    const deleteProduct = (id)=>{
        setProducts((prev)=>prev.filter((product)=>product.id !== id));
    };
    const getProductById = (id)=>{
        return products.find((product)=>product.id === id);
    };
    const getFeaturedProducts = ()=>{
        return products.filter((product)=>product.featured);
    };
    const value = {
        products,
        addProduct,
        updateProduct,
        deleteProduct,
        getProductById,
        getFeaturedProducts,
        isLoading
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(ProductContext.Provider, {
        value: value,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/contexts/ProductContext.tsx",
        lineNumber: 109,
        columnNumber: 5
    }, this);
}
function useProducts() {
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(ProductContext);
    if (context === undefined) {
        throw new Error('useProducts must be used within a ProductProvider');
    }
    return context;
}
function useFilteredProducts(filters) {
    const { products } = useProducts();
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useMemo(()=>{
        if (!filters) return products;
        return products.filter((product)=>{
            const matchesCategory = !filters.category || product.category === filters.category;
            const matchesSearch = !filters.searchTerm || product.name.toLowerCase().includes(filters.searchTerm.toLowerCase()) || product.description.toLowerCase().includes(filters.searchTerm.toLowerCase());
            let matchesPrice = true;
            if (filters.priceRange) {
                switch(filters.priceRange){
                    case 'under-500':
                        matchesPrice = product.price < 500;
                        break;
                    case '500-1000':
                        matchesPrice = product.price >= 500 && product.price <= 1000;
                        break;
                    case '1000-3000':
                        matchesPrice = product.price >= 1000 && product.price <= 3000;
                        break;
                    case 'over-3000':
                        matchesPrice = product.price > 3000;
                        break;
                }
            }
            const matchesFeatured = filters.featured === undefined || product.featured === filters.featured;
            return matchesCategory && matchesSearch && matchesPrice && matchesFeatured;
        });
    }, [
        products,
        filters
    ]);
}
}}),
"[project]/src/contexts/CategoryContext.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "CategoryProvider": (()=>CategoryProvider),
    "useCategories": (()=>useCategories)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
'use client';
;
;
const CategoryContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createContext"])(undefined);
// Default categories
const defaultCategories = [
    {
        id: '1',
        name: 'إلكترونيات',
        description: 'أجهزة إلكترونية وتقنية حديثة',
        isActive: true,
        productCount: 0,
        createdAt: new Date(),
        updatedAt: new Date()
    },
    {
        id: '2',
        name: 'إكسسوارات',
        description: 'إكسسوارات متنوعة وعملية',
        isActive: true,
        productCount: 0,
        createdAt: new Date(),
        updatedAt: new Date()
    },
    {
        id: '3',
        name: 'ملابس',
        description: 'ملابس عصرية للرجال والنساء',
        isActive: true,
        productCount: 0,
        createdAt: new Date(),
        updatedAt: new Date()
    },
    {
        id: '4',
        name: 'كتب',
        description: 'كتب ومراجع في مختلف المجالات',
        isActive: true,
        productCount: 0,
        createdAt: new Date(),
        updatedAt: new Date()
    },
    {
        id: '5',
        name: 'رياضة',
        description: 'معدات وأدوات رياضية',
        isActive: true,
        productCount: 0,
        createdAt: new Date(),
        updatedAt: new Date()
    },
    {
        id: '6',
        name: 'منزل وحديقة',
        description: 'أدوات ومستلزمات المنزل والحديقة',
        isActive: true,
        productCount: 0,
        createdAt: new Date(),
        updatedAt: new Date()
    }
];
function CategoryProvider({ children }) {
    const [categories, setCategories] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([]);
    const [isLoading, setIsLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(true);
    // Initialize categories from localStorage or use default data
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const initializeCategories = ()=>{
            try {
                const savedCategories = localStorage.getItem('ecommerce-categories');
                if (savedCategories) {
                    const parsedCategories = JSON.parse(savedCategories);
                    // Convert date strings back to Date objects
                    const categoriesWithDates = parsedCategories.map((category)=>({
                            ...category,
                            createdAt: new Date(category.createdAt),
                            updatedAt: new Date(category.updatedAt)
                        }));
                    setCategories(categoriesWithDates);
                } else {
                    // First time - use default data
                    setCategories(defaultCategories);
                    localStorage.setItem('ecommerce-categories', JSON.stringify(defaultCategories));
                }
            } catch (error) {
                console.error('Error loading categories from localStorage:', error);
                setCategories(defaultCategories);
            } finally{
                setIsLoading(false);
            }
        };
        initializeCategories();
    }, []);
    // Save to localStorage whenever categories change
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (!isLoading && categories.length > 0) {
            try {
                localStorage.setItem('ecommerce-categories', JSON.stringify(categories));
            } catch (error) {
                console.error('Error saving categories to localStorage:', error);
            }
        }
    }, [
        categories,
        isLoading
    ]);
    const addCategory = (categoryData)=>{
        const newCategory = {
            ...categoryData,
            id: Date.now().toString(),
            productCount: 0,
            createdAt: new Date(),
            updatedAt: new Date()
        };
        setCategories((prev)=>[
                ...prev,
                newCategory
            ]);
    };
    const updateCategory = (id, categoryData)=>{
        setCategories((prev)=>prev.map((category)=>category.id === id ? {
                    ...category,
                    ...categoryData,
                    updatedAt: new Date()
                } : category));
    };
    const deleteCategory = (id)=>{
        setCategories((prev)=>prev.filter((category)=>category.id !== id));
    };
    const getCategoryById = (id)=>{
        return categories.find((category)=>category.id === id);
    };
    const getActiveCategories = ()=>{
        return categories.filter((category)=>category.isActive);
    };
    const updateProductCount = (categoryName, count)=>{
        setCategories((prev)=>prev.map((category)=>category.name === categoryName ? {
                    ...category,
                    productCount: count
                } : category));
    };
    const value = {
        categories,
        addCategory,
        updateCategory,
        deleteCategory,
        getCategoryById,
        getActiveCategories,
        updateProductCount,
        isLoading
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(CategoryContext.Provider, {
        value: value,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/contexts/CategoryContext.tsx",
        lineNumber: 186,
        columnNumber: 5
    }, this);
}
function useCategories() {
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(CategoryContext);
    if (context === undefined) {
        throw new Error('useCategories must be used within a CategoryProvider');
    }
    return context;
}
}}),
"[project]/src/contexts/OrderContext.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "OrderProvider": (()=>OrderProvider),
    "useOrders": (()=>useOrders)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
'use client';
;
;
const OrderContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createContext"])(undefined);
// Sample orders data
const sampleOrders = [
    {
        id: '1',
        orderNumber: 'ORD-2024-001',
        customerId: '1',
        customerName: 'أحمد محمد',
        customerEmail: '<EMAIL>',
        items: [
            {
                id: '1',
                productId: '1',
                productName: 'لابتوب Dell XPS 13',
                productImage: '/images/laptop.jpg',
                price: 2500,
                quantity: 1,
                total: 2500
            },
            {
                id: '2',
                productId: '2',
                productName: 'سماعات لاسلكية',
                productImage: '/images/headphones.jpg',
                price: 150,
                quantity: 2,
                total: 300
            }
        ],
        subtotal: 2800,
        shipping: 50,
        tax: 420,
        total: 3270,
        status: 'confirmed',
        paymentMethod: 'card',
        paymentStatus: 'paid',
        shippingAddress: {
            fullName: 'أحمد محمد علي',
            phone: '+************',
            address: 'شارع الملك فهد، حي النخيل',
            city: 'الرياض',
            postalCode: '12345',
            country: 'السعودية'
        },
        notes: 'يرجى التسليم في المساء',
        createdAt: new Date('2024-01-15T10:30:00'),
        updatedAt: new Date('2024-01-15T11:00:00')
    },
    {
        id: '2',
        orderNumber: 'ORD-2024-002',
        customerId: '2',
        customerName: 'فاطمة أحمد',
        customerEmail: '<EMAIL>',
        items: [
            {
                id: '3',
                productId: '3',
                productName: 'هاتف ذكي Samsung Galaxy',
                productImage: '/images/phone.jpg',
                price: 1200,
                quantity: 1,
                total: 1200
            }
        ],
        subtotal: 1200,
        shipping: 30,
        tax: 180,
        total: 1410,
        status: 'processing',
        paymentMethod: 'cash',
        paymentStatus: 'pending',
        shippingAddress: {
            fullName: 'فاطمة أحمد محمد',
            phone: '+966507654321',
            address: 'طريق الأمير محمد بن عبدالعزيز',
            city: 'جدة',
            postalCode: '21589',
            country: 'السعودية'
        },
        createdAt: new Date('2024-01-16T14:20:00'),
        updatedAt: new Date('2024-01-16T15:45:00')
    },
    {
        id: '3',
        orderNumber: 'ORD-2024-003',
        customerId: '1',
        customerName: 'أحمد محمد',
        customerEmail: '<EMAIL>',
        items: [
            {
                id: '4',
                productId: '4',
                productName: 'تابلت iPad Pro',
                productImage: '/images/tablet.jpg',
                price: 1800,
                quantity: 1,
                total: 1800
            }
        ],
        subtotal: 1800,
        shipping: 40,
        tax: 270,
        total: 2110,
        status: 'delivered',
        paymentMethod: 'bank_transfer',
        paymentStatus: 'paid',
        shippingAddress: {
            fullName: 'أحمد محمد علي',
            phone: '+************',
            address: 'شارع الملك فهد، حي النخيل',
            city: 'الرياض',
            postalCode: '12345',
            country: 'السعودية'
        },
        createdAt: new Date('2024-01-10T09:15:00'),
        updatedAt: new Date('2024-01-12T16:30:00'),
        deliveredAt: new Date('2024-01-12T16:30:00')
    }
];
function OrderProvider({ children }) {
    const [orders, setOrders] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([]);
    const [isLoading, setIsLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(true);
    // Initialize orders from localStorage or use sample data
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const initializeOrders = ()=>{
            try {
                const savedOrders = localStorage.getItem('ecommerce-orders');
                if (savedOrders) {
                    const parsedOrders = JSON.parse(savedOrders);
                    // Convert date strings back to Date objects
                    const ordersWithDates = parsedOrders.map((order)=>({
                            ...order,
                            createdAt: new Date(order.createdAt),
                            updatedAt: new Date(order.updatedAt),
                            deliveredAt: order.deliveredAt ? new Date(order.deliveredAt) : undefined
                        }));
                    setOrders(ordersWithDates);
                } else {
                    // First time - use sample data
                    setOrders(sampleOrders);
                    localStorage.setItem('ecommerce-orders', JSON.stringify(sampleOrders));
                }
            } catch (error) {
                console.error('Error loading orders from localStorage:', error);
                setOrders(sampleOrders);
            } finally{
                setIsLoading(false);
            }
        };
        initializeOrders();
    }, []);
    // Save to localStorage whenever orders change
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (!isLoading && orders.length > 0) {
            try {
                localStorage.setItem('ecommerce-orders', JSON.stringify(orders));
            } catch (error) {
                console.error('Error saving orders to localStorage:', error);
            }
        }
    }, [
        orders,
        isLoading
    ]);
    const generateOrderNumber = ()=>{
        const year = new Date().getFullYear();
        const orderCount = orders.length + 1;
        return `ORD-${year}-${orderCount.toString().padStart(3, '0')}`;
    };
    const addOrder = (orderData)=>{
        const newOrder = {
            ...orderData,
            id: Date.now().toString(),
            orderNumber: generateOrderNumber(),
            createdAt: new Date(),
            updatedAt: new Date()
        };
        setOrders((prev)=>[
                newOrder,
                ...prev
            ]);
    };
    const updateOrderStatus = (id, status)=>{
        setOrders((prev)=>prev.map((order)=>{
                if (order.id === id) {
                    const updatedOrder = {
                        ...order,
                        status,
                        updatedAt: new Date()
                    };
                    // Set deliveredAt when status changes to delivered
                    if (status === 'delivered' && !order.deliveredAt) {
                        updatedOrder.deliveredAt = new Date();
                    }
                    return updatedOrder;
                }
                return order;
            }));
    };
    const updateOrder = (id, updates)=>{
        setOrders((prev)=>prev.map((order)=>order.id === id ? {
                    ...order,
                    ...updates,
                    updatedAt: new Date()
                } : order));
    };
    const deleteOrder = (id)=>{
        setOrders((prev)=>prev.filter((order)=>order.id !== id));
    };
    const getOrderById = (id)=>{
        return orders.find((order)=>order.id === id);
    };
    const getOrdersByStatus = (status)=>{
        return orders.filter((order)=>order.status === status);
    };
    const getOrdersByCustomer = (customerId)=>{
        return orders.filter((order)=>order.customerId === customerId);
    };
    const getTotalRevenue = ()=>{
        return orders.filter((order)=>order.paymentStatus === 'paid').reduce((total, order)=>total + order.total, 0);
    };
    const getOrdersCount = ()=>{
        return orders.length;
    };
    const getRecentOrders = (limit = 5)=>{
        return orders.sort((a, b)=>b.createdAt.getTime() - a.createdAt.getTime()).slice(0, limit);
    };
    const value = {
        orders,
        addOrder,
        updateOrderStatus,
        updateOrder,
        deleteOrder,
        getOrderById,
        getOrdersByStatus,
        getOrdersByCustomer,
        getTotalRevenue,
        getOrdersCount,
        getRecentOrders,
        isLoading
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(OrderContext.Provider, {
        value: value,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/contexts/OrderContext.tsx",
        lineNumber: 286,
        columnNumber: 5
    }, this);
}
function useOrders() {
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(OrderContext);
    if (context === undefined) {
        throw new Error('useOrders must be used within an OrderProvider');
    }
    return context;
}
}}),
"[project]/src/contexts/UserContext.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "UserProvider": (()=>UserProvider),
    "useUsers": (()=>useUsers)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
'use client';
;
;
const UserContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createContext"])(undefined);
// Sample users data
const sampleUsers = [
    {
        id: '1',
        name: 'أحمد محمد علي',
        email: '<EMAIL>',
        phone: '+************',
        role: 'customer',
        isActive: true,
        lastLogin: new Date('2024-01-16T10:30:00'),
        totalOrders: 3,
        totalSpent: 5780,
        address: {
            street: 'شارع الملك فهد، حي النخيل',
            city: 'الرياض',
            postalCode: '12345',
            country: 'السعودية'
        },
        createdAt: new Date('2024-01-01T08:00:00'),
        updatedAt: new Date('2024-01-16T10:30:00')
    },
    {
        id: '2',
        name: 'فاطمة أحمد محمد',
        email: '<EMAIL>',
        phone: '+966507654321',
        role: 'customer',
        isActive: true,
        lastLogin: new Date('2024-01-15T14:20:00'),
        totalOrders: 1,
        totalSpent: 1410,
        address: {
            street: 'طريق الأمير محمد بن عبدالعزيز',
            city: 'جدة',
            postalCode: '21589',
            country: 'السعودية'
        },
        createdAt: new Date('2024-01-05T12:00:00'),
        updatedAt: new Date('2024-01-15T14:20:00')
    },
    {
        id: '3',
        name: 'محمد عبدالله',
        email: '<EMAIL>',
        phone: '+966509876543',
        role: 'customer',
        isActive: false,
        lastLogin: new Date('2024-01-10T09:15:00'),
        totalOrders: 0,
        totalSpent: 0,
        address: {
            street: 'شارع العليا',
            city: 'الرياض',
            postalCode: '11564',
            country: 'السعودية'
        },
        createdAt: new Date('2024-01-08T16:30:00'),
        updatedAt: new Date('2024-01-12T11:00:00')
    },
    {
        id: '4',
        name: 'سارة خالد',
        email: '<EMAIL>',
        phone: '+966502468135',
        role: 'customer',
        isActive: true,
        lastLogin: new Date('2024-01-14T11:45:00'),
        totalOrders: 2,
        totalSpent: 3200,
        address: {
            street: 'حي الملقا',
            city: 'الرياض',
            postalCode: '13524',
            country: 'السعودية'
        },
        createdAt: new Date('2024-01-03T09:20:00'),
        updatedAt: new Date('2024-01-14T11:45:00')
    },
    {
        id: '5',
        name: 'عبدالرحمن أحمد',
        email: '<EMAIL>',
        phone: '+966501111111',
        role: 'admin',
        isActive: true,
        lastLogin: new Date('2024-01-16T16:00:00'),
        totalOrders: 0,
        totalSpent: 0,
        createdAt: new Date('2023-12-01T10:00:00'),
        updatedAt: new Date('2024-01-16T16:00:00')
    }
];
function UserProvider({ children }) {
    const [users, setUsers] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([]);
    const [isLoading, setIsLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(true);
    // Initialize users from localStorage or use sample data
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const initializeUsers = ()=>{
            try {
                const savedUsers = localStorage.getItem('ecommerce-users');
                if (savedUsers) {
                    const parsedUsers = JSON.parse(savedUsers);
                    // Convert date strings back to Date objects
                    const usersWithDates = parsedUsers.map((user)=>({
                            ...user,
                            lastLogin: user.lastLogin ? new Date(user.lastLogin) : undefined,
                            createdAt: new Date(user.createdAt),
                            updatedAt: new Date(user.updatedAt)
                        }));
                    setUsers(usersWithDates);
                } else {
                    // First time - use sample data
                    setUsers(sampleUsers);
                    localStorage.setItem('ecommerce-users', JSON.stringify(sampleUsers));
                }
            } catch (error) {
                console.error('Error loading users from localStorage:', error);
                setUsers(sampleUsers);
            } finally{
                setIsLoading(false);
            }
        };
        initializeUsers();
    }, []);
    // Save to localStorage whenever users change
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (!isLoading && users.length > 0) {
            try {
                localStorage.setItem('ecommerce-users', JSON.stringify(users));
            } catch (error) {
                console.error('Error saving users to localStorage:', error);
            }
        }
    }, [
        users,
        isLoading
    ]);
    const addUser = (userData)=>{
        const newUser = {
            ...userData,
            id: Date.now().toString(),
            totalOrders: 0,
            totalSpent: 0,
            createdAt: new Date(),
            updatedAt: new Date()
        };
        setUsers((prev)=>[
                newUser,
                ...prev
            ]);
    };
    const updateUser = (id, userData)=>{
        setUsers((prev)=>prev.map((user)=>user.id === id ? {
                    ...user,
                    ...userData,
                    updatedAt: new Date()
                } : user));
    };
    const deleteUser = (id)=>{
        setUsers((prev)=>prev.filter((user)=>user.id !== id));
    };
    const toggleUserStatus = (id)=>{
        setUsers((prev)=>prev.map((user)=>user.id === id ? {
                    ...user,
                    isActive: !user.isActive,
                    updatedAt: new Date()
                } : user));
    };
    const getUserById = (id)=>{
        return users.find((user)=>user.id === id);
    };
    const getActiveUsers = ()=>{
        return users.filter((user)=>user.isActive);
    };
    const getUsersByRole = (role)=>{
        return users.filter((user)=>user.role === role);
    };
    const updateUserStats = (userId, orderCount, totalSpent)=>{
        setUsers((prev)=>prev.map((user)=>user.id === userId ? {
                    ...user,
                    totalOrders: orderCount,
                    totalSpent: totalSpent,
                    lastLogin: new Date(),
                    updatedAt: new Date()
                } : user));
    };
    const getTotalUsers = ()=>{
        return users.length;
    };
    const getActiveUsersCount = ()=>{
        return users.filter((user)=>user.isActive).length;
    };
    const getRecentUsers = (limit = 5)=>{
        return users.sort((a, b)=>b.createdAt.getTime() - a.createdAt.getTime()).slice(0, limit);
    };
    const value = {
        users,
        addUser,
        updateUser,
        deleteUser,
        toggleUserStatus,
        getUserById,
        getActiveUsers,
        getUsersByRole,
        updateUserStats,
        getTotalUsers,
        getActiveUsersCount,
        getRecentUsers,
        isLoading
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(UserContext.Provider, {
        value: value,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/contexts/UserContext.tsx",
        lineNumber: 256,
        columnNumber: 5
    }, this);
}
function useUsers() {
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(UserContext);
    if (context === undefined) {
        throw new Error('useUsers must be used within a UserProvider');
    }
    return context;
}
}}),
"[project]/src/contexts/ReportsContext.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "ReportsProvider": (()=>ReportsProvider),
    "useReports": (()=>useReports)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$OrderContext$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/contexts/OrderContext.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$ProductContext$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/contexts/ProductContext.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$UserContext$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/contexts/UserContext.tsx [app-ssr] (ecmascript)");
'use client';
;
;
;
;
;
const ReportsContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createContext"])(undefined);
function ReportsProvider({ children }) {
    const { orders } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$OrderContext$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useOrders"])();
    const { products } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$ProductContext$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useProducts"])();
    const { users } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$UserContext$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useUsers"])();
    const [isLoading, setIsLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    // Helper function to get date range
    const getDateRange = (startDate, endDate)=>{
        const end = endDate || new Date();
        const start = startDate || new Date(end.getTime() - 30 * 24 * 60 * 60 * 1000); // 30 days ago
        return {
            start,
            end
        };
    };
    // Helper function to filter orders by date
    const filterOrdersByDate = (startDate, endDate)=>{
        const { start, end } = getDateRange(startDate, endDate);
        return orders.filter((order)=>{
            const orderDate = new Date(order.createdAt);
            return orderDate >= start && orderDate <= end && order.status !== 'cancelled';
        });
    };
    // Daily Sales Report
    const getDailySales = (days = 30)=>{
        const salesMap = new Map();
        // Initialize last N days
        for(let i = 0; i < days; i++){
            const date = new Date();
            date.setDate(date.getDate() - i);
            const dateStr = date.toISOString().split('T')[0];
            salesMap.set(dateStr, {
                sales: 0,
                orders: 0,
                profit: 0
            });
        }
        // Aggregate sales data
        orders.forEach((order)=>{
            if (order.status === 'cancelled') return;
            const dateStr = new Date(order.createdAt).toISOString().split('T')[0];
            if (salesMap.has(dateStr)) {
                const current = salesMap.get(dateStr);
                const profit = order.total * 0.3; // Assume 30% profit margin
                salesMap.set(dateStr, {
                    sales: current.sales + order.total,
                    orders: current.orders + 1,
                    profit: current.profit + profit
                });
            }
        });
        return Array.from(salesMap.entries()).map(([date, data])=>({
                date,
                sales: data.sales,
                orders: data.orders,
                profit: data.profit
            })).sort((a, b)=>a.date.localeCompare(b.date));
    };
    // Weekly Sales Report
    const getWeeklySales = (weeks = 12)=>{
        const salesMap = new Map();
        // Initialize last N weeks
        for(let i = 0; i < weeks; i++){
            const date = new Date();
            date.setDate(date.getDate() - i * 7);
            const weekStart = new Date(date.setDate(date.getDate() - date.getDay()));
            const weekStr = weekStart.toISOString().split('T')[0];
            salesMap.set(weekStr, {
                sales: 0,
                orders: 0,
                profit: 0
            });
        }
        // Aggregate weekly data
        orders.forEach((order)=>{
            if (order.status === 'cancelled') return;
            const orderDate = new Date(order.createdAt);
            const weekStart = new Date(orderDate.setDate(orderDate.getDate() - orderDate.getDay()));
            const weekStr = weekStart.toISOString().split('T')[0];
            if (salesMap.has(weekStr)) {
                const current = salesMap.get(weekStr);
                const profit = order.total * 0.3;
                salesMap.set(weekStr, {
                    sales: current.sales + order.total,
                    orders: current.orders + 1,
                    profit: current.profit + profit
                });
            }
        });
        return Array.from(salesMap.entries()).map(([date, data])=>({
                date,
                sales: data.sales,
                orders: data.orders,
                profit: data.profit
            })).sort((a, b)=>a.date.localeCompare(b.date));
    };
    // Monthly Sales Report
    const getMonthlySales = (months = 12)=>{
        const salesMap = new Map();
        // Initialize last N months
        for(let i = 0; i < months; i++){
            const date = new Date();
            date.setMonth(date.getMonth() - i);
            const monthStr = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
            salesMap.set(monthStr, {
                sales: 0,
                orders: 0,
                profit: 0
            });
        }
        // Aggregate monthly data
        orders.forEach((order)=>{
            if (order.status === 'cancelled') return;
            const orderDate = new Date(order.createdAt);
            const monthStr = `${orderDate.getFullYear()}-${String(orderDate.getMonth() + 1).padStart(2, '0')}`;
            if (salesMap.has(monthStr)) {
                const current = salesMap.get(monthStr);
                const profit = order.total * 0.3;
                salesMap.set(monthStr, {
                    sales: current.sales + order.total,
                    orders: current.orders + 1,
                    profit: current.profit + profit
                });
            }
        });
        return Array.from(salesMap.entries()).map(([date, data])=>({
                date,
                sales: data.sales,
                orders: data.orders,
                profit: data.profit
            })).sort((a, b)=>a.date.localeCompare(b.date));
    };
    // Top Selling Products
    const getTopSellingProducts = (limit = 10)=>{
        const productSales = new Map();
        orders.forEach((order)=>{
            if (order.status === 'cancelled') return;
            order.items.forEach((item)=>{
                const current = productSales.get(item.id) || {
                    quantity: 0,
                    revenue: 0,
                    profit: 0
                };
                const itemRevenue = item.price * item.quantity;
                const itemProfit = itemRevenue * 0.3; // 30% profit margin
                productSales.set(item.id, {
                    quantity: current.quantity + item.quantity,
                    revenue: current.revenue + itemRevenue,
                    profit: current.profit + itemProfit
                });
            });
        });
        return Array.from(productSales.entries()).map(([productId, data])=>{
            const product = products.find((p)=>p.id === productId);
            return {
                productId,
                productName: product?.name || 'منتج محذوف',
                quantitySold: data.quantity,
                totalRevenue: data.revenue,
                totalProfit: data.profit,
                averagePrice: data.revenue / data.quantity
            };
        }).sort((a, b)=>b.quantitySold - a.quantitySold).slice(0, limit);
    };
    // Most Profitable Products
    const getMostProfitableProducts = (limit = 10)=>{
        return getTopSellingProducts(products.length).sort((a, b)=>b.totalProfit - a.totalProfit).slice(0, limit);
    };
    // Product Performance
    const getProductPerformance = (productId)=>{
        const allProducts = getTopSellingProducts(products.length);
        return allProducts.find((p)=>p.productId === productId) || null;
    };
    // Top Customers
    const getTopCustomers = (limit = 10)=>{
        const customerData = new Map();
        orders.forEach((order)=>{
            if (order.status === 'cancelled') return;
            const current = customerData.get(order.userId) || {
                orders: 0,
                spent: 0,
                lastOrder: new Date(0)
            };
            customerData.set(order.userId, {
                orders: current.orders + 1,
                spent: current.spent + order.total,
                lastOrder: new Date(Math.max(current.lastOrder.getTime(), new Date(order.createdAt).getTime()))
            });
        });
        return Array.from(customerData.entries()).map(([userId, data])=>{
            const user = users.find((u)=>u.id === userId);
            return {
                userId,
                userName: user?.name || 'مستخدم محذوف',
                totalOrders: data.orders,
                totalSpent: data.spent,
                averageOrderValue: data.spent / data.orders,
                lastOrderDate: data.lastOrder
            };
        }).sort((a, b)=>b.totalSpent - a.totalSpent).slice(0, limit);
    };
    // Customer Analysis
    const getCustomerAnalysis = (userId)=>{
        const allCustomers = getTopCustomers(users.length);
        return allCustomers.find((c)=>c.userId === userId) || null;
    };
    // Financial Reports
    const getTotalRevenue = (startDate, endDate)=>{
        const filteredOrders = filterOrdersByDate(startDate, endDate);
        return filteredOrders.reduce((total, order)=>total + order.total, 0);
    };
    const getTotalProfit = (startDate, endDate)=>{
        const revenue = getTotalRevenue(startDate, endDate);
        return revenue * 0.3; // 30% profit margin
    };
    const getAverageOrderValue = (startDate, endDate)=>{
        const filteredOrders = filterOrdersByDate(startDate, endDate);
        if (filteredOrders.length === 0) return 0;
        return getTotalRevenue(startDate, endDate) / filteredOrders.length;
    };
    // Growth Analytics
    const getRevenueGrowth = (period)=>{
        const salesData = period === 'daily' ? getDailySales(60) : period === 'weekly' ? getWeeklySales(24) : getMonthlySales(24);
        if (salesData.length < 2) return 0;
        const midPoint = Math.floor(salesData.length / 2);
        const firstHalf = salesData.slice(0, midPoint);
        const secondHalf = salesData.slice(midPoint);
        const firstHalfRevenue = firstHalf.reduce((sum, data)=>sum + data.sales, 0);
        const secondHalfRevenue = secondHalf.reduce((sum, data)=>sum + data.sales, 0);
        if (firstHalfRevenue === 0) return 0;
        return (secondHalfRevenue - firstHalfRevenue) / firstHalfRevenue * 100;
    };
    const getOrderGrowth = (period)=>{
        const salesData = period === 'daily' ? getDailySales(60) : period === 'weekly' ? getWeeklySales(24) : getMonthlySales(24);
        if (salesData.length < 2) return 0;
        const midPoint = Math.floor(salesData.length / 2);
        const firstHalf = salesData.slice(0, midPoint);
        const secondHalf = salesData.slice(midPoint);
        const firstHalfOrders = firstHalf.reduce((sum, data)=>sum + data.orders, 0);
        const secondHalfOrders = secondHalf.reduce((sum, data)=>sum + data.orders, 0);
        if (firstHalfOrders === 0) return 0;
        return (secondHalfOrders - firstHalfOrders) / firstHalfOrders * 100;
    };
    // Summary Statistics
    const getSummaryStats = ()=>{
        const totalRevenue = getTotalRevenue();
        const totalOrders = orders.filter((o)=>o.status !== 'cancelled').length;
        const totalProfit = getTotalProfit();
        const averageOrderValue = getAverageOrderValue();
        const topProducts = getTopSellingProducts(1);
        const topCustomers = getTopCustomers(1);
        return {
            totalRevenue,
            totalOrders,
            totalProfit,
            averageOrderValue,
            topProduct: topProducts[0]?.productName || 'لا يوجد',
            topCustomer: topCustomers[0]?.userName || 'لا يوجد',
            revenueGrowth: getRevenueGrowth('monthly'),
            orderGrowth: getOrderGrowth('monthly')
        };
    };
    const value = {
        getDailySales,
        getWeeklySales,
        getMonthlySales,
        getTopSellingProducts,
        getMostProfitableProducts,
        getProductPerformance,
        getTopCustomers,
        getCustomerAnalysis,
        getTotalRevenue,
        getTotalProfit,
        getAverageOrderValue,
        getRevenueGrowth,
        getOrderGrowth,
        getSummaryStats,
        isLoading
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(ReportsContext.Provider, {
        value: value,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/contexts/ReportsContext.tsx",
        lineNumber: 401,
        columnNumber: 5
    }, this);
}
function useReports() {
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(ReportsContext);
    if (context === undefined) {
        throw new Error('useReports must be used within a ReportsProvider');
    }
    return context;
}
}}),
"[externals]/stream [external] (stream, cjs)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
const mod = __turbopack_external_require__("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
const mod = __turbopack_external_require__("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/url [external] (url, cjs)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
const mod = __turbopack_external_require__("url", () => require("url"));

module.exports = mod;
}}),
"[externals]/punycode [external] (punycode, cjs)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
const mod = __turbopack_external_require__("punycode", () => require("punycode"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
const mod = __turbopack_external_require__("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
const mod = __turbopack_external_require__("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[project]/src/lib/supabase.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "formatSupabaseResponse": (()=>formatSupabaseResponse),
    "handleSupabaseError": (()=>handleSupabaseError),
    "supabase": (()=>supabase),
    "supabaseAdmin": (()=>supabaseAdmin)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$supabase$2d$js$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_import__("[project]/node_modules/@supabase/supabase-js/dist/module/index.js [app-ssr] (ecmascript) <locals>");
;
// التحقق من وجود متغيرات البيئة
const supabaseUrl = ("TURBOPACK compile-time value", "https://your-project-id.supabase.co");
const supabaseAnonKey = ("TURBOPACK compile-time value", "your_supabase_anon_key");
// إذا لم تكن متغيرات البيئة موجودة، استخدم قيم افتراضية للتطوير
const defaultUrl = 'https://placeholder.supabase.co';
const defaultKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBsYWNlaG9sZGVyIiwicm9sZSI6ImFub24iLCJpYXQiOjE2NDUxOTI3MjAsImV4cCI6MTk2MDc2ODcyMH0.placeholder';
const supabase = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$supabase$2d$js$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createClient"])(supabaseUrl || defaultUrl, supabaseAnonKey || defaultKey);
const supabaseAdmin = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$supabase$2d$js$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createClient"])(supabaseUrl || defaultUrl, process.env.SUPABASE_SERVICE_ROLE_KEY || defaultKey, {
    auth: {
        autoRefreshToken: false,
        persistSession: false
    }
});
function handleSupabaseError(error) {
    console.error('Supabase error:', error);
    if (error?.message) {
        return error.message;
    }
    return 'حدث خطأ غير متوقع';
}
function formatSupabaseResponse(data, error) {
    if (error) {
        throw new Error(handleSupabaseError(error));
    }
    return data || [];
}
}}),
"[project]/src/services/database.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "categoriesService": (()=>categoriesService),
    "ordersService": (()=>ordersService),
    "productsService": (()=>productsService),
    "usersService": (()=>usersService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/lib/supabase.ts [app-ssr] (ecmascript)");
;
// Helper functions to convert database types to app types
function dbProductToProduct(dbProduct) {
    return {
        id: dbProduct.id,
        name: dbProduct.name,
        description: dbProduct.description,
        price: dbProduct.price,
        image: dbProduct.image,
        category: dbProduct.category_id,
        stock: dbProduct.stock,
        featured: dbProduct.featured,
        createdAt: new Date(dbProduct.created_at),
        updatedAt: new Date(dbProduct.updated_at)
    };
}
function dbCategoryToCategory(dbCategory) {
    return {
        id: dbCategory.id,
        name: dbCategory.name,
        description: dbCategory.description || '',
        isActive: dbCategory.is_active,
        productCount: dbCategory.product_count,
        createdAt: new Date(dbCategory.created_at),
        updatedAt: new Date(dbCategory.updated_at)
    };
}
function dbUserToUser(dbUser) {
    return {
        id: dbUser.id,
        name: dbUser.name,
        email: dbUser.email,
        phone: dbUser.phone || undefined,
        role: dbUser.role,
        isActive: dbUser.is_active,
        lastLogin: dbUser.last_login ? new Date(dbUser.last_login) : undefined,
        totalOrders: dbUser.total_orders,
        totalSpent: dbUser.total_spent,
        address: dbUser.address,
        createdAt: new Date(dbUser.created_at),
        updatedAt: new Date(dbUser.updated_at)
    };
}
function dbOrderToOrder(dbOrder) {
    return {
        id: dbOrder.id,
        orderNumber: dbOrder.order_number,
        customerId: dbOrder.customer_id,
        customerName: dbOrder.customer_name,
        customerEmail: dbOrder.customer_email,
        items: dbOrder.items,
        subtotal: dbOrder.subtotal,
        shipping: dbOrder.shipping,
        tax: dbOrder.tax,
        total: dbOrder.total,
        status: dbOrder.status,
        paymentMethod: dbOrder.payment_method,
        paymentStatus: dbOrder.payment_status,
        shippingAddress: dbOrder.shipping_address,
        notes: dbOrder.notes || undefined,
        createdAt: new Date(dbOrder.created_at),
        updatedAt: new Date(dbOrder.updated_at),
        deliveredAt: dbOrder.delivered_at ? new Date(dbOrder.delivered_at) : undefined
    };
}
const productsService = {
    async getAll () {
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["supabase"].from('products').select('*').order('created_at', {
            ascending: false
        });
        const products = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["formatSupabaseResponse"])(data, error);
        return products.map(dbProductToProduct);
    },
    async getById (id) {
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["supabase"].from('products').select('*').eq('id', id).single();
        if (error || !data) return null;
        return dbProductToProduct(data);
    },
    async getFeatured () {
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["supabase"].from('products').select('*').eq('featured', true).order('created_at', {
            ascending: false
        });
        const products = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["formatSupabaseResponse"])(data, error);
        return products.map(dbProductToProduct);
    },
    async getByCategory (categoryId) {
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["supabase"].from('products').select('*').eq('category_id', categoryId).order('created_at', {
            ascending: false
        });
        const products = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["formatSupabaseResponse"])(data, error);
        return products.map(dbProductToProduct);
    },
    async create (product) {
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["supabase"].from('products').insert({
            name: product.name,
            description: product.description,
            price: product.price,
            image: product.image,
            category_id: product.category,
            stock: product.stock,
            featured: product.featured
        }).select().single();
        if (error || !data) {
            throw new Error(error?.message || 'Failed to create product');
        }
        return dbProductToProduct(data);
    },
    async update (id, updates) {
        const updateData = {};
        if (updates.name) updateData.name = updates.name;
        if (updates.description) updateData.description = updates.description;
        if (updates.price !== undefined) updateData.price = updates.price;
        if (updates.image) updateData.image = updates.image;
        if (updates.category) updateData.category_id = updates.category;
        if (updates.stock !== undefined) updateData.stock = updates.stock;
        if (updates.featured !== undefined) updateData.featured = updates.featured;
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["supabase"].from('products').update(updateData).eq('id', id).select().single();
        if (error || !data) {
            throw new Error(error?.message || 'Failed to update product');
        }
        return dbProductToProduct(data);
    },
    async delete (id) {
        const { error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["supabase"].from('products').delete().eq('id', id);
        if (error) {
            throw new Error(error.message);
        }
    }
};
const categoriesService = {
    async getAll () {
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["supabase"].from('categories').select('*').order('name');
        const categories = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["formatSupabaseResponse"])(data, error);
        return categories.map(dbCategoryToCategory);
    },
    async getById (id) {
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["supabase"].from('categories').select('*').eq('id', id).single();
        if (error || !data) return null;
        return dbCategoryToCategory(data);
    },
    async create (category) {
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["supabase"].from('categories').insert({
            name: category.name,
            description: category.description,
            is_active: category.isActive
        }).select().single();
        if (error || !data) {
            throw new Error(error?.message || 'Failed to create category');
        }
        return dbCategoryToCategory(data);
    },
    async update (id, updates) {
        const updateData = {};
        if (updates.name) updateData.name = updates.name;
        if (updates.description !== undefined) updateData.description = updates.description;
        if (updates.isActive !== undefined) updateData.is_active = updates.isActive;
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["supabase"].from('categories').update(updateData).eq('id', id).select().single();
        if (error || !data) {
            throw new Error(error?.message || 'Failed to update category');
        }
        return dbCategoryToCategory(data);
    },
    async delete (id) {
        const { error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["supabase"].from('categories').delete().eq('id', id);
        if (error) {
            throw new Error(error.message);
        }
    }
};
const ordersService = {
    async getAll () {
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["supabase"].from('orders').select('*').order('created_at', {
            ascending: false
        });
        const orders = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["formatSupabaseResponse"])(data, error);
        return orders.map(dbOrderToOrder);
    },
    async getById (id) {
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["supabase"].from('orders').select('*').eq('id', id).single();
        if (error || !data) return null;
        return dbOrderToOrder(data);
    },
    async getByCustomerId (customerId) {
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["supabase"].from('orders').select('*').eq('customer_id', customerId).order('created_at', {
            ascending: false
        });
        const orders = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["formatSupabaseResponse"])(data, error);
        return orders.map(dbOrderToOrder);
    },
    async create (order) {
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["supabase"].from('orders').insert({
            order_number: order.orderNumber,
            customer_id: order.customerId,
            customer_name: order.customerName,
            customer_email: order.customerEmail,
            items: order.items,
            subtotal: order.subtotal,
            shipping: order.shipping,
            tax: order.tax,
            total: order.total,
            status: order.status,
            payment_method: order.paymentMethod,
            payment_status: order.paymentStatus,
            shipping_address: order.shippingAddress,
            notes: order.notes
        }).select().single();
        if (error || !data) {
            throw new Error(error?.message || 'Failed to create order');
        }
        return dbOrderToOrder(data);
    },
    async updateStatus (id, status) {
        const updateData = {
            status
        };
        if (status === 'delivered') {
            updateData.delivered_at = new Date().toISOString();
        }
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["supabase"].from('orders').update(updateData).eq('id', id).select().single();
        if (error || !data) {
            throw new Error(error?.message || 'Failed to update order status');
        }
        return dbOrderToOrder(data);
    },
    async updatePaymentStatus (id, paymentStatus) {
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["supabase"].from('orders').update({
            payment_status: paymentStatus
        }).eq('id', id).select().single();
        if (error || !data) {
            throw new Error(error?.message || 'Failed to update payment status');
        }
        return dbOrderToOrder(data);
    }
};
const usersService = {
    async getAll () {
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["supabase"].from('users').select('*').order('created_at', {
            ascending: false
        });
        const users = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["formatSupabaseResponse"])(data, error);
        return users.map(dbUserToUser);
    },
    async getById (id) {
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["supabase"].from('users').select('*').eq('id', id).single();
        if (error || !data) return null;
        return dbUserToUser(data);
    },
    async create (user) {
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["supabase"].from('users').insert({
            name: user.name,
            email: user.email,
            phone: user.phone,
            role: user.role,
            is_active: user.isActive,
            address: user.address
        }).select().single();
        if (error || !data) {
            throw new Error(error?.message || 'Failed to create user');
        }
        return dbUserToUser(data);
    },
    async update (id, updates) {
        const updateData = {};
        if (updates.name) updateData.name = updates.name;
        if (updates.email) updateData.email = updates.email;
        if (updates.phone !== undefined) updateData.phone = updates.phone;
        if (updates.role) updateData.role = updates.role;
        if (updates.isActive !== undefined) updateData.is_active = updates.isActive;
        if (updates.address !== undefined) updateData.address = updates.address;
        if (updates.totalOrders !== undefined) updateData.total_orders = updates.totalOrders;
        if (updates.totalSpent !== undefined) updateData.total_spent = updates.totalSpent;
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["supabase"].from('users').update(updateData).eq('id', id).select().single();
        if (error || !data) {
            throw new Error(error?.message || 'Failed to update user');
        }
        return dbUserToUser(data);
    },
    async delete (id) {
        const { error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["supabase"].from('users').delete().eq('id', id);
        if (error) {
            throw new Error(error.message);
        }
    },
    async updateLastLogin (id) {
        const { error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["supabase"].from('users').update({
            last_login: new Date().toISOString()
        }).eq('id', id);
        if (error) {
            throw new Error(error.message);
        }
    },
    async getByEmail (email) {
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["supabase"].from('users').select('*').eq('email', email).single();
        if (error) {
            if (error.code === 'PGRST116') {
                return null; // User not found
            }
            throw new Error(`Failed to get user by email: ${error.message}`);
        }
        return dbUserToUser(data);
    }
};
}}),
"[project]/src/contexts/AuthContext.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "AuthProvider": (()=>AuthProvider),
    "useAuth": (()=>useAuth),
    "useRequireAdmin": (()=>useRequireAdmin),
    "useRequireAuth": (()=>useRequireAuth)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/lib/supabase.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$database$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/services/database.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
;
const AuthContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createContext"])(undefined);
function AuthProvider({ children }) {
    const [user, setUser] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [appUser, setAppUser] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [session, setSession] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [loading, setLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(true);
    // تحميل بيانات المستخدم من قاعدة البيانات
    const loadAppUser = async (userId)=>{
        try {
            const userData = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$database$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["usersService"].getById(userId);
            setAppUser(userData);
            // تحديث آخر تسجيل دخول
            if (userData) {
                await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$database$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["usersService"].updateLastLogin(userId);
            }
        } catch (error) {
            console.error('خطأ في تحميل بيانات المستخدم:', error);
        }
    };
    // مراقبة تغييرات المصادقة
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        // الحصول على الجلسة الحالية
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["supabase"].auth.getSession().then(({ data: { session } })=>{
            setSession(session);
            setUser(session?.user ?? null);
            if (session?.user) {
                loadAppUser(session.user.id);
            }
            setLoading(false);
        });
        // الاستماع لتغييرات المصادقة
        const { data: { subscription } } = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["supabase"].auth.onAuthStateChange(async (event, session)=>{
            setSession(session);
            setUser(session?.user ?? null);
            if (session?.user) {
                await loadAppUser(session.user.id);
            } else {
                setAppUser(null);
            }
            setLoading(false);
        });
        return ()=>subscription.unsubscribe();
    }, []);
    // تسجيل حساب جديد
    const signUp = async (email, password, userData)=>{
        try {
            const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["supabase"].auth.signUp({
                email,
                password,
                options: {
                    data: {
                        name: userData.name,
                        phone: userData.phone
                    }
                }
            });
            if (error) return {
                error
            };
            // إنشاء سجل المستخدم في قاعدة البيانات
            if (data.user) {
                try {
                    await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$database$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["usersService"].create({
                        id: data.user.id,
                        name: userData.name,
                        email: email,
                        phone: userData.phone,
                        role: 'customer',
                        isActive: true
                    });
                } catch (dbError) {
                    console.error('خطأ في إنشاء سجل المستخدم:', dbError);
                }
            }
            return {
                error: null
            };
        } catch (error) {
            return {
                error: error
            };
        }
    };
    // تسجيل الدخول
    const signIn = async (email, password)=>{
        try {
            const { error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["supabase"].auth.signInWithPassword({
                email,
                password
            });
            return {
                error
            };
        } catch (error) {
            return {
                error: error
            };
        }
    };
    // تسجيل الخروج
    const signOut = async ()=>{
        try {
            const { error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["supabase"].auth.signOut();
            if (!error) {
                setUser(null);
                setAppUser(null);
                setSession(null);
            }
            return {
                error
            };
        } catch (error) {
            return {
                error: error
            };
        }
    };
    // إعادة تعيين كلمة المرور
    const resetPassword = async (email)=>{
        try {
            const { error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["supabase"].auth.resetPasswordForEmail(email, {
                redirectTo: `${window.location.origin}/auth/reset-password`
            });
            return {
                error
            };
        } catch (error) {
            return {
                error: error
            };
        }
    };
    // تحديث الملف الشخصي
    const updateProfile = async (updates)=>{
        if (!user || !appUser) {
            return {
                error: new Error('المستخدم غير مسجل الدخول')
            };
        }
        try {
            // تحديث بيانات المصادقة إذا لزم الأمر
            if (updates.email && updates.email !== user.email) {
                const { error: authError } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["supabase"].auth.updateUser({
                    email: updates.email
                });
                if (authError) {
                    return {
                        error: new Error(authError.message)
                    };
                }
            }
            // تحديث بيانات التطبيق
            const updatedUser = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$database$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["usersService"].update(user.id, updates);
            setAppUser(updatedUser);
            return {
                error: null
            };
        } catch (error) {
            return {
                error: error
            };
        }
    };
    const isAdmin = appUser?.role === 'admin';
    const isAuthenticated = !!user;
    const value = {
        user,
        appUser,
        session,
        loading,
        signUp,
        signIn,
        signOut,
        resetPassword,
        updateProfile,
        isAdmin,
        isAuthenticated
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(AuthContext.Provider, {
        value: value,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/contexts/AuthContext.tsx",
        lineNumber: 216,
        columnNumber: 5
    }, this);
}
function useAuth() {
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(AuthContext);
    if (context === undefined) {
        throw new Error('useAuth must be used within an AuthProvider');
    }
    return context;
}
function useRequireAuth(redirectTo = '/auth/login') {
    const { isAuthenticated, loading } = useAuth();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (!loading && !isAuthenticated) {
            window.location.href = redirectTo;
        }
    }, [
        isAuthenticated,
        loading,
        redirectTo
    ]);
    return {
        isAuthenticated,
        loading
    };
}
function useRequireAdmin(redirectTo = '/') {
    const { isAdmin, loading, isAuthenticated } = useAuth();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (!loading && (!isAuthenticated || !isAdmin)) {
            window.location.href = redirectTo;
        }
    }, [
        isAdmin,
        loading,
        isAuthenticated,
        redirectTo
    ]);
    return {
        isAdmin,
        loading
    };
}
}}),
"[project]/src/contexts/CartContext.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "CartProvider": (()=>CartProvider),
    "useCart": (()=>useCart)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
'use client';
;
;
const CartContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createContext"])(undefined);
function CartProvider({ children }) {
    const [items, setItems] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([]);
    // تحميل السلة من localStorage عند بدء التطبيق
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const savedCart = localStorage.getItem('cart');
        if (savedCart) {
            try {
                setItems(JSON.parse(savedCart));
            } catch (error) {
                console.error('Error loading cart from localStorage:', error);
            }
        }
    }, []);
    // حفظ السلة في localStorage عند تغييرها
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        localStorage.setItem('cart', JSON.stringify(items));
    }, [
        items
    ]);
    // حساب إجمالي العناصر
    const totalItems = items.reduce((total, item)=>total + item.quantity, 0);
    // حساب إجمالي السعر
    const totalPrice = items.reduce((total, item)=>total + item.product.price * item.quantity, 0);
    // إضافة منتج للسلة
    const addToCart = (product, quantity = 1, options)=>{
        setItems((currentItems)=>{
            const existingItemIndex = currentItems.findIndex((item)=>item.product.id === product.id && item.selectedSize === options?.size && item.selectedColor === options?.color);
            if (existingItemIndex > -1) {
                // إذا كان المنتج موجود، زيادة الكمية
                const updatedItems = [
                    ...currentItems
                ];
                updatedItems[existingItemIndex].quantity += quantity;
                return updatedItems;
            } else {
                // إضافة منتج جديد
                const newItem = {
                    id: `${product.id}-${options?.size || ''}-${options?.color || ''}-${Date.now()}`,
                    product,
                    quantity,
                    selectedSize: options?.size,
                    selectedColor: options?.color
                };
                return [
                    ...currentItems,
                    newItem
                ];
            }
        });
    };
    // إزالة منتج من السلة
    const removeFromCart = (itemId)=>{
        setItems((currentItems)=>currentItems.filter((item)=>item.id !== itemId));
    };
    // تحديث كمية منتج
    const updateQuantity = (itemId, quantity)=>{
        if (quantity <= 0) {
            removeFromCart(itemId);
            return;
        }
        setItems((currentItems)=>currentItems.map((item)=>item.id === itemId ? {
                    ...item,
                    quantity
                } : item));
    };
    // مسح السلة
    const clearCart = ()=>{
        setItems([]);
    };
    // التحقق من وجود منتج في السلة
    const isInCart = (productId)=>{
        return items.some((item)=>item.product.id === productId);
    };
    // الحصول على عنصر من السلة
    const getCartItem = (productId)=>{
        return items.find((item)=>item.product.id === productId);
    };
    const value = {
        items,
        totalItems,
        totalPrice,
        addToCart,
        removeFromCart,
        updateQuantity,
        clearCart,
        isInCart,
        getCartItem
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(CartContext.Provider, {
        value: value,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/contexts/CartContext.tsx",
        lineNumber: 137,
        columnNumber: 5
    }, this);
}
function useCart() {
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(CartContext);
    if (context === undefined) {
        throw new Error('useCart must be used within a CartProvider');
    }
    return context;
}
}}),
"[project]/src/app/layout.tsx [app-rsc] (ecmascript, Next.js server component, client modules ssr)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
}}),

};

//# sourceMappingURL=%5Broot%20of%20the%20server%5D__51769f._.js.map