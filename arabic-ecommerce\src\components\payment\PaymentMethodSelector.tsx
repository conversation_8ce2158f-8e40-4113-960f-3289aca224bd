'use client';

import React from 'react';
import { PaymentMethod, PaymentOption, Currency } from '@/types/payment';
import { usePayment, usePaymentMethod } from '@/contexts/PaymentContext';

interface PaymentMethodSelectorProps {
  selectedMethod: PaymentMethod | null;
  onMethodSelect: (method: PaymentMethod) => void;
  amount: number;
  currency: Currency;
  className?: string;
}

export default function PaymentMethodSelector({
  selectedMethod,
  onMethodSelect,
  amount,
  currency,
  className = '',
}: PaymentMethodSelectorProps) {
  const { getAvailablePaymentMethods, calculateTotalWithFees } = usePayment();
  
  // الحصول على طرق الدفع المتاحة للمبلغ والعملة المحددة
  const availableMethods = getAvailablePaymentMethods(amount, currency);

  const handleMethodSelect = (method: PaymentMethod) => {
    onMethodSelect(method);
  };

  return (
    <div className={`space-y-4 ${className}`}>
      <h3 className="text-lg font-semibold text-gray-900 mb-4">
        اختر طريقة الدفع
      </h3>
      
      <div className="grid gap-3">
        {availableMethods.map((option) => {
          const totalWithFees = calculateTotalWithFees(amount, option.method);
          const fees = totalWithFees - amount;
          const isSelected = selectedMethod === option.method;
          
          return (
            <PaymentMethodCard
              key={option.method}
              option={option}
              isSelected={isSelected}
              onSelect={() => handleMethodSelect(option.method)}
              amount={amount}
              totalWithFees={totalWithFees}
              fees={fees}
              currency={currency}
            />
          );
        })}
      </div>
      
      {availableMethods.length === 0 && (
        <div className="text-center py-8 text-gray-500">
          <p>لا توجد طرق دفع متاحة للمبلغ المحدد</p>
          <p className="text-sm mt-1">
            المبلغ: {amount} {currency}
          </p>
        </div>
      )}
    </div>
  );
}

interface PaymentMethodCardProps {
  option: PaymentOption;
  isSelected: boolean;
  onSelect: () => void;
  amount: number;
  totalWithFees: number;
  fees: number;
  currency: Currency;
}

function PaymentMethodCard({
  option,
  isSelected,
  onSelect,
  amount,
  totalWithFees,
  fees,
  currency,
}: PaymentMethodCardProps) {
  return (
    <div
      className={`
        relative border-2 rounded-lg p-4 cursor-pointer transition-all duration-200
        ${isSelected 
          ? 'border-primary-500 bg-primary-50 shadow-md' 
          : 'border-gray-200 bg-white hover:border-gray-300 hover:shadow-sm'
        }
      `}
      onClick={onSelect}
    >
      {/* زر الاختيار */}
      <div className="flex items-start justify-between">
        <div className="flex items-center space-x-3 space-x-reverse">
          <div className="text-2xl">{option.icon}</div>
          <div className="flex-1">
            <h4 className="font-semibold text-gray-900">
              {option.nameAr}
            </h4>
            <p className="text-sm text-gray-600 mt-1">
              {option.descriptionAr}
            </p>
            <div className="flex items-center space-x-2 space-x-reverse mt-2 text-xs text-gray-500">
              <span>⏱️ {option.processingTimeAr}</span>
              {option.minAmount && (
                <span>• الحد الأدنى: {option.minAmount} {currency}</span>
              )}
              {option.maxAmount && option.maxAmount !== Infinity && (
                <span>• الحد الأقصى: {option.maxAmount} {currency}</span>
              )}
            </div>
          </div>
        </div>
        
        {/* دائرة الاختيار */}
        <div className={`
          w-5 h-5 rounded-full border-2 flex items-center justify-center
          ${isSelected 
            ? 'border-primary-500 bg-primary-500' 
            : 'border-gray-300'
          }
        `}>
          {isSelected && (
            <div className="w-2 h-2 rounded-full bg-white"></div>
          )}
        </div>
      </div>
      
      {/* معلومات الرسوم والمجموع */}
      <div className="mt-3 pt-3 border-t border-gray-100">
        <div className="flex justify-between items-center text-sm">
          <span className="text-gray-600">المبلغ الأساسي:</span>
          <span className="font-medium">{amount} {currency}</span>
        </div>
        
        {fees > 0 && (
          <div className="flex justify-between items-center text-sm mt-1">
            <span className="text-gray-600">رسوم الخدمة:</span>
            <span className="font-medium text-orange-600">+{fees} {currency}</span>
          </div>
        )}
        
        <div className="flex justify-between items-center text-base font-semibold mt-2 pt-2 border-t border-gray-100">
          <span className="text-gray-900">المجموع:</span>
          <span className="text-primary-600">{totalWithFees} {currency}</span>
        </div>
      </div>
      
      {/* مؤشر الاختيار */}
      {isSelected && (
        <div className="absolute top-2 left-2">
          <div className="bg-primary-500 text-white text-xs px-2 py-1 rounded-full">
            ✓ محدد
          </div>
        </div>
      )}
    </div>
  );
}

// مكون مبسط لعرض طريقة دفع واحدة
export function PaymentMethodBadge({ 
  method, 
  className = '' 
}: { 
  method: PaymentMethod; 
  className?: string; 
}) {
  const { option } = usePaymentMethod(method);
  
  if (!option) return null;
  
  return (
    <div className={`inline-flex items-center space-x-2 space-x-reverse px-3 py-1 bg-gray-100 rounded-full text-sm ${className}`}>
      <span>{option.icon}</span>
      <span className="font-medium">{option.nameAr}</span>
    </div>
  );
}

// مكون لعرض ملخص طريقة الدفع المختارة
export function PaymentMethodSummary({ 
  method, 
  amount, 
  currency,
  className = '' 
}: { 
  method: PaymentMethod; 
  amount: number;
  currency: Currency;
  className?: string; 
}) {
  const { calculateTotalWithFees } = usePayment();
  const { option } = usePaymentMethod(method);
  
  if (!option) return null;
  
  const totalWithFees = calculateTotalWithFees(amount, method);
  const fees = totalWithFees - amount;
  
  return (
    <div className={`bg-gray-50 rounded-lg p-4 ${className}`}>
      <div className="flex items-center space-x-3 space-x-reverse mb-3">
        <div className="text-xl">{option.icon}</div>
        <div>
          <h4 className="font-semibold text-gray-900">{option.nameAr}</h4>
          <p className="text-sm text-gray-600">{option.descriptionAr}</p>
        </div>
      </div>
      
      <div className="space-y-2 text-sm">
        <div className="flex justify-between">
          <span className="text-gray-600">المبلغ:</span>
          <span>{amount} {currency}</span>
        </div>
        
        {fees > 0 && (
          <div className="flex justify-between">
            <span className="text-gray-600">رسوم الخدمة:</span>
            <span className="text-orange-600">+{fees} {currency}</span>
          </div>
        )}
        
        <div className="flex justify-between font-semibold pt-2 border-t border-gray-200">
          <span>المجموع:</span>
          <span className="text-primary-600">{totalWithFees} {currency}</span>
        </div>
        
        <div className="flex items-center space-x-2 space-x-reverse text-xs text-gray-500 mt-2">
          <span>⏱️</span>
          <span>وقت المعالجة: {option.processingTimeAr}</span>
        </div>
      </div>
    </div>
  );
}
