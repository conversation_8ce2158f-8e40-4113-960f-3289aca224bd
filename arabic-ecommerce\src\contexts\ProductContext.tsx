'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode, useMemo } from 'react';
import { Product } from '@/types';
import { sampleProducts } from '@/lib/data';

interface ProductContextType {
  products: Product[];
  addProduct: (product: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>) => void;
  updateProduct: (id: string, product: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>) => void;
  deleteProduct: (id: string) => void;
  getProductById: (id: string) => Product | undefined;
  getFeaturedProducts: () => Product[];
  loading: boolean;
  isLoading: boolean; // للتوافق مع الإصدارات السابقة
}

const ProductContext = createContext<ProductContextType | undefined>(undefined);

interface ProductProviderProps {
  children: ReactNode;
}

export function ProductProvider({ children }: ProductProviderProps) {
  const [products, setProducts] = useState<Product[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Initialize products from localStorage or use sample data
  useEffect(() => {
    const initializeProducts = () => {
      console.log('ProductContext: Initializing products...');
      console.log('ProductContext: Sample products:', sampleProducts);

      try {
        // Check if we're in the browser
        if (typeof window !== 'undefined') {
          console.log('ProductContext: Running in browser');
          const savedProducts = localStorage.getItem('ecommerce-products');
          if (savedProducts) {
            console.log('ProductContext: Found saved products in localStorage');
            const parsedProducts = JSON.parse(savedProducts);
            // Convert date strings back to Date objects
            const productsWithDates = parsedProducts.map((product: any) => ({
              ...product,
              createdAt: new Date(product.createdAt),
              updatedAt: new Date(product.updatedAt),
            }));
            console.log('ProductContext: Setting products from localStorage:', productsWithDates);
            setProducts(productsWithDates);
          } else {
            // First time - use sample data
            console.log('ProductContext: No saved products, using sample data');
            setProducts(sampleProducts);
            localStorage.setItem('ecommerce-products', JSON.stringify(sampleProducts));
          }
        } else {
          // Server-side - just use sample data
          console.log('ProductContext: Running on server, using sample data');
          setProducts(sampleProducts);
        }
      } catch (error) {
        console.error('ProductContext: Error loading products from localStorage:', error);
        setProducts(sampleProducts);
      } finally {
        console.log('ProductContext: Setting loading to false');
        setIsLoading(false);
      }
    };

    initializeProducts();
  }, []);

  // Save to localStorage whenever products change
  useEffect(() => {
    if (!isLoading && products.length > 0 && typeof window !== 'undefined') {
      try {
        localStorage.setItem('ecommerce-products', JSON.stringify(products));
      } catch (error) {
        console.error('Error saving products to localStorage:', error);
      }
    }
  }, [products, isLoading]);

  const addProduct = (productData: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>) => {
    const newProduct: Product = {
      ...productData,
      id: Date.now().toString(),
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    setProducts(prev => [...prev, newProduct]);
  };

  const updateProduct = (id: string, productData: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>) => {
    setProducts(prev => prev.map(product =>
      product.id === id
        ? { ...product, ...productData, updatedAt: new Date() }
        : product
    ));
  };

  const deleteProduct = (id: string) => {
    setProducts(prev => prev.filter(product => product.id !== id));
  };

  const getProductById = (id: string): Product | undefined => {
    return products.find(product => product.id === id);
  };

  const getFeaturedProducts = (): Product[] => {
    return products.filter(product => product.featured);
  };

  const value: ProductContextType = {
    products,
    addProduct,
    updateProduct,
    deleteProduct,
    getProductById,
    getFeaturedProducts,
    loading: isLoading,
    isLoading, // للتوافق مع الإصدارات السابقة
  };

  return (
    <ProductContext.Provider value={value}>
      {children}
    </ProductContext.Provider>
  );
}

export function useProducts() {
  const context = useContext(ProductContext);
  if (context === undefined) {
    throw new Error('useProducts must be used within a ProductProvider');
  }
  return context;
}


