'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { Product } from '@/types';
import { sampleProducts } from '@/lib/data';

interface ProductContextType {
  products: Product[];
  addProduct: (product: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>) => void;
  updateProduct: (id: string, product: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>) => void;
  deleteProduct: (id: string) => void;
  getProductById: (id: string) => Product | undefined;
  getFeaturedProducts: () => Product[];
  isLoading: boolean;
}

const ProductContext = createContext<ProductContextType | undefined>(undefined);

interface ProductProviderProps {
  children: ReactNode;
}

export function ProductProvider({ children }: ProductProviderProps) {
  const [products, setProducts] = useState<Product[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Initialize products from localStorage or use sample data
  useEffect(() => {
    const initializeProducts = () => {
      try {
        const savedProducts = localStorage.getItem('ecommerce-products');
        if (savedProducts) {
          const parsedProducts = JSON.parse(savedProducts);
          // Convert date strings back to Date objects
          const productsWithDates = parsedProducts.map((product: any) => ({
            ...product,
            createdAt: new Date(product.createdAt),
            updatedAt: new Date(product.updatedAt),
          }));
          setProducts(productsWithDates);
        } else {
          // First time - use sample data
          setProducts(sampleProducts);
          localStorage.setItem('ecommerce-products', JSON.stringify(sampleProducts));
        }
      } catch (error) {
        console.error('Error loading products from localStorage:', error);
        setProducts(sampleProducts);
      } finally {
        setIsLoading(false);
      }
    };

    initializeProducts();
  }, []);

  // Save to localStorage whenever products change
  useEffect(() => {
    if (!isLoading && products.length > 0) {
      try {
        localStorage.setItem('ecommerce-products', JSON.stringify(products));
      } catch (error) {
        console.error('Error saving products to localStorage:', error);
      }
    }
  }, [products, isLoading]);

  const addProduct = (productData: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>) => {
    const newProduct: Product = {
      ...productData,
      id: Date.now().toString(),
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    setProducts(prev => [...prev, newProduct]);
  };

  const updateProduct = (id: string, productData: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>) => {
    setProducts(prev => prev.map(product => 
      product.id === id 
        ? { ...product, ...productData, updatedAt: new Date() }
        : product
    ));
  };

  const deleteProduct = (id: string) => {
    setProducts(prev => prev.filter(product => product.id !== id));
  };

  const getProductById = (id: string): Product | undefined => {
    return products.find(product => product.id === id);
  };

  const getFeaturedProducts = (): Product[] => {
    return products.filter(product => product.featured);
  };

  const value: ProductContextType = {
    products,
    addProduct,
    updateProduct,
    deleteProduct,
    getProductById,
    getFeaturedProducts,
    isLoading,
  };

  return (
    <ProductContext.Provider value={value}>
      {children}
    </ProductContext.Provider>
  );
}

export function useProducts() {
  const context = useContext(ProductContext);
  if (context === undefined) {
    throw new Error('useProducts must be used within a ProductProvider');
  }
  return context;
}

// Hook for getting products with optional filtering
export function useFilteredProducts(filters?: {
  category?: string;
  priceRange?: string;
  searchTerm?: string;
  featured?: boolean;
}) {
  const { products } = useProducts();

  return React.useMemo(() => {
    if (!filters) return products;

    return products.filter(product => {
      const matchesCategory = !filters.category || product.category === filters.category;
      const matchesSearch = !filters.searchTerm || 
        product.name.toLowerCase().includes(filters.searchTerm.toLowerCase()) ||
        product.description.toLowerCase().includes(filters.searchTerm.toLowerCase());
      
      let matchesPrice = true;
      if (filters.priceRange) {
        switch (filters.priceRange) {
          case 'under-500':
            matchesPrice = product.price < 500;
            break;
          case '500-1000':
            matchesPrice = product.price >= 500 && product.price <= 1000;
            break;
          case '1000-3000':
            matchesPrice = product.price >= 1000 && product.price <= 3000;
            break;
          case 'over-3000':
            matchesPrice = product.price > 3000;
            break;
        }
      }

      const matchesFeatured = filters.featured === undefined || product.featured === filters.featured;

      return matchesCategory && matchesSearch && matchesPrice && matchesFeatured;
    });
  }, [products, filters]);
}
