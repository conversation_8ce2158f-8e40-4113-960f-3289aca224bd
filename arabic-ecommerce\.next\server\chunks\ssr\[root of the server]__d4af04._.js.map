{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/New%20folder%20%288%29/arabic-ecommerce/src/lib/data.ts"], "sourcesContent": ["import { Product, User } from '@/types';\n\n// Sample products data\nexport const sampleProducts: Product[] = [\n  {\n    id: '1',\n    name: 'هاتف ذكي متطور',\n    description: 'هاتف ذكي بمواصفات عالية وكاميرا متقدمة',\n    price: 2500,\n    image: '/images/phone.jpg',\n    category: 'إلكترونيات',\n    stock: 15,\n    featured: true,\n    createdAt: new Date(),\n    updatedAt: new Date(),\n  },\n  {\n    id: '2',\n    name: 'لابتوب للألعاب',\n    description: 'لابتوب قوي مخصص للألعاب والتصميم',\n    price: 4500,\n    image: '/images/laptop.jpg',\n    category: 'إلكترونيات',\n    stock: 8,\n    featured: true,\n    createdAt: new Date(),\n    updatedAt: new Date(),\n  },\n  {\n    id: '3',\n    name: 'ساعة ذكية',\n    description: 'ساعة ذكية لتتبع اللياقة البدنية',\n    price: 800,\n    image: '/images/watch.jpg',\n    category: 'إكسسوارات',\n    stock: 25,\n    featured: false,\n    createdAt: new Date(),\n    updatedAt: new Date(),\n  },\n  {\n    id: '4',\n    name: 'سماعات لاسلكية',\n    description: 'سماعات بلوتوث عالية الجودة',\n    price: 350,\n    image: '/images/headphones.jpg',\n    category: 'إكسسوارات',\n    stock: 30,\n    featured: true,\n    createdAt: new Date(),\n    updatedAt: new Date(),\n  },\n  {\n    id: '5',\n    name: 'كاميرا رقمية',\n    description: 'كاميرا احترافية للتصوير الفوتوغرافي',\n    price: 3200,\n    image: '/images/camera.jpg',\n    category: 'إلكترونيات',\n    stock: 12,\n    featured: false,\n    createdAt: new Date(),\n    updatedAt: new Date(),\n  },\n  {\n    id: '6',\n    name: 'تابلت للرسم',\n    description: 'تابلت مخصص للرسم والتصميم الرقمي',\n    price: 1800,\n    image: '/images/tablet.jpg',\n    category: 'إلكترونيات',\n    stock: 18,\n    featured: true,\n    createdAt: new Date(),\n    updatedAt: new Date(),\n  },\n];\n\n// Sample users data\nexport const sampleUsers: User[] = [\n  {\n    id: '1',\n    name: 'أحمد محمد',\n    email: '<EMAIL>',\n    role: 'admin',\n    createdAt: new Date(),\n    updatedAt: new Date(),\n  },\n  {\n    id: '2',\n    name: 'فاطمة علي',\n    email: '<EMAIL>',\n    role: 'customer',\n    createdAt: new Date(),\n    updatedAt: new Date(),\n  },\n];\n\n// Categories (deprecated - use CategoryContext instead)\nexport const categories = [\n  'إلكترونيات',\n  'إكسسوارات',\n  'ملابس',\n  'كتب',\n  'رياضة',\n  'منزل وحديقة',\n];\n"], "names": [], "mappings": ";;;;;AAGO,MAAM,iBAA4B;IACvC;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,OAAO;QACP,UAAU;QACV,OAAO;QACP,UAAU;QACV,WAAW,IAAI;QACf,WAAW,IAAI;IACjB;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,OAAO;QACP,UAAU;QACV,OAAO;QACP,UAAU;QACV,WAAW,IAAI;QACf,WAAW,IAAI;IACjB;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,OAAO;QACP,UAAU;QACV,OAAO;QACP,UAAU;QACV,WAAW,IAAI;QACf,WAAW,IAAI;IACjB;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,OAAO;QACP,UAAU;QACV,OAAO;QACP,UAAU;QACV,WAAW,IAAI;QACf,WAAW,IAAI;IACjB;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,OAAO;QACP,UAAU;QACV,OAAO;QACP,UAAU;QACV,WAAW,IAAI;QACf,WAAW,IAAI;IACjB;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,OAAO;QACP,UAAU;QACV,OAAO;QACP,UAAU;QACV,WAAW,IAAI;QACf,WAAW,IAAI;IACjB;CACD;AAGM,MAAM,cAAsB;IACjC;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,MAAM;QACN,WAAW,IAAI;QACf,WAAW,IAAI;IACjB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,MAAM;QACN,WAAW,IAAI;QACf,WAAW,IAAI;IACjB;CACD;AAGM,MAAM,aAAa;IACxB;IACA;IACA;IACA;IACA;IACA;CACD"}}, {"offset": {"line": 120, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 126, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/New%20folder%20%288%29/arabic-ecommerce/src/contexts/ProductContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';\nimport { Product } from '@/types';\nimport { sampleProducts } from '@/lib/data';\n\ninterface ProductContextType {\n  products: Product[];\n  addProduct: (product: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>) => void;\n  updateProduct: (id: string, product: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>) => void;\n  deleteProduct: (id: string) => void;\n  getProductById: (id: string) => Product | undefined;\n  getFeaturedProducts: () => Product[];\n  isLoading: boolean;\n}\n\nconst ProductContext = createContext<ProductContextType | undefined>(undefined);\n\ninterface ProductProviderProps {\n  children: ReactNode;\n}\n\nexport function ProductProvider({ children }: ProductProviderProps) {\n  const [products, setProducts] = useState<Product[]>([]);\n  const [isLoading, setIsLoading] = useState(true);\n\n  // Initialize products from localStorage or use sample data\n  useEffect(() => {\n    const initializeProducts = () => {\n      try {\n        const savedProducts = localStorage.getItem('ecommerce-products');\n        if (savedProducts) {\n          const parsedProducts = JSON.parse(savedProducts);\n          // Convert date strings back to Date objects\n          const productsWithDates = parsedProducts.map((product: any) => ({\n            ...product,\n            createdAt: new Date(product.createdAt),\n            updatedAt: new Date(product.updatedAt),\n          }));\n          setProducts(productsWithDates);\n        } else {\n          // First time - use sample data\n          setProducts(sampleProducts);\n          localStorage.setItem('ecommerce-products', JSON.stringify(sampleProducts));\n        }\n      } catch (error) {\n        console.error('Error loading products from localStorage:', error);\n        setProducts(sampleProducts);\n      } finally {\n        setIsLoading(false);\n      }\n    };\n\n    initializeProducts();\n  }, []);\n\n  // Save to localStorage whenever products change\n  useEffect(() => {\n    if (!isLoading && products.length > 0) {\n      try {\n        localStorage.setItem('ecommerce-products', JSON.stringify(products));\n      } catch (error) {\n        console.error('Error saving products to localStorage:', error);\n      }\n    }\n  }, [products, isLoading]);\n\n  const addProduct = (productData: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>) => {\n    const newProduct: Product = {\n      ...productData,\n      id: Date.now().toString(),\n      createdAt: new Date(),\n      updatedAt: new Date(),\n    };\n    setProducts(prev => [...prev, newProduct]);\n  };\n\n  const updateProduct = (id: string, productData: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>) => {\n    setProducts(prev => prev.map(product => \n      product.id === id \n        ? { ...product, ...productData, updatedAt: new Date() }\n        : product\n    ));\n  };\n\n  const deleteProduct = (id: string) => {\n    setProducts(prev => prev.filter(product => product.id !== id));\n  };\n\n  const getProductById = (id: string): Product | undefined => {\n    return products.find(product => product.id === id);\n  };\n\n  const getFeaturedProducts = (): Product[] => {\n    return products.filter(product => product.featured);\n  };\n\n  const value: ProductContextType = {\n    products,\n    addProduct,\n    updateProduct,\n    deleteProduct,\n    getProductById,\n    getFeaturedProducts,\n    isLoading,\n  };\n\n  return (\n    <ProductContext.Provider value={value}>\n      {children}\n    </ProductContext.Provider>\n  );\n}\n\nexport function useProducts() {\n  const context = useContext(ProductContext);\n  if (context === undefined) {\n    throw new Error('useProducts must be used within a ProductProvider');\n  }\n  return context;\n}\n\n// Hook for getting products with optional filtering\nexport function useFilteredProducts(filters?: {\n  category?: string;\n  priceRange?: string;\n  searchTerm?: string;\n  featured?: boolean;\n}) {\n  const { products } = useProducts();\n\n  return React.useMemo(() => {\n    if (!filters) return products;\n\n    return products.filter(product => {\n      const matchesCategory = !filters.category || product.category === filters.category;\n      const matchesSearch = !filters.searchTerm || \n        product.name.toLowerCase().includes(filters.searchTerm.toLowerCase()) ||\n        product.description.toLowerCase().includes(filters.searchTerm.toLowerCase());\n      \n      let matchesPrice = true;\n      if (filters.priceRange) {\n        switch (filters.priceRange) {\n          case 'under-500':\n            matchesPrice = product.price < 500;\n            break;\n          case '500-1000':\n            matchesPrice = product.price >= 500 && product.price <= 1000;\n            break;\n          case '1000-3000':\n            matchesPrice = product.price >= 1000 && product.price <= 3000;\n            break;\n          case 'over-3000':\n            matchesPrice = product.price > 3000;\n            break;\n        }\n      }\n\n      const matchesFeatured = filters.featured === undefined || product.featured === filters.featured;\n\n      return matchesCategory && matchesSearch && matchesPrice && matchesFeatured;\n    });\n  }, [products, filters]);\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AAEA;AAJA;;;;AAgBA,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAkC;AAM9D,SAAS,gBAAgB,EAAE,QAAQ,EAAwB;IAChE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,2DAA2D;IAC3D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,qBAAqB;YACzB,IAAI;gBACF,MAAM,gBAAgB,aAAa,OAAO,CAAC;gBAC3C,IAAI,eAAe;oBACjB,MAAM,iBAAiB,KAAK,KAAK,CAAC;oBAClC,4CAA4C;oBAC5C,MAAM,oBAAoB,eAAe,GAAG,CAAC,CAAC,UAAiB,CAAC;4BAC9D,GAAG,OAAO;4BACV,WAAW,IAAI,KAAK,QAAQ,SAAS;4BACrC,WAAW,IAAI,KAAK,QAAQ,SAAS;wBACvC,CAAC;oBACD,YAAY;gBACd,OAAO;oBACL,+BAA+B;oBAC/B,YAAY,kHAAA,CAAA,iBAAc;oBAC1B,aAAa,OAAO,CAAC,sBAAsB,KAAK,SAAS,CAAC,kHAAA,CAAA,iBAAc;gBAC1E;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,6CAA6C;gBAC3D,YAAY,kHAAA,CAAA,iBAAc;YAC5B,SAAU;gBACR,aAAa;YACf;QACF;QAEA;IACF,GAAG,EAAE;IAEL,gDAAgD;IAChD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,aAAa,SAAS,MAAM,GAAG,GAAG;YACrC,IAAI;gBACF,aAAa,OAAO,CAAC,sBAAsB,KAAK,SAAS,CAAC;YAC5D,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,0CAA0C;YAC1D;QACF;IACF,GAAG;QAAC;QAAU;KAAU;IAExB,MAAM,aAAa,CAAC;QAClB,MAAM,aAAsB;YAC1B,GAAG,WAAW;YACd,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,WAAW,IAAI;YACf,WAAW,IAAI;QACjB;QACA,YAAY,CAAA,OAAQ;mBAAI;gBAAM;aAAW;IAC3C;IAEA,MAAM,gBAAgB,CAAC,IAAY;QACjC,YAAY,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,UAC3B,QAAQ,EAAE,KAAK,KACX;oBAAE,GAAG,OAAO;oBAAE,GAAG,WAAW;oBAAE,WAAW,IAAI;gBAAO,IACpD;IAER;IAEA,MAAM,gBAAgB,CAAC;QACrB,YAAY,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK;IAC5D;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAO,SAAS,IAAI,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK;IACjD;IAEA,MAAM,sBAAsB;QAC1B,OAAO,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,QAAQ;IACpD;IAEA,MAAM,QAA4B;QAChC;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,8OAAC,eAAe,QAAQ;QAAC,OAAO;kBAC7B;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAGO,SAAS,oBAAoB,OAKnC;IACC,MAAM,EAAE,QAAQ,EAAE,GAAG;IAErB,OAAO,qMAAA,CAAA,UAAK,CAAC,OAAO,CAAC;QACnB,IAAI,CAAC,SAAS,OAAO;QAErB,OAAO,SAAS,MAAM,CAAC,CAAA;YACrB,MAAM,kBAAkB,CAAC,QAAQ,QAAQ,IAAI,QAAQ,QAAQ,KAAK,QAAQ,QAAQ;YAClF,MAAM,gBAAgB,CAAC,QAAQ,UAAU,IACvC,QAAQ,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,QAAQ,UAAU,CAAC,WAAW,OAClE,QAAQ,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,QAAQ,UAAU,CAAC,WAAW;YAE3E,IAAI,eAAe;YACnB,IAAI,QAAQ,UAAU,EAAE;gBACtB,OAAQ,QAAQ,UAAU;oBACxB,KAAK;wBACH,eAAe,QAAQ,KAAK,GAAG;wBAC/B;oBACF,KAAK;wBACH,eAAe,QAAQ,KAAK,IAAI,OAAO,QAAQ,KAAK,IAAI;wBACxD;oBACF,KAAK;wBACH,eAAe,QAAQ,KAAK,IAAI,QAAQ,QAAQ,KAAK,IAAI;wBACzD;oBACF,KAAK;wBACH,eAAe,QAAQ,KAAK,GAAG;wBAC/B;gBACJ;YACF;YAEA,MAAM,kBAAkB,QAAQ,QAAQ,KAAK,aAAa,QAAQ,QAAQ,KAAK,QAAQ,QAAQ;YAE/F,OAAO,mBAAmB,iBAAiB,gBAAgB;QAC7D;IACF,GAAG;QAAC;QAAU;KAAQ;AACxB"}}, {"offset": {"line": 268, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 274, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/New%20folder%20%288%29/arabic-ecommerce/src/contexts/CategoryContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';\n\nexport interface Category {\n  id: string;\n  name: string;\n  description: string;\n  isActive: boolean;\n  productCount: number;\n  createdAt: Date;\n  updatedAt: Date;\n}\n\ninterface CategoryContextType {\n  categories: Category[];\n  addCategory: (category: Omit<Category, 'id' | 'productCount' | 'createdAt' | 'updatedAt'>) => void;\n  updateCategory: (id: string, category: Omit<Category, 'id' | 'productCount' | 'createdAt' | 'updatedAt'>) => void;\n  deleteCategory: (id: string) => void;\n  getCategoryById: (id: string) => Category | undefined;\n  getActiveCategories: () => Category[];\n  updateProductCount: (categoryName: string, count: number) => void;\n  isLoading: boolean;\n}\n\nconst CategoryContext = createContext<CategoryContextType | undefined>(undefined);\n\ninterface CategoryProviderProps {\n  children: ReactNode;\n}\n\n// Default categories\nconst defaultCategories: Category[] = [\n  {\n    id: '1',\n    name: 'إلكترونيات',\n    description: 'أجهزة إلكترونية وتقنية حديثة',\n    isActive: true,\n    productCount: 0,\n    createdAt: new Date(),\n    updatedAt: new Date(),\n  },\n  {\n    id: '2',\n    name: 'إكسسوارات',\n    description: 'إكسسوارات متنوعة وعملية',\n    isActive: true,\n    productCount: 0,\n    createdAt: new Date(),\n    updatedAt: new Date(),\n  },\n  {\n    id: '3',\n    name: 'ملابس',\n    description: 'ملابس عصرية للرجال والنساء',\n    isActive: true,\n    productCount: 0,\n    createdAt: new Date(),\n    updatedAt: new Date(),\n  },\n  {\n    id: '4',\n    name: 'كتب',\n    description: 'كتب ومراجع في مختلف المجالات',\n    isActive: true,\n    productCount: 0,\n    createdAt: new Date(),\n    updatedAt: new Date(),\n  },\n  {\n    id: '5',\n    name: 'رياضة',\n    description: 'معدات وأدوات رياضية',\n    isActive: true,\n    productCount: 0,\n    createdAt: new Date(),\n    updatedAt: new Date(),\n  },\n  {\n    id: '6',\n    name: 'منزل وحديقة',\n    description: 'أدوات ومستلزمات المنزل والحديقة',\n    isActive: true,\n    productCount: 0,\n    createdAt: new Date(),\n    updatedAt: new Date(),\n  },\n];\n\nexport function CategoryProvider({ children }: CategoryProviderProps) {\n  const [categories, setCategories] = useState<Category[]>([]);\n  const [isLoading, setIsLoading] = useState(true);\n\n  // Initialize categories from localStorage or use default data\n  useEffect(() => {\n    const initializeCategories = () => {\n      try {\n        const savedCategories = localStorage.getItem('ecommerce-categories');\n        if (savedCategories) {\n          const parsedCategories = JSON.parse(savedCategories);\n          // Convert date strings back to Date objects\n          const categoriesWithDates = parsedCategories.map((category: any) => ({\n            ...category,\n            createdAt: new Date(category.createdAt),\n            updatedAt: new Date(category.updatedAt),\n          }));\n          setCategories(categoriesWithDates);\n        } else {\n          // First time - use default data\n          setCategories(defaultCategories);\n          localStorage.setItem('ecommerce-categories', JSON.stringify(defaultCategories));\n        }\n      } catch (error) {\n        console.error('Error loading categories from localStorage:', error);\n        setCategories(defaultCategories);\n      } finally {\n        setIsLoading(false);\n      }\n    };\n\n    initializeCategories();\n  }, []);\n\n  // Save to localStorage whenever categories change\n  useEffect(() => {\n    if (!isLoading && categories.length > 0) {\n      try {\n        localStorage.setItem('ecommerce-categories', JSON.stringify(categories));\n      } catch (error) {\n        console.error('Error saving categories to localStorage:', error);\n      }\n    }\n  }, [categories, isLoading]);\n\n  const addCategory = (categoryData: Omit<Category, 'id' | 'productCount' | 'createdAt' | 'updatedAt'>) => {\n    const newCategory: Category = {\n      ...categoryData,\n      id: Date.now().toString(),\n      productCount: 0,\n      createdAt: new Date(),\n      updatedAt: new Date(),\n    };\n    setCategories(prev => [...prev, newCategory]);\n  };\n\n  const updateCategory = (id: string, categoryData: Omit<Category, 'id' | 'productCount' | 'createdAt' | 'updatedAt'>) => {\n    setCategories(prev => prev.map(category => \n      category.id === id \n        ? { ...category, ...categoryData, updatedAt: new Date() }\n        : category\n    ));\n  };\n\n  const deleteCategory = (id: string) => {\n    setCategories(prev => prev.filter(category => category.id !== id));\n  };\n\n  const getCategoryById = (id: string): Category | undefined => {\n    return categories.find(category => category.id === id);\n  };\n\n  const getActiveCategories = (): Category[] => {\n    return categories.filter(category => category.isActive);\n  };\n\n  const updateProductCount = (categoryName: string, count: number) => {\n    setCategories(prev => prev.map(category => \n      category.name === categoryName \n        ? { ...category, productCount: count }\n        : category\n    ));\n  };\n\n  const value: CategoryContextType = {\n    categories,\n    addCategory,\n    updateCategory,\n    deleteCategory,\n    getCategoryById,\n    getActiveCategories,\n    updateProductCount,\n    isLoading,\n  };\n\n  return (\n    <CategoryContext.Provider value={value}>\n      {children}\n    </CategoryContext.Provider>\n  );\n}\n\nexport function useCategories() {\n  const context = useContext(CategoryContext);\n  if (context === undefined) {\n    throw new Error('useCategories must be used within a CategoryProvider');\n  }\n  return context;\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAyBA,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAmC;AAMvE,qBAAqB;AACrB,MAAM,oBAAgC;IACpC;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,cAAc;QACd,WAAW,IAAI;QACf,WAAW,IAAI;IACjB;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,cAAc;QACd,WAAW,IAAI;QACf,WAAW,IAAI;IACjB;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,cAAc;QACd,WAAW,IAAI;QACf,WAAW,IAAI;IACjB;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,cAAc;QACd,WAAW,IAAI;QACf,WAAW,IAAI;IACjB;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,cAAc;QACd,WAAW,IAAI;QACf,WAAW,IAAI;IACjB;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,cAAc;QACd,WAAW,IAAI;QACf,WAAW,IAAI;IACjB;CACD;AAEM,SAAS,iBAAiB,EAAE,QAAQ,EAAyB;IAClE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IAC3D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,8DAA8D;IAC9D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,uBAAuB;YAC3B,IAAI;gBACF,MAAM,kBAAkB,aAAa,OAAO,CAAC;gBAC7C,IAAI,iBAAiB;oBACnB,MAAM,mBAAmB,KAAK,KAAK,CAAC;oBACpC,4CAA4C;oBAC5C,MAAM,sBAAsB,iBAAiB,GAAG,CAAC,CAAC,WAAkB,CAAC;4BACnE,GAAG,QAAQ;4BACX,WAAW,IAAI,KAAK,SAAS,SAAS;4BACtC,WAAW,IAAI,KAAK,SAAS,SAAS;wBACxC,CAAC;oBACD,cAAc;gBAChB,OAAO;oBACL,gCAAgC;oBAChC,cAAc;oBACd,aAAa,OAAO,CAAC,wBAAwB,KAAK,SAAS,CAAC;gBAC9D;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,+CAA+C;gBAC7D,cAAc;YAChB,SAAU;gBACR,aAAa;YACf;QACF;QAEA;IACF,GAAG,EAAE;IAEL,kDAAkD;IAClD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,aAAa,WAAW,MAAM,GAAG,GAAG;YACvC,IAAI;gBACF,aAAa,OAAO,CAAC,wBAAwB,KAAK,SAAS,CAAC;YAC9D,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,4CAA4C;YAC5D;QACF;IACF,GAAG;QAAC;QAAY;KAAU;IAE1B,MAAM,cAAc,CAAC;QACnB,MAAM,cAAwB;YAC5B,GAAG,YAAY;YACf,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,cAAc;YACd,WAAW,IAAI;YACf,WAAW,IAAI;QACjB;QACA,cAAc,CAAA,OAAQ;mBAAI;gBAAM;aAAY;IAC9C;IAEA,MAAM,iBAAiB,CAAC,IAAY;QAClC,cAAc,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,WAC7B,SAAS,EAAE,KAAK,KACZ;oBAAE,GAAG,QAAQ;oBAAE,GAAG,YAAY;oBAAE,WAAW,IAAI;gBAAO,IACtD;IAER;IAEA,MAAM,iBAAiB,CAAC;QACtB,cAAc,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,WAAY,SAAS,EAAE,KAAK;IAChE;IAEA,MAAM,kBAAkB,CAAC;QACvB,OAAO,WAAW,IAAI,CAAC,CAAA,WAAY,SAAS,EAAE,KAAK;IACrD;IAEA,MAAM,sBAAsB;QAC1B,OAAO,WAAW,MAAM,CAAC,CAAA,WAAY,SAAS,QAAQ;IACxD;IAEA,MAAM,qBAAqB,CAAC,cAAsB;QAChD,cAAc,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,WAC7B,SAAS,IAAI,KAAK,eACd;oBAAE,GAAG,QAAQ;oBAAE,cAAc;gBAAM,IACnC;IAER;IAEA,MAAM,QAA6B;QACjC;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,8OAAC,gBAAgB,QAAQ;QAAC,OAAO;kBAC9B;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT"}}, {"offset": {"line": 446, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 452, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/New%20folder%20%288%29/arabic-ecommerce/src/contexts/OrderContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';\nimport { Order, OrderStatus } from '@/types';\n\ninterface OrderContextType {\n  orders: Order[];\n  addOrder: (order: Omit<Order, 'id' | 'orderNumber' | 'createdAt' | 'updatedAt'>) => void;\n  updateOrderStatus: (id: string, status: OrderStatus) => void;\n  updateOrder: (id: string, updates: Partial<Order>) => void;\n  deleteOrder: (id: string) => void;\n  getOrderById: (id: string) => Order | undefined;\n  getOrdersByStatus: (status: OrderStatus) => Order[];\n  getOrdersByCustomer: (customerId: string) => Order[];\n  getTotalRevenue: () => number;\n  getOrdersCount: () => number;\n  getRecentOrders: (limit?: number) => Order[];\n  isLoading: boolean;\n}\n\nconst OrderContext = createContext<OrderContextType | undefined>(undefined);\n\ninterface OrderProviderProps {\n  children: ReactNode;\n}\n\n// Sample orders data\nconst sampleOrders: Order[] = [\n  {\n    id: '1',\n    orderNumber: 'ORD-2024-001',\n    customerId: '1',\n    customerName: 'أحمد محمد',\n    customerEmail: '<EMAIL>',\n    items: [\n      {\n        id: '1',\n        productId: '1',\n        productName: 'لابتوب Dell XPS 13',\n        productImage: '/images/laptop.jpg',\n        price: 2500,\n        quantity: 1,\n        total: 2500,\n      },\n      {\n        id: '2',\n        productId: '2',\n        productName: 'سماعات لاسلكية',\n        productImage: '/images/headphones.jpg',\n        price: 150,\n        quantity: 2,\n        total: 300,\n      },\n    ],\n    subtotal: 2800,\n    shipping: 50,\n    tax: 420,\n    total: 3270,\n    status: 'confirmed',\n    paymentMethod: 'card',\n    paymentStatus: 'paid',\n    shippingAddress: {\n      fullName: 'أحمد محمد علي',\n      phone: '+************',\n      address: 'شارع الملك فهد، حي النخيل',\n      city: 'الرياض',\n      postalCode: '12345',\n      country: 'السعودية',\n    },\n    notes: 'يرجى التسليم في المساء',\n    createdAt: new Date('2024-01-15T10:30:00'),\n    updatedAt: new Date('2024-01-15T11:00:00'),\n  },\n  {\n    id: '2',\n    orderNumber: 'ORD-2024-002',\n    customerId: '2',\n    customerName: 'فاطمة أحمد',\n    customerEmail: '<EMAIL>',\n    items: [\n      {\n        id: '3',\n        productId: '3',\n        productName: 'هاتف ذكي Samsung Galaxy',\n        productImage: '/images/phone.jpg',\n        price: 1200,\n        quantity: 1,\n        total: 1200,\n      },\n    ],\n    subtotal: 1200,\n    shipping: 30,\n    tax: 180,\n    total: 1410,\n    status: 'processing',\n    paymentMethod: 'cash',\n    paymentStatus: 'pending',\n    shippingAddress: {\n      fullName: 'فاطمة أحمد محمد',\n      phone: '+966507654321',\n      address: 'طريق الأمير محمد بن عبدالعزيز',\n      city: 'جدة',\n      postalCode: '21589',\n      country: 'السعودية',\n    },\n    createdAt: new Date('2024-01-16T14:20:00'),\n    updatedAt: new Date('2024-01-16T15:45:00'),\n  },\n  {\n    id: '3',\n    orderNumber: 'ORD-2024-003',\n    customerId: '1',\n    customerName: 'أحمد محمد',\n    customerEmail: '<EMAIL>',\n    items: [\n      {\n        id: '4',\n        productId: '4',\n        productName: 'تابلت iPad Pro',\n        productImage: '/images/tablet.jpg',\n        price: 1800,\n        quantity: 1,\n        total: 1800,\n      },\n    ],\n    subtotal: 1800,\n    shipping: 40,\n    tax: 270,\n    total: 2110,\n    status: 'delivered',\n    paymentMethod: 'bank_transfer',\n    paymentStatus: 'paid',\n    shippingAddress: {\n      fullName: 'أحمد محمد علي',\n      phone: '+************',\n      address: 'شارع الملك فهد، حي النخيل',\n      city: 'الرياض',\n      postalCode: '12345',\n      country: 'السعودية',\n    },\n    createdAt: new Date('2024-01-10T09:15:00'),\n    updatedAt: new Date('2024-01-12T16:30:00'),\n    deliveredAt: new Date('2024-01-12T16:30:00'),\n  },\n];\n\nexport function OrderProvider({ children }: OrderProviderProps) {\n  const [orders, setOrders] = useState<Order[]>([]);\n  const [isLoading, setIsLoading] = useState(true);\n\n  // Initialize orders from localStorage or use sample data\n  useEffect(() => {\n    const initializeOrders = () => {\n      try {\n        const savedOrders = localStorage.getItem('ecommerce-orders');\n        if (savedOrders) {\n          const parsedOrders = JSON.parse(savedOrders);\n          // Convert date strings back to Date objects\n          const ordersWithDates = parsedOrders.map((order: any) => ({\n            ...order,\n            createdAt: new Date(order.createdAt),\n            updatedAt: new Date(order.updatedAt),\n            deliveredAt: order.deliveredAt ? new Date(order.deliveredAt) : undefined,\n          }));\n          setOrders(ordersWithDates);\n        } else {\n          // First time - use sample data\n          setOrders(sampleOrders);\n          localStorage.setItem('ecommerce-orders', JSON.stringify(sampleOrders));\n        }\n      } catch (error) {\n        console.error('Error loading orders from localStorage:', error);\n        setOrders(sampleOrders);\n      } finally {\n        setIsLoading(false);\n      }\n    };\n\n    initializeOrders();\n  }, []);\n\n  // Save to localStorage whenever orders change\n  useEffect(() => {\n    if (!isLoading && orders.length > 0) {\n      try {\n        localStorage.setItem('ecommerce-orders', JSON.stringify(orders));\n      } catch (error) {\n        console.error('Error saving orders to localStorage:', error);\n      }\n    }\n  }, [orders, isLoading]);\n\n  const generateOrderNumber = (): string => {\n    const year = new Date().getFullYear();\n    const orderCount = orders.length + 1;\n    return `ORD-${year}-${orderCount.toString().padStart(3, '0')}`;\n  };\n\n  const addOrder = (orderData: Omit<Order, 'id' | 'orderNumber' | 'createdAt' | 'updatedAt'>) => {\n    const newOrder: Order = {\n      ...orderData,\n      id: Date.now().toString(),\n      orderNumber: generateOrderNumber(),\n      createdAt: new Date(),\n      updatedAt: new Date(),\n    };\n    setOrders(prev => [newOrder, ...prev]);\n  };\n\n  const updateOrderStatus = (id: string, status: OrderStatus) => {\n    setOrders(prev => prev.map(order => {\n      if (order.id === id) {\n        const updatedOrder = { \n          ...order, \n          status, \n          updatedAt: new Date() \n        };\n        \n        // Set deliveredAt when status changes to delivered\n        if (status === 'delivered' && !order.deliveredAt) {\n          updatedOrder.deliveredAt = new Date();\n        }\n        \n        return updatedOrder;\n      }\n      return order;\n    }));\n  };\n\n  const updateOrder = (id: string, updates: Partial<Order>) => {\n    setOrders(prev => prev.map(order => \n      order.id === id \n        ? { ...order, ...updates, updatedAt: new Date() }\n        : order\n    ));\n  };\n\n  const deleteOrder = (id: string) => {\n    setOrders(prev => prev.filter(order => order.id !== id));\n  };\n\n  const getOrderById = (id: string): Order | undefined => {\n    return orders.find(order => order.id === id);\n  };\n\n  const getOrdersByStatus = (status: OrderStatus): Order[] => {\n    return orders.filter(order => order.status === status);\n  };\n\n  const getOrdersByCustomer = (customerId: string): Order[] => {\n    return orders.filter(order => order.customerId === customerId);\n  };\n\n  const getTotalRevenue = (): number => {\n    return orders\n      .filter(order => order.paymentStatus === 'paid')\n      .reduce((total, order) => total + order.total, 0);\n  };\n\n  const getOrdersCount = (): number => {\n    return orders.length;\n  };\n\n  const getRecentOrders = (limit: number = 5): Order[] => {\n    return orders\n      .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())\n      .slice(0, limit);\n  };\n\n  const value: OrderContextType = {\n    orders,\n    addOrder,\n    updateOrderStatus,\n    updateOrder,\n    deleteOrder,\n    getOrderById,\n    getOrdersByStatus,\n    getOrdersByCustomer,\n    getTotalRevenue,\n    getOrdersCount,\n    getRecentOrders,\n    isLoading,\n  };\n\n  return (\n    <OrderContext.Provider value={value}>\n      {children}\n    </OrderContext.Provider>\n  );\n}\n\nexport function useOrders() {\n  const context = useContext(OrderContext);\n  if (context === undefined) {\n    throw new Error('useOrders must be used within an OrderProvider');\n  }\n  return context;\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAoBA,MAAM,6BAAe,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAgC;AAMjE,qBAAqB;AACrB,MAAM,eAAwB;IAC5B;QACE,IAAI;QACJ,aAAa;QACb,YAAY;QACZ,cAAc;QACd,eAAe;QACf,OAAO;YACL;gBACE,IAAI;gBACJ,WAAW;gBACX,aAAa;gBACb,cAAc;gBACd,OAAO;gBACP,UAAU;gBACV,OAAO;YACT;YACA;gBACE,IAAI;gBACJ,WAAW;gBACX,aAAa;gBACb,cAAc;gBACd,OAAO;gBACP,UAAU;gBACV,OAAO;YACT;SACD;QACD,UAAU;QACV,UAAU;QACV,KAAK;QACL,OAAO;QACP,QAAQ;QACR,eAAe;QACf,eAAe;QACf,iBAAiB;YACf,UAAU;YACV,OAAO;YACP,SAAS;YACT,MAAM;YACN,YAAY;YACZ,SAAS;QACX;QACA,OAAO;QACP,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,aAAa;QACb,YAAY;QACZ,cAAc;QACd,eAAe;QACf,OAAO;YACL;gBACE,IAAI;gBACJ,WAAW;gBACX,aAAa;gBACb,cAAc;gBACd,OAAO;gBACP,UAAU;gBACV,OAAO;YACT;SACD;QACD,UAAU;QACV,UAAU;QACV,KAAK;QACL,OAAO;QACP,QAAQ;QACR,eAAe;QACf,eAAe;QACf,iBAAiB;YACf,UAAU;YACV,OAAO;YACP,SAAS;YACT,MAAM;YACN,YAAY;YACZ,SAAS;QACX;QACA,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,aAAa;QACb,YAAY;QACZ,cAAc;QACd,eAAe;QACf,OAAO;YACL;gBACE,IAAI;gBACJ,WAAW;gBACX,aAAa;gBACb,cAAc;gBACd,OAAO;gBACP,UAAU;gBACV,OAAO;YACT;SACD;QACD,UAAU;QACV,UAAU;QACV,KAAK;QACL,OAAO;QACP,QAAQ;QACR,eAAe;QACf,eAAe;QACf,iBAAiB;YACf,UAAU;YACV,OAAO;YACP,SAAS;YACT,MAAM;YACN,YAAY;YACZ,SAAS;QACX;QACA,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;QACpB,aAAa,IAAI,KAAK;IACxB;CACD;AAEM,SAAS,cAAc,EAAE,QAAQ,EAAsB;IAC5D,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW,EAAE;IAChD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,yDAAyD;IACzD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,mBAAmB;YACvB,IAAI;gBACF,MAAM,cAAc,aAAa,OAAO,CAAC;gBACzC,IAAI,aAAa;oBACf,MAAM,eAAe,KAAK,KAAK,CAAC;oBAChC,4CAA4C;oBAC5C,MAAM,kBAAkB,aAAa,GAAG,CAAC,CAAC,QAAe,CAAC;4BACxD,GAAG,KAAK;4BACR,WAAW,IAAI,KAAK,MAAM,SAAS;4BACnC,WAAW,IAAI,KAAK,MAAM,SAAS;4BACnC,aAAa,MAAM,WAAW,GAAG,IAAI,KAAK,MAAM,WAAW,IAAI;wBACjE,CAAC;oBACD,UAAU;gBACZ,OAAO;oBACL,+BAA+B;oBAC/B,UAAU;oBACV,aAAa,OAAO,CAAC,oBAAoB,KAAK,SAAS,CAAC;gBAC1D;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,2CAA2C;gBACzD,UAAU;YACZ,SAAU;gBACR,aAAa;YACf;QACF;QAEA;IACF,GAAG,EAAE;IAEL,8CAA8C;IAC9C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,aAAa,OAAO,MAAM,GAAG,GAAG;YACnC,IAAI;gBACF,aAAa,OAAO,CAAC,oBAAoB,KAAK,SAAS,CAAC;YAC1D,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,wCAAwC;YACxD;QACF;IACF,GAAG;QAAC;QAAQ;KAAU;IAEtB,MAAM,sBAAsB;QAC1B,MAAM,OAAO,IAAI,OAAO,WAAW;QACnC,MAAM,aAAa,OAAO,MAAM,GAAG;QACnC,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,WAAW,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;IAChE;IAEA,MAAM,WAAW,CAAC;QAChB,MAAM,WAAkB;YACtB,GAAG,SAAS;YACZ,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,aAAa;YACb,WAAW,IAAI;YACf,WAAW,IAAI;QACjB;QACA,UAAU,CAAA,OAAQ;gBAAC;mBAAa;aAAK;IACvC;IAEA,MAAM,oBAAoB,CAAC,IAAY;QACrC,UAAU,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA;gBACzB,IAAI,MAAM,EAAE,KAAK,IAAI;oBACnB,MAAM,eAAe;wBACnB,GAAG,KAAK;wBACR;wBACA,WAAW,IAAI;oBACjB;oBAEA,mDAAmD;oBACnD,IAAI,WAAW,eAAe,CAAC,MAAM,WAAW,EAAE;wBAChD,aAAa,WAAW,GAAG,IAAI;oBACjC;oBAEA,OAAO;gBACT;gBACA,OAAO;YACT;IACF;IAEA,MAAM,cAAc,CAAC,IAAY;QAC/B,UAAU,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,QACzB,MAAM,EAAE,KAAK,KACT;oBAAE,GAAG,KAAK;oBAAE,GAAG,OAAO;oBAAE,WAAW,IAAI;gBAAO,IAC9C;IAER;IAEA,MAAM,cAAc,CAAC;QACnB,UAAU,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;IACtD;IAEA,MAAM,eAAe,CAAC;QACpB,OAAO,OAAO,IAAI,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;IAC3C;IAEA,MAAM,oBAAoB,CAAC;QACzB,OAAO,OAAO,MAAM,CAAC,CAAA,QAAS,MAAM,MAAM,KAAK;IACjD;IAEA,MAAM,sBAAsB,CAAC;QAC3B,OAAO,OAAO,MAAM,CAAC,CAAA,QAAS,MAAM,UAAU,KAAK;IACrD;IAEA,MAAM,kBAAkB;QACtB,OAAO,OACJ,MAAM,CAAC,CAAA,QAAS,MAAM,aAAa,KAAK,QACxC,MAAM,CAAC,CAAC,OAAO,QAAU,QAAQ,MAAM,KAAK,EAAE;IACnD;IAEA,MAAM,iBAAiB;QACrB,OAAO,OAAO,MAAM;IACtB;IAEA,MAAM,kBAAkB,CAAC,QAAgB,CAAC;QACxC,OAAO,OACJ,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,SAAS,CAAC,OAAO,KAAK,EAAE,SAAS,CAAC,OAAO,IAC1D,KAAK,CAAC,GAAG;IACd;IAEA,MAAM,QAA0B;QAC9B;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,8OAAC,aAAa,QAAQ;QAAC,OAAO;kBAC3B;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT"}}, {"offset": {"line": 719, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 729, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/New%20folder%20%288%29/arabic-ecommerce/node_modules/next/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n    } else if (process.env.TURBOPACK) {\n      module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n    } else {\n      module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n    } else if (process.env.TURBOPACK) {\n      module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n    } else {\n      module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": ";AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAQzC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1CJ,OAAOC,OAAO,GAAGC,QAAQ;QAC3B,OAAO,IAAIL,QAAQC,GAAG,CAACO,SAAS,EAAE;;QAIlC;IACF;AACF", "ignoreList": [0]}}, {"offset": {"line": 743, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 748, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/New%20folder%20%288%29/arabic-ecommerce/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-ssr'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,0HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0]}}, {"offset": {"line": 750, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 755, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/New%20folder%20%288%29/arabic-ecommerce/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored['react-ssr'].React\n"], "names": ["module", "exports", "require", "vendored", "React"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,0HAAyBC,QAAQ,CAAC,YAAY,CAACC,KAAK", "ignoreList": [0]}}, {"offset": {"line": 757, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}