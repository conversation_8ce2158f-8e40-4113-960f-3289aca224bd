'use client';

import Link from 'next/link';
import { useState } from 'react';

export default function Navbar() {
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [cartItemsCount, setCartItemsCount] = useState(0);

  return (
    <nav className="bg-white shadow-lg border-b border-gray-200">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <div className="flex-shrink-0">
            <Link href="/" className="text-2xl font-bold text-primary-600">
              🛍️ المتجر العربي
            </Link>
          </div>

          {/* Navigation Links */}
          <div className="hidden md:block">
            <div className="mr-10 flex items-baseline space-x-4 space-x-reverse">
              <Link
                href="/"
                className="text-gray-700 hover:text-primary-600 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 flex items-center"
              >
                🏠 الصفحة الرئيسية
              </Link>
              <Link
                href="/products"
                className="text-gray-700 hover:text-primary-600 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 flex items-center"
              >
                📦 المنتجات
              </Link>
              {isLoggedIn && (
                <Link
                  href="/admin"
                  className="text-gray-700 hover:text-primary-600 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 flex items-center"
                >
                  ⚙️ لوحة التحكم
                </Link>
              )}
            </div>
          </div>

          {/* Right side - Cart and Auth */}
          <div className="flex items-center space-x-4 space-x-reverse">
            {/* Cart */}
            <Link
              href="/cart"
              className="relative text-gray-700 hover:text-primary-600 transition-colors duration-200"
            >
              <span className="text-2xl">🛒</span>
              {cartItemsCount > 0 && (
                <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                  {cartItemsCount}
                </span>
              )}
            </Link>

            {/* Auth Links */}
            {isLoggedIn ? (
              <div className="flex items-center space-x-2 space-x-reverse">
                <span className="text-gray-700 text-sm">مرحباً، أحمد</span>
                <button
                  onClick={() => setIsLoggedIn(false)}
                  className="bg-red-500 hover:bg-red-600 text-white px-3 py-1 rounded text-sm transition-colors duration-200"
                >
                  تسجيل الخروج
                </button>
              </div>
            ) : (
              <div className="flex items-center space-x-2 space-x-reverse">
                <Link
                  href="/login"
                  className="text-gray-700 hover:text-primary-600 px-3 py-1 rounded text-sm transition-colors duration-200"
                >
                  تسجيل الدخول
                </Link>
                <Link
                  href="/register"
                  className="bg-primary-600 hover:bg-primary-700 text-white px-3 py-1 rounded text-sm transition-colors duration-200"
                >
                  إنشاء حساب
                </Link>
              </div>
            )}
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <button
              type="button"
              className="text-gray-700 hover:text-primary-600 focus:outline-none focus:text-primary-600"
            >
              <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </nav>
  );
}
