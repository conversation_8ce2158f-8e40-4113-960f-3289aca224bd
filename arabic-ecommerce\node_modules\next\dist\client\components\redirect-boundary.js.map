{"version": 3, "sources": ["../../../src/client/components/redirect-boundary.tsx"], "sourcesContent": ["'use client'\nimport React, { useEffect } from 'react'\nimport type { AppRouterInstance } from '../../shared/lib/app-router-context.shared-runtime'\nimport { useRouter } from './navigation'\nimport { getRedirectTypeFromError, getURLFromRedirectError } from './redirect'\nimport { RedirectType, isRedirectError } from './redirect-error'\n\ninterface RedirectBoundaryProps {\n  router: AppRouterInstance\n  children: React.ReactNode\n}\n\nfunction HandleRedirect({\n  redirect,\n  reset,\n  redirectType,\n}: {\n  redirect: string\n  redirectType: RedirectType\n  reset: () => void\n}) {\n  const router = useRouter()\n\n  useEffect(() => {\n    React.startTransition(() => {\n      if (redirectType === RedirectType.push) {\n        router.push(redirect, {})\n      } else {\n        router.replace(redirect, {})\n      }\n      reset()\n    })\n  }, [redirect, redirectType, reset, router])\n\n  return null\n}\n\nexport class RedirectErrorBoundary extends React.Component<\n  RedirectBoundaryProps,\n  { redirect: string | null; redirectType: RedirectType | null }\n> {\n  constructor(props: RedirectBoundaryProps) {\n    super(props)\n    this.state = { redirect: null, redirectType: null }\n  }\n\n  static getDerivedStateFromError(error: any) {\n    if (isRedirectError(error)) {\n      const url = getURLFromRedirectError(error)\n      const redirectType = getRedirectTypeFromError(error)\n      return { redirect: url, redirectType }\n    }\n    // Re-throw if error is not for redirect\n    throw error\n  }\n\n  // Explicit type is needed to avoid the generated `.d.ts` having a wide return type that could be specific to the `@types/react` version.\n  render(): React.ReactNode {\n    const { redirect, redirectType } = this.state\n    if (redirect !== null && redirectType !== null) {\n      return (\n        <HandleRedirect\n          redirect={redirect}\n          redirectType={redirectType}\n          reset={() => this.setState({ redirect: null })}\n        />\n      )\n    }\n\n    return this.props.children\n  }\n}\n\nexport function RedirectBoundary({ children }: { children: React.ReactNode }) {\n  const router = useRouter()\n  return (\n    <RedirectErrorBoundary router={router}>{children}</RedirectErrorBoundary>\n  )\n}\n"], "names": ["RedirectBoundary", "RedirectErrorBoundary", "HandleRedirect", "redirect", "reset", "redirectType", "router", "useRouter", "useEffect", "React", "startTransition", "RedirectType", "push", "replace", "Component", "getDerivedStateFromError", "error", "isRedirectError", "url", "getURLFromRedirectError", "getRedirectTypeFromError", "render", "state", "setState", "props", "children", "constructor"], "mappings": "AAAA;;;;;;;;;;;;;;;;IAyEgBA,gBAAgB;eAAhBA;;IApCHC,qBAAqB;eAArBA;;;;;iEApCoB;4BAEP;0BACwC;+BACpB;AAO9C,SAASC,eAAe,KAQvB;IARuB,IAAA,EACtBC,QAAQ,EACRC,KAAK,EACLC,YAAY,EAKb,GARuB;IAStB,MAAMC,SAASC,IAAAA,qBAAS;IAExBC,IAAAA,gBAAS,EAAC;QACRC,cAAK,CAACC,eAAe,CAAC;YACpB,IAAIL,iBAAiBM,2BAAY,CAACC,IAAI,EAAE;gBACtCN,OAAOM,IAAI,CAACT,UAAU,CAAC;YACzB,OAAO;gBACLG,OAAOO,OAAO,CAACV,UAAU,CAAC;YAC5B;YACAC;QACF;IACF,GAAG;QAACD;QAAUE;QAAcD;QAAOE;KAAO;IAE1C,OAAO;AACT;AAEO,MAAML,8BAA8BQ,cAAK,CAACK,SAAS;IASxD,OAAOC,yBAAyBC,KAAU,EAAE;QAC1C,IAAIC,IAAAA,8BAAe,EAACD,QAAQ;YAC1B,MAAME,MAAMC,IAAAA,iCAAuB,EAACH;YACpC,MAAMX,eAAee,IAAAA,kCAAwB,EAACJ;YAC9C,OAAO;gBAAEb,UAAUe;gBAAKb;YAAa;QACvC;QACA,wCAAwC;QACxC,MAAMW;IACR;IAEA,yIAAyI;IACzIK,SAA0B;QACxB,MAAM,EAAElB,QAAQ,EAAEE,YAAY,EAAE,GAAG,IAAI,CAACiB,KAAK;QAC7C,IAAInB,aAAa,QAAQE,iBAAiB,MAAM;YAC9C,qBACE,qBAACH;gBACCC,UAAUA;gBACVE,cAAcA;gBACdD,OAAO,IAAM,IAAI,CAACmB,QAAQ,CAAC;wBAAEpB,UAAU;oBAAK;;QAGlD;QAEA,OAAO,IAAI,CAACqB,KAAK,CAACC,QAAQ;IAC5B;IA7BAC,YAAYF,KAA4B,CAAE;QACxC,KAAK,CAACA;QACN,IAAI,CAACF,KAAK,GAAG;YAAEnB,UAAU;YAAME,cAAc;QAAK;IACpD;AA2BF;AAEO,SAASL,iBAAiB,KAA2C;IAA3C,IAAA,EAAEyB,QAAQ,EAAiC,GAA3C;IAC/B,MAAMnB,SAASC,IAAAA,qBAAS;IACxB,qBACE,qBAACN;QAAsBK,QAAQA;kBAASmB;;AAE5C"}