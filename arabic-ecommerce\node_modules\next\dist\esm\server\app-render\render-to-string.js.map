{"version": 3, "sources": ["../../../src/server/app-render/render-to-string.tsx"], "sourcesContent": ["import { streamToString } from '../stream-utils/node-web-streams-helper'\nimport { AppRenderSpan } from '../lib/trace/constants'\nimport { getTracer } from '../lib/trace/tracer'\n\nexport async function renderToString({\n  ReactDOMServer,\n  element,\n}: {\n  ReactDOMServer: typeof import('react-dom/server.edge')\n  element: React.ReactElement\n}) {\n  return getTracer().trace(AppRenderSpan.renderToString, async () => {\n    const renderStream = await ReactDOMServer.renderToReadableStream(element)\n    await renderStream.allReady\n    return streamToString(renderStream)\n  })\n}\n"], "names": ["streamToString", "AppRenderSpan", "getTracer", "renderToString", "ReactDOMServer", "element", "trace", "renderStream", "renderToReadableStream", "allReady"], "mappings": "AAAA,SAASA,cAAc,QAAQ,0CAAyC;AACxE,SAASC,aAAa,QAAQ,yBAAwB;AACtD,SAASC,SAAS,QAAQ,sBAAqB;AAE/C,OAAO,eAAeC,eAAe,EACnCC,cAAc,EACdC,OAAO,EAIR;IACC,OAAOH,YAAYI,KAAK,CAACL,cAAcE,cAAc,EAAE;QACrD,MAAMI,eAAe,MAAMH,eAAeI,sBAAsB,CAACH;QACjE,MAAME,aAAaE,QAAQ;QAC3B,OAAOT,eAAeO;IACxB;AACF"}