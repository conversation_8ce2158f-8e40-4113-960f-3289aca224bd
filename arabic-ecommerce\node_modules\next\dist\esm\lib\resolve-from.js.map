{"version": 3, "sources": ["../../src/lib/resolve-from.ts"], "sourcesContent": ["// source: https://github.com/sindresorhus/resolve-from\nimport path from 'path'\nimport isError from './is-error'\nimport { realpathSync } from './realpath'\n\nconst Module = require('module')\n\nexport const resolveFrom = (\n  fromDirectory: string,\n  moduleId: string,\n  silent?: boolean\n) => {\n  if (typeof fromDirectory !== 'string') {\n    throw new TypeError(\n      `Expected \\`fromDir\\` to be of type \\`string\\`, got \\`${typeof fromDirectory}\\``\n    )\n  }\n\n  if (typeof moduleId !== 'string') {\n    throw new TypeError(\n      `Expected \\`moduleId\\` to be of type \\`string\\`, got \\`${typeof moduleId}\\``\n    )\n  }\n\n  try {\n    fromDirectory = realpathSync(fromDirectory)\n  } catch (error: unknown) {\n    if (isError(error) && error.code === 'ENOENT') {\n      fromDirectory = path.resolve(fromDirectory)\n    } else if (silent) {\n      return\n    } else {\n      throw error\n    }\n  }\n\n  const fromFile = path.join(fromDirectory, 'noop.js')\n\n  const resolveFileName = () =>\n    Module._resolveFilename(moduleId, {\n      id: fromFile,\n      filename: fromFile,\n      paths: Module._nodeModulePaths(fromDirectory),\n    })\n\n  if (silent) {\n    try {\n      return resolveFileName()\n    } catch (error) {\n      return\n    }\n  }\n\n  return resolveFileName()\n}\n"], "names": ["path", "isError", "realpathSync", "<PERSON><PERSON><PERSON>", "require", "resolveFrom", "fromDirectory", "moduleId", "silent", "TypeError", "error", "code", "resolve", "fromFile", "join", "resolveFileName", "_resolveFilename", "id", "filename", "paths", "_nodeModulePaths"], "mappings": "AAAA,uDAAuD;AACvD,OAAOA,UAAU,OAAM;AACvB,OAAOC,aAAa,aAAY;AAChC,SAASC,YAAY,QAAQ,aAAY;AAEzC,MAAMC,SAASC,QAAQ;AAEvB,OAAO,MAAMC,cAAc,CACzBC,eACAC,UACAC;IAEA,IAAI,OAAOF,kBAAkB,UAAU;QACrC,MAAM,IAAIG,UACR,CAAC,qDAAqD,EAAE,OAAOH,cAAc,EAAE,CAAC;IAEpF;IAEA,IAAI,OAAOC,aAAa,UAAU;QAChC,MAAM,IAAIE,UACR,CAAC,sDAAsD,EAAE,OAAOF,SAAS,EAAE,CAAC;IAEhF;IAEA,IAAI;QACFD,gBAAgBJ,aAAaI;IAC/B,EAAE,OAAOI,OAAgB;QACvB,IAAIT,QAAQS,UAAUA,MAAMC,IAAI,KAAK,UAAU;YAC7CL,gBAAgBN,KAAKY,OAAO,CAACN;QAC/B,OAAO,IAAIE,QAAQ;YACjB;QACF,OAAO;YACL,MAAME;QACR;IACF;IAEA,MAAMG,WAAWb,KAAKc,IAAI,CAACR,eAAe;IAE1C,MAAMS,kBAAkB,IACtBZ,OAAOa,gBAAgB,CAACT,UAAU;YAChCU,IAAIJ;YACJK,UAAUL;YACVM,OAAOhB,OAAOiB,gBAAgB,CAACd;QACjC;IAEF,IAAIE,QAAQ;QACV,IAAI;YACF,OAAOO;QACT,EAAE,OAAOL,OAAO;YACd;QACF;IACF;IAEA,OAAOK;AACT,EAAC"}