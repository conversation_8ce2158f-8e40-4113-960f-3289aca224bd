{"version": 3, "sources": ["../../../../../src/client/components/react-dev-overlay/app/ReactDevOverlay.tsx"], "sourcesContent": ["import React from 'react'\nimport type { OverlayState } from '../shared'\nimport { ShadowPortal } from '../internal/components/ShadowPortal'\nimport { BuildError } from '../internal/container/BuildError'\nimport { Errors } from '../internal/container/Errors'\nimport { StaticIndicator } from '../internal/container/StaticIndicator'\nimport { Base } from '../internal/styles/Base'\nimport { ComponentStyles } from '../internal/styles/ComponentStyles'\nimport { CssReset } from '../internal/styles/CssReset'\nimport { RootLayoutMissingTagsError } from '../internal/container/root-layout-missing-tags-error'\nimport type { Dispatcher } from './hot-reloader-client'\nimport { RuntimeErrorHandler } from '../internal/helpers/runtime-error-handler'\n\ninterface ReactDevOverlayState {\n  isReactError: boolean\n}\nexport default class ReactDevOverlay extends React.PureComponent<\n  {\n    state: OverlayState\n    dispatcher?: Dispatcher\n    children: React.ReactNode\n  },\n  ReactDevOverlayState\n> {\n  state = { isReactError: false }\n\n  static getDerivedStateFromError(error: Error): ReactDevOverlayState {\n    if (!error.stack) return { isReactError: false }\n\n    RuntimeErrorHandler.hadRuntimeError = true\n    return {\n      isReactError: true,\n    }\n  }\n\n  render() {\n    const { state, children, dispatcher } = this.props\n    const { isReactError } = this.state\n\n    const hasBuildError = state.buildError != null\n    const hasRuntimeErrors = Boolean(state.errors.length)\n    const hasStaticIndicator = state.staticIndicator\n    const debugInfo = state.debugInfo\n\n    return (\n      <>\n        {isReactError ? (\n          <html>\n            <head></head>\n            <body></body>\n          </html>\n        ) : (\n          children\n        )}\n        <ShadowPortal>\n          <CssReset />\n          <Base />\n          <ComponentStyles />\n          {state.rootLayoutMissingTags?.length ? (\n            <RootLayoutMissingTagsError\n              missingTags={state.rootLayoutMissingTags}\n            />\n          ) : hasBuildError ? (\n            <BuildError\n              message={state.buildError!}\n              versionInfo={state.versionInfo}\n            />\n          ) : (\n            <>\n              {hasRuntimeErrors ? (\n                <Errors\n                  isAppDir={true}\n                  initialDisplayState={\n                    isReactError ? 'fullscreen' : 'minimized'\n                  }\n                  errors={state.errors}\n                  versionInfo={state.versionInfo}\n                  hasStaticIndicator={hasStaticIndicator}\n                  debugInfo={debugInfo}\n                />\n              ) : null}\n\n              {hasStaticIndicator && (\n                <StaticIndicator dispatcher={dispatcher} />\n              )}\n            </>\n          )}\n        </ShadowPortal>\n      </>\n    )\n  }\n}\n"], "names": ["React", "ShadowPort<PERSON>", "BuildError", "Errors", "StaticIndicator", "Base", "ComponentStyles", "CssReset", "RootLayoutMissingTagsError", "RuntimeError<PERSON>andler", "ReactDevOverlay", "PureComponent", "getDerivedStateFromError", "error", "stack", "isReactError", "hadRuntimeError", "render", "state", "children", "dispatcher", "props", "hasBuildError", "buildError", "hasRuntimeErrors", "Boolean", "errors", "length", "hasStaticIndicator", "staticIndicator", "debugInfo", "html", "head", "body", "rootLayoutMissingTags", "missingTags", "message", "versionInfo", "isAppDir", "initialDisplayState"], "mappings": ";AAAA,OAAOA,WAAW,QAAO;AAEzB,SAASC,YAAY,QAAQ,sCAAqC;AAClE,SAASC,UAAU,QAAQ,mCAAkC;AAC7D,SAASC,MAAM,QAAQ,+BAA8B;AACrD,SAASC,eAAe,QAAQ,wCAAuC;AACvE,SAASC,IAAI,QAAQ,0BAAyB;AAC9C,SAASC,eAAe,QAAQ,qCAAoC;AACpE,SAASC,QAAQ,QAAQ,8BAA6B;AACtD,SAASC,0BAA0B,QAAQ,uDAAsD;AAEjG,SAASC,mBAAmB,QAAQ,4CAA2C;AAKhE,MAAMC,wBAAwBV,MAAMW,aAAa;IAU9D,OAAOC,yBAAyBC,KAAY,EAAwB;QAClE,IAAI,CAACA,MAAMC,KAAK,EAAE,OAAO;YAAEC,cAAc;QAAM;QAE/CN,oBAAoBO,eAAe,GAAG;QACtC,OAAO;YACLD,cAAc;QAChB;IACF;IAEAE,SAAS;YAuBAC;QAtBP,MAAM,EAAEA,KAAK,EAAEC,QAAQ,EAAEC,UAAU,EAAE,GAAG,IAAI,CAACC,KAAK;QAClD,MAAM,EAAEN,YAAY,EAAE,GAAG,IAAI,CAACG,KAAK;QAEnC,MAAMI,gBAAgBJ,MAAMK,UAAU,IAAI;QAC1C,MAAMC,mBAAmBC,QAAQP,MAAMQ,MAAM,CAACC,MAAM;QACpD,MAAMC,qBAAqBV,MAAMW,eAAe;QAChD,MAAMC,YAAYZ,MAAMY,SAAS;QAEjC,qBACE;;gBACGf,6BACC,MAACgB;;sCACC,KAACC;sCACD,KAACC;;qBAGHd;8BAEF,MAAClB;;sCACC,KAACM;sCACD,KAACF;sCACD,KAACC;wBACAY,EAAAA,+BAAAA,MAAMgB,qBAAqB,qBAA3BhB,6BAA6BS,MAAM,kBAClC,KAACnB;4BACC2B,aAAajB,MAAMgB,qBAAqB;6BAExCZ,8BACF,KAACpB;4BACCkC,SAASlB,MAAMK,UAAU;4BACzBc,aAAanB,MAAMmB,WAAW;2CAGhC;;gCACGb,iCACC,KAACrB;oCACCmC,UAAU;oCACVC,qBACExB,eAAe,eAAe;oCAEhCW,QAAQR,MAAMQ,MAAM;oCACpBW,aAAanB,MAAMmB,WAAW;oCAC9BT,oBAAoBA;oCACpBE,WAAWA;qCAEX;gCAEHF,oCACC,KAACxB;oCAAgBgB,YAAYA;;;;;;;;IAO3C;;QA1Ea,qBAQbF,QAAQ;YAAEH,cAAc;QAAM;;AAmEhC;AA3EA,SAAqBL,6BA2EpB"}