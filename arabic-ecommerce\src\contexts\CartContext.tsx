'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { Product } from '@/types';

export interface CartItem {
  id: string;
  product: Product;
  quantity: number;
  selectedSize?: string;
  selectedColor?: string;
}

interface CartContextType {
  items: CartItem[];
  totalItems: number;
  totalPrice: number;
  addToCart: (product: Product, quantity?: number, options?: { size?: string; color?: string }) => void;
  removeFromCart: (itemId: string) => void;
  updateQuantity: (itemId: string, quantity: number) => void;
  clearCart: () => void;
  isInCart: (productId: string) => boolean;
  getCartItem: (productId: string) => CartItem | undefined;
}

const CartContext = createContext<CartContextType | undefined>(undefined);

interface CartProviderProps {
  children: ReactNode;
}

export function CartProvider({ children }: CartProviderProps) {
  const [items, setItems] = useState<CartItem[]>([]);

  // تحميل السلة من localStorage عند بدء التطبيق
  useEffect(() => {
    const savedCart = localStorage.getItem('cart');
    if (savedCart) {
      try {
        setItems(JSON.parse(savedCart));
      } catch (error) {
        console.error('Error loading cart from localStorage:', error);
      }
    }
  }, []);

  // حفظ السلة في localStorage عند تغييرها
  useEffect(() => {
    localStorage.setItem('cart', JSON.stringify(items));
  }, [items]);

  // حساب إجمالي العناصر
  const totalItems = items.reduce((total, item) => total + item.quantity, 0);

  // حساب إجمالي السعر
  const totalPrice = items.reduce((total, item) => total + (item.product.price * item.quantity), 0);

  // إضافة منتج للسلة
  const addToCart = (
    product: Product, 
    quantity: number = 1, 
    options?: { size?: string; color?: string }
  ) => {
    setItems(currentItems => {
      const existingItemIndex = currentItems.findIndex(
        item => item.product.id === product.id && 
                item.selectedSize === options?.size && 
                item.selectedColor === options?.color
      );

      if (existingItemIndex > -1) {
        // إذا كان المنتج موجود، زيادة الكمية
        const updatedItems = [...currentItems];
        updatedItems[existingItemIndex].quantity += quantity;
        return updatedItems;
      } else {
        // إضافة منتج جديد
        const newItem: CartItem = {
          id: `${product.id}-${options?.size || ''}-${options?.color || ''}-${Date.now()}`,
          product,
          quantity,
          selectedSize: options?.size,
          selectedColor: options?.color,
        };
        return [...currentItems, newItem];
      }
    });
  };

  // إزالة منتج من السلة
  const removeFromCart = (itemId: string) => {
    setItems(currentItems => currentItems.filter(item => item.id !== itemId));
  };

  // تحديث كمية منتج
  const updateQuantity = (itemId: string, quantity: number) => {
    if (quantity <= 0) {
      removeFromCart(itemId);
      return;
    }

    setItems(currentItems =>
      currentItems.map(item =>
        item.id === itemId ? { ...item, quantity } : item
      )
    );
  };

  // مسح السلة
  const clearCart = () => {
    setItems([]);
  };

  // التحقق من وجود منتج في السلة
  const isInCart = (productId: string) => {
    return items.some(item => item.product.id === productId);
  };

  // الحصول على عنصر من السلة
  const getCartItem = (productId: string) => {
    return items.find(item => item.product.id === productId);
  };

  const value: CartContextType = {
    items,
    totalItems,
    totalPrice,
    addToCart,
    removeFromCart,
    updateQuantity,
    clearCart,
    isInCart,
    getCartItem,
  };

  return (
    <CartContext.Provider value={value}>
      {children}
    </CartContext.Provider>
  );
}

export function useCart() {
  const context = useContext(CartContext);
  if (context === undefined) {
    throw new Error('useCart must be used within a CartProvider');
  }
  return context;
}
