'use client';

import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { User, Session, AuthError } from '@supabase/supabase-js';
import { supabase } from '@/lib/supabase';
import { usersService } from '@/services/database';
import { User as AppUser } from '@/types';
import { isDatabaseConfigured } from '@/lib/config';

interface AuthContextType {
  user: User | null;
  appUser: AppUser | null;
  session: Session | null;
  loading: boolean;
  signUp: (email: string, password: string, userData: { name: string; phone?: string }) => Promise<{ error: AuthError | null }>;
  signIn: (email: string, password: string) => Promise<{ error: AuthError | null }>;
  signOut: () => Promise<{ error: AuthError | null }>;
  resetPassword: (email: string) => Promise<{ error: AuthError | null }>;
  updateProfile: (updates: Partial<AppUser>) => Promise<{ error: Error | null }>;
  isAdmin: boolean;
  isAuthenticated: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<User | null>(null);
  const [appUser, setAppUser] = useState<AppUser | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [loading, setLoading] = useState(true);

  // تحميل بيانات المستخدم من قاعدة البيانات
  const loadAppUser = async (userId: string) => {
    // إذا لم تكن قاعدة البيانات مُعدة، إنشاء مستخدم وهمي
    if (!isDatabaseConfigured()) {
      setAppUser({
        id: userId,
        name: 'مستخدم تجريبي',
        email: '<EMAIL>',
        role: 'customer',
        isActive: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      });
      return;
    }

    try {
      const userData = await usersService.getById(userId);
      setAppUser(userData);

      // تحديث آخر تسجيل دخول
      if (userData) {
        await usersService.updateLastLogin(userId);
      }
    } catch (error) {
      console.error('خطأ في تحميل بيانات المستخدم:', error);
      // في حالة الخطأ، إنشاء مستخدم وهمي
      setAppUser({
        id: userId,
        name: 'مستخدم',
        email: '<EMAIL>',
        role: 'customer',
        isActive: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      });
    }
  };

  // مراقبة تغييرات المصادقة
  useEffect(() => {
    // إذا لم تكن قاعدة البيانات مُعدة، استخدم نظام وهمي
    if (!isDatabaseConfigured()) {
      setLoading(false);
      return;
    }

    // الحصول على الجلسة الحالية
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session);
      setUser(session?.user ?? null);

      if (session?.user) {
        loadAppUser(session.user.id);
      }

      setLoading(false);
    }).catch((error) => {
      console.error('خطأ في الحصول على الجلسة:', error);
      setLoading(false);
    });

    // الاستماع لتغييرات المصادقة
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        setSession(session);
        setUser(session?.user ?? null);

        if (session?.user) {
          await loadAppUser(session.user.id);
        } else {
          setAppUser(null);
        }

        setLoading(false);
      }
    );

    return () => subscription.unsubscribe();
  }, []);

  // تسجيل حساب جديد
  const signUp = async (
    email: string,
    password: string,
    userData: { name: string; phone?: string }
  ) => {
    // إذا لم تكن قاعدة البيانات مُعدة، إرجاع خطأ
    if (!isDatabaseConfigured()) {
      return { error: { message: 'نظام المصادقة غير متاح حالياً. يرجى المحاولة لاحقاً.' } as AuthError };
    }

    try {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            name: userData.name,
            phone: userData.phone,
          }
        }
      });

      if (error) return { error };

      // إنشاء سجل المستخدم في قاعدة البيانات
      if (data.user) {
        try {
          await usersService.create({
            id: data.user.id,
            name: userData.name,
            email: email,
            phone: userData.phone,
            role: 'customer',
            isActive: true,
          });
        } catch (dbError) {
          console.error('خطأ في إنشاء سجل المستخدم:', dbError);
        }
      }

      return { error: null };
    } catch (error) {
      return { error: error as AuthError };
    }
  };

  // تسجيل الدخول
  const signIn = async (email: string, password: string) => {
    // إذا لم تكن قاعدة البيانات مُعدة، إرجاع خطأ
    if (!isDatabaseConfigured()) {
      return { error: { message: 'نظام المصادقة غير متاح حالياً. يرجى المحاولة لاحقاً.' } as AuthError };
    }

    try {
      const { error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      return { error };
    } catch (error) {
      return { error: error as AuthError };
    }
  };

  // تسجيل الخروج
  const signOut = async () => {
    try {
      const { error } = await supabase.auth.signOut();

      if (!error) {
        setUser(null);
        setAppUser(null);
        setSession(null);
      }

      return { error };
    } catch (error) {
      return { error: error as AuthError };
    }
  };

  // إعادة تعيين كلمة المرور
  const resetPassword = async (email: string) => {
    try {
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/auth/reset-password`,
      });

      return { error };
    } catch (error) {
      return { error: error as AuthError };
    }
  };

  // تحديث الملف الشخصي
  const updateProfile = async (updates: Partial<AppUser>) => {
    if (!user || !appUser) {
      return { error: new Error('المستخدم غير مسجل الدخول') };
    }

    try {
      // تحديث بيانات المصادقة إذا لزم الأمر
      if (updates.email && updates.email !== user.email) {
        const { error: authError } = await supabase.auth.updateUser({
          email: updates.email
        });

        if (authError) {
          return { error: new Error(authError.message) };
        }
      }

      // تحديث بيانات التطبيق
      const updatedUser = await usersService.update(user.id, updates);
      setAppUser(updatedUser);

      return { error: null };
    } catch (error) {
      return { error: error as Error };
    }
  };

  const isAdmin = appUser?.role === 'admin';
  const isAuthenticated = !!user;

  const value: AuthContextType = {
    user,
    appUser,
    session,
    loading,
    signUp,
    signIn,
    signOut,
    resetPassword,
    updateProfile,
    isAdmin,
    isAuthenticated,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

// Hook للتحقق من الصلاحيات
export function useRequireAuth(redirectTo = '/auth/login') {
  const { isAuthenticated, loading } = useAuth();

  useEffect(() => {
    if (!loading && !isAuthenticated) {
      window.location.href = redirectTo;
    }
  }, [isAuthenticated, loading, redirectTo]);

  return { isAuthenticated, loading };
}

// Hook للتحقق من صلاحيات المدير
export function useRequireAdmin(redirectTo = '/') {
  const { isAdmin, loading, isAuthenticated } = useAuth();

  useEffect(() => {
    if (!loading && (!isAuthenticated || !isAdmin)) {
      window.location.href = redirectTo;
    }
  }, [isAdmin, loading, isAuthenticated, redirectTo]);

  return { isAdmin, loading };
}
