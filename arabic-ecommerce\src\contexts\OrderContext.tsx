'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { Order, OrderStatus } from '@/types';

interface OrderContextType {
  orders: Order[];
  addOrder: (order: Omit<Order, 'id' | 'orderNumber' | 'createdAt' | 'updatedAt'>) => void;
  updateOrderStatus: (id: string, status: OrderStatus) => void;
  updateOrder: (id: string, updates: Partial<Order>) => void;
  deleteOrder: (id: string) => void;
  getOrderById: (id: string) => Order | undefined;
  getOrdersByStatus: (status: OrderStatus) => Order[];
  getOrdersByCustomer: (customerId: string) => Order[];
  getTotalRevenue: () => number;
  getOrdersCount: () => number;
  getRecentOrders: (limit?: number) => Order[];
  isLoading: boolean;
}

const OrderContext = createContext<OrderContextType | undefined>(undefined);

interface OrderProviderProps {
  children: ReactNode;
}

// Sample orders data
const sampleOrders: Order[] = [
  {
    id: '1',
    orderNumber: 'ORD-2024-001',
    customerId: '1',
    customerName: 'أحمد محمد',
    customerEmail: '<EMAIL>',
    items: [
      {
        id: '1',
        productId: '1',
        productName: 'لابتوب Dell XPS 13',
        productImage: '/images/laptop.jpg',
        price: 2500,
        quantity: 1,
        total: 2500,
      },
      {
        id: '2',
        productId: '2',
        productName: 'سماعات لاسلكية',
        productImage: '/images/headphones.jpg',
        price: 150,
        quantity: 2,
        total: 300,
      },
    ],
    subtotal: 2800,
    shipping: 50,
    tax: 420,
    total: 3270,
    status: 'confirmed',
    paymentMethod: 'card',
    paymentStatus: 'paid',
    shippingAddress: {
      fullName: 'أحمد محمد علي',
      phone: '+************',
      address: 'شارع الملك فهد، حي النخيل',
      city: 'الرياض',
      postalCode: '12345',
      country: 'السعودية',
    },
    notes: 'يرجى التسليم في المساء',
    createdAt: new Date('2024-01-15T10:30:00'),
    updatedAt: new Date('2024-01-15T11:00:00'),
  },
  {
    id: '2',
    orderNumber: 'ORD-2024-002',
    customerId: '2',
    customerName: 'فاطمة أحمد',
    customerEmail: '<EMAIL>',
    items: [
      {
        id: '3',
        productId: '3',
        productName: 'هاتف ذكي Samsung Galaxy',
        productImage: '/images/phone.jpg',
        price: 1200,
        quantity: 1,
        total: 1200,
      },
    ],
    subtotal: 1200,
    shipping: 30,
    tax: 180,
    total: 1410,
    status: 'processing',
    paymentMethod: 'cash',
    paymentStatus: 'pending',
    shippingAddress: {
      fullName: 'فاطمة أحمد محمد',
      phone: '+966507654321',
      address: 'طريق الأمير محمد بن عبدالعزيز',
      city: 'جدة',
      postalCode: '21589',
      country: 'السعودية',
    },
    createdAt: new Date('2024-01-16T14:20:00'),
    updatedAt: new Date('2024-01-16T15:45:00'),
  },
  {
    id: '3',
    orderNumber: 'ORD-2024-003',
    customerId: '1',
    customerName: 'أحمد محمد',
    customerEmail: '<EMAIL>',
    items: [
      {
        id: '4',
        productId: '4',
        productName: 'تابلت iPad Pro',
        productImage: '/images/tablet.jpg',
        price: 1800,
        quantity: 1,
        total: 1800,
      },
    ],
    subtotal: 1800,
    shipping: 40,
    tax: 270,
    total: 2110,
    status: 'delivered',
    paymentMethod: 'bank_transfer',
    paymentStatus: 'paid',
    shippingAddress: {
      fullName: 'أحمد محمد علي',
      phone: '+************',
      address: 'شارع الملك فهد، حي النخيل',
      city: 'الرياض',
      postalCode: '12345',
      country: 'السعودية',
    },
    createdAt: new Date('2024-01-10T09:15:00'),
    updatedAt: new Date('2024-01-12T16:30:00'),
    deliveredAt: new Date('2024-01-12T16:30:00'),
  },
];

export function OrderProvider({ children }: OrderProviderProps) {
  const [orders, setOrders] = useState<Order[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Initialize orders from localStorage or use sample data
  useEffect(() => {
    const initializeOrders = () => {
      try {
        const savedOrders = localStorage.getItem('ecommerce-orders');
        if (savedOrders) {
          const parsedOrders = JSON.parse(savedOrders);
          // Convert date strings back to Date objects
          const ordersWithDates = parsedOrders.map((order: any) => ({
            ...order,
            createdAt: new Date(order.createdAt),
            updatedAt: new Date(order.updatedAt),
            deliveredAt: order.deliveredAt ? new Date(order.deliveredAt) : undefined,
          }));
          setOrders(ordersWithDates);
        } else {
          // First time - use sample data
          setOrders(sampleOrders);
          localStorage.setItem('ecommerce-orders', JSON.stringify(sampleOrders));
        }
      } catch (error) {
        console.error('Error loading orders from localStorage:', error);
        setOrders(sampleOrders);
      } finally {
        setIsLoading(false);
      }
    };

    initializeOrders();
  }, []);

  // Save to localStorage whenever orders change
  useEffect(() => {
    if (!isLoading && orders.length > 0) {
      try {
        localStorage.setItem('ecommerce-orders', JSON.stringify(orders));
      } catch (error) {
        console.error('Error saving orders to localStorage:', error);
      }
    }
  }, [orders, isLoading]);

  const generateOrderNumber = (): string => {
    const year = new Date().getFullYear();
    const orderCount = orders.length + 1;
    return `ORD-${year}-${orderCount.toString().padStart(3, '0')}`;
  };

  const addOrder = (orderData: Omit<Order, 'id' | 'orderNumber' | 'createdAt' | 'updatedAt'>) => {
    const newOrder: Order = {
      ...orderData,
      id: Date.now().toString(),
      orderNumber: generateOrderNumber(),
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    setOrders(prev => [newOrder, ...prev]);
  };

  const updateOrderStatus = (id: string, status: OrderStatus) => {
    setOrders(prev => prev.map(order => {
      if (order.id === id) {
        const updatedOrder = { 
          ...order, 
          status, 
          updatedAt: new Date() 
        };
        
        // Set deliveredAt when status changes to delivered
        if (status === 'delivered' && !order.deliveredAt) {
          updatedOrder.deliveredAt = new Date();
        }
        
        return updatedOrder;
      }
      return order;
    }));
  };

  const updateOrder = (id: string, updates: Partial<Order>) => {
    setOrders(prev => prev.map(order => 
      order.id === id 
        ? { ...order, ...updates, updatedAt: new Date() }
        : order
    ));
  };

  const deleteOrder = (id: string) => {
    setOrders(prev => prev.filter(order => order.id !== id));
  };

  const getOrderById = (id: string): Order | undefined => {
    return orders.find(order => order.id === id);
  };

  const getOrdersByStatus = (status: OrderStatus): Order[] => {
    return orders.filter(order => order.status === status);
  };

  const getOrdersByCustomer = (customerId: string): Order[] => {
    return orders.filter(order => order.customerId === customerId);
  };

  const getTotalRevenue = (): number => {
    return orders
      .filter(order => order.paymentStatus === 'paid')
      .reduce((total, order) => total + order.total, 0);
  };

  const getOrdersCount = (): number => {
    return orders.length;
  };

  const getRecentOrders = (limit: number = 5): Order[] => {
    return orders
      .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())
      .slice(0, limit);
  };

  const value: OrderContextType = {
    orders,
    addOrder,
    updateOrderStatus,
    updateOrder,
    deleteOrder,
    getOrderById,
    getOrdersByStatus,
    getOrdersByCustomer,
    getTotalRevenue,
    getOrdersCount,
    getRecentOrders,
    isLoading,
  };

  return (
    <OrderContext.Provider value={value}>
      {children}
    </OrderContext.Provider>
  );
}

export function useOrders() {
  const context = useContext(OrderContext);
  if (context === undefined) {
    throw new Error('useOrders must be used within an OrderProvider');
  }
  return context;
}
