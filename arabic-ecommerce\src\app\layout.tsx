import type { Metada<PERSON> } from "next";
import { Cairo, Ta<PERSON><PERSON> } from "next/font/google";
import "./globals.css";
import { ProductProvider } from '@/contexts/ProductContext';
import { CategoryProvider } from '@/contexts/CategoryContext';
import { OrderProvider } from '@/contexts/OrderContext';
import { UserProvider } from '@/contexts/UserContext';

const cairo = Cairo({
  variable: "--font-cairo",
  subsets: ["arabic", "latin"],
  display: "swap",
});

const tajawal = Tajawal({
  variable: "--font-tajawal",
  subsets: ["arabic", "latin"],
  weight: ["200", "300", "400", "500", "700", "800", "900"],
  display: "swap",
});

export const metadata: Metadata = {
  title: "المتجر العربي - Arabic E-commerce",
  description: "متجر إلكتروني عربي حديث يدعم اللغة العربية",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="ar" dir="rtl">
      <body
        className={`${cairo.variable} ${tajawal.variable} font-arabic antialiased bg-gray-50`}
      >
        <UserProvider>
          <OrderProvider>
            <CategoryProvider>
              <ProductProvider>
                {children}
              </ProductProvider>
            </CategoryProvider>
          </OrderProvider>
        </UserProvider>
      </body>
    </html>
  );
}
