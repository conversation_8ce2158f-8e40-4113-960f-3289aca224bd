{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[next]/internal/font/google/cairo_71c97244.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_export_value__({\n  \"className\": \"cairo_71c97244-module___U0MEa__className\",\n  \"variable\": \"cairo_71c97244-module___U0MEa__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 10, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 16, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[next]/internal/font/google/cairo_71c97244.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Cairo%22,%22arguments%22:[{%22variable%22:%22--font-cairo%22,%22subsets%22:[%22arabic%22,%22latin%22],%22display%22:%22swap%22}],%22variableName%22:%22cairo%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Cairo', 'Cairo Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,wJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,wJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,wJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0]}}, {"offset": {"line": 32, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[next]/internal/font/google/tajawal_6c72e7e1.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_export_value__({\n  \"className\": \"tajawal_6c72e7e1-module__abEw0G__className\",\n  \"variable\": \"tajawal_6c72e7e1-module__abEw0G__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 41, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 47, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[next]/internal/font/google/tajawal_6c72e7e1.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Tajawal%22,%22arguments%22:[{%22variable%22:%22--font-tajawal%22,%22subsets%22:[%22arabic%22,%22latin%22],%22weight%22:[%22200%22,%22300%22,%22400%22,%22500%22,%22700%22,%22800%22,%22900%22],%22display%22:%22swap%22}],%22variableName%22:%22tajawal%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Tajawal', 'Tajawal Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,0JAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,0JAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,0JAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0]}}, {"offset": {"line": 63, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 69, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/New%20folder%20%288%29/arabic-ecommerce/src/app/layout.tsx"], "sourcesContent": ["import type { Metada<PERSON> } from \"next\";\nimport { Cairo, <PERSON><PERSON><PERSON> } from \"next/font/google\";\nimport \"./globals.css\";\n\nconst cairo = Cairo({\n  variable: \"--font-cairo\",\n  subsets: [\"arabic\", \"latin\"],\n  display: \"swap\",\n});\n\nconst tajawal = Tajawal({\n  variable: \"--font-tajawal\",\n  subsets: [\"arabic\", \"latin\"],\n  weight: [\"200\", \"300\", \"400\", \"500\", \"700\", \"800\", \"900\"],\n  display: \"swap\",\n});\n\nexport const metadata: Metadata = {\n  title: \"المتجر العربي - Arabic E-commerce\",\n  description: \"متجر إلكتروني عربي حديث يدعم اللغة العربية\",\n};\n\nexport default function RootLayout({\n  children,\n}: Readonly<{\n  children: React.ReactNode;\n}>) {\n  return (\n    <html lang=\"ar\" dir=\"rtl\">\n      <body\n        className={`${cairo.variable} ${tajawal.variable} font-arabic antialiased bg-gray-50`}\n      >\n        {children}\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAiBO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;AACf;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGR;IACA,qBACE,8OAAC;QAAK,MAAK;QAAK,KAAI;kBAClB,cAAA,8OAAC;YACC,WAAW,GAAG,yIAAA,CAAA,UAAK,CAAC,QAAQ,CAAC,CAAC,EAAE,2IAAA,CAAA,UAAO,CAAC,QAAQ,CAAC,mCAAmC,CAAC;sBAEpF;;;;;;;;;;;AAIT"}}, {"offset": {"line": 102, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 113, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/New%20folder%20%288%29/arabic-ecommerce/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-rsc'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,0HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0]}}, {"offset": {"line": 115, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}