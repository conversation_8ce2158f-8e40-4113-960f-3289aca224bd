{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/New%20folder%20%288%29/arabic-ecommerce/src/components/ProductForm.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Product } from '@/types';\nimport { useCategories } from '@/contexts/CategoryContext';\n\ninterface ProductFormProps {\n  product?: Product | null;\n  isOpen: boolean;\n  onClose: () => void;\n  onSave: (product: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>) => void;\n}\n\nexport default function ProductForm({ product, isOpen, onClose, onSave }: ProductFormProps) {\n  const { getActiveCategories } = useCategories();\n  const activeCategories = getActiveCategories();\n\n  const [formData, setFormData] = useState({\n    name: '',\n    description: '',\n    price: '',\n    image: '',\n    category: '',\n    stock: '',\n    featured: false,\n  });\n  const [isLoading, setIsLoading] = useState(false);\n  const [errors, setErrors] = useState<Record<string, string>>({});\n\n  useEffect(() => {\n    if (product) {\n      setFormData({\n        name: product.name,\n        description: product.description,\n        price: product.price.toString(),\n        image: product.image,\n        category: product.category,\n        stock: product.stock.toString(),\n        featured: product.featured,\n      });\n    } else {\n      setFormData({\n        name: '',\n        description: '',\n        price: '',\n        image: '',\n        category: '',\n        stock: '',\n        featured: false,\n      });\n    }\n    setErrors({});\n  }, [product, isOpen]);\n\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {\n    const { name, value, type } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value,\n    }));\n    // Clear error when user starts typing\n    if (errors[name]) {\n      setErrors(prev => ({ ...prev, [name]: '' }));\n    }\n  };\n\n  const validateForm = () => {\n    const newErrors: Record<string, string> = {};\n\n    if (!formData.name.trim()) {\n      newErrors.name = 'اسم المنتج مطلوب';\n    }\n    if (!formData.description.trim()) {\n      newErrors.description = 'وصف المنتج مطلوب';\n    }\n    if (!formData.price || isNaN(Number(formData.price)) || Number(formData.price) <= 0) {\n      newErrors.price = 'السعر يجب أن يكون رقم صحيح أكبر من صفر';\n    }\n    if (!formData.category) {\n      newErrors.category = 'التصنيف مطلوب';\n    }\n    if (!formData.stock || isNaN(Number(formData.stock)) || Number(formData.stock) < 0) {\n      newErrors.stock = 'الكمية يجب أن تكون رقم صحيح أكبر من أو يساوي صفر';\n    }\n    if (!formData.image.trim()) {\n      newErrors.image = 'رابط الصورة مطلوب';\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n\n    if (!validateForm()) {\n      return;\n    }\n\n    setIsLoading(true);\n\n    try {\n      // Simulate API call\n      await new Promise(resolve => setTimeout(resolve, 1000));\n\n      const productData = {\n        name: formData.name.trim(),\n        description: formData.description.trim(),\n        price: Number(formData.price),\n        image: formData.image.trim(),\n        category: formData.category,\n        stock: Number(formData.stock),\n        featured: formData.featured,\n      };\n\n      onSave(productData);\n      onClose();\n    } catch (error) {\n      console.error('Error saving product:', error);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  if (!isOpen) return null;\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n      <div className=\"bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto\">\n        <div className=\"px-6 py-4 border-b border-gray-200\">\n          <div className=\"flex justify-between items-center\">\n            <h2 className=\"text-xl font-bold text-gray-800\">\n              {product ? 'تعديل المنتج' : 'إضافة منتج جديد'}\n            </h2>\n            <button\n              onClick={onClose}\n              className=\"text-gray-400 hover:text-gray-600 text-2xl\"\n            >\n              ×\n            </button>\n          </div>\n        </div>\n\n        <form onSubmit={handleSubmit} className=\"p-6 space-y-6\">\n          {/* Product Name */}\n          <div>\n            <label htmlFor=\"name\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n              اسم المنتج *\n            </label>\n            <input\n              type=\"text\"\n              id=\"name\"\n              name=\"name\"\n              value={formData.name}\n              onChange={handleChange}\n              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 ${\n                errors.name ? 'border-red-500' : 'border-gray-300'\n              }`}\n              placeholder=\"أدخل اسم المنتج\"\n            />\n            {errors.name && <p className=\"text-red-500 text-sm mt-1\">{errors.name}</p>}\n          </div>\n\n          {/* Description */}\n          <div>\n            <label htmlFor=\"description\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n              وصف المنتج *\n            </label>\n            <textarea\n              id=\"description\"\n              name=\"description\"\n              value={formData.description}\n              onChange={handleChange}\n              rows={3}\n              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 ${\n                errors.description ? 'border-red-500' : 'border-gray-300'\n              }`}\n              placeholder=\"أدخل وصف المنتج\"\n            />\n            {errors.description && <p className=\"text-red-500 text-sm mt-1\">{errors.description}</p>}\n          </div>\n\n          {/* Price and Stock */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div>\n              <label htmlFor=\"price\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                السعر (ر.س) *\n              </label>\n              <input\n                type=\"number\"\n                id=\"price\"\n                name=\"price\"\n                value={formData.price}\n                onChange={handleChange}\n                min=\"0\"\n                step=\"0.01\"\n                className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 ${\n                  errors.price ? 'border-red-500' : 'border-gray-300'\n                }`}\n                placeholder=\"0.00\"\n              />\n              {errors.price && <p className=\"text-red-500 text-sm mt-1\">{errors.price}</p>}\n            </div>\n\n            <div>\n              <label htmlFor=\"stock\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                الكمية المتوفرة *\n              </label>\n              <input\n                type=\"number\"\n                id=\"stock\"\n                name=\"stock\"\n                value={formData.stock}\n                onChange={handleChange}\n                min=\"0\"\n                className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 ${\n                  errors.stock ? 'border-red-500' : 'border-gray-300'\n                }`}\n                placeholder=\"0\"\n              />\n              {errors.stock && <p className=\"text-red-500 text-sm mt-1\">{errors.stock}</p>}\n            </div>\n          </div>\n\n          {/* Category */}\n          <div>\n            <label htmlFor=\"category\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n              التصنيف *\n            </label>\n            <select\n              id=\"category\"\n              name=\"category\"\n              value={formData.category}\n              onChange={handleChange}\n              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 ${\n                errors.category ? 'border-red-500' : 'border-gray-300'\n              }`}\n            >\n              <option value=\"\">اختر التصنيف</option>\n              {activeCategories.map((category) => (\n                <option key={category.id} value={category.name}>\n                  {category.name}\n                </option>\n              ))}\n            </select>\n            {errors.category && <p className=\"text-red-500 text-sm mt-1\">{errors.category}</p>}\n          </div>\n\n          {/* Image URL */}\n          <div>\n            <label htmlFor=\"image\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n              رابط الصورة *\n            </label>\n            <input\n              type=\"url\"\n              id=\"image\"\n              name=\"image\"\n              value={formData.image}\n              onChange={handleChange}\n              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 ${\n                errors.image ? 'border-red-500' : 'border-gray-300'\n              }`}\n              placeholder=\"https://example.com/image.jpg\"\n            />\n            {errors.image && <p className=\"text-red-500 text-sm mt-1\">{errors.image}</p>}\n          </div>\n\n          {/* Featured */}\n          <div className=\"flex items-center\">\n            <input\n              type=\"checkbox\"\n              id=\"featured\"\n              name=\"featured\"\n              checked={formData.featured}\n              onChange={handleChange}\n              className=\"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded\"\n            />\n            <label htmlFor=\"featured\" className=\"mr-2 block text-sm text-gray-700\">\n              منتج مميز\n            </label>\n          </div>\n\n          {/* Form Actions */}\n          <div className=\"flex justify-end space-x-3 space-x-reverse pt-6 border-t border-gray-200\">\n            <button\n              type=\"button\"\n              onClick={onClose}\n              className=\"px-4 py-2 text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-lg transition-colors duration-200\"\n            >\n              إلغاء\n            </button>\n            <button\n              type=\"submit\"\n              disabled={isLoading}\n              className={`px-4 py-2 text-white rounded-lg transition-colors duration-200 ${\n                isLoading\n                  ? 'bg-gray-400 cursor-not-allowed'\n                  : 'bg-primary-600 hover:bg-primary-700'\n              }`}\n            >\n              {isLoading ? 'جاري الحفظ...' : product ? 'تحديث المنتج' : 'إضافة المنتج'}\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;;;AAJA;;;AAae,SAAS,YAAY,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAoB;;IACxF,MAAM,EAAE,mBAAmB,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,gBAAa,AAAD;IAC5C,MAAM,mBAAmB;IAEzB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,aAAa;QACb,OAAO;QACP,OAAO;QACP,UAAU;QACV,OAAO;QACP,UAAU;IACZ;IACA,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAE9D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,IAAI,SAAS;gBACX,YAAY;oBACV,MAAM,QAAQ,IAAI;oBAClB,aAAa,QAAQ,WAAW;oBAChC,OAAO,QAAQ,KAAK,CAAC,QAAQ;oBAC7B,OAAO,QAAQ,KAAK;oBACpB,UAAU,QAAQ,QAAQ;oBAC1B,OAAO,QAAQ,KAAK,CAAC,QAAQ;oBAC7B,UAAU,QAAQ,QAAQ;gBAC5B;YACF,OAAO;gBACL,YAAY;oBACV,MAAM;oBACN,aAAa;oBACb,OAAO;oBACP,OAAO;oBACP,UAAU;oBACV,OAAO;oBACP,UAAU;gBACZ;YACF;YACA,UAAU,CAAC;QACb;gCAAG;QAAC;QAAS;KAAO;IAEpB,MAAM,eAAe,CAAC;QACpB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM;QACtC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE,SAAS,aAAa,AAAC,EAAE,MAAM,CAAsB,OAAO,GAAG;YACzE,CAAC;QACD,sCAAsC;QACtC,IAAI,MAAM,CAAC,KAAK,EAAE;YAChB,UAAU,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,CAAC,KAAK,EAAE;gBAAG,CAAC;QAC5C;IACF;IAEA,MAAM,eAAe;QACnB,MAAM,YAAoC,CAAC;QAE3C,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,IAAI;YACzB,UAAU,IAAI,GAAG;QACnB;QACA,IAAI,CAAC,SAAS,WAAW,CAAC,IAAI,IAAI;YAChC,UAAU,WAAW,GAAG;QAC1B;QACA,IAAI,CAAC,SAAS,KAAK,IAAI,MAAM,OAAO,SAAS,KAAK,MAAM,OAAO,SAAS,KAAK,KAAK,GAAG;YACnF,UAAU,KAAK,GAAG;QACpB;QACA,IAAI,CAAC,SAAS,QAAQ,EAAE;YACtB,UAAU,QAAQ,GAAG;QACvB;QACA,IAAI,CAAC,SAAS,KAAK,IAAI,MAAM,OAAO,SAAS,KAAK,MAAM,OAAO,SAAS,KAAK,IAAI,GAAG;YAClF,UAAU,KAAK,GAAG;QACpB;QACA,IAAI,CAAC,SAAS,KAAK,CAAC,IAAI,IAAI;YAC1B,UAAU,KAAK,GAAG;QACpB;QAEA,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;IAC3C;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,gBAAgB;YACnB;QACF;QAEA,aAAa;QAEb,IAAI;YACF,oBAAoB;YACpB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,MAAM,cAAc;gBAClB,MAAM,SAAS,IAAI,CAAC,IAAI;gBACxB,aAAa,SAAS,WAAW,CAAC,IAAI;gBACtC,OAAO,OAAO,SAAS,KAAK;gBAC5B,OAAO,SAAS,KAAK,CAAC,IAAI;gBAC1B,UAAU,SAAS,QAAQ;gBAC3B,OAAO,OAAO,SAAS,KAAK;gBAC5B,UAAU,SAAS,QAAQ;YAC7B;YAEA,OAAO;YACP;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;QACzC,SAAU;YACR,aAAa;QACf;IACF;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CACX,UAAU,iBAAiB;;;;;;0CAE9B,6LAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;;;;;;8BAML,6LAAC;oBAAK,UAAU;oBAAc,WAAU;;sCAEtC,6LAAC;;8CACC,6LAAC;oCAAM,SAAQ;oCAAO,WAAU;8CAA+C;;;;;;8CAG/E,6LAAC;oCACC,MAAK;oCACL,IAAG;oCACH,MAAK;oCACL,OAAO,SAAS,IAAI;oCACpB,UAAU;oCACV,WAAW,CAAC,0FAA0F,EACpG,OAAO,IAAI,GAAG,mBAAmB,mBACjC;oCACF,aAAY;;;;;;gCAEb,OAAO,IAAI,kBAAI,6LAAC;oCAAE,WAAU;8CAA6B,OAAO,IAAI;;;;;;;;;;;;sCAIvE,6LAAC;;8CACC,6LAAC;oCAAM,SAAQ;oCAAc,WAAU;8CAA+C;;;;;;8CAGtF,6LAAC;oCACC,IAAG;oCACH,MAAK;oCACL,OAAO,SAAS,WAAW;oCAC3B,UAAU;oCACV,MAAM;oCACN,WAAW,CAAC,0FAA0F,EACpG,OAAO,WAAW,GAAG,mBAAmB,mBACxC;oCACF,aAAY;;;;;;gCAEb,OAAO,WAAW,kBAAI,6LAAC;oCAAE,WAAU;8CAA6B,OAAO,WAAW;;;;;;;;;;;;sCAIrF,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAQ,WAAU;sDAA+C;;;;;;sDAGhF,6LAAC;4CACC,MAAK;4CACL,IAAG;4CACH,MAAK;4CACL,OAAO,SAAS,KAAK;4CACrB,UAAU;4CACV,KAAI;4CACJ,MAAK;4CACL,WAAW,CAAC,0FAA0F,EACpG,OAAO,KAAK,GAAG,mBAAmB,mBAClC;4CACF,aAAY;;;;;;wCAEb,OAAO,KAAK,kBAAI,6LAAC;4CAAE,WAAU;sDAA6B,OAAO,KAAK;;;;;;;;;;;;8CAGzE,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAQ,WAAU;sDAA+C;;;;;;sDAGhF,6LAAC;4CACC,MAAK;4CACL,IAAG;4CACH,MAAK;4CACL,OAAO,SAAS,KAAK;4CACrB,UAAU;4CACV,KAAI;4CACJ,WAAW,CAAC,0FAA0F,EACpG,OAAO,KAAK,GAAG,mBAAmB,mBAClC;4CACF,aAAY;;;;;;wCAEb,OAAO,KAAK,kBAAI,6LAAC;4CAAE,WAAU;sDAA6B,OAAO,KAAK;;;;;;;;;;;;;;;;;;sCAK3E,6LAAC;;8CACC,6LAAC;oCAAM,SAAQ;oCAAW,WAAU;8CAA+C;;;;;;8CAGnF,6LAAC;oCACC,IAAG;oCACH,MAAK;oCACL,OAAO,SAAS,QAAQ;oCACxB,UAAU;oCACV,WAAW,CAAC,0FAA0F,EACpG,OAAO,QAAQ,GAAG,mBAAmB,mBACrC;;sDAEF,6LAAC;4CAAO,OAAM;sDAAG;;;;;;wCAChB,iBAAiB,GAAG,CAAC,CAAC,yBACrB,6LAAC;gDAAyB,OAAO,SAAS,IAAI;0DAC3C,SAAS,IAAI;+CADH,SAAS,EAAE;;;;;;;;;;;gCAK3B,OAAO,QAAQ,kBAAI,6LAAC;oCAAE,WAAU;8CAA6B,OAAO,QAAQ;;;;;;;;;;;;sCAI/E,6LAAC;;8CACC,6LAAC;oCAAM,SAAQ;oCAAQ,WAAU;8CAA+C;;;;;;8CAGhF,6LAAC;oCACC,MAAK;oCACL,IAAG;oCACH,MAAK;oCACL,OAAO,SAAS,KAAK;oCACrB,UAAU;oCACV,WAAW,CAAC,0FAA0F,EACpG,OAAO,KAAK,GAAG,mBAAmB,mBAClC;oCACF,aAAY;;;;;;gCAEb,OAAO,KAAK,kBAAI,6LAAC;oCAAE,WAAU;8CAA6B,OAAO,KAAK;;;;;;;;;;;;sCAIzE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,MAAK;oCACL,IAAG;oCACH,MAAK;oCACL,SAAS,SAAS,QAAQ;oCAC1B,UAAU;oCACV,WAAU;;;;;;8CAEZ,6LAAC;oCAAM,SAAQ;oCAAW,WAAU;8CAAmC;;;;;;;;;;;;sCAMzE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,MAAK;oCACL,SAAS;oCACT,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCACC,MAAK;oCACL,UAAU;oCACV,WAAW,CAAC,+DAA+D,EACzE,YACI,mCACA,uCACJ;8CAED,YAAY,kBAAkB,UAAU,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOxE;GAtSwB;;QACU,sIAAA,CAAA,gBAAa;;;KADvB"}}, {"offset": {"line": 512, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 518, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/New%20folder%20%288%29/arabic-ecommerce/src/components/CategoryForm.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Category } from '@/contexts/CategoryContext';\n\ninterface CategoryFormProps {\n  category?: Category | null;\n  isOpen: boolean;\n  onClose: () => void;\n  onSave: (category: Omit<Category, 'id' | 'productCount' | 'createdAt' | 'updatedAt'>) => void;\n}\n\nexport default function CategoryForm({ category, isOpen, onClose, onSave }: CategoryFormProps) {\n  const [formData, setFormData] = useState({\n    name: '',\n    description: '',\n    isActive: true,\n  });\n  const [isLoading, setIsLoading] = useState(false);\n  const [errors, setErrors] = useState<Record<string, string>>({});\n\n  useEffect(() => {\n    if (category) {\n      setFormData({\n        name: category.name,\n        description: category.description,\n        isActive: category.isActive,\n      });\n    } else {\n      setFormData({\n        name: '',\n        description: '',\n        isActive: true,\n      });\n    }\n    setErrors({});\n  }, [category, isOpen]);\n\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {\n    const { name, value, type } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value,\n    }));\n    // Clear error when user starts typing\n    if (errors[name]) {\n      setErrors(prev => ({ ...prev, [name]: '' }));\n    }\n  };\n\n  const validateForm = () => {\n    const newErrors: Record<string, string> = {};\n\n    if (!formData.name.trim()) {\n      newErrors.name = 'اسم التصنيف مطلوب';\n    } else if (formData.name.trim().length < 2) {\n      newErrors.name = 'اسم التصنيف يجب أن يكون حرفين على الأقل';\n    }\n\n    if (!formData.description.trim()) {\n      newErrors.description = 'وصف التصنيف مطلوب';\n    } else if (formData.description.trim().length < 5) {\n      newErrors.description = 'وصف التصنيف يجب أن يكون 5 أحرف على الأقل';\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (!validateForm()) {\n      return;\n    }\n\n    setIsLoading(true);\n\n    try {\n      // Simulate API call\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      \n      const categoryData = {\n        name: formData.name.trim(),\n        description: formData.description.trim(),\n        isActive: formData.isActive,\n      };\n\n      onSave(categoryData);\n      onClose();\n    } catch (error) {\n      console.error('Error saving category:', error);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  if (!isOpen) return null;\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n      <div className=\"bg-white rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto\">\n        <div className=\"px-6 py-4 border-b border-gray-200\">\n          <div className=\"flex justify-between items-center\">\n            <h2 className=\"text-xl font-bold text-gray-800\">\n              {category ? 'تعديل التصنيف' : 'إضافة تصنيف جديد'}\n            </h2>\n            <button\n              onClick={onClose}\n              className=\"text-gray-400 hover:text-gray-600 text-2xl\"\n            >\n              ×\n            </button>\n          </div>\n        </div>\n\n        <form onSubmit={handleSubmit} className=\"p-6 space-y-6\">\n          {/* Category Name */}\n          <div>\n            <label htmlFor=\"name\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n              اسم التصنيف *\n            </label>\n            <input\n              type=\"text\"\n              id=\"name\"\n              name=\"name\"\n              value={formData.name}\n              onChange={handleChange}\n              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 ${\n                errors.name ? 'border-red-500' : 'border-gray-300'\n              }`}\n              placeholder=\"أدخل اسم التصنيف\"\n            />\n            {errors.name && <p className=\"text-red-500 text-sm mt-1\">{errors.name}</p>}\n          </div>\n\n          {/* Description */}\n          <div>\n            <label htmlFor=\"description\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n              وصف التصنيف *\n            </label>\n            <textarea\n              id=\"description\"\n              name=\"description\"\n              value={formData.description}\n              onChange={handleChange}\n              rows={3}\n              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 ${\n                errors.description ? 'border-red-500' : 'border-gray-300'\n              }`}\n              placeholder=\"أدخل وصف التصنيف\"\n            />\n            {errors.description && <p className=\"text-red-500 text-sm mt-1\">{errors.description}</p>}\n          </div>\n\n          {/* Active Status */}\n          <div className=\"flex items-center\">\n            <input\n              type=\"checkbox\"\n              id=\"isActive\"\n              name=\"isActive\"\n              checked={formData.isActive}\n              onChange={handleChange}\n              className=\"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded\"\n            />\n            <label htmlFor=\"isActive\" className=\"mr-2 block text-sm text-gray-700\">\n              تصنيف نشط\n            </label>\n          </div>\n\n          {/* Form Actions */}\n          <div className=\"flex justify-end space-x-3 space-x-reverse pt-6 border-t border-gray-200\">\n            <button\n              type=\"button\"\n              onClick={onClose}\n              className=\"px-4 py-2 text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-lg transition-colors duration-200\"\n            >\n              إلغاء\n            </button>\n            <button\n              type=\"submit\"\n              disabled={isLoading}\n              className={`px-4 py-2 text-white rounded-lg transition-colors duration-200 ${\n                isLoading\n                  ? 'bg-gray-400 cursor-not-allowed'\n                  : 'bg-primary-600 hover:bg-primary-700'\n              }`}\n            >\n              {isLoading ? 'جاري الحفظ...' : category ? 'تحديث التصنيف' : 'إضافة التصنيف'}\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAYe,SAAS,aAAa,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAqB;;IAC3F,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,aAAa;QACb,UAAU;IACZ;IACA,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAE9D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,UAAU;gBACZ,YAAY;oBACV,MAAM,SAAS,IAAI;oBACnB,aAAa,SAAS,WAAW;oBACjC,UAAU,SAAS,QAAQ;gBAC7B;YACF,OAAO;gBACL,YAAY;oBACV,MAAM;oBACN,aAAa;oBACb,UAAU;gBACZ;YACF;YACA,UAAU,CAAC;QACb;iCAAG;QAAC;QAAU;KAAO;IAErB,MAAM,eAAe,CAAC;QACpB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM;QACtC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE,SAAS,aAAa,AAAC,EAAE,MAAM,CAAsB,OAAO,GAAG;YACzE,CAAC;QACD,sCAAsC;QACtC,IAAI,MAAM,CAAC,KAAK,EAAE;YAChB,UAAU,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,CAAC,KAAK,EAAE;gBAAG,CAAC;QAC5C;IACF;IAEA,MAAM,eAAe;QACnB,MAAM,YAAoC,CAAC;QAE3C,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,IAAI;YACzB,UAAU,IAAI,GAAG;QACnB,OAAO,IAAI,SAAS,IAAI,CAAC,IAAI,GAAG,MAAM,GAAG,GAAG;YAC1C,UAAU,IAAI,GAAG;QACnB;QAEA,IAAI,CAAC,SAAS,WAAW,CAAC,IAAI,IAAI;YAChC,UAAU,WAAW,GAAG;QAC1B,OAAO,IAAI,SAAS,WAAW,CAAC,IAAI,GAAG,MAAM,GAAG,GAAG;YACjD,UAAU,WAAW,GAAG;QAC1B;QAEA,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;IAC3C;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,gBAAgB;YACnB;QACF;QAEA,aAAa;QAEb,IAAI;YACF,oBAAoB;YACpB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,MAAM,eAAe;gBACnB,MAAM,SAAS,IAAI,CAAC,IAAI;gBACxB,aAAa,SAAS,WAAW,CAAC,IAAI;gBACtC,UAAU,SAAS,QAAQ;YAC7B;YAEA,OAAO;YACP;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C,SAAU;YACR,aAAa;QACf;IACF;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CACX,WAAW,kBAAkB;;;;;;0CAEhC,6LAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;;;;;;8BAML,6LAAC;oBAAK,UAAU;oBAAc,WAAU;;sCAEtC,6LAAC;;8CACC,6LAAC;oCAAM,SAAQ;oCAAO,WAAU;8CAA+C;;;;;;8CAG/E,6LAAC;oCACC,MAAK;oCACL,IAAG;oCACH,MAAK;oCACL,OAAO,SAAS,IAAI;oCACpB,UAAU;oCACV,WAAW,CAAC,0FAA0F,EACpG,OAAO,IAAI,GAAG,mBAAmB,mBACjC;oCACF,aAAY;;;;;;gCAEb,OAAO,IAAI,kBAAI,6LAAC;oCAAE,WAAU;8CAA6B,OAAO,IAAI;;;;;;;;;;;;sCAIvE,6LAAC;;8CACC,6LAAC;oCAAM,SAAQ;oCAAc,WAAU;8CAA+C;;;;;;8CAGtF,6LAAC;oCACC,IAAG;oCACH,MAAK;oCACL,OAAO,SAAS,WAAW;oCAC3B,UAAU;oCACV,MAAM;oCACN,WAAW,CAAC,0FAA0F,EACpG,OAAO,WAAW,GAAG,mBAAmB,mBACxC;oCACF,aAAY;;;;;;gCAEb,OAAO,WAAW,kBAAI,6LAAC;oCAAE,WAAU;8CAA6B,OAAO,WAAW;;;;;;;;;;;;sCAIrF,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,MAAK;oCACL,IAAG;oCACH,MAAK;oCACL,SAAS,SAAS,QAAQ;oCAC1B,UAAU;oCACV,WAAU;;;;;;8CAEZ,6LAAC;oCAAM,SAAQ;oCAAW,WAAU;8CAAmC;;;;;;;;;;;;sCAMzE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,MAAK;oCACL,SAAS;oCACT,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCACC,MAAK;oCACL,UAAU;oCACV,WAAW,CAAC,+DAA+D,EACzE,YACI,mCACA,uCACJ;8CAED,YAAY,kBAAkB,WAAW,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO1E;GAvLwB;KAAA"}}, {"offset": {"line": 811, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 817, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/New%20folder%20%288%29/arabic-ecommerce/src/components/UserForm.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { User } from '@/types';\n\ninterface UserFormProps {\n  user?: User | null;\n  isOpen: boolean;\n  onClose: () => void;\n  onSave: (user: Omit<User, 'id' | 'totalOrders' | 'totalSpent' | 'createdAt' | 'updatedAt'>) => void;\n}\n\nexport default function UserForm({ user, isOpen, onClose, onSave }: UserFormProps) {\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    phone: '',\n    role: 'customer' as 'admin' | 'customer',\n    isActive: true,\n    address: {\n      street: '',\n      city: '',\n      postalCode: '',\n      country: 'السعودية',\n    },\n  });\n  const [isLoading, setIsLoading] = useState(false);\n  const [errors, setErrors] = useState<Record<string, string>>({});\n\n  useEffect(() => {\n    if (user) {\n      setFormData({\n        name: user.name,\n        email: user.email,\n        phone: user.phone || '',\n        role: user.role,\n        isActive: user.isActive,\n        address: user.address || {\n          street: '',\n          city: '',\n          postalCode: '',\n          country: 'السعودية',\n        },\n      });\n    } else {\n      setFormData({\n        name: '',\n        email: '',\n        phone: '',\n        role: 'customer',\n        isActive: true,\n        address: {\n          street: '',\n          city: '',\n          postalCode: '',\n          country: 'السعودية',\n        },\n      });\n    }\n    setErrors({});\n  }, [user, isOpen]);\n\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {\n    const { name, value, type } = e.target;\n    \n    if (name.startsWith('address.')) {\n      const addressField = name.split('.')[1];\n      setFormData(prev => ({\n        ...prev,\n        address: {\n          ...prev.address,\n          [addressField]: value,\n        },\n      }));\n    } else {\n      setFormData(prev => ({\n        ...prev,\n        [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value,\n      }));\n    }\n    \n    // Clear error when user starts typing\n    if (errors[name]) {\n      setErrors(prev => ({ ...prev, [name]: '' }));\n    }\n  };\n\n  const validateForm = () => {\n    const newErrors: Record<string, string> = {};\n\n    if (!formData.name.trim()) {\n      newErrors.name = 'اسم المستخدم مطلوب';\n    } else if (formData.name.trim().length < 2) {\n      newErrors.name = 'اسم المستخدم يجب أن يكون حرفين على الأقل';\n    }\n\n    if (!formData.email.trim()) {\n      newErrors.email = 'البريد الإلكتروني مطلوب';\n    } else if (!/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(formData.email)) {\n      newErrors.email = 'البريد الإلكتروني غير صحيح';\n    }\n\n    if (formData.phone && !/^(\\+966|0)?[5-9]\\d{8}$/.test(formData.phone.replace(/\\s/g, ''))) {\n      newErrors.phone = 'رقم الهاتف غير صحيح (يجب أن يكون رقم سعودي)';\n    }\n\n    if (formData.address.street && formData.address.street.trim().length < 5) {\n      newErrors['address.street'] = 'العنوان يجب أن يكون 5 أحرف على الأقل';\n    }\n\n    if (formData.address.city && formData.address.city.trim().length < 2) {\n      newErrors['address.city'] = 'اسم المدينة يجب أن يكون حرفين على الأقل';\n    }\n\n    if (formData.address.postalCode && !/^\\d{5}$/.test(formData.address.postalCode)) {\n      newErrors['address.postalCode'] = 'الرمز البريدي يجب أن يكون 5 أرقام';\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (!validateForm()) {\n      return;\n    }\n\n    setIsLoading(true);\n\n    try {\n      // Simulate API call\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      \n      const userData = {\n        name: formData.name.trim(),\n        email: formData.email.trim().toLowerCase(),\n        phone: formData.phone.trim() || undefined,\n        role: formData.role,\n        isActive: formData.isActive,\n        address: formData.address.street.trim() ? {\n          street: formData.address.street.trim(),\n          city: formData.address.city.trim(),\n          postalCode: formData.address.postalCode.trim(),\n          country: formData.address.country,\n        } : undefined,\n      };\n\n      onSave(userData);\n      onClose();\n    } catch (error) {\n      console.error('Error saving user:', error);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  if (!isOpen) return null;\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n      <div className=\"bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto\">\n        <div className=\"px-6 py-4 border-b border-gray-200\">\n          <div className=\"flex justify-between items-center\">\n            <h2 className=\"text-xl font-bold text-gray-800\">\n              {user ? 'تعديل المستخدم' : 'إضافة مستخدم جديد'}\n            </h2>\n            <button\n              onClick={onClose}\n              className=\"text-gray-400 hover:text-gray-600 text-2xl\"\n            >\n              ×\n            </button>\n          </div>\n        </div>\n\n        <form onSubmit={handleSubmit} className=\"p-6 space-y-6\">\n          {/* Basic Information */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            {/* Name */}\n            <div>\n              <label htmlFor=\"name\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                الاسم الكامل *\n              </label>\n              <input\n                type=\"text\"\n                id=\"name\"\n                name=\"name\"\n                value={formData.name}\n                onChange={handleChange}\n                className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 ${\n                  errors.name ? 'border-red-500' : 'border-gray-300'\n                }`}\n                placeholder=\"أدخل الاسم الكامل\"\n              />\n              {errors.name && <p className=\"text-red-500 text-sm mt-1\">{errors.name}</p>}\n            </div>\n\n            {/* Email */}\n            <div>\n              <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                البريد الإلكتروني *\n              </label>\n              <input\n                type=\"email\"\n                id=\"email\"\n                name=\"email\"\n                value={formData.email}\n                onChange={handleChange}\n                className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 ${\n                  errors.email ? 'border-red-500' : 'border-gray-300'\n                }`}\n                placeholder=\"أدخل البريد الإلكتروني\"\n              />\n              {errors.email && <p className=\"text-red-500 text-sm mt-1\">{errors.email}</p>}\n            </div>\n\n            {/* Phone */}\n            <div>\n              <label htmlFor=\"phone\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                رقم الهاتف\n              </label>\n              <input\n                type=\"tel\"\n                id=\"phone\"\n                name=\"phone\"\n                value={formData.phone}\n                onChange={handleChange}\n                className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 ${\n                  errors.phone ? 'border-red-500' : 'border-gray-300'\n                }`}\n                placeholder=\"+966501234567\"\n              />\n              {errors.phone && <p className=\"text-red-500 text-sm mt-1\">{errors.phone}</p>}\n            </div>\n\n            {/* Role */}\n            <div>\n              <label htmlFor=\"role\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                نوع المستخدم *\n              </label>\n              <select\n                id=\"role\"\n                name=\"role\"\n                value={formData.role}\n                onChange={handleChange}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500\"\n              >\n                <option value=\"customer\">عميل</option>\n                <option value=\"admin\">مدير</option>\n              </select>\n            </div>\n          </div>\n\n          {/* Address Information */}\n          <div className=\"border-t border-gray-200 pt-6\">\n            <h3 className=\"text-lg font-medium text-gray-800 mb-4\">معلومات العنوان (اختيارية)</h3>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              {/* Street */}\n              <div className=\"md:col-span-2\">\n                <label htmlFor=\"address.street\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  العنوان\n                </label>\n                <input\n                  type=\"text\"\n                  id=\"address.street\"\n                  name=\"address.street\"\n                  value={formData.address.street}\n                  onChange={handleChange}\n                  className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 ${\n                    errors['address.street'] ? 'border-red-500' : 'border-gray-300'\n                  }`}\n                  placeholder=\"أدخل العنوان\"\n                />\n                {errors['address.street'] && <p className=\"text-red-500 text-sm mt-1\">{errors['address.street']}</p>}\n              </div>\n\n              {/* City */}\n              <div>\n                <label htmlFor=\"address.city\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  المدينة\n                </label>\n                <input\n                  type=\"text\"\n                  id=\"address.city\"\n                  name=\"address.city\"\n                  value={formData.address.city}\n                  onChange={handleChange}\n                  className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 ${\n                    errors['address.city'] ? 'border-red-500' : 'border-gray-300'\n                  }`}\n                  placeholder=\"أدخل المدينة\"\n                />\n                {errors['address.city'] && <p className=\"text-red-500 text-sm mt-1\">{errors['address.city']}</p>}\n              </div>\n\n              {/* Postal Code */}\n              <div>\n                <label htmlFor=\"address.postalCode\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  الرمز البريدي\n                </label>\n                <input\n                  type=\"text\"\n                  id=\"address.postalCode\"\n                  name=\"address.postalCode\"\n                  value={formData.address.postalCode}\n                  onChange={handleChange}\n                  className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 ${\n                    errors['address.postalCode'] ? 'border-red-500' : 'border-gray-300'\n                  }`}\n                  placeholder=\"12345\"\n                />\n                {errors['address.postalCode'] && <p className=\"text-red-500 text-sm mt-1\">{errors['address.postalCode']}</p>}\n              </div>\n            </div>\n          </div>\n\n          {/* Status */}\n          <div className=\"flex items-center\">\n            <input\n              type=\"checkbox\"\n              id=\"isActive\"\n              name=\"isActive\"\n              checked={formData.isActive}\n              onChange={handleChange}\n              className=\"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded\"\n            />\n            <label htmlFor=\"isActive\" className=\"mr-2 block text-sm text-gray-700\">\n              حساب نشط\n            </label>\n          </div>\n\n          {/* Form Actions */}\n          <div className=\"flex justify-end space-x-3 space-x-reverse pt-6 border-t border-gray-200\">\n            <button\n              type=\"button\"\n              onClick={onClose}\n              className=\"px-4 py-2 text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-lg transition-colors duration-200\"\n            >\n              إلغاء\n            </button>\n            <button\n              type=\"submit\"\n              disabled={isLoading}\n              className={`px-4 py-2 text-white rounded-lg transition-colors duration-200 ${\n                isLoading\n                  ? 'bg-gray-400 cursor-not-allowed'\n                  : 'bg-primary-600 hover:bg-primary-700'\n              }`}\n            >\n              {isLoading ? 'جاري الحفظ...' : user ? 'تحديث المستخدم' : 'إضافة المستخدم'}\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAYe,SAAS,SAAS,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAiB;;IAC/E,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,OAAO;QACP,OAAO;QACP,MAAM;QACN,UAAU;QACV,SAAS;YACP,QAAQ;YACR,MAAM;YACN,YAAY;YACZ,SAAS;QACX;IACF;IACA,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAE9D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,IAAI,MAAM;gBACR,YAAY;oBACV,MAAM,KAAK,IAAI;oBACf,OAAO,KAAK,KAAK;oBACjB,OAAO,KAAK,KAAK,IAAI;oBACrB,MAAM,KAAK,IAAI;oBACf,UAAU,KAAK,QAAQ;oBACvB,SAAS,KAAK,OAAO,IAAI;wBACvB,QAAQ;wBACR,MAAM;wBACN,YAAY;wBACZ,SAAS;oBACX;gBACF;YACF,OAAO;gBACL,YAAY;oBACV,MAAM;oBACN,OAAO;oBACP,OAAO;oBACP,MAAM;oBACN,UAAU;oBACV,SAAS;wBACP,QAAQ;wBACR,MAAM;wBACN,YAAY;wBACZ,SAAS;oBACX;gBACF;YACF;YACA,UAAU,CAAC;QACb;6BAAG;QAAC;QAAM;KAAO;IAEjB,MAAM,eAAe,CAAC;QACpB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM;QAEtC,IAAI,KAAK,UAAU,CAAC,aAAa;YAC/B,MAAM,eAAe,KAAK,KAAK,CAAC,IAAI,CAAC,EAAE;YACvC,YAAY,CAAA,OAAQ,CAAC;oBACnB,GAAG,IAAI;oBACP,SAAS;wBACP,GAAG,KAAK,OAAO;wBACf,CAAC,aAAa,EAAE;oBAClB;gBACF,CAAC;QACH,OAAO;YACL,YAAY,CAAA,OAAQ,CAAC;oBACnB,GAAG,IAAI;oBACP,CAAC,KAAK,EAAE,SAAS,aAAa,AAAC,EAAE,MAAM,CAAsB,OAAO,GAAG;gBACzE,CAAC;QACH;QAEA,sCAAsC;QACtC,IAAI,MAAM,CAAC,KAAK,EAAE;YAChB,UAAU,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,CAAC,KAAK,EAAE;gBAAG,CAAC;QAC5C;IACF;IAEA,MAAM,eAAe;QACnB,MAAM,YAAoC,CAAC;QAE3C,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,IAAI;YACzB,UAAU,IAAI,GAAG;QACnB,OAAO,IAAI,SAAS,IAAI,CAAC,IAAI,GAAG,MAAM,GAAG,GAAG;YAC1C,UAAU,IAAI,GAAG;QACnB;QAEA,IAAI,CAAC,SAAS,KAAK,CAAC,IAAI,IAAI;YAC1B,UAAU,KAAK,GAAG;QACpB,OAAO,IAAI,CAAC,6BAA6B,IAAI,CAAC,SAAS,KAAK,GAAG;YAC7D,UAAU,KAAK,GAAG;QACpB;QAEA,IAAI,SAAS,KAAK,IAAI,CAAC,yBAAyB,IAAI,CAAC,SAAS,KAAK,CAAC,OAAO,CAAC,OAAO,MAAM;YACvF,UAAU,KAAK,GAAG;QACpB;QAEA,IAAI,SAAS,OAAO,CAAC,MAAM,IAAI,SAAS,OAAO,CAAC,MAAM,CAAC,IAAI,GAAG,MAAM,GAAG,GAAG;YACxE,SAAS,CAAC,iBAAiB,GAAG;QAChC;QAEA,IAAI,SAAS,OAAO,CAAC,IAAI,IAAI,SAAS,OAAO,CAAC,IAAI,CAAC,IAAI,GAAG,MAAM,GAAG,GAAG;YACpE,SAAS,CAAC,eAAe,GAAG;QAC9B;QAEA,IAAI,SAAS,OAAO,CAAC,UAAU,IAAI,CAAC,UAAU,IAAI,CAAC,SAAS,OAAO,CAAC,UAAU,GAAG;YAC/E,SAAS,CAAC,qBAAqB,GAAG;QACpC;QAEA,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;IAC3C;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,gBAAgB;YACnB;QACF;QAEA,aAAa;QAEb,IAAI;YACF,oBAAoB;YACpB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,MAAM,WAAW;gBACf,MAAM,SAAS,IAAI,CAAC,IAAI;gBACxB,OAAO,SAAS,KAAK,CAAC,IAAI,GAAG,WAAW;gBACxC,OAAO,SAAS,KAAK,CAAC,IAAI,MAAM;gBAChC,MAAM,SAAS,IAAI;gBACnB,UAAU,SAAS,QAAQ;gBAC3B,SAAS,SAAS,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK;oBACxC,QAAQ,SAAS,OAAO,CAAC,MAAM,CAAC,IAAI;oBACpC,MAAM,SAAS,OAAO,CAAC,IAAI,CAAC,IAAI;oBAChC,YAAY,SAAS,OAAO,CAAC,UAAU,CAAC,IAAI;oBAC5C,SAAS,SAAS,OAAO,CAAC,OAAO;gBACnC,IAAI;YACN;YAEA,OAAO;YACP;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sBAAsB;QACtC,SAAU;YACR,aAAa;QACf;IACF;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CACX,OAAO,mBAAmB;;;;;;0CAE7B,6LAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;;;;;;8BAML,6LAAC;oBAAK,UAAU;oBAAc,WAAU;;sCAEtC,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAO,WAAU;sDAA+C;;;;;;sDAG/E,6LAAC;4CACC,MAAK;4CACL,IAAG;4CACH,MAAK;4CACL,OAAO,SAAS,IAAI;4CACpB,UAAU;4CACV,WAAW,CAAC,0FAA0F,EACpG,OAAO,IAAI,GAAG,mBAAmB,mBACjC;4CACF,aAAY;;;;;;wCAEb,OAAO,IAAI,kBAAI,6LAAC;4CAAE,WAAU;sDAA6B,OAAO,IAAI;;;;;;;;;;;;8CAIvE,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAQ,WAAU;sDAA+C;;;;;;sDAGhF,6LAAC;4CACC,MAAK;4CACL,IAAG;4CACH,MAAK;4CACL,OAAO,SAAS,KAAK;4CACrB,UAAU;4CACV,WAAW,CAAC,0FAA0F,EACpG,OAAO,KAAK,GAAG,mBAAmB,mBAClC;4CACF,aAAY;;;;;;wCAEb,OAAO,KAAK,kBAAI,6LAAC;4CAAE,WAAU;sDAA6B,OAAO,KAAK;;;;;;;;;;;;8CAIzE,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAQ,WAAU;sDAA+C;;;;;;sDAGhF,6LAAC;4CACC,MAAK;4CACL,IAAG;4CACH,MAAK;4CACL,OAAO,SAAS,KAAK;4CACrB,UAAU;4CACV,WAAW,CAAC,0FAA0F,EACpG,OAAO,KAAK,GAAG,mBAAmB,mBAClC;4CACF,aAAY;;;;;;wCAEb,OAAO,KAAK,kBAAI,6LAAC;4CAAE,WAAU;sDAA6B,OAAO,KAAK;;;;;;;;;;;;8CAIzE,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAO,WAAU;sDAA+C;;;;;;sDAG/E,6LAAC;4CACC,IAAG;4CACH,MAAK;4CACL,OAAO,SAAS,IAAI;4CACpB,UAAU;4CACV,WAAU;;8DAEV,6LAAC;oDAAO,OAAM;8DAAW;;;;;;8DACzB,6LAAC;oDAAO,OAAM;8DAAQ;;;;;;;;;;;;;;;;;;;;;;;;sCAM5B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAyC;;;;;;8CACvD,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAM,SAAQ;oDAAiB,WAAU;8DAA+C;;;;;;8DAGzF,6LAAC;oDACC,MAAK;oDACL,IAAG;oDACH,MAAK;oDACL,OAAO,SAAS,OAAO,CAAC,MAAM;oDAC9B,UAAU;oDACV,WAAW,CAAC,0FAA0F,EACpG,MAAM,CAAC,iBAAiB,GAAG,mBAAmB,mBAC9C;oDACF,aAAY;;;;;;gDAEb,MAAM,CAAC,iBAAiB,kBAAI,6LAAC;oDAAE,WAAU;8DAA6B,MAAM,CAAC,iBAAiB;;;;;;;;;;;;sDAIjG,6LAAC;;8DACC,6LAAC;oDAAM,SAAQ;oDAAe,WAAU;8DAA+C;;;;;;8DAGvF,6LAAC;oDACC,MAAK;oDACL,IAAG;oDACH,MAAK;oDACL,OAAO,SAAS,OAAO,CAAC,IAAI;oDAC5B,UAAU;oDACV,WAAW,CAAC,0FAA0F,EACpG,MAAM,CAAC,eAAe,GAAG,mBAAmB,mBAC5C;oDACF,aAAY;;;;;;gDAEb,MAAM,CAAC,eAAe,kBAAI,6LAAC;oDAAE,WAAU;8DAA6B,MAAM,CAAC,eAAe;;;;;;;;;;;;sDAI7F,6LAAC;;8DACC,6LAAC;oDAAM,SAAQ;oDAAqB,WAAU;8DAA+C;;;;;;8DAG7F,6LAAC;oDACC,MAAK;oDACL,IAAG;oDACH,MAAK;oDACL,OAAO,SAAS,OAAO,CAAC,UAAU;oDAClC,UAAU;oDACV,WAAW,CAAC,0FAA0F,EACpG,MAAM,CAAC,qBAAqB,GAAG,mBAAmB,mBAClD;oDACF,aAAY;;;;;;gDAEb,MAAM,CAAC,qBAAqB,kBAAI,6LAAC;oDAAE,WAAU;8DAA6B,MAAM,CAAC,qBAAqB;;;;;;;;;;;;;;;;;;;;;;;;sCAM7G,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,MAAK;oCACL,IAAG;oCACH,MAAK;oCACL,SAAS,SAAS,QAAQ;oCAC1B,UAAU;oCACV,WAAU;;;;;;8CAEZ,6LAAC;oCAAM,SAAQ;oCAAW,WAAU;8CAAmC;;;;;;;;;;;;sCAMzE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,MAAK;oCACL,SAAS;oCACT,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCACC,MAAK;oCACL,UAAU;oCACV,WAAW,CAAC,+DAA+D,EACzE,YACI,mCACA,uCACJ;8CAED,YAAY,kBAAkB,OAAO,mBAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOvE;GA1VwB;KAAA"}}, {"offset": {"line": 1399, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1405, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/New%20folder%20%288%29/arabic-ecommerce/src/components/OrderDetailsModal.tsx"], "sourcesContent": ["'use client';\n\nimport { Order, OrderStatus } from '@/types';\nimport { useState } from 'react';\n\ninterface OrderDetailsModalProps {\n  order: Order | null;\n  isOpen: boolean;\n  onClose: () => void;\n  onUpdateStatus: (orderId: string, status: OrderStatus) => void;\n}\n\nconst statusLabels: Record<OrderStatus, string> = {\n  pending: 'في الانتظار',\n  confirmed: 'مؤكد',\n  processing: 'قيد المعالجة',\n  shipped: 'تم الشحن',\n  delivered: 'تم التسليم',\n  cancelled: 'ملغي',\n};\n\nconst statusColors: Record<OrderStatus, string> = {\n  pending: 'bg-yellow-100 text-yellow-800',\n  confirmed: 'bg-blue-100 text-blue-800',\n  processing: 'bg-purple-100 text-purple-800',\n  shipped: 'bg-indigo-100 text-indigo-800',\n  delivered: 'bg-green-100 text-green-800',\n  cancelled: 'bg-red-100 text-red-800',\n};\n\nconst paymentMethodLabels = {\n  cash: 'الدفع عند الاستلام',\n  visa: 'فيزا',\n  mastercard: 'ماستركارد',\n  paypal: 'PayPal',\n  apple_pay: 'Apple Pay',\n  stc_pay: 'STC Pay',\n};\n\nconst paymentStatusLabels = {\n  pending: 'في الانتظار',\n  paid: 'مدفوع',\n  failed: 'فشل',\n  refunded: 'مسترد',\n};\n\nexport default function OrderDetailsModal({ order, isOpen, onClose, onUpdateStatus }: OrderDetailsModalProps) {\n  const [selectedStatus, setSelectedStatus] = useState<OrderStatus>(order?.status || 'pending');\n  const [isUpdating, setIsUpdating] = useState(false);\n\n  if (!isOpen || !order) return null;\n\n  const handleStatusUpdate = async () => {\n    if (selectedStatus === order.status) return;\n\n    setIsUpdating(true);\n    try {\n      // Simulate API call\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      onUpdateStatus(order.id, selectedStatus);\n    } catch (error) {\n      console.error('Error updating order status:', error);\n    } finally {\n      setIsUpdating(false);\n    }\n  };\n\n  const handlePrint = () => {\n    window.print();\n  };\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n      <div className=\"bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto\">\n        {/* Header */}\n        <div className=\"px-6 py-4 border-b border-gray-200\">\n          <div className=\"flex justify-between items-center\">\n            <div>\n              <h2 className=\"text-xl font-bold text-gray-800\">تفاصيل الطلب</h2>\n              <p className=\"text-gray-600\">رقم الطلب: {order.orderNumber}</p>\n            </div>\n            <div className=\"flex items-center space-x-2 space-x-reverse\">\n              <button\n                onClick={handlePrint}\n                className=\"bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition-colors duration-200\"\n              >\n                طباعة\n              </button>\n              <button\n                onClick={onClose}\n                className=\"text-gray-400 hover:text-gray-600 text-2xl\"\n              >\n                ×\n              </button>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"p-6 space-y-6\">\n          {/* Order Status */}\n          <div className=\"bg-gray-50 rounded-lg p-4\">\n            <h3 className=\"text-lg font-semibold text-gray-800 mb-3\">حالة الطلب</h3>\n            <div className=\"flex items-center space-x-4 space-x-reverse\">\n              <span className={`inline-flex px-3 py-1 text-sm font-semibold rounded-full ${statusColors[order.status]}`}>\n                {statusLabels[order.status]}\n              </span>\n              <select\n                value={selectedStatus}\n                onChange={(e) => setSelectedStatus(e.target.value as OrderStatus)}\n                className=\"border border-gray-300 rounded-md px-3 py-1 text-sm\"\n              >\n                {Object.entries(statusLabels).map(([status, label]) => (\n                  <option key={status} value={status}>\n                    {label}\n                  </option>\n                ))}\n              </select>\n              {selectedStatus !== order.status && (\n                <button\n                  onClick={handleStatusUpdate}\n                  disabled={isUpdating}\n                  className={`px-4 py-1 text-sm text-white rounded-md transition-colors duration-200 ${\n                    isUpdating\n                      ? 'bg-gray-400 cursor-not-allowed'\n                      : 'bg-primary-600 hover:bg-primary-700'\n                  }`}\n                >\n                  {isUpdating ? 'جاري التحديث...' : 'تحديث الحالة'}\n                </button>\n              )}\n            </div>\n          </div>\n\n          {/* Customer Information */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n            <div className=\"bg-gray-50 rounded-lg p-4\">\n              <h3 className=\"text-lg font-semibold text-gray-800 mb-3\">معلومات العميل</h3>\n              <div className=\"space-y-2\">\n                <p><span className=\"font-medium\">الاسم:</span> {order.customerName}</p>\n                <p><span className=\"font-medium\">البريد الإلكتروني:</span> {order.customerEmail}</p>\n                <p><span className=\"font-medium\">تاريخ الطلب:</span> {order.createdAt.toLocaleDateString('ar-SA')}</p>\n                {order.deliveredAt && (\n                  <p><span className=\"font-medium\">تاريخ التسليم:</span> {order.deliveredAt.toLocaleDateString('ar-SA')}</p>\n                )}\n              </div>\n            </div>\n\n            <div className=\"bg-gray-50 rounded-lg p-4\">\n              <h3 className=\"text-lg font-semibold text-gray-800 mb-3\">عنوان الشحن</h3>\n              <div className=\"space-y-2\">\n                <p><span className=\"font-medium\">الاسم:</span> {order.shippingAddress.fullName}</p>\n                <p><span className=\"font-medium\">الهاتف:</span> {order.shippingAddress.phone}</p>\n                <p><span className=\"font-medium\">العنوان:</span> {order.shippingAddress.address}</p>\n                <p><span className=\"font-medium\">المدينة:</span> {order.shippingAddress.city}</p>\n                <p><span className=\"font-medium\">الرمز البريدي:</span> {order.shippingAddress.postalCode}</p>\n                <p><span className=\"font-medium\">البلد:</span> {order.shippingAddress.country}</p>\n              </div>\n            </div>\n          </div>\n\n          {/* Order Items */}\n          <div className=\"bg-gray-50 rounded-lg p-4\">\n            <h3 className=\"text-lg font-semibold text-gray-800 mb-3\">المنتجات</h3>\n            <div className=\"overflow-x-auto\">\n              <table className=\"min-w-full\">\n                <thead>\n                  <tr className=\"border-b border-gray-200\">\n                    <th className=\"text-right py-2\">المنتج</th>\n                    <th className=\"text-right py-2\">السعر</th>\n                    <th className=\"text-right py-2\">الكمية</th>\n                    <th className=\"text-right py-2\">المجموع</th>\n                  </tr>\n                </thead>\n                <tbody>\n                  {order.items.map((item) => (\n                    <tr key={item.id} className=\"border-b border-gray-100\">\n                      <td className=\"py-3\">\n                        <div className=\"flex items-center\">\n                          <img\n                            src={item.productImage}\n                            alt={item.productName}\n                            className=\"w-12 h-12 object-cover rounded-md ml-3\"\n                            onError={(e) => {\n                              e.currentTarget.src = 'https://via.placeholder.com/48x48?text=صورة';\n                            }}\n                          />\n                          <span className=\"font-medium\">{item.productName}</span>\n                        </div>\n                      </td>\n                      <td className=\"py-3\">{item.price.toLocaleString('ar-SA')} ر.س</td>\n                      <td className=\"py-3\">{item.quantity}</td>\n                      <td className=\"py-3 font-medium\">{item.total.toLocaleString('ar-SA')} ر.س</td>\n                    </tr>\n                  ))}\n                </tbody>\n              </table>\n            </div>\n          </div>\n\n          {/* Payment and Total */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n            <div className=\"bg-gray-50 rounded-lg p-4\">\n              <h3 className=\"text-lg font-semibold text-gray-800 mb-3\">معلومات الدفع</h3>\n              <div className=\"space-y-2\">\n                <p><span className=\"font-medium\">طريقة الدفع:</span> {paymentMethodLabels[order.paymentMethod]}</p>\n                <p><span className=\"font-medium\">حالة الدفع:</span>\n                  <span className={`mr-2 px-2 py-1 text-xs rounded-full ${\n                    order.paymentStatus === 'paid' ? 'bg-green-100 text-green-800' :\n                    order.paymentStatus === 'pending' ? 'bg-yellow-100 text-yellow-800' :\n                    order.paymentStatus === 'failed' ? 'bg-red-100 text-red-800' :\n                    'bg-gray-100 text-gray-800'\n                  }`}>\n                    {paymentStatusLabels[order.paymentStatus]}\n                  </span>\n                </p>\n              </div>\n            </div>\n\n            <div className=\"bg-gray-50 rounded-lg p-4\">\n              <h3 className=\"text-lg font-semibold text-gray-800 mb-3\">ملخص الطلب</h3>\n              <div className=\"space-y-2\">\n                <div className=\"flex justify-between\">\n                  <span>المجموع الفرعي:</span>\n                  <span>{order.subtotal.toLocaleString('ar-SA')} ر.س</span>\n                </div>\n                <div className=\"flex justify-between\">\n                  <span>الشحن:</span>\n                  <span>{order.shipping.toLocaleString('ar-SA')} ر.س</span>\n                </div>\n                <div className=\"flex justify-between\">\n                  <span>الضريبة:</span>\n                  <span>{order.tax.toLocaleString('ar-SA')} ر.س</span>\n                </div>\n                <div className=\"flex justify-between font-bold text-lg border-t border-gray-200 pt-2\">\n                  <span>المجموع الكلي:</span>\n                  <span>{order.total.toLocaleString('ar-SA')} ر.س</span>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Notes */}\n          {order.notes && (\n            <div className=\"bg-gray-50 rounded-lg p-4\">\n              <h3 className=\"text-lg font-semibold text-gray-800 mb-3\">ملاحظات</h3>\n              <p className=\"text-gray-700\">{order.notes}</p>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;;;AAHA;;AAYA,MAAM,eAA4C;IAChD,SAAS;IACT,WAAW;IACX,YAAY;IACZ,SAAS;IACT,WAAW;IACX,WAAW;AACb;AAEA,MAAM,eAA4C;IAChD,SAAS;IACT,WAAW;IACX,YAAY;IACZ,SAAS;IACT,WAAW;IACX,WAAW;AACb;AAEA,MAAM,sBAAsB;IAC1B,MAAM;IACN,MAAM;IACN,YAAY;IACZ,QAAQ;IACR,WAAW;IACX,SAAS;AACX;AAEA,MAAM,sBAAsB;IAC1B,SAAS;IACT,MAAM;IACN,QAAQ;IACR,UAAU;AACZ;AAEe,SAAS,kBAAkB,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,cAAc,EAA0B;;IAC1G,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe,OAAO,UAAU;IACnF,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,IAAI,CAAC,UAAU,CAAC,OAAO,OAAO;IAE9B,MAAM,qBAAqB;QACzB,IAAI,mBAAmB,MAAM,MAAM,EAAE;QAErC,cAAc;QACd,IAAI;YACF,oBAAoB;YACpB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YACjD,eAAe,MAAM,EAAE,EAAE;QAC3B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;QAChD,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,cAAc;QAClB,OAAO,KAAK;IACd;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAkC;;;;;;kDAChD,6LAAC;wCAAE,WAAU;;4CAAgB;4CAAY,MAAM,WAAW;;;;;;;;;;;;;0CAE5D,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,SAAS;wCACT,WAAU;kDACX;;;;;;kDAGD,6LAAC;wCACC,SAAS;wCACT,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;8BAOP,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA2C;;;;;;8CACzD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAW,CAAC,yDAAyD,EAAE,YAAY,CAAC,MAAM,MAAM,CAAC,EAAE;sDACtG,YAAY,CAAC,MAAM,MAAM,CAAC;;;;;;sDAE7B,6LAAC;4CACC,OAAO;4CACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;4CACjD,WAAU;sDAET,OAAO,OAAO,CAAC,cAAc,GAAG,CAAC,CAAC,CAAC,QAAQ,MAAM,iBAChD,6LAAC;oDAAoB,OAAO;8DACzB;mDADU;;;;;;;;;;wCAKhB,mBAAmB,MAAM,MAAM,kBAC9B,6LAAC;4CACC,SAAS;4CACT,UAAU;4CACV,WAAW,CAAC,uEAAuE,EACjF,aACI,mCACA,uCACJ;sDAED,aAAa,oBAAoB;;;;;;;;;;;;;;;;;;sCAO1C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEAAE,6LAAC;4DAAK,WAAU;sEAAc;;;;;;wDAAa;wDAAE,MAAM,YAAY;;;;;;;8DAClE,6LAAC;;sEAAE,6LAAC;4DAAK,WAAU;sEAAc;;;;;;wDAAyB;wDAAE,MAAM,aAAa;;;;;;;8DAC/E,6LAAC;;sEAAE,6LAAC;4DAAK,WAAU;sEAAc;;;;;;wDAAmB;wDAAE,MAAM,SAAS,CAAC,kBAAkB,CAAC;;;;;;;gDACxF,MAAM,WAAW,kBAChB,6LAAC;;sEAAE,6LAAC;4DAAK,WAAU;sEAAc;;;;;;wDAAqB;wDAAE,MAAM,WAAW,CAAC,kBAAkB,CAAC;;;;;;;;;;;;;;;;;;;8CAKnG,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEAAE,6LAAC;4DAAK,WAAU;sEAAc;;;;;;wDAAa;wDAAE,MAAM,eAAe,CAAC,QAAQ;;;;;;;8DAC9E,6LAAC;;sEAAE,6LAAC;4DAAK,WAAU;sEAAc;;;;;;wDAAc;wDAAE,MAAM,eAAe,CAAC,KAAK;;;;;;;8DAC5E,6LAAC;;sEAAE,6LAAC;4DAAK,WAAU;sEAAc;;;;;;wDAAe;wDAAE,MAAM,eAAe,CAAC,OAAO;;;;;;;8DAC/E,6LAAC;;sEAAE,6LAAC;4DAAK,WAAU;sEAAc;;;;;;wDAAe;wDAAE,MAAM,eAAe,CAAC,IAAI;;;;;;;8DAC5E,6LAAC;;sEAAE,6LAAC;4DAAK,WAAU;sEAAc;;;;;;wDAAqB;wDAAE,MAAM,eAAe,CAAC,UAAU;;;;;;;8DACxF,6LAAC;;sEAAE,6LAAC;4DAAK,WAAU;sEAAc;;;;;;wDAAa;wDAAE,MAAM,eAAe,CAAC,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;sCAMnF,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA2C;;;;;;8CACzD,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAM,WAAU;;0DACf,6LAAC;0DACC,cAAA,6LAAC;oDAAG,WAAU;;sEACZ,6LAAC;4DAAG,WAAU;sEAAkB;;;;;;sEAChC,6LAAC;4DAAG,WAAU;sEAAkB;;;;;;sEAChC,6LAAC;4DAAG,WAAU;sEAAkB;;;;;;sEAChC,6LAAC;4DAAG,WAAU;sEAAkB;;;;;;;;;;;;;;;;;0DAGpC,6LAAC;0DACE,MAAM,KAAK,CAAC,GAAG,CAAC,CAAC,qBAChB,6LAAC;wDAAiB,WAAU;;0EAC1B,6LAAC;gEAAG,WAAU;0EACZ,cAAA,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EACC,KAAK,KAAK,YAAY;4EACtB,KAAK,KAAK,WAAW;4EACrB,WAAU;4EACV,SAAS,CAAC;gFACR,EAAE,aAAa,CAAC,GAAG,GAAG;4EACxB;;;;;;sFAEF,6LAAC;4EAAK,WAAU;sFAAe,KAAK,WAAW;;;;;;;;;;;;;;;;;0EAGnD,6LAAC;gEAAG,WAAU;;oEAAQ,KAAK,KAAK,CAAC,cAAc,CAAC;oEAAS;;;;;;;0EACzD,6LAAC;gEAAG,WAAU;0EAAQ,KAAK,QAAQ;;;;;;0EACnC,6LAAC;gEAAG,WAAU;;oEAAoB,KAAK,KAAK,CAAC,cAAc,CAAC;oEAAS;;;;;;;;uDAhB9D,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAyB1B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEAAE,6LAAC;4DAAK,WAAU;sEAAc;;;;;;wDAAmB;wDAAE,mBAAmB,CAAC,MAAM,aAAa,CAAC;;;;;;;8DAC9F,6LAAC;;sEAAE,6LAAC;4DAAK,WAAU;sEAAc;;;;;;sEAC/B,6LAAC;4DAAK,WAAW,CAAC,oCAAoC,EACpD,MAAM,aAAa,KAAK,SAAS,gCACjC,MAAM,aAAa,KAAK,YAAY,kCACpC,MAAM,aAAa,KAAK,WAAW,4BACnC,6BACA;sEACC,mBAAmB,CAAC,MAAM,aAAa,CAAC;;;;;;;;;;;;;;;;;;;;;;;;8CAMjD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;sEAAK;;;;;;sEACN,6LAAC;;gEAAM,MAAM,QAAQ,CAAC,cAAc,CAAC;gEAAS;;;;;;;;;;;;;8DAEhD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;sEAAK;;;;;;sEACN,6LAAC;;gEAAM,MAAM,QAAQ,CAAC,cAAc,CAAC;gEAAS;;;;;;;;;;;;;8DAEhD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;sEAAK;;;;;;sEACN,6LAAC;;gEAAM,MAAM,GAAG,CAAC,cAAc,CAAC;gEAAS;;;;;;;;;;;;;8DAE3C,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;sEAAK;;;;;;sEACN,6LAAC;;gEAAM,MAAM,KAAK,CAAC,cAAc,CAAC;gEAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wBAOlD,MAAM,KAAK,kBACV,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA2C;;;;;;8CACzD,6LAAC;oCAAE,WAAU;8CAAiB,MAAM,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOvD;GA9MwB;KAAA"}}, {"offset": {"line": 2263, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2269, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/New%20folder%20%288%29/arabic-ecommerce/src/components/DeleteConfirmModal.tsx"], "sourcesContent": ["'use client';\n\ninterface DeleteConfirmModalProps {\n  isOpen: boolean;\n  title: string;\n  message: string;\n  onConfirm: () => void;\n  onCancel: () => void;\n  isLoading?: boolean;\n}\n\nexport default function DeleteConfirmModal({\n  isOpen,\n  title,\n  message,\n  onConfirm,\n  onCancel,\n  isLoading = false,\n}: DeleteConfirmModalProps) {\n  if (!isOpen) return null;\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n      <div className=\"bg-white rounded-lg shadow-xl max-w-md w-full\">\n        <div className=\"p-6\">\n          {/* Icon */}\n          <div className=\"flex items-center justify-center w-12 h-12 mx-auto mb-4 bg-red-100 rounded-full\">\n            <svg\n              className=\"w-6 h-6 text-red-600\"\n              fill=\"none\"\n              stroke=\"currentColor\"\n              viewBox=\"0 0 24 24\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                strokeWidth={2}\n                d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z\"\n              />\n            </svg>\n          </div>\n\n          {/* Title */}\n          <h3 className=\"text-lg font-medium text-gray-900 text-center mb-2\">\n            {title}\n          </h3>\n\n          {/* Message */}\n          <p className=\"text-sm text-gray-500 text-center mb-6\">\n            {message}\n          </p>\n\n          {/* Actions */}\n          <div className=\"flex space-x-3 space-x-reverse\">\n            <button\n              onClick={onCancel}\n              disabled={isLoading}\n              className=\"flex-1 px-4 py-2 text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed\"\n            >\n              إلغاء\n            </button>\n            <button\n              onClick={onConfirm}\n              disabled={isLoading}\n              className={`flex-1 px-4 py-2 text-white rounded-lg transition-colors duration-200 ${\n                isLoading\n                  ? 'bg-gray-400 cursor-not-allowed'\n                  : 'bg-red-600 hover:bg-red-700'\n              }`}\n            >\n              {isLoading ? 'جاري الحذف...' : 'تأكيد الحذف'}\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAWe,SAAS,mBAAmB,EACzC,MAAM,EACN,KAAK,EACL,OAAO,EACP,SAAS,EACT,QAAQ,EACR,YAAY,KAAK,EACO;IACxB,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,WAAU;4BACV,MAAK;4BACL,QAAO;4BACP,SAAQ;sCAER,cAAA,6LAAC;gCACC,eAAc;gCACd,gBAAe;gCACf,aAAa;gCACb,GAAE;;;;;;;;;;;;;;;;kCAMR,6LAAC;wBAAG,WAAU;kCACX;;;;;;kCAIH,6LAAC;wBAAE,WAAU;kCACV;;;;;;kCAIH,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS;gCACT,UAAU;gCACV,WAAU;0CACX;;;;;;0CAGD,6LAAC;gCACC,SAAS;gCACT,UAAU;gCACV,WAAW,CAAC,sEAAsE,EAChF,YACI,mCACA,+BACJ;0CAED,YAAY,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO7C;KAlEwB"}}, {"offset": {"line": 2379, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2385, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/New%20folder%20%288%29/arabic-ecommerce/src/components/Charts.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\n\ninterface ChartData {\n  label: string;\n  value: number;\n  color?: string;\n}\n\ninterface LineChartProps {\n  data: ChartData[];\n  title: string;\n  height?: number;\n  color?: string;\n}\n\nexport function LineChart({ data, title, height = 300, color = '#3B82F6' }: LineChartProps) {\n  if (data.length === 0) {\n    return (\n      <div className=\"bg-white rounded-lg shadow p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-800 mb-4\">{title}</h3>\n        <div className=\"flex items-center justify-center h-64 text-gray-500\">\n          لا توجد بيانات للعرض\n        </div>\n      </div>\n    );\n  }\n\n  const maxValue = Math.max(...data.map(d => d.value));\n  const minValue = Math.min(...data.map(d => d.value));\n  const range = maxValue - minValue || 1;\n\n  return (\n    <div className=\"bg-white rounded-lg shadow p-6\">\n      <h3 className=\"text-lg font-semibold text-gray-800 mb-4\">{title}</h3>\n      <div className=\"relative\" style={{ height }}>\n        {/* Y-axis labels */}\n        <div className=\"absolute left-0 top-0 h-full flex flex-col justify-between text-xs text-gray-500 pr-2\">\n          <span>{maxValue.toLocaleString('ar-SA')}</span>\n          <span>{((maxValue + minValue) / 2).toLocaleString('ar-SA')}</span>\n          <span>{minValue.toLocaleString('ar-SA')}</span>\n        </div>\n        \n        {/* Chart area */}\n        <div className=\"mr-12 h-full relative\">\n          {/* Grid lines */}\n          <div className=\"absolute inset-0\">\n            {[0, 25, 50, 75, 100].map(percent => (\n              <div\n                key={percent}\n                className=\"absolute w-full border-t border-gray-200\"\n                style={{ top: `${percent}%` }}\n              />\n            ))}\n          </div>\n          \n          {/* Line chart */}\n          <svg className=\"absolute inset-0 w-full h-full\">\n            <polyline\n              fill=\"none\"\n              stroke={color}\n              strokeWidth=\"2\"\n              points={data.map((point, index) => {\n                const x = (index / (data.length - 1)) * 100;\n                const y = 100 - ((point.value - minValue) / range) * 100;\n                return `${x}%,${y}%`;\n              }).join(' ')}\n            />\n            {/* Data points */}\n            {data.map((point, index) => {\n              const x = (index / (data.length - 1)) * 100;\n              const y = 100 - ((point.value - minValue) / range) * 100;\n              return (\n                <circle\n                  key={index}\n                  cx={`${x}%`}\n                  cy={`${y}%`}\n                  r=\"4\"\n                  fill={color}\n                  className=\"hover:r-6 transition-all duration-200\"\n                />\n              );\n            })}\n          </svg>\n        </div>\n        \n        {/* X-axis labels */}\n        <div className=\"absolute bottom-0 right-12 left-0 flex justify-between text-xs text-gray-500 mt-2\">\n          {data.map((point, index) => (\n            <span key={index} className=\"transform -rotate-45 origin-top-right\">\n              {point.label}\n            </span>\n          ))}\n        </div>\n      </div>\n    </div>\n  );\n}\n\ninterface BarChartProps {\n  data: ChartData[];\n  title: string;\n  height?: number;\n  horizontal?: boolean;\n}\n\nexport function BarChart({ data, title, height = 300, horizontal = false }: BarChartProps) {\n  if (data.length === 0) {\n    return (\n      <div className=\"bg-white rounded-lg shadow p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-800 mb-4\">{title}</h3>\n        <div className=\"flex items-center justify-center h-64 text-gray-500\">\n          لا توجد بيانات للعرض\n        </div>\n      </div>\n    );\n  }\n\n  const maxValue = Math.max(...data.map(d => d.value));\n  const colors = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#06B6D4', '#84CC16', '#F97316'];\n\n  if (horizontal) {\n    return (\n      <div className=\"bg-white rounded-lg shadow p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-800 mb-4\">{title}</h3>\n        <div className=\"space-y-3\">\n          {data.map((item, index) => (\n            <div key={index} className=\"flex items-center\">\n              <div className=\"w-24 text-sm text-gray-600 text-right\">{item.label}</div>\n              <div className=\"flex-1 mx-3\">\n                <div className=\"bg-gray-200 rounded-full h-6 relative\">\n                  <div\n                    className=\"h-6 rounded-full flex items-center justify-end pr-2 text-white text-xs font-medium transition-all duration-500\"\n                    style={{\n                      width: `${(item.value / maxValue) * 100}%`,\n                      backgroundColor: item.color || colors[index % colors.length]\n                    }}\n                  >\n                    {item.value > 0 && item.value.toLocaleString('ar-SA')}\n                  </div>\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"bg-white rounded-lg shadow p-6\">\n      <h3 className=\"text-lg font-semibold text-gray-800 mb-4\">{title}</h3>\n      <div className=\"relative\" style={{ height }}>\n        {/* Y-axis labels */}\n        <div className=\"absolute left-0 top-0 h-full flex flex-col justify-between text-xs text-gray-500 pr-2\">\n          <span>{maxValue.toLocaleString('ar-SA')}</span>\n          <span>{(maxValue * 0.75).toLocaleString('ar-SA')}</span>\n          <span>{(maxValue * 0.5).toLocaleString('ar-SA')}</span>\n          <span>{(maxValue * 0.25).toLocaleString('ar-SA')}</span>\n          <span>0</span>\n        </div>\n        \n        {/* Chart area */}\n        <div className=\"mr-12 h-full relative\">\n          {/* Grid lines */}\n          <div className=\"absolute inset-0\">\n            {[0, 25, 50, 75, 100].map(percent => (\n              <div\n                key={percent}\n                className=\"absolute w-full border-t border-gray-200\"\n                style={{ top: `${percent}%` }}\n              />\n            ))}\n          </div>\n          \n          {/* Bars */}\n          <div className=\"absolute inset-0 flex items-end justify-between px-2\">\n            {data.map((item, index) => (\n              <div key={index} className=\"flex flex-col items-center flex-1 mx-1\">\n                <div\n                  className=\"w-full rounded-t transition-all duration-500 hover:opacity-80 relative group\"\n                  style={{\n                    height: `${(item.value / maxValue) * 100}%`,\n                    backgroundColor: item.color || colors[index % colors.length]\n                  }}\n                >\n                  {/* Tooltip */}\n                  <div className=\"absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-800 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap\">\n                    {item.value.toLocaleString('ar-SA')}\n                  </div>\n                </div>\n                <div className=\"text-xs text-gray-600 mt-2 text-center transform -rotate-45 origin-top\">\n                  {item.label}\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n\ninterface PieChartProps {\n  data: ChartData[];\n  title: string;\n  size?: number;\n}\n\nexport function PieChart({ data, title, size = 200 }: PieChartProps) {\n  if (data.length === 0) {\n    return (\n      <div className=\"bg-white rounded-lg shadow p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-800 mb-4\">{title}</h3>\n        <div className=\"flex items-center justify-center h-64 text-gray-500\">\n          لا توجد بيانات للعرض\n        </div>\n      </div>\n    );\n  }\n\n  const total = data.reduce((sum, item) => sum + item.value, 0);\n  const colors = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#06B6D4', '#84CC16', '#F97316'];\n  \n  let cumulativePercentage = 0;\n\n  return (\n    <div className=\"bg-white rounded-lg shadow p-6\">\n      <h3 className=\"text-lg font-semibold text-gray-800 mb-4\">{title}</h3>\n      <div className=\"flex items-center justify-center\">\n        <div className=\"relative\">\n          <svg width={size} height={size} className=\"transform -rotate-90\">\n            {data.map((item, index) => {\n              const percentage = (item.value / total) * 100;\n              const strokeDasharray = `${percentage} ${100 - percentage}`;\n              const strokeDashoffset = -cumulativePercentage;\n              \n              cumulativePercentage += percentage;\n              \n              return (\n                <circle\n                  key={index}\n                  cx={size / 2}\n                  cy={size / 2}\n                  r={size / 2 - 20}\n                  fill=\"transparent\"\n                  stroke={item.color || colors[index % colors.length]}\n                  strokeWidth=\"20\"\n                  strokeDasharray={strokeDasharray}\n                  strokeDashoffset={strokeDashoffset}\n                  className=\"transition-all duration-500 hover:stroke-width-24\"\n                />\n              );\n            })}\n          </svg>\n          \n          {/* Center text */}\n          <div className=\"absolute inset-0 flex items-center justify-center\">\n            <div className=\"text-center\">\n              <div className=\"text-2xl font-bold text-gray-800\">{total.toLocaleString('ar-SA')}</div>\n              <div className=\"text-sm text-gray-500\">المجموع</div>\n            </div>\n          </div>\n        </div>\n        \n        {/* Legend */}\n        <div className=\"mr-6 space-y-2\">\n          {data.map((item, index) => (\n            <div key={index} className=\"flex items-center\">\n              <div\n                className=\"w-4 h-4 rounded ml-2\"\n                style={{ backgroundColor: item.color || colors[index % colors.length] }}\n              />\n              <div className=\"text-sm\">\n                <div className=\"font-medium text-gray-800\">{item.label}</div>\n                <div className=\"text-gray-500\">\n                  {item.value.toLocaleString('ar-SA')} ({((item.value / total) * 100).toFixed(1)}%)\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n    </div>\n  );\n}\n\ninterface StatCardProps {\n  title: string;\n  value: string | number;\n  change?: number;\n  icon: string;\n  color?: string;\n}\n\nexport function StatCard({ title, value, change, icon, color = 'blue' }: StatCardProps) {\n  const colorClasses = {\n    blue: 'bg-blue-500',\n    green: 'bg-green-500',\n    yellow: 'bg-yellow-500',\n    red: 'bg-red-500',\n    purple: 'bg-purple-500',\n    indigo: 'bg-indigo-500'\n  };\n\n  return (\n    <div className=\"bg-white rounded-lg shadow p-6\">\n      <div className=\"flex items-center\">\n        <div className={`text-3xl p-3 rounded-lg ${colorClasses[color as keyof typeof colorClasses]} text-white`}>\n          {icon}\n        </div>\n        <div className=\"mr-4 flex-1\">\n          <p className=\"text-sm font-medium text-gray-600\">{title}</p>\n          <p className=\"text-2xl font-bold text-gray-900\">\n            {typeof value === 'number' ? value.toLocaleString('ar-SA') : value}\n          </p>\n          {change !== undefined && (\n            <p className={`text-sm ${change >= 0 ? 'text-green-600' : 'text-red-600'}`}>\n              {change >= 0 ? '↗' : '↘'} {Math.abs(change).toFixed(1)}%\n            </p>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;;AAiBO,SAAS,UAAU,EAAE,IAAI,EAAE,KAAK,EAAE,SAAS,GAAG,EAAE,QAAQ,SAAS,EAAkB;IACxF,IAAI,KAAK,MAAM,KAAK,GAAG;QACrB,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAG,WAAU;8BAA4C;;;;;;8BAC1D,6LAAC;oBAAI,WAAU;8BAAsD;;;;;;;;;;;;IAK3E;IAEA,MAAM,WAAW,KAAK,GAAG,IAAI,KAAK,GAAG,CAAC,CAAA,IAAK,EAAE,KAAK;IAClD,MAAM,WAAW,KAAK,GAAG,IAAI,KAAK,GAAG,CAAC,CAAA,IAAK,EAAE,KAAK;IAClD,MAAM,QAAQ,WAAW,YAAY;IAErC,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAG,WAAU;0BAA4C;;;;;;0BAC1D,6LAAC;gBAAI,WAAU;gBAAW,OAAO;oBAAE;gBAAO;;kCAExC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;0CAAM,SAAS,cAAc,CAAC;;;;;;0CAC/B,6LAAC;0CAAM,CAAC,CAAC,WAAW,QAAQ,IAAI,CAAC,EAAE,cAAc,CAAC;;;;;;0CAClD,6LAAC;0CAAM,SAAS,cAAc,CAAC;;;;;;;;;;;;kCAIjC,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;0CACZ;oCAAC;oCAAG;oCAAI;oCAAI;oCAAI;iCAAI,CAAC,GAAG,CAAC,CAAA,wBACxB,6LAAC;wCAEC,WAAU;wCACV,OAAO;4CAAE,KAAK,GAAG,QAAQ,CAAC,CAAC;wCAAC;uCAFvB;;;;;;;;;;0CAQX,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,MAAK;wCACL,QAAQ;wCACR,aAAY;wCACZ,QAAQ,KAAK,GAAG,CAAC,CAAC,OAAO;4CACvB,MAAM,IAAI,AAAC,QAAQ,CAAC,KAAK,MAAM,GAAG,CAAC,IAAK;4CACxC,MAAM,IAAI,MAAM,AAAC,CAAC,MAAM,KAAK,GAAG,QAAQ,IAAI,QAAS;4CACrD,OAAO,GAAG,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;wCACtB,GAAG,IAAI,CAAC;;;;;;oCAGT,KAAK,GAAG,CAAC,CAAC,OAAO;wCAChB,MAAM,IAAI,AAAC,QAAQ,CAAC,KAAK,MAAM,GAAG,CAAC,IAAK;wCACxC,MAAM,IAAI,MAAM,AAAC,CAAC,MAAM,KAAK,GAAG,QAAQ,IAAI,QAAS;wCACrD,qBACE,6LAAC;4CAEC,IAAI,GAAG,EAAE,CAAC,CAAC;4CACX,IAAI,GAAG,EAAE,CAAC,CAAC;4CACX,GAAE;4CACF,MAAM;4CACN,WAAU;2CALL;;;;;oCAQX;;;;;;;;;;;;;kCAKJ,6LAAC;wBAAI,WAAU;kCACZ,KAAK,GAAG,CAAC,CAAC,OAAO,sBAChB,6LAAC;gCAAiB,WAAU;0CACzB,MAAM,KAAK;+BADH;;;;;;;;;;;;;;;;;;;;;;AAQvB;KAjFgB;AA0FT,SAAS,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,SAAS,GAAG,EAAE,aAAa,KAAK,EAAiB;IACvF,IAAI,KAAK,MAAM,KAAK,GAAG;QACrB,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAG,WAAU;8BAA4C;;;;;;8BAC1D,6LAAC;oBAAI,WAAU;8BAAsD;;;;;;;;;;;;IAK3E;IAEA,MAAM,WAAW,KAAK,GAAG,IAAI,KAAK,GAAG,CAAC,CAAA,IAAK,EAAE,KAAK;IAClD,MAAM,SAAS;QAAC;QAAW;QAAW;QAAW;QAAW;QAAW;QAAW;QAAW;KAAU;IAEvG,IAAI,YAAY;QACd,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAG,WAAU;8BAA4C;;;;;;8BAC1D,6LAAC;oBAAI,WAAU;8BACZ,KAAK,GAAG,CAAC,CAAC,MAAM,sBACf,6LAAC;4BAAgB,WAAU;;8CACzB,6LAAC;oCAAI,WAAU;8CAAyC,KAAK,KAAK;;;;;;8CAClE,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CACC,WAAU;4CACV,OAAO;gDACL,OAAO,GAAG,AAAC,KAAK,KAAK,GAAG,WAAY,IAAI,CAAC,CAAC;gDAC1C,iBAAiB,KAAK,KAAK,IAAI,MAAM,CAAC,QAAQ,OAAO,MAAM,CAAC;4CAC9D;sDAEC,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,CAAC,cAAc,CAAC;;;;;;;;;;;;;;;;;2BAX3C;;;;;;;;;;;;;;;;IAoBpB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAG,WAAU;0BAA4C;;;;;;0BAC1D,6LAAC;gBAAI,WAAU;gBAAW,OAAO;oBAAE;gBAAO;;kCAExC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;0CAAM,SAAS,cAAc,CAAC;;;;;;0CAC/B,6LAAC;0CAAM,CAAC,WAAW,IAAI,EAAE,cAAc,CAAC;;;;;;0CACxC,6LAAC;0CAAM,CAAC,WAAW,GAAG,EAAE,cAAc,CAAC;;;;;;0CACvC,6LAAC;0CAAM,CAAC,WAAW,IAAI,EAAE,cAAc,CAAC;;;;;;0CACxC,6LAAC;0CAAK;;;;;;;;;;;;kCAIR,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;0CACZ;oCAAC;oCAAG;oCAAI;oCAAI;oCAAI;iCAAI,CAAC,GAAG,CAAC,CAAA,wBACxB,6LAAC;wCAEC,WAAU;wCACV,OAAO;4CAAE,KAAK,GAAG,QAAQ,CAAC,CAAC;wCAAC;uCAFvB;;;;;;;;;;0CAQX,6LAAC;gCAAI,WAAU;0CACZ,KAAK,GAAG,CAAC,CAAC,MAAM,sBACf,6LAAC;wCAAgB,WAAU;;0DACzB,6LAAC;gDACC,WAAU;gDACV,OAAO;oDACL,QAAQ,GAAG,AAAC,KAAK,KAAK,GAAG,WAAY,IAAI,CAAC,CAAC;oDAC3C,iBAAiB,KAAK,KAAK,IAAI,MAAM,CAAC,QAAQ,OAAO,MAAM,CAAC;gDAC9D;0DAGA,cAAA,6LAAC;oDAAI,WAAU;8DACZ,KAAK,KAAK,CAAC,cAAc,CAAC;;;;;;;;;;;0DAG/B,6LAAC;gDAAI,WAAU;0DACZ,KAAK,KAAK;;;;;;;uCAdL;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuBxB;MA/FgB;AAuGT,SAAS,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,GAAG,EAAiB;IACjE,IAAI,KAAK,MAAM,KAAK,GAAG;QACrB,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAG,WAAU;8BAA4C;;;;;;8BAC1D,6LAAC;oBAAI,WAAU;8BAAsD;;;;;;;;;;;;IAK3E;IAEA,MAAM,QAAQ,KAAK,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,KAAK,EAAE;IAC3D,MAAM,SAAS;QAAC;QAAW;QAAW;QAAW;QAAW;QAAW;QAAW;QAAW;KAAU;IAEvG,IAAI,uBAAuB;IAE3B,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAG,WAAU;0BAA4C;;;;;;0BAC1D,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,OAAO;gCAAM,QAAQ;gCAAM,WAAU;0CACvC,KAAK,GAAG,CAAC,CAAC,MAAM;oCACf,MAAM,aAAa,AAAC,KAAK,KAAK,GAAG,QAAS;oCAC1C,MAAM,kBAAkB,GAAG,WAAW,CAAC,EAAE,MAAM,YAAY;oCAC3D,MAAM,mBAAmB,CAAC;oCAE1B,wBAAwB;oCAExB,qBACE,6LAAC;wCAEC,IAAI,OAAO;wCACX,IAAI,OAAO;wCACX,GAAG,OAAO,IAAI;wCACd,MAAK;wCACL,QAAQ,KAAK,KAAK,IAAI,MAAM,CAAC,QAAQ,OAAO,MAAM,CAAC;wCACnD,aAAY;wCACZ,iBAAiB;wCACjB,kBAAkB;wCAClB,WAAU;uCATL;;;;;gCAYX;;;;;;0CAIF,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAAoC,MAAM,cAAc,CAAC;;;;;;sDACxE,6LAAC;4CAAI,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;kCAM7C,6LAAC;wBAAI,WAAU;kCACZ,KAAK,GAAG,CAAC,CAAC,MAAM,sBACf,6LAAC;gCAAgB,WAAU;;kDACzB,6LAAC;wCACC,WAAU;wCACV,OAAO;4CAAE,iBAAiB,KAAK,KAAK,IAAI,MAAM,CAAC,QAAQ,OAAO,MAAM,CAAC;wCAAC;;;;;;kDAExE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAA6B,KAAK,KAAK;;;;;;0DACtD,6LAAC;gDAAI,WAAU;;oDACZ,KAAK,KAAK,CAAC,cAAc,CAAC;oDAAS;oDAAG,CAAC,AAAC,KAAK,KAAK,GAAG,QAAS,GAAG,EAAE,OAAO,CAAC;oDAAG;;;;;;;;;;;;;;+BAR3E;;;;;;;;;;;;;;;;;;;;;;AAiBtB;MA5EgB;AAsFT,SAAS,SAAS,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,QAAQ,MAAM,EAAiB;IACpF,MAAM,eAAe;QACnB,MAAM;QACN,OAAO;QACP,QAAQ;QACR,KAAK;QACL,QAAQ;QACR,QAAQ;IACV;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAW,CAAC,wBAAwB,EAAE,YAAY,CAAC,MAAmC,CAAC,WAAW,CAAC;8BACrG;;;;;;8BAEH,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAE,WAAU;sCAAqC;;;;;;sCAClD,6LAAC;4BAAE,WAAU;sCACV,OAAO,UAAU,WAAW,MAAM,cAAc,CAAC,WAAW;;;;;;wBAE9D,WAAW,2BACV,6LAAC;4BAAE,WAAW,CAAC,QAAQ,EAAE,UAAU,IAAI,mBAAmB,gBAAgB;;gCACvE,UAAU,IAAI,MAAM;gCAAI;gCAAE,KAAK,GAAG,CAAC,QAAQ,OAAO,CAAC;gCAAG;;;;;;;;;;;;;;;;;;;;;;;;AAOrE;MA9BgB"}}, {"offset": {"line": 3105, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3111, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/New%20folder%20%288%29/arabic-ecommerce/src/app/admin/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\nimport { Product, Order, OrderStatus, User } from '@/types';\nimport { useProducts } from '@/contexts/ProductContext';\nimport { useCategories, Category } from '@/contexts/CategoryContext';\nimport { useOrders } from '@/contexts/OrderContext';\nimport { useUsers } from '@/contexts/UserContext';\nimport { useReports } from '@/contexts/ReportsContext';\nimport ProductForm from '@/components/ProductForm';\nimport CategoryForm from '@/components/CategoryForm';\nimport UserForm from '@/components/UserForm';\nimport OrderDetailsModal from '@/components/OrderDetailsModal';\nimport DeleteConfirmModal from '@/components/DeleteConfirmModal';\nimport { LineChart, BarChart, PieChart, StatCard } from '@/components/Charts';\n\nexport default function AdminDashboard() {\n  const [activeTab, setActiveTab] = useState('overview');\n  const [isProductFormOpen, setIsProductFormOpen] = useState(false);\n  const [editingProduct, setEditingProduct] = useState<Product | null>(null);\n  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);\n  const [productToDelete, setProductToDelete] = useState<Product | null>(null);\n  const [isDeleting, setIsDeleting] = useState(false);\n\n  // Category management states\n  const [isCategoryFormOpen, setIsCategoryFormOpen] = useState(false);\n  const [editingCategory, setEditingCategory] = useState<Category | null>(null);\n  const [categoryToDelete, setCategoryToDelete] = useState<Category | null>(null);\n\n  // Order management states\n  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);\n  const [isOrderDetailsOpen, setIsOrderDetailsOpen] = useState(false);\n  const [orderStatusFilter, setOrderStatusFilter] = useState<OrderStatus | 'all'>('all');\n\n  // User management states\n  const [isUserFormOpen, setIsUserFormOpen] = useState(false);\n  const [editingUser, setEditingUser] = useState<User | null>(null);\n  const [userToDelete, setUserToDelete] = useState<User | null>(null);\n  const [userRoleFilter, setUserRoleFilter] = useState<'all' | 'admin' | 'customer'>('all');\n  const [userStatusFilter, setUserStatusFilter] = useState<'all' | 'active' | 'inactive'>('all');\n\n  const { products, addProduct, updateProduct, deleteProduct, isLoading } = useProducts();\n  const { categories, addCategory, updateCategory, deleteCategory: deleteCategoryFromContext, isLoading: categoriesLoading } = useCategories();\n  const { orders, updateOrderStatus, getTotalRevenue, getOrdersCount, getRecentOrders, isLoading: ordersLoading } = useOrders();\n  const { users, addUser, updateUser, deleteUser, toggleUserStatus, getTotalUsers, getActiveUsersCount, getRecentUsers, isLoading: usersLoading } = useUsers();\n  const {\n    getDailySales,\n    getWeeklySales,\n    getMonthlySales,\n    getTopSellingProducts,\n    getMostProfitableProducts,\n    getTopCustomers,\n    getSummaryStats,\n    getTotalProfit,\n    getRevenueGrowth,\n    getOrderGrowth,\n    isLoading: reportsLoading\n  } = useReports();\n\n  const stats = {\n    totalProducts: products.length,\n    totalCategories: categories.length,\n    totalUsers: getTotalUsers(),\n    activeUsers: getActiveUsersCount(),\n    totalOrders: getOrdersCount(),\n    totalRevenue: getTotalRevenue(),\n  };\n\n  // Product management functions\n  const handleAddProduct = () => {\n    setEditingProduct(null);\n    setIsProductFormOpen(true);\n  };\n\n  const handleEditProduct = (product: Product) => {\n    setEditingProduct(product);\n    setIsProductFormOpen(true);\n  };\n\n  const handleDeleteProduct = (product: Product) => {\n    setProductToDelete(product);\n    setIsDeleteModalOpen(true);\n  };\n\n  const handleSaveProduct = (productData: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>) => {\n    if (editingProduct) {\n      // Update existing product\n      updateProduct(editingProduct.id, productData);\n    } else {\n      // Add new product\n      addProduct(productData);\n    }\n  };\n\n  const confirmDeleteProduct = async () => {\n    if (!productToDelete) return;\n\n    setIsDeleting(true);\n    try {\n      // Simulate API call\n      await new Promise(resolve => setTimeout(resolve, 1000));\n\n      deleteProduct(productToDelete.id);\n      setIsDeleteModalOpen(false);\n      setProductToDelete(null);\n    } catch (error) {\n      console.error('Error deleting product:', error);\n    } finally {\n      setIsDeleting(false);\n    }\n  };\n\n  // Category management functions\n  const handleAddCategory = () => {\n    setEditingCategory(null);\n    setIsCategoryFormOpen(true);\n  };\n\n  const handleEditCategory = (category: Category) => {\n    setEditingCategory(category);\n    setIsCategoryFormOpen(true);\n  };\n\n  const handleDeleteCategory = (category: Category) => {\n    setCategoryToDelete(category);\n    setIsDeleteModalOpen(true);\n  };\n\n  const handleSaveCategory = (categoryData: Omit<Category, 'id' | 'productCount' | 'createdAt' | 'updatedAt'>) => {\n    if (editingCategory) {\n      // Update existing category\n      updateCategory(editingCategory.id, categoryData);\n    } else {\n      // Add new category\n      addCategory(categoryData);\n    }\n  };\n\n  const confirmDeleteCategory = async () => {\n    if (!categoryToDelete) return;\n\n    setIsDeleting(true);\n    try {\n      // Simulate API call\n      await new Promise(resolve => setTimeout(resolve, 1000));\n\n      deleteCategoryFromContext(categoryToDelete.id);\n      setIsDeleteModalOpen(false);\n      setCategoryToDelete(null);\n    } catch (error) {\n      console.error('Error deleting category:', error);\n    } finally {\n      setIsDeleting(false);\n    }\n  };\n\n  // Order management functions\n  const handleViewOrder = (order: Order) => {\n    setSelectedOrder(order);\n    setIsOrderDetailsOpen(true);\n  };\n\n  const handleUpdateOrderStatus = (orderId: string, status: OrderStatus) => {\n    updateOrderStatus(orderId, status);\n    // Update the selected order if it's the one being updated\n    if (selectedOrder && selectedOrder.id === orderId) {\n      setSelectedOrder({ ...selectedOrder, status });\n    }\n  };\n\n  const getFilteredOrders = () => {\n    if (orderStatusFilter === 'all') {\n      return orders;\n    }\n    return orders.filter(order => order.status === orderStatusFilter);\n  };\n\n  // User management functions\n  const handleAddUser = () => {\n    setEditingUser(null);\n    setIsUserFormOpen(true);\n  };\n\n  const handleEditUser = (user: User) => {\n    setEditingUser(user);\n    setIsUserFormOpen(true);\n  };\n\n  const handleDeleteUser = (user: User) => {\n    setUserToDelete(user);\n    setIsDeleteModalOpen(true);\n  };\n\n  const handleToggleUserStatus = (userId: string) => {\n    toggleUserStatus(userId);\n  };\n\n  const handleSaveUser = (userData: Omit<User, 'id' | 'totalOrders' | 'totalSpent' | 'createdAt' | 'updatedAt'>) => {\n    if (editingUser) {\n      // Update existing user\n      updateUser(editingUser.id, userData);\n    } else {\n      // Add new user\n      addUser(userData);\n    }\n  };\n\n  const confirmDeleteUser = async () => {\n    if (!userToDelete) return;\n\n    setIsDeleting(true);\n    try {\n      // Simulate API call\n      await new Promise(resolve => setTimeout(resolve, 1000));\n\n      deleteUser(userToDelete.id);\n      setIsDeleteModalOpen(false);\n      setUserToDelete(null);\n    } catch (error) {\n      console.error('Error deleting user:', error);\n    } finally {\n      setIsDeleting(false);\n    }\n  };\n\n  const getFilteredUsers = () => {\n    let filtered = users;\n\n    if (userRoleFilter !== 'all') {\n      filtered = filtered.filter(user => user.role === userRoleFilter);\n    }\n\n    if (userStatusFilter !== 'all') {\n      filtered = filtered.filter(user =>\n        userStatusFilter === 'active' ? user.isActive : !user.isActive\n      );\n    }\n\n    return filtered;\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <div className=\"bg-white shadow\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center py-6\">\n            <div>\n              <Link href=\"/\" className=\"text-2xl font-bold text-primary-600\">\n                🛍️ المتجر العربي\n              </Link>\n              <p className=\"text-gray-600 mt-1\">لوحة التحكم</p>\n            </div>\n            <div className=\"flex items-center space-x-4 space-x-reverse\">\n              <span className=\"text-gray-700\">مرحباً، أحمد</span>\n              <Link\n                href=\"/\"\n                className=\"bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg transition-colors duration-200\"\n              >\n                عرض المتجر\n              </Link>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Navigation Tabs */}\n        <div className=\"border-b border-gray-200 mb-8\">\n          <nav className=\"-mb-px flex space-x-8 space-x-reverse\">\n            {[\n              { id: 'overview', name: 'نظرة عامة', icon: '📊' },\n              { id: 'products', name: 'المنتجات', icon: '📦' },\n              { id: 'categories', name: 'التصنيفات', icon: '🏷️' },\n              { id: 'orders', name: 'الطلبات', icon: '🛒' },\n              { id: 'users', name: 'المستخدمين', icon: '👥' },\n              { id: 'reports', name: 'التقارير', icon: '📈' },\n            ].map((tab) => (\n              <button\n                key={tab.id}\n                onClick={() => setActiveTab(tab.id)}\n                className={`py-2 px-1 border-b-2 font-medium text-sm ${\n                  activeTab === tab.id\n                    ? 'border-primary-500 text-primary-600'\n                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                }`}\n              >\n                {tab.icon} {tab.name}\n              </button>\n            ))}\n          </nav>\n        </div>\n\n        {/* Overview Tab */}\n        {activeTab === 'overview' && (\n          <div>\n            <h2 className=\"text-2xl font-bold text-gray-800 mb-6\">نظرة عامة</h2>\n\n            {/* Stats Cards */}\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-5 gap-6 mb-8\">\n              <div className=\"bg-white rounded-lg shadow p-6\">\n                <div className=\"flex items-center\">\n                  <div className=\"text-3xl\">📦</div>\n                  <div className=\"mr-4\">\n                    <p className=\"text-sm font-medium text-gray-600\">إجمالي المنتجات</p>\n                    <p className=\"text-2xl font-bold text-gray-900\">{stats.totalProducts}</p>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"bg-white rounded-lg shadow p-6\">\n                <div className=\"flex items-center\">\n                  <div className=\"text-3xl\">🏷️</div>\n                  <div className=\"mr-4\">\n                    <p className=\"text-sm font-medium text-gray-600\">إجمالي التصنيفات</p>\n                    <p className=\"text-2xl font-bold text-gray-900\">{stats.totalCategories}</p>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"bg-white rounded-lg shadow p-6\">\n                <div className=\"flex items-center\">\n                  <div className=\"text-3xl\">👥</div>\n                  <div className=\"mr-4\">\n                    <p className=\"text-sm font-medium text-gray-600\">إجمالي المستخدمين</p>\n                    <p className=\"text-2xl font-bold text-gray-900\">{stats.totalUsers}</p>\n                    <p className=\"text-xs text-green-600\">{stats.activeUsers} نشط</p>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"bg-white rounded-lg shadow p-6\">\n                <div className=\"flex items-center\">\n                  <div className=\"text-3xl\">🛒</div>\n                  <div className=\"mr-4\">\n                    <p className=\"text-sm font-medium text-gray-600\">إجمالي الطلبات</p>\n                    <p className=\"text-2xl font-bold text-gray-900\">{stats.totalOrders}</p>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"bg-white rounded-lg shadow p-6\">\n                <div className=\"flex items-center\">\n                  <div className=\"text-3xl\">💰</div>\n                  <div className=\"mr-4\">\n                    <p className=\"text-sm font-medium text-gray-600\">إجمالي المبيعات</p>\n                    <p className=\"text-2xl font-bold text-gray-900\">{stats.totalRevenue.toLocaleString('ar-SA')} ر.س</p>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* Recent Activity */}\n            <div className=\"bg-white rounded-lg shadow\">\n              <div className=\"px-6 py-4 border-b border-gray-200\">\n                <h3 className=\"text-lg font-medium text-gray-900\">الطلبات الأخيرة</h3>\n              </div>\n              <div className=\"p-6\">\n                {ordersLoading ? (\n                  <div className=\"text-center py-4\">\n                    <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto\"></div>\n                    <p className=\"text-gray-600 mt-2\">جاري التحميل...</p>\n                  </div>\n                ) : getRecentOrders(3).length === 0 ? (\n                  <div className=\"text-center py-8\">\n                    <div className=\"text-4xl mb-2\">📋</div>\n                    <p className=\"text-gray-500\">لا توجد طلبات حديثة</p>\n                  </div>\n                ) : (\n                  <div className=\"space-y-4\">\n                    {getRecentOrders(3).map((order) => (\n                      <div key={order.id} className=\"flex items-center justify-between p-3 bg-gray-50 rounded-lg\">\n                        <div className=\"flex items-center\">\n                          <div className=\"text-2xl ml-3\">🛒</div>\n                          <div>\n                            <p className=\"text-sm font-medium text-gray-900\">\n                              طلب {order.orderNumber}\n                            </p>\n                            <p className=\"text-sm text-gray-500\">\n                              {order.customerName} - {order.total.toLocaleString('ar-SA')} ر.س\n                            </p>\n                          </div>\n                        </div>\n                        <div className=\"text-left\">\n                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${\n                            order.status === 'delivered' ? 'bg-green-100 text-green-800' :\n                            order.status === 'shipped' ? 'bg-blue-100 text-blue-800' :\n                            order.status === 'processing' ? 'bg-yellow-100 text-yellow-800' :\n                            order.status === 'confirmed' ? 'bg-purple-100 text-purple-800' :\n                            order.status === 'pending' ? 'bg-gray-100 text-gray-800' :\n                            'bg-red-100 text-red-800'\n                          }`}>\n                            {order.status === 'pending' ? 'في الانتظار' :\n                             order.status === 'confirmed' ? 'مؤكد' :\n                             order.status === 'processing' ? 'قيد المعالجة' :\n                             order.status === 'shipped' ? 'تم الشحن' :\n                             order.status === 'delivered' ? 'تم التسليم' :\n                             'ملغي'}\n                          </span>\n                          <p className=\"text-xs text-gray-500 mt-1\">\n                            {order.createdAt.toLocaleDateString('ar-SA')}\n                          </p>\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                )}\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Products Tab */}\n        {activeTab === 'products' && (\n          <div>\n            <div className=\"flex justify-between items-center mb-6\">\n              <h2 className=\"text-2xl font-bold text-gray-800\">إدارة المنتجات</h2>\n              <button\n                onClick={handleAddProduct}\n                className=\"bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg transition-colors duration-200\"\n              >\n                إضافة منتج جديد\n              </button>\n            </div>\n\n            {isLoading ? (\n              <div className=\"bg-white rounded-lg shadow p-12 text-center\">\n                <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4\"></div>\n                <p className=\"text-gray-600\">جاري تحميل المنتجات...</p>\n              </div>\n            ) : products.length === 0 ? (\n              <div className=\"bg-white rounded-lg shadow p-12 text-center\">\n                <div className=\"text-6xl mb-4\">📦</div>\n                <h3 className=\"text-xl font-semibold text-gray-700 mb-2\">\n                  لا توجد منتجات\n                </h3>\n                <p className=\"text-gray-500 mb-6\">\n                  لم يتم إضافة أي منتجات بعد. ابدأ بإضافة منتجك الأول!\n                </p>\n                <button\n                  onClick={handleAddProduct}\n                  className=\"bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-lg transition-colors duration-200\"\n                >\n                  إضافة منتج جديد\n                </button>\n              </div>\n            ) : (\n              <div className=\"bg-white rounded-lg shadow overflow-hidden\">\n                <table className=\"min-w-full divide-y divide-gray-200\">\n                  <thead className=\"bg-gray-50\">\n                    <tr>\n                      <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        المنتج\n                      </th>\n                      <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        السعر\n                      </th>\n                      <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        المخزون\n                      </th>\n                      <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        الحالة\n                      </th>\n                      <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        الإجراءات\n                      </th>\n                    </tr>\n                  </thead>\n                  <tbody className=\"bg-white divide-y divide-gray-200\">\n                    {products.map((product) => (\n                    <tr key={product.id}>\n                      <td className=\"px-6 py-4 whitespace-nowrap\">\n                        <div className=\"flex items-center\">\n                          <div className=\"h-10 w-10 bg-gray-200 rounded-lg flex items-center justify-center\">\n                            <span className=\"text-xs text-gray-500\">📦</span>\n                          </div>\n                          <div className=\"mr-4\">\n                            <div className=\"text-sm font-medium text-gray-900\">{product.name}</div>\n                            <div className=\"text-sm text-gray-500\">{product.category}</div>\n                            {product.featured && (\n                              <span className=\"inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800\">\n                                مميز\n                              </span>\n                            )}\n                          </div>\n                        </div>\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                        {product.price.toLocaleString('ar-SA')} ر.س\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                        {product.stock}\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap\">\n                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${\n                          product.stock > 0 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'\n                        }`}>\n                          {product.stock > 0 ? 'متوفر' : 'نفد المخزون'}\n                        </span>\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                        <button\n                          onClick={() => handleEditProduct(product)}\n                          className=\"text-primary-600 hover:text-primary-900 ml-4\"\n                        >\n                          تعديل\n                        </button>\n                        <button\n                          onClick={() => handleDeleteProduct(product)}\n                          className=\"text-red-600 hover:text-red-900\"\n                        >\n                          حذف\n                        </button>\n                      </td>\n                    </tr>\n                  ))}\n                  </tbody>\n                </table>\n              </div>\n            )}\n          </div>\n        )}\n\n        {/* Categories Tab */}\n        {activeTab === 'categories' && (\n          <div>\n            <div className=\"flex justify-between items-center mb-6\">\n              <h2 className=\"text-2xl font-bold text-gray-800\">إدارة التصنيفات</h2>\n              <button\n                onClick={handleAddCategory}\n                className=\"bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg transition-colors duration-200\"\n              >\n                إضافة تصنيف جديد\n              </button>\n            </div>\n\n            {categoriesLoading ? (\n              <div className=\"bg-white rounded-lg shadow p-12 text-center\">\n                <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4\"></div>\n                <p className=\"text-gray-600\">جاري تحميل التصنيفات...</p>\n              </div>\n            ) : categories.length === 0 ? (\n              <div className=\"bg-white rounded-lg shadow p-12 text-center\">\n                <div className=\"text-6xl mb-4\">🏷️</div>\n                <h3 className=\"text-xl font-semibold text-gray-700 mb-2\">\n                  لا توجد تصنيفات\n                </h3>\n                <p className=\"text-gray-500 mb-6\">\n                  ابدأ بإضافة تصنيف جديد لتنظيم منتجاتك\n                </p>\n                <button\n                  onClick={handleAddCategory}\n                  className=\"bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-lg transition-colors duration-200\"\n                >\n                  إضافة تصنيف جديد\n                </button>\n              </div>\n            ) : (\n              <div className=\"bg-white rounded-lg shadow overflow-hidden\">\n                <table className=\"min-w-full divide-y divide-gray-200\">\n                  <thead className=\"bg-gray-50\">\n                    <tr>\n                      <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        اسم التصنيف\n                      </th>\n                      <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        الوصف\n                      </th>\n                      <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        عدد المنتجات\n                      </th>\n                      <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        الحالة\n                      </th>\n                      <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        تاريخ الإنشاء\n                      </th>\n                      <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        الإجراءات\n                      </th>\n                    </tr>\n                  </thead>\n                  <tbody className=\"bg-white divide-y divide-gray-200\">\n                    {categories.map((category) => (\n                      <tr key={category.id} className=\"hover:bg-gray-50\">\n                        <td className=\"px-6 py-4 whitespace-nowrap\">\n                          <div className=\"text-sm font-medium text-gray-900\">{category.name}</div>\n                        </td>\n                        <td className=\"px-6 py-4\">\n                          <div className=\"text-sm text-gray-500 max-w-xs truncate\">{category.description}</div>\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap\">\n                          <div className=\"text-sm text-gray-900\">{category.productCount}</div>\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap\">\n                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${\n                            category.isActive\n                              ? 'bg-green-100 text-green-800'\n                              : 'bg-red-100 text-red-800'\n                          }`}>\n                            {category.isActive ? 'نشط' : 'غير نشط'}\n                          </span>\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                          {category.createdAt.toLocaleDateString('ar-SA')}\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                          <div className=\"flex space-x-2 space-x-reverse\">\n                            <button\n                              onClick={() => handleEditCategory(category)}\n                              className=\"text-primary-600 hover:text-primary-900\"\n                            >\n                              تعديل\n                            </button>\n                            <button\n                              onClick={() => handleDeleteCategory(category)}\n                              className=\"text-red-600 hover:text-red-900\"\n                            >\n                              حذف\n                            </button>\n                          </div>\n                        </td>\n                      </tr>\n                    ))}\n                  </tbody>\n                </table>\n              </div>\n            )}\n          </div>\n        )}\n\n        {/* Orders Tab */}\n        {activeTab === 'orders' && (\n          <div>\n            <div className=\"flex justify-between items-center mb-6\">\n              <h2 className=\"text-2xl font-bold text-gray-800\">إدارة الطلبات</h2>\n              <div className=\"flex items-center space-x-4 space-x-reverse\">\n                <select\n                  value={orderStatusFilter}\n                  onChange={(e) => setOrderStatusFilter(e.target.value as OrderStatus | 'all')}\n                  className=\"border border-gray-300 rounded-md px-3 py-2\"\n                >\n                  <option value=\"all\">جميع الطلبات</option>\n                  <option value=\"pending\">في الانتظار</option>\n                  <option value=\"confirmed\">مؤكد</option>\n                  <option value=\"processing\">قيد المعالجة</option>\n                  <option value=\"shipped\">تم الشحن</option>\n                  <option value=\"delivered\">تم التسليم</option>\n                  <option value=\"cancelled\">ملغي</option>\n                </select>\n              </div>\n            </div>\n\n            {ordersLoading ? (\n              <div className=\"bg-white rounded-lg shadow p-12 text-center\">\n                <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4\"></div>\n                <p className=\"text-gray-600\">جاري تحميل الطلبات...</p>\n              </div>\n            ) : getFilteredOrders().length === 0 ? (\n              <div className=\"bg-white rounded-lg shadow p-12 text-center\">\n                <div className=\"text-6xl mb-4\">📋</div>\n                <h3 className=\"text-xl font-semibold text-gray-700 mb-2\">\n                  {orderStatusFilter === 'all' ? 'لا توجد طلبات' : `لا توجد طلبات ${\n                    orderStatusFilter === 'pending' ? 'في الانتظار' :\n                    orderStatusFilter === 'confirmed' ? 'مؤكدة' :\n                    orderStatusFilter === 'processing' ? 'قيد المعالجة' :\n                    orderStatusFilter === 'shipped' ? 'مشحونة' :\n                    orderStatusFilter === 'delivered' ? 'مسلمة' :\n                    'ملغية'\n                  }`}\n                </h3>\n                <p className=\"text-gray-500\">\n                  {orderStatusFilter === 'all'\n                    ? 'لم يتم إنشاء أي طلبات بعد'\n                    : 'جرب تغيير فلتر الحالة لعرض طلبات أخرى'\n                  }\n                </p>\n              </div>\n            ) : (\n              <div className=\"bg-white rounded-lg shadow overflow-hidden\">\n                <table className=\"min-w-full divide-y divide-gray-200\">\n                  <thead className=\"bg-gray-50\">\n                    <tr>\n                      <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        رقم الطلب\n                      </th>\n                      <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        العميل\n                      </th>\n                      <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        المبلغ\n                      </th>\n                      <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        الحالة\n                      </th>\n                      <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        طريقة الدفع\n                      </th>\n                      <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        تاريخ الطلب\n                      </th>\n                      <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        الإجراءات\n                      </th>\n                    </tr>\n                  </thead>\n                  <tbody className=\"bg-white divide-y divide-gray-200\">\n                    {getFilteredOrders().map((order) => (\n                      <tr key={order.id} className=\"hover:bg-gray-50\">\n                        <td className=\"px-6 py-4 whitespace-nowrap\">\n                          <div className=\"text-sm font-medium text-gray-900\">{order.orderNumber}</div>\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap\">\n                          <div>\n                            <div className=\"text-sm font-medium text-gray-900\">{order.customerName}</div>\n                            <div className=\"text-sm text-gray-500\">{order.customerEmail}</div>\n                          </div>\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap\">\n                          <div className=\"text-sm font-medium text-gray-900\">\n                            {order.total.toLocaleString('ar-SA')} ر.س\n                          </div>\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap\">\n                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${\n                            order.status === 'delivered' ? 'bg-green-100 text-green-800' :\n                            order.status === 'shipped' ? 'bg-blue-100 text-blue-800' :\n                            order.status === 'processing' ? 'bg-yellow-100 text-yellow-800' :\n                            order.status === 'confirmed' ? 'bg-purple-100 text-purple-800' :\n                            order.status === 'pending' ? 'bg-gray-100 text-gray-800' :\n                            'bg-red-100 text-red-800'\n                          }`}>\n                            {order.status === 'pending' ? 'في الانتظار' :\n                             order.status === 'confirmed' ? 'مؤكد' :\n                             order.status === 'processing' ? 'قيد المعالجة' :\n                             order.status === 'shipped' ? 'تم الشحن' :\n                             order.status === 'delivered' ? 'تم التسليم' :\n                             'ملغي'}\n                          </span>\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                          {order.paymentMethod === 'cash' ? 'الدفع عند الاستلام' :\n                           order.paymentMethod === 'card' ? 'بطاقة ائتمانية' :\n                           'تحويل بنكي'}\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                          {order.createdAt.toLocaleDateString('ar-SA')}\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                          <button\n                            onClick={() => handleViewOrder(order)}\n                            className=\"text-primary-600 hover:text-primary-900\"\n                          >\n                            عرض التفاصيل\n                          </button>\n                        </td>\n                      </tr>\n                    ))}\n                  </tbody>\n                </table>\n              </div>\n            )}\n          </div>\n        )}\n\n        {/* Users Tab */}\n        {activeTab === 'users' && (\n          <div>\n            <div className=\"flex justify-between items-center mb-6\">\n              <h2 className=\"text-2xl font-bold text-gray-800\">إدارة المستخدمين</h2>\n              <button\n                onClick={handleAddUser}\n                className=\"bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg transition-colors duration-200\"\n              >\n                إضافة مستخدم جديد\n              </button>\n            </div>\n\n            {/* Filters */}\n            <div className=\"bg-white rounded-lg shadow p-4 mb-6\">\n              <div className=\"flex flex-wrap gap-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">نوع المستخدم</label>\n                  <select\n                    value={userRoleFilter}\n                    onChange={(e) => setUserRoleFilter(e.target.value as 'all' | 'admin' | 'customer')}\n                    className=\"border border-gray-300 rounded-md px-3 py-2\"\n                  >\n                    <option value=\"all\">جميع الأنواع</option>\n                    <option value=\"admin\">مدير</option>\n                    <option value=\"customer\">عميل</option>\n                  </select>\n                </div>\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">حالة الحساب</label>\n                  <select\n                    value={userStatusFilter}\n                    onChange={(e) => setUserStatusFilter(e.target.value as 'all' | 'active' | 'inactive')}\n                    className=\"border border-gray-300 rounded-md px-3 py-2\"\n                  >\n                    <option value=\"all\">جميع الحالات</option>\n                    <option value=\"active\">نشط</option>\n                    <option value=\"inactive\">غير نشط</option>\n                  </select>\n                </div>\n              </div>\n            </div>\n\n            {usersLoading ? (\n              <div className=\"bg-white rounded-lg shadow p-12 text-center\">\n                <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4\"></div>\n                <p className=\"text-gray-600\">جاري تحميل المستخدمين...</p>\n              </div>\n            ) : getFilteredUsers().length === 0 ? (\n              <div className=\"bg-white rounded-lg shadow p-12 text-center\">\n                <div className=\"text-6xl mb-4\">👥</div>\n                <h3 className=\"text-xl font-semibold text-gray-700 mb-2\">\n                  لا توجد مستخدمين\n                </h3>\n                <p className=\"text-gray-500 mb-6\">\n                  {userRoleFilter !== 'all' || userStatusFilter !== 'all'\n                    ? 'جرب تغيير الفلاتر لعرض مستخدمين آخرين'\n                    : 'ابدأ بإضافة مستخدم جديد'\n                  }\n                </p>\n                <button\n                  onClick={handleAddUser}\n                  className=\"bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-lg transition-colors duration-200\"\n                >\n                  إضافة مستخدم جديد\n                </button>\n              </div>\n            ) : (\n              <div className=\"bg-white rounded-lg shadow overflow-hidden\">\n                <table className=\"min-w-full divide-y divide-gray-200\">\n                  <thead className=\"bg-gray-50\">\n                    <tr>\n                      <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        المستخدم\n                      </th>\n                      <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        النوع\n                      </th>\n                      <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        الحالة\n                      </th>\n                      <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        الطلبات\n                      </th>\n                      <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        إجمالي الإنفاق\n                      </th>\n                      <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        آخر دخول\n                      </th>\n                      <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        الإجراءات\n                      </th>\n                    </tr>\n                  </thead>\n                  <tbody className=\"bg-white divide-y divide-gray-200\">\n                    {getFilteredUsers().map((user) => (\n                      <tr key={user.id} className=\"hover:bg-gray-50\">\n                        <td className=\"px-6 py-4 whitespace-nowrap\">\n                          <div>\n                            <div className=\"text-sm font-medium text-gray-900\">{user.name}</div>\n                            <div className=\"text-sm text-gray-500\">{user.email}</div>\n                            {user.phone && (\n                              <div className=\"text-sm text-gray-500\">{user.phone}</div>\n                            )}\n                          </div>\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap\">\n                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${\n                            user.role === 'admin'\n                              ? 'bg-purple-100 text-purple-800'\n                              : 'bg-blue-100 text-blue-800'\n                          }`}>\n                            {user.role === 'admin' ? 'مدير' : 'عميل'}\n                          </span>\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap\">\n                          <button\n                            onClick={() => handleToggleUserStatus(user.id)}\n                            className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full transition-colors duration-200 ${\n                              user.isActive\n                                ? 'bg-green-100 text-green-800 hover:bg-green-200'\n                                : 'bg-red-100 text-red-800 hover:bg-red-200'\n                            }`}\n                          >\n                            {user.isActive ? 'نشط' : 'غير نشط'}\n                          </button>\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                          {user.totalOrders}\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                          {user.totalSpent.toLocaleString('ar-SA')} ر.س\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                          {user.lastLogin\n                            ? user.lastLogin.toLocaleDateString('ar-SA')\n                            : 'لم يسجل دخول'\n                          }\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                          <div className=\"flex space-x-2 space-x-reverse\">\n                            <button\n                              onClick={() => handleEditUser(user)}\n                              className=\"text-primary-600 hover:text-primary-900\"\n                            >\n                              تعديل\n                            </button>\n                            <button\n                              onClick={() => handleDeleteUser(user)}\n                              className=\"text-red-600 hover:text-red-900\"\n                            >\n                              حذف\n                            </button>\n                          </div>\n                        </td>\n                      </tr>\n                    ))}\n                  </tbody>\n                </table>\n              </div>\n            )}\n          </div>\n        )}\n\n        {/* Reports Tab */}\n        {activeTab === 'reports' && (\n          <div>\n            <h2 className=\"text-2xl font-bold text-gray-800 mb-6\">تقارير المبيعات والأرباح</h2>\n\n            {reportsLoading ? (\n              <div className=\"bg-white rounded-lg shadow p-12 text-center\">\n                <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4\"></div>\n                <p className=\"text-gray-600\">جاري تحميل التقارير...</p>\n              </div>\n            ) : (\n              <div className=\"space-y-8\">\n                {/* Summary Statistics */}\n                <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n                  <StatCard\n                    key=\"total-sales\"\n                    title=\"إجمالي المبيعات\"\n                    value={`${getSummaryStats().totalRevenue.toLocaleString('ar-SA')} ر.س`}\n                    change={getRevenueGrowth('monthly')}\n                    icon=\"💰\"\n                    color=\"green\"\n                  />\n                  <StatCard\n                    key=\"total-profit\"\n                    title=\"إجمالي الأرباح\"\n                    value={`${getSummaryStats().totalProfit.toLocaleString('ar-SA')} ر.س`}\n                    change={getRevenueGrowth('monthly') * 0.3}\n                    icon=\"📈\"\n                    color=\"blue\"\n                  />\n                  <StatCard\n                    key=\"total-orders\"\n                    title=\"عدد الطلبات\"\n                    value={getSummaryStats().totalOrders}\n                    change={getOrderGrowth('monthly')}\n                    icon=\"🛒\"\n                    color=\"purple\"\n                  />\n                  <StatCard\n                    key=\"average-order-value\"\n                    title=\"متوسط قيمة الطلب\"\n                    value={`${getSummaryStats().averageOrderValue.toLocaleString('ar-SA')} ر.س`}\n                    icon=\"📊\"\n                    color=\"yellow\"\n                  />\n                </div>\n\n                {/* Charts Row 1 */}\n                <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n                  <LineChart\n                    key=\"daily-sales-chart\"\n                    data={getDailySales(30).map(item => ({\n                      label: new Date(item.date).toLocaleDateString('ar-SA', { month: 'short', day: 'numeric' }),\n                      value: item.sales\n                    }))}\n                    title=\"المبيعات اليومية (آخر 30 يوم)\"\n                    color=\"#10B981\"\n                  />\n                  <LineChart\n                    key=\"daily-profit-chart\"\n                    data={getDailySales(30).map(item => ({\n                      label: new Date(item.date).toLocaleDateString('ar-SA', { month: 'short', day: 'numeric' }),\n                      value: item.profit\n                    }))}\n                    title=\"الأرباح اليومية (آخر 30 يوم)\"\n                    color=\"#3B82F6\"\n                  />\n                </div>\n\n                {/* Charts Row 2 */}\n                <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n                  <BarChart\n                    key=\"top-selling-products-chart\"\n                    data={getTopSellingProducts(10).map(product => ({\n                      label: product.productName.length > 15\n                        ? product.productName.substring(0, 15) + '...'\n                        : product.productName,\n                      value: product.quantitySold\n                    }))}\n                    title=\"أفضل المنتجات مبيعاً\"\n                    horizontal={true}\n                  />\n                  <BarChart\n                    key=\"most-profitable-products-chart\"\n                    data={getMostProfitableProducts(10).map(product => ({\n                      label: product.productName.length > 15\n                        ? product.productName.substring(0, 15) + '...'\n                        : product.productName,\n                      value: product.totalProfit\n                    }))}\n                    title=\"أكثر المنتجات ربحاً\"\n                    horizontal={true}\n                  />\n                </div>\n\n                {/* Charts Row 3 */}\n                <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n                  <PieChart\n                    key=\"sales-distribution-chart\"\n                    data={getTopSellingProducts(5).map((product, index) => ({\n                      label: product.productName,\n                      value: product.totalRevenue,\n                      color: ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6'][index]\n                    }))}\n                    title=\"توزيع المبيعات حسب المنتج\"\n                  />\n                  <div key=\"top-customers-widget\" className=\"bg-white rounded-lg shadow p-6\">\n                    <h3 className=\"text-lg font-semibold text-gray-800 mb-4\">أفضل العملاء</h3>\n                    <div className=\"space-y-3\">\n                      {getTopCustomers(5).map((customer, index) => (\n                        <div key={customer.userId} className=\"flex items-center justify-between p-3 bg-gray-50 rounded-lg\">\n                          <div className=\"flex items-center\">\n                            <div className=\"w-8 h-8 bg-primary-500 text-white rounded-full flex items-center justify-center text-sm font-bold\">\n                              {index + 1}\n                            </div>\n                            <div className=\"mr-3\">\n                              <div className=\"font-medium text-gray-900\">{customer.userName}</div>\n                              <div className=\"text-sm text-gray-500\">{customer.totalOrders} طلب</div>\n                            </div>\n                          </div>\n                          <div className=\"text-left\">\n                            <div className=\"font-bold text-gray-900\">\n                              {customer.totalSpent.toLocaleString('ar-SA')} ر.س\n                            </div>\n                            <div className=\"text-sm text-gray-500\">\n                              متوسط: {customer.averageOrderValue.toLocaleString('ar-SA')} ر.س\n                            </div>\n                          </div>\n                        </div>\n                      ))}\n                    </div>\n                  </div>\n                </div>\n\n                {/* Monthly Sales Table */}\n                <div key=\"monthly-sales-table\" className=\"bg-white rounded-lg shadow\">\n                  <div className=\"px-6 py-4 border-b border-gray-200\">\n                    <h3 className=\"text-lg font-medium text-gray-900\">المبيعات الشهرية</h3>\n                  </div>\n                  <div className=\"overflow-x-auto\">\n                    <table className=\"min-w-full divide-y divide-gray-200\">\n                      <thead className=\"bg-gray-50\">\n                        <tr>\n                          <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                            الشهر\n                          </th>\n                          <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                            المبيعات\n                          </th>\n                          <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                            الأرباح\n                          </th>\n                          <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                            عدد الطلبات\n                          </th>\n                          <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                            متوسط قيمة الطلب\n                          </th>\n                        </tr>\n                      </thead>\n                      <tbody className=\"bg-white divide-y divide-gray-200\">\n                        {getMonthlySales(6).reverse().map((monthData, index) => (\n                          <tr key={monthData.date} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>\n                            <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\">\n                              {new Date(monthData.date + '-01').toLocaleDateString('ar-SA', {\n                                year: 'numeric',\n                                month: 'long'\n                              })}\n                            </td>\n                            <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                              {monthData.sales.toLocaleString('ar-SA')} ر.س\n                            </td>\n                            <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                              {monthData.profit.toLocaleString('ar-SA')} ر.س\n                            </td>\n                            <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                              {monthData.orders}\n                            </td>\n                            <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                              {monthData.orders > 0\n                                ? (monthData.sales / monthData.orders).toLocaleString('ar-SA')\n                                : '0'} ر.س\n                            </td>\n                          </tr>\n                        ))}\n                      </tbody>\n                    </table>\n                  </div>\n                </div>\n              </div>\n            )}\n          </div>\n        )}\n      </div>\n\n      {/* Product Form Modal */}\n      <ProductForm\n        product={editingProduct}\n        isOpen={isProductFormOpen}\n        onClose={() => {\n          setIsProductFormOpen(false);\n          setEditingProduct(null);\n        }}\n        onSave={handleSaveProduct}\n      />\n\n      {/* Category Form Modal */}\n      <CategoryForm\n        category={editingCategory}\n        isOpen={isCategoryFormOpen}\n        onClose={() => {\n          setIsCategoryFormOpen(false);\n          setEditingCategory(null);\n        }}\n        onSave={handleSaveCategory}\n      />\n\n      {/* User Form Modal */}\n      <UserForm\n        user={editingUser}\n        isOpen={isUserFormOpen}\n        onClose={() => {\n          setIsUserFormOpen(false);\n          setEditingUser(null);\n        }}\n        onSave={handleSaveUser}\n      />\n\n      {/* Order Details Modal */}\n      <OrderDetailsModal\n        order={selectedOrder}\n        isOpen={isOrderDetailsOpen}\n        onClose={() => {\n          setIsOrderDetailsOpen(false);\n          setSelectedOrder(null);\n        }}\n        onUpdateStatus={handleUpdateOrderStatus}\n      />\n\n      {/* Delete Confirmation Modal */}\n      <DeleteConfirmModal\n        isOpen={isDeleteModalOpen}\n        title={\n          productToDelete ? \"تأكيد حذف المنتج\" :\n          categoryToDelete ? \"تأكيد حذف التصنيف\" :\n          \"تأكيد حذف المستخدم\"\n        }\n        message={\n          productToDelete\n            ? `هل أنت متأكد من حذف المنتج \"${productToDelete?.name}\"؟ لا يمكن التراجع عن هذا الإجراء.`\n            : categoryToDelete\n            ? `هل أنت متأكد من حذف التصنيف \"${categoryToDelete?.name}\"؟ لا يمكن التراجع عن هذا الإجراء.`\n            : `هل أنت متأكد من حذف المستخدم \"${userToDelete?.name}\"؟ لا يمكن التراجع عن هذا الإجراء.`\n        }\n        onConfirm={\n          productToDelete ? confirmDeleteProduct :\n          categoryToDelete ? confirmDeleteCategory :\n          confirmDeleteUser\n        }\n        onCancel={() => {\n          setIsDeleteModalOpen(false);\n          setProductToDelete(null);\n          setCategoryToDelete(null);\n          setUserToDelete(null);\n        }}\n        isLoading={isDeleting}\n      />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAfA;;;;;;;;;;;;;;AAiBe,SAAS;;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IACrE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IACvE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,6BAA6B;IAC7B,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB;IACxE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB;IAE1E,0BAA0B;IAC1B,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB;IACjE,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB;IAEhF,yBAAyB;IACzB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC5D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgC;IACnF,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiC;IAExF,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,aAAa,EAAE,aAAa,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IACpF,MAAM,EAAE,UAAU,EAAE,WAAW,EAAE,cAAc,EAAE,gBAAgB,yBAAyB,EAAE,WAAW,iBAAiB,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,gBAAa,AAAD;IACzI,MAAM,EAAE,MAAM,EAAE,iBAAiB,EAAE,eAAe,EAAE,cAAc,EAAE,eAAe,EAAE,WAAW,aAAa,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,YAAS,AAAD;IAC1H,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,UAAU,EAAE,UAAU,EAAE,gBAAgB,EAAE,aAAa,EAAE,mBAAmB,EAAE,cAAc,EAAE,WAAW,YAAY,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,WAAQ,AAAD;IACzJ,MAAM,EACJ,aAAa,EACb,cAAc,EACd,eAAe,EACf,qBAAqB,EACrB,yBAAyB,EACzB,eAAe,EACf,eAAe,EACf,cAAc,EACd,gBAAgB,EAChB,cAAc,EACd,WAAW,cAAc,EAC1B,GAAG,CAAA,GAAA,qIAAA,CAAA,aAAU,AAAD;IAEb,MAAM,QAAQ;QACZ,eAAe,SAAS,MAAM;QAC9B,iBAAiB,WAAW,MAAM;QAClC,YAAY;QACZ,aAAa;QACb,aAAa;QACb,cAAc;IAChB;IAEA,+BAA+B;IAC/B,MAAM,mBAAmB;QACvB,kBAAkB;QAClB,qBAAqB;IACvB;IAEA,MAAM,oBAAoB,CAAC;QACzB,kBAAkB;QAClB,qBAAqB;IACvB;IAEA,MAAM,sBAAsB,CAAC;QAC3B,mBAAmB;QACnB,qBAAqB;IACvB;IAEA,MAAM,oBAAoB,CAAC;QACzB,IAAI,gBAAgB;YAClB,0BAA0B;YAC1B,cAAc,eAAe,EAAE,EAAE;QACnC,OAAO;YACL,kBAAkB;YAClB,WAAW;QACb;IACF;IAEA,MAAM,uBAAuB;QAC3B,IAAI,CAAC,iBAAiB;QAEtB,cAAc;QACd,IAAI;YACF,oBAAoB;YACpB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,cAAc,gBAAgB,EAAE;YAChC,qBAAqB;YACrB,mBAAmB;QACrB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;QAC3C,SAAU;YACR,cAAc;QAChB;IACF;IAEA,gCAAgC;IAChC,MAAM,oBAAoB;QACxB,mBAAmB;QACnB,sBAAsB;IACxB;IAEA,MAAM,qBAAqB,CAAC;QAC1B,mBAAmB;QACnB,sBAAsB;IACxB;IAEA,MAAM,uBAAuB,CAAC;QAC5B,oBAAoB;QACpB,qBAAqB;IACvB;IAEA,MAAM,qBAAqB,CAAC;QAC1B,IAAI,iBAAiB;YACnB,2BAA2B;YAC3B,eAAe,gBAAgB,EAAE,EAAE;QACrC,OAAO;YACL,mBAAmB;YACnB,YAAY;QACd;IACF;IAEA,MAAM,wBAAwB;QAC5B,IAAI,CAAC,kBAAkB;QAEvB,cAAc;QACd,IAAI;YACF,oBAAoB;YACpB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,0BAA0B,iBAAiB,EAAE;YAC7C,qBAAqB;YACrB,oBAAoB;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;QAC5C,SAAU;YACR,cAAc;QAChB;IACF;IAEA,6BAA6B;IAC7B,MAAM,kBAAkB,CAAC;QACvB,iBAAiB;QACjB,sBAAsB;IACxB;IAEA,MAAM,0BAA0B,CAAC,SAAiB;QAChD,kBAAkB,SAAS;QAC3B,0DAA0D;QAC1D,IAAI,iBAAiB,cAAc,EAAE,KAAK,SAAS;YACjD,iBAAiB;gBAAE,GAAG,aAAa;gBAAE;YAAO;QAC9C;IACF;IAEA,MAAM,oBAAoB;QACxB,IAAI,sBAAsB,OAAO;YAC/B,OAAO;QACT;QACA,OAAO,OAAO,MAAM,CAAC,CAAA,QAAS,MAAM,MAAM,KAAK;IACjD;IAEA,4BAA4B;IAC5B,MAAM,gBAAgB;QACpB,eAAe;QACf,kBAAkB;IACpB;IAEA,MAAM,iBAAiB,CAAC;QACtB,eAAe;QACf,kBAAkB;IACpB;IAEA,MAAM,mBAAmB,CAAC;QACxB,gBAAgB;QAChB,qBAAqB;IACvB;IAEA,MAAM,yBAAyB,CAAC;QAC9B,iBAAiB;IACnB;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,aAAa;YACf,uBAAuB;YACvB,WAAW,YAAY,EAAE,EAAE;QAC7B,OAAO;YACL,eAAe;YACf,QAAQ;QACV;IACF;IAEA,MAAM,oBAAoB;QACxB,IAAI,CAAC,cAAc;QAEnB,cAAc;QACd,IAAI;YACF,oBAAoB;YACpB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,WAAW,aAAa,EAAE;YAC1B,qBAAqB;YACrB,gBAAgB;QAClB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;QACxC,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,mBAAmB;QACvB,IAAI,WAAW;QAEf,IAAI,mBAAmB,OAAO;YAC5B,WAAW,SAAS,MAAM,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK;QACnD;QAEA,IAAI,qBAAqB,OAAO;YAC9B,WAAW,SAAS,MAAM,CAAC,CAAA,OACzB,qBAAqB,WAAW,KAAK,QAAQ,GAAG,CAAC,KAAK,QAAQ;QAElE;QAEA,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAI,WAAU;kDAAsC;;;;;;kDAG/D,6LAAC;wCAAE,WAAU;kDAAqB;;;;;;;;;;;;0CAEpC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAAgB;;;;;;kDAChC,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQT,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACZ;gCACC;oCAAE,IAAI;oCAAY,MAAM;oCAAa,MAAM;gCAAK;gCAChD;oCAAE,IAAI;oCAAY,MAAM;oCAAY,MAAM;gCAAK;gCAC/C;oCAAE,IAAI;oCAAc,MAAM;oCAAa,MAAM;gCAAM;gCACnD;oCAAE,IAAI;oCAAU,MAAM;oCAAW,MAAM;gCAAK;gCAC5C;oCAAE,IAAI;oCAAS,MAAM;oCAAc,MAAM;gCAAK;gCAC9C;oCAAE,IAAI;oCAAW,MAAM;oCAAY,MAAM;gCAAK;6BAC/C,CAAC,GAAG,CAAC,CAAC,oBACL,6LAAC;oCAEC,SAAS,IAAM,aAAa,IAAI,EAAE;oCAClC,WAAW,CAAC,yCAAyC,EACnD,cAAc,IAAI,EAAE,GAChB,wCACA,8EACJ;;wCAED,IAAI,IAAI;wCAAC;wCAAE,IAAI,IAAI;;mCARf,IAAI,EAAE;;;;;;;;;;;;;;;oBAelB,cAAc,4BACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAwC;;;;;;0CAGtD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAW;;;;;;8DAC1B,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAE,WAAU;sEAAoC;;;;;;sEACjD,6LAAC;4DAAE,WAAU;sEAAoC,MAAM,aAAa;;;;;;;;;;;;;;;;;;;;;;;kDAK1E,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAW;;;;;;8DAC1B,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAE,WAAU;sEAAoC;;;;;;sEACjD,6LAAC;4DAAE,WAAU;sEAAoC,MAAM,eAAe;;;;;;;;;;;;;;;;;;;;;;;kDAK5E,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAW;;;;;;8DAC1B,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAE,WAAU;sEAAoC;;;;;;sEACjD,6LAAC;4DAAE,WAAU;sEAAoC,MAAM,UAAU;;;;;;sEACjE,6LAAC;4DAAE,WAAU;;gEAA0B,MAAM,WAAW;gEAAC;;;;;;;;;;;;;;;;;;;;;;;;kDAK/D,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAW;;;;;;8DAC1B,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAE,WAAU;sEAAoC;;;;;;sEACjD,6LAAC;4DAAE,WAAU;sEAAoC,MAAM,WAAW;;;;;;;;;;;;;;;;;;;;;;;kDAKxE,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAW;;;;;;8DAC1B,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAE,WAAU;sEAAoC;;;;;;sEACjD,6LAAC;4DAAE,WAAU;;gEAAoC,MAAM,YAAY,CAAC,cAAc,CAAC;gEAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAOpG,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAG,WAAU;sDAAoC;;;;;;;;;;;kDAEpD,6LAAC;wCAAI,WAAU;kDACZ,8BACC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;oDAAE,WAAU;8DAAqB;;;;;;;;;;;mDAElC,gBAAgB,GAAG,MAAM,KAAK,kBAChC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAgB;;;;;;8DAC/B,6LAAC;oDAAE,WAAU;8DAAgB;;;;;;;;;;;iEAG/B,6LAAC;4CAAI,WAAU;sDACZ,gBAAgB,GAAG,GAAG,CAAC,CAAC,sBACvB,6LAAC;oDAAmB,WAAU;;sEAC5B,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EAAgB;;;;;;8EAC/B,6LAAC;;sFACC,6LAAC;4EAAE,WAAU;;gFAAoC;gFAC1C,MAAM,WAAW;;;;;;;sFAExB,6LAAC;4EAAE,WAAU;;gFACV,MAAM,YAAY;gFAAC;gFAAI,MAAM,KAAK,CAAC,cAAc,CAAC;gFAAS;;;;;;;;;;;;;;;;;;;sEAIlE,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAW,CAAC,yDAAyD,EACzE,MAAM,MAAM,KAAK,cAAc,gCAC/B,MAAM,MAAM,KAAK,YAAY,8BAC7B,MAAM,MAAM,KAAK,eAAe,kCAChC,MAAM,MAAM,KAAK,cAAc,kCAC/B,MAAM,MAAM,KAAK,YAAY,8BAC7B,2BACA;8EACC,MAAM,MAAM,KAAK,YAAY,gBAC7B,MAAM,MAAM,KAAK,cAAc,SAC/B,MAAM,MAAM,KAAK,eAAe,iBAChC,MAAM,MAAM,KAAK,YAAY,aAC7B,MAAM,MAAM,KAAK,cAAc,eAC/B;;;;;;8EAEH,6LAAC;oEAAE,WAAU;8EACV,MAAM,SAAS,CAAC,kBAAkB,CAAC;;;;;;;;;;;;;mDA7BhC,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;oBA0C/B,cAAc,4BACb,6LAAC;;0CACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAmC;;;;;;kDACjD,6LAAC;wCACC,SAAS;wCACT,WAAU;kDACX;;;;;;;;;;;;4BAKF,0BACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;uCAE7B,SAAS,MAAM,KAAK,kBACtB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAAgB;;;;;;kDAC/B,6LAAC;wCAAG,WAAU;kDAA2C;;;;;;kDAGzD,6LAAC;wCAAE,WAAU;kDAAqB;;;;;;kDAGlC,6LAAC;wCACC,SAAS;wCACT,WAAU;kDACX;;;;;;;;;;;qDAKH,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAM,WAAU;;sDACf,6LAAC;4CAAM,WAAU;sDACf,cAAA,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAAkF;;;;;;kEAGhG,6LAAC;wDAAG,WAAU;kEAAkF;;;;;;kEAGhG,6LAAC;wDAAG,WAAU;kEAAkF;;;;;;kEAGhG,6LAAC;wDAAG,WAAU;kEAAkF;;;;;;kEAGhG,6LAAC;wDAAG,WAAU;kEAAkF;;;;;;;;;;;;;;;;;sDAKpG,6LAAC;4CAAM,WAAU;sDACd,SAAS,GAAG,CAAC,CAAC,wBACf,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEACZ,cAAA,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC;4EAAK,WAAU;sFAAwB;;;;;;;;;;;kFAE1C,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAI,WAAU;0FAAqC,QAAQ,IAAI;;;;;;0FAChE,6LAAC;gFAAI,WAAU;0FAAyB,QAAQ,QAAQ;;;;;;4EACvD,QAAQ,QAAQ,kBACf,6LAAC;gFAAK,WAAU;0FAAiG;;;;;;;;;;;;;;;;;;;;;;;sEAOzH,6LAAC;4DAAG,WAAU;;gEACX,QAAQ,KAAK,CAAC,cAAc,CAAC;gEAAS;;;;;;;sEAEzC,6LAAC;4DAAG,WAAU;sEACX,QAAQ,KAAK;;;;;;sEAEhB,6LAAC;4DAAG,WAAU;sEACZ,cAAA,6LAAC;gEAAK,WAAW,CAAC,yDAAyD,EACzE,QAAQ,KAAK,GAAG,IAAI,gCAAgC,2BACpD;0EACC,QAAQ,KAAK,GAAG,IAAI,UAAU;;;;;;;;;;;sEAGnC,6LAAC;4DAAG,WAAU;;8EACZ,6LAAC;oEACC,SAAS,IAAM,kBAAkB;oEACjC,WAAU;8EACX;;;;;;8EAGD,6LAAC;oEACC,SAAS,IAAM,oBAAoB;oEACnC,WAAU;8EACX;;;;;;;;;;;;;mDAxCI,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAsD9B,cAAc,8BACb,6LAAC;;0CACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAmC;;;;;;kDACjD,6LAAC;wCACC,SAAS;wCACT,WAAU;kDACX;;;;;;;;;;;;4BAKF,kCACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;uCAE7B,WAAW,MAAM,KAAK,kBACxB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAAgB;;;;;;kDAC/B,6LAAC;wCAAG,WAAU;kDAA2C;;;;;;kDAGzD,6LAAC;wCAAE,WAAU;kDAAqB;;;;;;kDAGlC,6LAAC;wCACC,SAAS;wCACT,WAAU;kDACX;;;;;;;;;;;qDAKH,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAM,WAAU;;sDACf,6LAAC;4CAAM,WAAU;sDACf,cAAA,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAAkF;;;;;;kEAGhG,6LAAC;wDAAG,WAAU;kEAAkF;;;;;;kEAGhG,6LAAC;wDAAG,WAAU;kEAAkF;;;;;;kEAGhG,6LAAC;wDAAG,WAAU;kEAAkF;;;;;;kEAGhG,6LAAC;wDAAG,WAAU;kEAAkF;;;;;;kEAGhG,6LAAC;wDAAG,WAAU;kEAAkF;;;;;;;;;;;;;;;;;sDAKpG,6LAAC;4CAAM,WAAU;sDACd,WAAW,GAAG,CAAC,CAAC,yBACf,6LAAC;oDAAqB,WAAU;;sEAC9B,6LAAC;4DAAG,WAAU;sEACZ,cAAA,6LAAC;gEAAI,WAAU;0EAAqC,SAAS,IAAI;;;;;;;;;;;sEAEnE,6LAAC;4DAAG,WAAU;sEACZ,cAAA,6LAAC;gEAAI,WAAU;0EAA2C,SAAS,WAAW;;;;;;;;;;;sEAEhF,6LAAC;4DAAG,WAAU;sEACZ,cAAA,6LAAC;gEAAI,WAAU;0EAAyB,SAAS,YAAY;;;;;;;;;;;sEAE/D,6LAAC;4DAAG,WAAU;sEACZ,cAAA,6LAAC;gEAAK,WAAW,CAAC,yDAAyD,EACzE,SAAS,QAAQ,GACb,gCACA,2BACJ;0EACC,SAAS,QAAQ,GAAG,QAAQ;;;;;;;;;;;sEAGjC,6LAAC;4DAAG,WAAU;sEACX,SAAS,SAAS,CAAC,kBAAkB,CAAC;;;;;;sEAEzC,6LAAC;4DAAG,WAAU;sEACZ,cAAA,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEACC,SAAS,IAAM,mBAAmB;wEAClC,WAAU;kFACX;;;;;;kFAGD,6LAAC;wEACC,SAAS,IAAM,qBAAqB;wEACpC,WAAU;kFACX;;;;;;;;;;;;;;;;;;mDAjCE,SAAS,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAgDjC,cAAc,0BACb,6LAAC;;0CACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAmC;;;;;;kDACjD,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CACC,OAAO;4CACP,UAAU,CAAC,IAAM,qBAAqB,EAAE,MAAM,CAAC,KAAK;4CACpD,WAAU;;8DAEV,6LAAC;oDAAO,OAAM;8DAAM;;;;;;8DACpB,6LAAC;oDAAO,OAAM;8DAAU;;;;;;8DACxB,6LAAC;oDAAO,OAAM;8DAAY;;;;;;8DAC1B,6LAAC;oDAAO,OAAM;8DAAa;;;;;;8DAC3B,6LAAC;oDAAO,OAAM;8DAAU;;;;;;8DACxB,6LAAC;oDAAO,OAAM;8DAAY;;;;;;8DAC1B,6LAAC;oDAAO,OAAM;8DAAY;;;;;;;;;;;;;;;;;;;;;;;4BAK/B,8BACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;uCAE7B,oBAAoB,MAAM,KAAK,kBACjC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAAgB;;;;;;kDAC/B,6LAAC;wCAAG,WAAU;kDACX,sBAAsB,QAAQ,kBAAkB,CAAC,cAAc,EAC9D,sBAAsB,YAAY,gBAClC,sBAAsB,cAAc,UACpC,sBAAsB,eAAe,iBACrC,sBAAsB,YAAY,WAClC,sBAAsB,cAAc,UACpC,SACA;;;;;;kDAEJ,6LAAC;wCAAE,WAAU;kDACV,sBAAsB,QACnB,8BACA;;;;;;;;;;;qDAKR,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAM,WAAU;;sDACf,6LAAC;4CAAM,WAAU;sDACf,cAAA,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAAkF;;;;;;kEAGhG,6LAAC;wDAAG,WAAU;kEAAkF;;;;;;kEAGhG,6LAAC;wDAAG,WAAU;kEAAkF;;;;;;kEAGhG,6LAAC;wDAAG,WAAU;kEAAkF;;;;;;kEAGhG,6LAAC;wDAAG,WAAU;kEAAkF;;;;;;kEAGhG,6LAAC;wDAAG,WAAU;kEAAkF;;;;;;kEAGhG,6LAAC;wDAAG,WAAU;kEAAkF;;;;;;;;;;;;;;;;;sDAKpG,6LAAC;4CAAM,WAAU;sDACd,oBAAoB,GAAG,CAAC,CAAC,sBACxB,6LAAC;oDAAkB,WAAU;;sEAC3B,6LAAC;4DAAG,WAAU;sEACZ,cAAA,6LAAC;gEAAI,WAAU;0EAAqC,MAAM,WAAW;;;;;;;;;;;sEAEvE,6LAAC;4DAAG,WAAU;sEACZ,cAAA,6LAAC;;kFACC,6LAAC;wEAAI,WAAU;kFAAqC,MAAM,YAAY;;;;;;kFACtE,6LAAC;wEAAI,WAAU;kFAAyB,MAAM,aAAa;;;;;;;;;;;;;;;;;sEAG/D,6LAAC;4DAAG,WAAU;sEACZ,cAAA,6LAAC;gEAAI,WAAU;;oEACZ,MAAM,KAAK,CAAC,cAAc,CAAC;oEAAS;;;;;;;;;;;;sEAGzC,6LAAC;4DAAG,WAAU;sEACZ,cAAA,6LAAC;gEAAK,WAAW,CAAC,yDAAyD,EACzE,MAAM,MAAM,KAAK,cAAc,gCAC/B,MAAM,MAAM,KAAK,YAAY,8BAC7B,MAAM,MAAM,KAAK,eAAe,kCAChC,MAAM,MAAM,KAAK,cAAc,kCAC/B,MAAM,MAAM,KAAK,YAAY,8BAC7B,2BACA;0EACC,MAAM,MAAM,KAAK,YAAY,gBAC7B,MAAM,MAAM,KAAK,cAAc,SAC/B,MAAM,MAAM,KAAK,eAAe,iBAChC,MAAM,MAAM,KAAK,YAAY,aAC7B,MAAM,MAAM,KAAK,cAAc,eAC/B;;;;;;;;;;;sEAGL,6LAAC;4DAAG,WAAU;sEACX,MAAM,aAAa,KAAK,SAAS,uBACjC,MAAM,aAAa,KAAK,SAAS,mBACjC;;;;;;sEAEH,6LAAC;4DAAG,WAAU;sEACX,MAAM,SAAS,CAAC,kBAAkB,CAAC;;;;;;sEAEtC,6LAAC;4DAAG,WAAU;sEACZ,cAAA,6LAAC;gEACC,SAAS,IAAM,gBAAgB;gEAC/B,WAAU;0EACX;;;;;;;;;;;;mDA5CI,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;oBA0D9B,cAAc,yBACb,6LAAC;;0CACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAmC;;;;;;kDACjD,6LAAC;wCACC,SAAS;wCACT,WAAU;kDACX;;;;;;;;;;;;0CAMH,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAChE,6LAAC;oDACC,OAAO;oDACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;oDACjD,WAAU;;sEAEV,6LAAC;4DAAO,OAAM;sEAAM;;;;;;sEACpB,6LAAC;4DAAO,OAAM;sEAAQ;;;;;;sEACtB,6LAAC;4DAAO,OAAM;sEAAW;;;;;;;;;;;;;;;;;;sDAG7B,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAChE,6LAAC;oDACC,OAAO;oDACP,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK;oDACnD,WAAU;;sEAEV,6LAAC;4DAAO,OAAM;sEAAM;;;;;;sEACpB,6LAAC;4DAAO,OAAM;sEAAS;;;;;;sEACvB,6LAAC;4DAAO,OAAM;sEAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BAMhC,6BACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;uCAE7B,mBAAmB,MAAM,KAAK,kBAChC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAAgB;;;;;;kDAC/B,6LAAC;wCAAG,WAAU;kDAA2C;;;;;;kDAGzD,6LAAC;wCAAE,WAAU;kDACV,mBAAmB,SAAS,qBAAqB,QAC9C,0CACA;;;;;;kDAGN,6LAAC;wCACC,SAAS;wCACT,WAAU;kDACX;;;;;;;;;;;qDAKH,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAM,WAAU;;sDACf,6LAAC;4CAAM,WAAU;sDACf,cAAA,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAAkF;;;;;;kEAGhG,6LAAC;wDAAG,WAAU;kEAAkF;;;;;;kEAGhG,6LAAC;wDAAG,WAAU;kEAAkF;;;;;;kEAGhG,6LAAC;wDAAG,WAAU;kEAAkF;;;;;;kEAGhG,6LAAC;wDAAG,WAAU;kEAAkF;;;;;;kEAGhG,6LAAC;wDAAG,WAAU;kEAAkF;;;;;;kEAGhG,6LAAC;wDAAG,WAAU;kEAAkF;;;;;;;;;;;;;;;;;sDAKpG,6LAAC;4CAAM,WAAU;sDACd,mBAAmB,GAAG,CAAC,CAAC,qBACvB,6LAAC;oDAAiB,WAAU;;sEAC1B,6LAAC;4DAAG,WAAU;sEACZ,cAAA,6LAAC;;kFACC,6LAAC;wEAAI,WAAU;kFAAqC,KAAK,IAAI;;;;;;kFAC7D,6LAAC;wEAAI,WAAU;kFAAyB,KAAK,KAAK;;;;;;oEACjD,KAAK,KAAK,kBACT,6LAAC;wEAAI,WAAU;kFAAyB,KAAK,KAAK;;;;;;;;;;;;;;;;;sEAIxD,6LAAC;4DAAG,WAAU;sEACZ,cAAA,6LAAC;gEAAK,WAAW,CAAC,yDAAyD,EACzE,KAAK,IAAI,KAAK,UACV,kCACA,6BACJ;0EACC,KAAK,IAAI,KAAK,UAAU,SAAS;;;;;;;;;;;sEAGtC,6LAAC;4DAAG,WAAU;sEACZ,cAAA,6LAAC;gEACC,SAAS,IAAM,uBAAuB,KAAK,EAAE;gEAC7C,WAAW,CAAC,wFAAwF,EAClG,KAAK,QAAQ,GACT,mDACA,4CACJ;0EAED,KAAK,QAAQ,GAAG,QAAQ;;;;;;;;;;;sEAG7B,6LAAC;4DAAG,WAAU;sEACX,KAAK,WAAW;;;;;;sEAEnB,6LAAC;4DAAG,WAAU;;gEACX,KAAK,UAAU,CAAC,cAAc,CAAC;gEAAS;;;;;;;sEAE3C,6LAAC;4DAAG,WAAU;sEACX,KAAK,SAAS,GACX,KAAK,SAAS,CAAC,kBAAkB,CAAC,WAClC;;;;;;sEAGN,6LAAC;4DAAG,WAAU;sEACZ,cAAA,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEACC,SAAS,IAAM,eAAe;wEAC9B,WAAU;kFACX;;;;;;kFAGD,6LAAC;wEACC,SAAS,IAAM,iBAAiB;wEAChC,WAAU;kFACX;;;;;;;;;;;;;;;;;;mDAtDE,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAqE7B,cAAc,2BACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAwC;;;;;;4BAErD,+BACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;qDAG/B,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,+HAAA,CAAA,WAAQ;gDAEP,OAAM;gDACN,OAAO,GAAG,kBAAkB,YAAY,CAAC,cAAc,CAAC,SAAS,IAAI,CAAC;gDACtE,QAAQ,iBAAiB;gDACzB,MAAK;gDACL,OAAM;+CALF;;;;;0DAON,6LAAC,+HAAA,CAAA,WAAQ;gDAEP,OAAM;gDACN,OAAO,GAAG,kBAAkB,WAAW,CAAC,cAAc,CAAC,SAAS,IAAI,CAAC;gDACrE,QAAQ,iBAAiB,aAAa;gDACtC,MAAK;gDACL,OAAM;+CALF;;;;;0DAON,6LAAC,+HAAA,CAAA,WAAQ;gDAEP,OAAM;gDACN,OAAO,kBAAkB,WAAW;gDACpC,QAAQ,eAAe;gDACvB,MAAK;gDACL,OAAM;+CALF;;;;;0DAON,6LAAC,+HAAA,CAAA,WAAQ;gDAEP,OAAM;gDACN,OAAO,GAAG,kBAAkB,iBAAiB,CAAC,cAAc,CAAC,SAAS,IAAI,CAAC;gDAC3E,MAAK;gDACL,OAAM;+CAJF;;;;;;;;;;;kDASR,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,+HAAA,CAAA,YAAS;gDAER,MAAM,cAAc,IAAI,GAAG,CAAC,CAAA,OAAQ,CAAC;wDACnC,OAAO,IAAI,KAAK,KAAK,IAAI,EAAE,kBAAkB,CAAC,SAAS;4DAAE,OAAO;4DAAS,KAAK;wDAAU;wDACxF,OAAO,KAAK,KAAK;oDACnB,CAAC;gDACD,OAAM;gDACN,OAAM;+CANF;;;;;0DAQN,6LAAC,+HAAA,CAAA,YAAS;gDAER,MAAM,cAAc,IAAI,GAAG,CAAC,CAAA,OAAQ,CAAC;wDACnC,OAAO,IAAI,KAAK,KAAK,IAAI,EAAE,kBAAkB,CAAC,SAAS;4DAAE,OAAO;4DAAS,KAAK;wDAAU;wDACxF,OAAO,KAAK,MAAM;oDACpB,CAAC;gDACD,OAAM;gDACN,OAAM;+CANF;;;;;;;;;;;kDAWR,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,+HAAA,CAAA,WAAQ;gDAEP,MAAM,sBAAsB,IAAI,GAAG,CAAC,CAAA,UAAW,CAAC;wDAC9C,OAAO,QAAQ,WAAW,CAAC,MAAM,GAAG,KAChC,QAAQ,WAAW,CAAC,SAAS,CAAC,GAAG,MAAM,QACvC,QAAQ,WAAW;wDACvB,OAAO,QAAQ,YAAY;oDAC7B,CAAC;gDACD,OAAM;gDACN,YAAY;+CARR;;;;;0DAUN,6LAAC,+HAAA,CAAA,WAAQ;gDAEP,MAAM,0BAA0B,IAAI,GAAG,CAAC,CAAA,UAAW,CAAC;wDAClD,OAAO,QAAQ,WAAW,CAAC,MAAM,GAAG,KAChC,QAAQ,WAAW,CAAC,SAAS,CAAC,GAAG,MAAM,QACvC,QAAQ,WAAW;wDACvB,OAAO,QAAQ,WAAW;oDAC5B,CAAC;gDACD,OAAM;gDACN,YAAY;+CARR;;;;;;;;;;;kDAaR,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,+HAAA,CAAA,WAAQ;gDAEP,MAAM,sBAAsB,GAAG,GAAG,CAAC,CAAC,SAAS,QAAU,CAAC;wDACtD,OAAO,QAAQ,WAAW;wDAC1B,OAAO,QAAQ,YAAY;wDAC3B,OAAO;4DAAC;4DAAW;4DAAW;4DAAW;4DAAW;yDAAU,CAAC,MAAM;oDACvE,CAAC;gDACD,OAAM;+CANF;;;;;0DAQN,6LAAC;gDAA+B,WAAU;;kEACxC,6LAAC;wDAAG,WAAU;kEAA2C;;;;;;kEACzD,6LAAC;wDAAI,WAAU;kEACZ,gBAAgB,GAAG,GAAG,CAAC,CAAC,UAAU,sBACjC,6LAAC;gEAA0B,WAAU;;kFACnC,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAI,WAAU;0FACZ,QAAQ;;;;;;0FAEX,6LAAC;gFAAI,WAAU;;kGACb,6LAAC;wFAAI,WAAU;kGAA6B,SAAS,QAAQ;;;;;;kGAC7D,6LAAC;wFAAI,WAAU;;4FAAyB,SAAS,WAAW;4FAAC;;;;;;;;;;;;;;;;;;;kFAGjE,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAI,WAAU;;oFACZ,SAAS,UAAU,CAAC,cAAc,CAAC;oFAAS;;;;;;;0FAE/C,6LAAC;gFAAI,WAAU;;oFAAwB;oFAC7B,SAAS,iBAAiB,CAAC,cAAc,CAAC;oFAAS;;;;;;;;;;;;;;+DAfvD,SAAS,MAAM;;;;;;;;;;;+CAJtB;;;;;;;;;;;kDA6BX,6LAAC;wCAA8B,WAAU;;0DACvC,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAG,WAAU;8DAAoC;;;;;;;;;;;0DAEpD,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAM,WAAU;;sEACf,6LAAC;4DAAM,WAAU;sEACf,cAAA,6LAAC;;kFACC,6LAAC;wEAAG,WAAU;kFAAkF;;;;;;kFAGhG,6LAAC;wEAAG,WAAU;kFAAkF;;;;;;kFAGhG,6LAAC;wEAAG,WAAU;kFAAkF;;;;;;kFAGhG,6LAAC;wEAAG,WAAU;kFAAkF;;;;;;kFAGhG,6LAAC;wEAAG,WAAU;kFAAkF;;;;;;;;;;;;;;;;;sEAKpG,6LAAC;4DAAM,WAAU;sEACd,gBAAgB,GAAG,OAAO,GAAG,GAAG,CAAC,CAAC,WAAW,sBAC5C,6LAAC;oEAAwB,WAAW,QAAQ,MAAM,IAAI,aAAa;;sFACjE,6LAAC;4EAAG,WAAU;sFACX,IAAI,KAAK,UAAU,IAAI,GAAG,OAAO,kBAAkB,CAAC,SAAS;gFAC5D,MAAM;gFACN,OAAO;4EACT;;;;;;sFAEF,6LAAC;4EAAG,WAAU;;gFACX,UAAU,KAAK,CAAC,cAAc,CAAC;gFAAS;;;;;;;sFAE3C,6LAAC;4EAAG,WAAU;;gFACX,UAAU,MAAM,CAAC,cAAc,CAAC;gFAAS;;;;;;;sFAE5C,6LAAC;4EAAG,WAAU;sFACX,UAAU,MAAM;;;;;;sFAEnB,6LAAC;4EAAG,WAAU;;gFACX,UAAU,MAAM,GAAG,IAChB,CAAC,UAAU,KAAK,GAAG,UAAU,MAAM,EAAE,cAAc,CAAC,WACpD;gFAAI;;;;;;;;mEAnBH,UAAU,IAAI;;;;;;;;;;;;;;;;;;;;;;uCA3BxB;;;;;;;;;;;;;;;;;;;;;;;0BA6DnB,6LAAC,oIAAA,CAAA,UAAW;gBACV,SAAS;gBACT,QAAQ;gBACR,SAAS;oBACP,qBAAqB;oBACrB,kBAAkB;gBACpB;gBACA,QAAQ;;;;;;0BAIV,6LAAC,qIAAA,CAAA,UAAY;gBACX,UAAU;gBACV,QAAQ;gBACR,SAAS;oBACP,sBAAsB;oBACtB,mBAAmB;gBACrB;gBACA,QAAQ;;;;;;0BAIV,6LAAC,iIAAA,CAAA,UAAQ;gBACP,MAAM;gBACN,QAAQ;gBACR,SAAS;oBACP,kBAAkB;oBAClB,eAAe;gBACjB;gBACA,QAAQ;;;;;;0BAIV,6LAAC,0IAAA,CAAA,UAAiB;gBAChB,OAAO;gBACP,QAAQ;gBACR,SAAS;oBACP,sBAAsB;oBACtB,iBAAiB;gBACnB;gBACA,gBAAgB;;;;;;0BAIlB,6LAAC,2IAAA,CAAA,UAAkB;gBACjB,QAAQ;gBACR,OACE,kBAAkB,qBAClB,mBAAmB,sBACnB;gBAEF,SACE,kBACI,CAAC,4BAA4B,EAAE,iBAAiB,KAAK,kCAAkC,CAAC,GACxF,mBACA,CAAC,6BAA6B,EAAE,kBAAkB,KAAK,kCAAkC,CAAC,GAC1F,CAAC,8BAA8B,EAAE,cAAc,KAAK,kCAAkC,CAAC;gBAE7F,WACE,kBAAkB,uBAClB,mBAAmB,wBACnB;gBAEF,UAAU;oBACR,qBAAqB;oBACrB,mBAAmB;oBACnB,oBAAoB;oBACpB,gBAAgB;gBAClB;gBACA,WAAW;;;;;;;;;;;;AAInB;GA/pCwB;;QAyBoD,qIAAA,CAAA,cAAW;QACwC,sIAAA,CAAA,gBAAa;QACxB,mIAAA,CAAA,YAAS;QACuB,kIAAA,CAAA,WAAQ;QAatJ,qIAAA,CAAA,aAAU;;;KAzCQ"}}, {"offset": {"line": 5905, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}