'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useOrders } from './OrderContext';
import { useProducts } from './ProductContext';
import { useUsers } from './UserContext';

interface SalesData {
  date: string;
  sales: number;
  orders: number;
  profit: number;
}

interface ProductSales {
  productId: string;
  productName: string;
  quantitySold: number;
  totalRevenue: number;
  totalProfit: number;
  averagePrice: number;
}

interface CustomerAnalysis {
  userId: string;
  userName: string;
  totalOrders: number;
  totalSpent: number;
  averageOrderValue: number;
  lastOrderDate: Date;
}

interface ReportsContextType {
  // Sales Reports
  getDailySales: (days?: number) => SalesData[];
  getWeeklySales: (weeks?: number) => SalesData[];
  getMonthlySales: (months?: number) => SalesData[];
  
  // Product Analytics
  getTopSellingProducts: (limit?: number) => ProductSales[];
  getMostProfitableProducts: (limit?: number) => ProductSales[];
  getProductPerformance: (productId: string) => ProductSales | null;
  
  // Customer Analytics
  getTopCustomers: (limit?: number) => CustomerAnalysis[];
  getCustomerAnalysis: (userId: string) => CustomerAnalysis | null;
  
  // Financial Reports
  getTotalRevenue: (startDate?: Date, endDate?: Date) => number;
  getTotalProfit: (startDate?: Date, endDate?: Date) => number;
  getAverageOrderValue: (startDate?: Date, endDate?: Date) => number;
  
  // Growth Analytics
  getRevenueGrowth: (period: 'daily' | 'weekly' | 'monthly') => number;
  getOrderGrowth: (period: 'daily' | 'weekly' | 'monthly') => number;
  
  // Summary Stats
  getSummaryStats: () => {
    totalRevenue: number;
    totalOrders: number;
    totalProfit: number;
    averageOrderValue: number;
    topProduct: string;
    topCustomer: string;
    revenueGrowth: number;
    orderGrowth: number;
  };
  
  isLoading: boolean;
}

const ReportsContext = createContext<ReportsContextType | undefined>(undefined);

interface ReportsProviderProps {
  children: ReactNode;
}

export function ReportsProvider({ children }: ReportsProviderProps) {
  const { orders } = useOrders();
  const { products } = useProducts();
  const { users } = useUsers();
  const [isLoading, setIsLoading] = useState(false);

  // Helper function to get date range
  const getDateRange = (startDate?: Date, endDate?: Date) => {
    const end = endDate || new Date();
    const start = startDate || new Date(end.getTime() - 30 * 24 * 60 * 60 * 1000); // 30 days ago
    return { start, end };
  };

  // Helper function to filter orders by date
  const filterOrdersByDate = (startDate?: Date, endDate?: Date) => {
    const { start, end } = getDateRange(startDate, endDate);
    return orders.filter(order => {
      const orderDate = new Date(order.createdAt);
      return orderDate >= start && orderDate <= end && order.status !== 'cancelled';
    });
  };

  // Daily Sales Report
  const getDailySales = (days: number = 30): SalesData[] => {
    const salesMap = new Map<string, { sales: number; orders: number; profit: number }>();
    
    // Initialize last N days
    for (let i = 0; i < days; i++) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      const dateStr = date.toISOString().split('T')[0];
      salesMap.set(dateStr, { sales: 0, orders: 0, profit: 0 });
    }

    // Aggregate sales data
    orders.forEach(order => {
      if (order.status === 'cancelled') return;
      
      const dateStr = new Date(order.createdAt).toISOString().split('T')[0];
      if (salesMap.has(dateStr)) {
        const current = salesMap.get(dateStr)!;
        const profit = order.total * 0.3; // Assume 30% profit margin
        salesMap.set(dateStr, {
          sales: current.sales + order.total,
          orders: current.orders + 1,
          profit: current.profit + profit
        });
      }
    });

    return Array.from(salesMap.entries())
      .map(([date, data]) => ({
        date,
        sales: data.sales,
        orders: data.orders,
        profit: data.profit
      }))
      .sort((a, b) => a.date.localeCompare(b.date));
  };

  // Weekly Sales Report
  const getWeeklySales = (weeks: number = 12): SalesData[] => {
    const salesMap = new Map<string, { sales: number; orders: number; profit: number }>();
    
    // Initialize last N weeks
    for (let i = 0; i < weeks; i++) {
      const date = new Date();
      date.setDate(date.getDate() - (i * 7));
      const weekStart = new Date(date.setDate(date.getDate() - date.getDay()));
      const weekStr = weekStart.toISOString().split('T')[0];
      salesMap.set(weekStr, { sales: 0, orders: 0, profit: 0 });
    }

    // Aggregate weekly data
    orders.forEach(order => {
      if (order.status === 'cancelled') return;
      
      const orderDate = new Date(order.createdAt);
      const weekStart = new Date(orderDate.setDate(orderDate.getDate() - orderDate.getDay()));
      const weekStr = weekStart.toISOString().split('T')[0];
      
      if (salesMap.has(weekStr)) {
        const current = salesMap.get(weekStr)!;
        const profit = order.total * 0.3;
        salesMap.set(weekStr, {
          sales: current.sales + order.total,
          orders: current.orders + 1,
          profit: current.profit + profit
        });
      }
    });

    return Array.from(salesMap.entries())
      .map(([date, data]) => ({
        date,
        sales: data.sales,
        orders: data.orders,
        profit: data.profit
      }))
      .sort((a, b) => a.date.localeCompare(b.date));
  };

  // Monthly Sales Report
  const getMonthlySales = (months: number = 12): SalesData[] => {
    const salesMap = new Map<string, { sales: number; orders: number; profit: number }>();
    
    // Initialize last N months
    for (let i = 0; i < months; i++) {
      const date = new Date();
      date.setMonth(date.getMonth() - i);
      const monthStr = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
      salesMap.set(monthStr, { sales: 0, orders: 0, profit: 0 });
    }

    // Aggregate monthly data
    orders.forEach(order => {
      if (order.status === 'cancelled') return;
      
      const orderDate = new Date(order.createdAt);
      const monthStr = `${orderDate.getFullYear()}-${String(orderDate.getMonth() + 1).padStart(2, '0')}`;
      
      if (salesMap.has(monthStr)) {
        const current = salesMap.get(monthStr)!;
        const profit = order.total * 0.3;
        salesMap.set(monthStr, {
          sales: current.sales + order.total,
          orders: current.orders + 1,
          profit: current.profit + profit
        });
      }
    });

    return Array.from(salesMap.entries())
      .map(([date, data]) => ({
        date,
        sales: data.sales,
        orders: data.orders,
        profit: data.profit
      }))
      .sort((a, b) => a.date.localeCompare(b.date));
  };

  // Top Selling Products
  const getTopSellingProducts = (limit: number = 10): ProductSales[] => {
    const productSales = new Map<string, { quantity: number; revenue: number; profit: number }>();

    orders.forEach(order => {
      if (order.status === 'cancelled') return;
      
      order.items.forEach(item => {
        const current = productSales.get(item.id) || { quantity: 0, revenue: 0, profit: 0 };
        const itemRevenue = item.price * item.quantity;
        const itemProfit = itemRevenue * 0.3; // 30% profit margin
        
        productSales.set(item.id, {
          quantity: current.quantity + item.quantity,
          revenue: current.revenue + itemRevenue,
          profit: current.profit + itemProfit
        });
      });
    });

    return Array.from(productSales.entries())
      .map(([productId, data]) => {
        const product = products.find(p => p.id === productId);
        return {
          productId,
          productName: product?.name || 'منتج محذوف',
          quantitySold: data.quantity,
          totalRevenue: data.revenue,
          totalProfit: data.profit,
          averagePrice: data.revenue / data.quantity
        };
      })
      .sort((a, b) => b.quantitySold - a.quantitySold)
      .slice(0, limit);
  };

  // Most Profitable Products
  const getMostProfitableProducts = (limit: number = 10): ProductSales[] => {
    return getTopSellingProducts(products.length)
      .sort((a, b) => b.totalProfit - a.totalProfit)
      .slice(0, limit);
  };

  // Product Performance
  const getProductPerformance = (productId: string): ProductSales | null => {
    const allProducts = getTopSellingProducts(products.length);
    return allProducts.find(p => p.productId === productId) || null;
  };

  // Top Customers
  const getTopCustomers = (limit: number = 10): CustomerAnalysis[] => {
    const customerData = new Map<string, { orders: number; spent: number; lastOrder: Date }>();

    orders.forEach(order => {
      if (order.status === 'cancelled') return;
      
      const current = customerData.get(order.userId) || { orders: 0, spent: 0, lastOrder: new Date(0) };
      customerData.set(order.userId, {
        orders: current.orders + 1,
        spent: current.spent + order.total,
        lastOrder: new Date(Math.max(current.lastOrder.getTime(), new Date(order.createdAt).getTime()))
      });
    });

    return Array.from(customerData.entries())
      .map(([userId, data]) => {
        const user = users.find(u => u.id === userId);
        return {
          userId,
          userName: user?.name || 'مستخدم محذوف',
          totalOrders: data.orders,
          totalSpent: data.spent,
          averageOrderValue: data.spent / data.orders,
          lastOrderDate: data.lastOrder
        };
      })
      .sort((a, b) => b.totalSpent - a.totalSpent)
      .slice(0, limit);
  };

  // Customer Analysis
  const getCustomerAnalysis = (userId: string): CustomerAnalysis | null => {
    const allCustomers = getTopCustomers(users.length);
    return allCustomers.find(c => c.userId === userId) || null;
  };

  // Financial Reports
  const getTotalRevenue = (startDate?: Date, endDate?: Date): number => {
    const filteredOrders = filterOrdersByDate(startDate, endDate);
    return filteredOrders.reduce((total, order) => total + order.total, 0);
  };

  const getTotalProfit = (startDate?: Date, endDate?: Date): number => {
    const revenue = getTotalRevenue(startDate, endDate);
    return revenue * 0.3; // 30% profit margin
  };

  const getAverageOrderValue = (startDate?: Date, endDate?: Date): number => {
    const filteredOrders = filterOrdersByDate(startDate, endDate);
    if (filteredOrders.length === 0) return 0;
    return getTotalRevenue(startDate, endDate) / filteredOrders.length;
  };

  // Growth Analytics
  const getRevenueGrowth = (period: 'daily' | 'weekly' | 'monthly'): number => {
    const salesData = period === 'daily' ? getDailySales(60) : 
                     period === 'weekly' ? getWeeklySales(24) : 
                     getMonthlySales(24);
    
    if (salesData.length < 2) return 0;
    
    const midPoint = Math.floor(salesData.length / 2);
    const firstHalf = salesData.slice(0, midPoint);
    const secondHalf = salesData.slice(midPoint);
    
    const firstHalfRevenue = firstHalf.reduce((sum, data) => sum + data.sales, 0);
    const secondHalfRevenue = secondHalf.reduce((sum, data) => sum + data.sales, 0);
    
    if (firstHalfRevenue === 0) return 0;
    return ((secondHalfRevenue - firstHalfRevenue) / firstHalfRevenue) * 100;
  };

  const getOrderGrowth = (period: 'daily' | 'weekly' | 'monthly'): number => {
    const salesData = period === 'daily' ? getDailySales(60) : 
                     period === 'weekly' ? getWeeklySales(24) : 
                     getMonthlySales(24);
    
    if (salesData.length < 2) return 0;
    
    const midPoint = Math.floor(salesData.length / 2);
    const firstHalf = salesData.slice(0, midPoint);
    const secondHalf = salesData.slice(midPoint);
    
    const firstHalfOrders = firstHalf.reduce((sum, data) => sum + data.orders, 0);
    const secondHalfOrders = secondHalf.reduce((sum, data) => sum + data.orders, 0);
    
    if (firstHalfOrders === 0) return 0;
    return ((secondHalfOrders - firstHalfOrders) / firstHalfOrders) * 100;
  };

  // Summary Statistics
  const getSummaryStats = () => {
    const totalRevenue = getTotalRevenue();
    const totalOrders = orders.filter(o => o.status !== 'cancelled').length;
    const totalProfit = getTotalProfit();
    const averageOrderValue = getAverageOrderValue();
    
    const topProducts = getTopSellingProducts(1);
    const topCustomers = getTopCustomers(1);
    
    return {
      totalRevenue,
      totalOrders,
      totalProfit,
      averageOrderValue,
      topProduct: topProducts[0]?.productName || 'لا يوجد',
      topCustomer: topCustomers[0]?.userName || 'لا يوجد',
      revenueGrowth: getRevenueGrowth('monthly'),
      orderGrowth: getOrderGrowth('monthly')
    };
  };

  const value: ReportsContextType = {
    getDailySales,
    getWeeklySales,
    getMonthlySales,
    getTopSellingProducts,
    getMostProfitableProducts,
    getProductPerformance,
    getTopCustomers,
    getCustomerAnalysis,
    getTotalRevenue,
    getTotalProfit,
    getAverageOrderValue,
    getRevenueGrowth,
    getOrderGrowth,
    getSummaryStats,
    isLoading,
  };

  return (
    <ReportsContext.Provider value={value}>
      {children}
    </ReportsContext.Provider>
  );
}

export function useReports() {
  const context = useContext(ReportsContext);
  if (context === undefined) {
    throw new Error('useReports must be used within a ReportsProvider');
  }
  return context;
}
