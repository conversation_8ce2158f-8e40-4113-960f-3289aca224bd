{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/New%20folder%20%288%29/arabic-ecommerce/src/components/Navbar.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { useState } from 'react';\n\nexport default function Navbar() {\n  const [isLoggedIn, setIsLoggedIn] = useState(false);\n  const [cartItemsCount, setCartItemsCount] = useState(0);\n\n  return (\n    <nav className=\"bg-white shadow-lg border-b border-gray-200\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center h-16\">\n          {/* Logo */}\n          <div className=\"flex-shrink-0\">\n            <Link href=\"/\" className=\"text-2xl font-bold text-primary-600\">\n              🛍️ المتجر العربي\n            </Link>\n          </div>\n\n          {/* Navigation Links */}\n          <div className=\"hidden md:block\">\n            <div className=\"mr-10 flex items-baseline space-x-4 space-x-reverse\">\n              <Link\n                href=\"/\"\n                className=\"text-gray-700 hover:text-primary-600 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 flex items-center\"\n              >\n                🏠 الصفحة الرئيسية\n              </Link>\n              <Link\n                href=\"/products\"\n                className=\"text-gray-700 hover:text-primary-600 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 flex items-center\"\n              >\n                📦 المنتجات\n              </Link>\n              {isLoggedIn && (\n                <Link\n                  href=\"/admin\"\n                  className=\"text-gray-700 hover:text-primary-600 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 flex items-center\"\n                >\n                  ⚙️ لوحة التحكم\n                </Link>\n              )}\n            </div>\n          </div>\n\n          {/* Right side - Cart and Auth */}\n          <div className=\"flex items-center space-x-4 space-x-reverse\">\n            {/* Cart */}\n            <Link\n              href=\"/cart\"\n              className=\"relative text-gray-700 hover:text-primary-600 transition-colors duration-200\"\n            >\n              <span className=\"text-2xl\">🛒</span>\n              {cartItemsCount > 0 && (\n                <span className=\"absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center\">\n                  {cartItemsCount}\n                </span>\n              )}\n            </Link>\n\n            {/* Auth Links */}\n            {isLoggedIn ? (\n              <div className=\"flex items-center space-x-2 space-x-reverse\">\n                <span className=\"text-gray-700 text-sm\">مرحباً، أحمد</span>\n                <button\n                  onClick={() => setIsLoggedIn(false)}\n                  className=\"bg-red-500 hover:bg-red-600 text-white px-3 py-1 rounded text-sm transition-colors duration-200\"\n                >\n                  تسجيل الخروج\n                </button>\n              </div>\n            ) : (\n              <div className=\"flex items-center space-x-2 space-x-reverse\">\n                <Link\n                  href=\"/login\"\n                  className=\"text-gray-700 hover:text-primary-600 px-3 py-1 rounded text-sm transition-colors duration-200\"\n                >\n                  تسجيل الدخول\n                </Link>\n                <Link\n                  href=\"/register\"\n                  className=\"bg-primary-600 hover:bg-primary-700 text-white px-3 py-1 rounded text-sm transition-colors duration-200\"\n                >\n                  إنشاء حساب\n                </Link>\n              </div>\n            )}\n          </div>\n\n          {/* Mobile menu button */}\n          <div className=\"md:hidden\">\n            <button\n              type=\"button\"\n              className=\"text-gray-700 hover:text-primary-600 focus:outline-none focus:text-primary-600\"\n            >\n              <svg className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\n              </svg>\n            </button>\n          </div>\n        </div>\n      </div>\n    </nav>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;sCAAsC;;;;;;;;;;;kCAMjE,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCA<PERSON>,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;gCAGA,4BACC,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;kCAQP,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;kDAEV,8OAAC;wCAAK,WAAU;kDAAW;;;;;;oCAC1B,iBAAiB,mBAChB,8OAAC;wCAAK,WAAU;kDACb;;;;;;;;;;;;4BAMN,2BACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAwB;;;;;;kDACxC,8OAAC;wCACC,SAAS,IAAM,cAAc;wCAC7B,WAAU;kDACX;;;;;;;;;;;qDAKH,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;kCAQP,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,MAAK;4BACL,WAAU;sCAEV,cAAA,8OAAC;gCAAI,WAAU;gCAAU,MAAK;gCAAO,SAAQ;gCAAY,QAAO;0CAC9D,cAAA,8OAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQrF"}}, {"offset": {"line": 249, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 255, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/New%20folder%20%288%29/arabic-ecommerce/src/app/cart/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useRouter } from 'next/navigation';\nimport Link from 'next/link';\nimport Navbar from '@/components/Navbar';\nimport { sampleProducts } from '@/lib/data';\n\n// Mock cart data\nconst mockCartItems = [\n  {\n    id: '1',\n    productId: '1',\n    product: sampleProducts[0],\n    quantity: 2,\n    price: sampleProducts[0].price,\n  },\n  {\n    id: '2',\n    productId: '3',\n    product: sampleProducts[2],\n    quantity: 1,\n    price: sampleProducts[2].price,\n  },\n];\n\nexport default function CartPage() {\n  const router = useRouter();\n  const [cartItems, setCartItems] = useState(mockCartItems);\n\n  const updateQuantity = (itemId: string, newQuantity: number) => {\n    if (newQuantity === 0) {\n      removeItem(itemId);\n      return;\n    }\n\n    setCartItems(items =>\n      items.map(item =>\n        item.id === itemId ? { ...item, quantity: newQuantity } : item\n      )\n    );\n  };\n\n  const removeItem = (itemId: string) => {\n    setCartItems(items => items.filter(item => item.id !== itemId));\n  };\n\n  const subtotal = cartItems.reduce((sum, item) => sum + (item.price * item.quantity), 0);\n  const shipping = 50; // Fixed shipping cost\n  const total = subtotal + shipping;\n\n  const handleCheckout = () => {\n    if (cartItems.length === 0) {\n      alert('السلة فارغة! أضف منتجات أولاً.');\n      return;\n    }\n    router.push('/checkout');\n  };\n\n  if (cartItems.length === 0) {\n    return (\n      <div className=\"min-h-screen bg-gray-50\">\n        <Navbar />\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16\">\n          <div className=\"text-center\">\n            <div className=\"text-6xl mb-4\">🛒</div>\n            <h2 className=\"text-2xl font-bold text-gray-800 mb-4\">\n              سلة التسوق فارغة\n            </h2>\n            <p className=\"text-gray-600 mb-8\">\n              لم تقم بإضافة أي منتجات إلى سلة التسوق بعد\n            </p>\n            <Link\n              href=\"/products\"\n              className=\"inline-block bg-primary-600 hover:bg-primary-700 text-white font-bold py-3 px-8 rounded-lg transition-colors duration-200\"\n            >\n              تصفح المنتجات\n            </Link>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Navbar />\n\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <h1 className=\"text-3xl font-bold text-gray-800 mb-8\">سلة التسوق</h1>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n          {/* Cart Items */}\n          <div className=\"lg:col-span-2\">\n            <div className=\"bg-white rounded-lg shadow-md overflow-hidden\">\n              <div className=\"px-6 py-4 border-b border-gray-200\">\n                <h2 className=\"text-lg font-semibold text-gray-800\">\n                  المنتجات ({cartItems.length})\n                </h2>\n              </div>\n\n              <div className=\"divide-y divide-gray-200\">\n                {cartItems.map((item) => (\n                  <div key={item.id} className=\"p-6\">\n                    <div className=\"flex items-center\">\n                      <div className=\"h-20 w-20 bg-gray-200 rounded-lg flex-shrink-0\"></div>\n\n                      <div className=\"mr-4 flex-1\">\n                        <h3 className=\"text-lg font-medium text-gray-800\">\n                          {item.product.name}\n                        </h3>\n                        <p className=\"text-gray-600 text-sm mt-1\">\n                          {item.product.category}\n                        </p>\n                        <p className=\"text-primary-600 font-bold mt-2\">\n                          {item.price.toLocaleString('ar-SA')} ر.س\n                        </p>\n                      </div>\n\n                      <div className=\"flex items-center space-x-2 space-x-reverse\">\n                        <button\n                          onClick={() => updateQuantity(item.id, item.quantity - 1)}\n                          className=\"w-8 h-8 rounded-full bg-gray-200 hover:bg-gray-300 flex items-center justify-center\"\n                        >\n                          -\n                        </button>\n                        <span className=\"w-12 text-center font-medium\">\n                          {item.quantity}\n                        </span>\n                        <button\n                          onClick={() => updateQuantity(item.id, item.quantity + 1)}\n                          className=\"w-8 h-8 rounded-full bg-gray-200 hover:bg-gray-300 flex items-center justify-center\"\n                        >\n                          +\n                        </button>\n                      </div>\n\n                      <button\n                        onClick={() => removeItem(item.id)}\n                        className=\"mr-4 text-red-500 hover:text-red-700\"\n                      >\n                        🗑️\n                      </button>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n          </div>\n\n          {/* Order Summary */}\n          <div className=\"lg:col-span-1\">\n            <div className=\"bg-white rounded-lg shadow-md p-6 sticky top-4\">\n              <h2 className=\"text-lg font-semibold text-gray-800 mb-4\">\n                ملخص الطلب\n              </h2>\n\n              <div className=\"space-y-3\">\n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-600\">المجموع الفرعي</span>\n                  <span className=\"font-medium\">\n                    {subtotal.toLocaleString('ar-SA')} ر.س\n                  </span>\n                </div>\n\n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-600\">الشحن</span>\n                  <span className=\"font-medium\">\n                    {shipping.toLocaleString('ar-SA')} ر.س\n                  </span>\n                </div>\n\n                <div className=\"border-t border-gray-200 pt-3\">\n                  <div className=\"flex justify-between\">\n                    <span className=\"text-lg font-semibold\">المجموع الكلي</span>\n                    <span className=\"text-lg font-bold text-primary-600\">\n                      {total.toLocaleString('ar-SA')} ر.س\n                    </span>\n                  </div>\n                </div>\n              </div>\n\n              <button\n                onClick={handleCheckout}\n                className=\"w-full bg-primary-600 hover:bg-primary-700 text-white font-bold py-3 px-4 rounded-lg mt-6 transition-colors duration-200\"\n              >\n                متابعة للدفع\n              </button>\n\n              <Link\n                href=\"/products\"\n                className=\"block text-center text-primary-600 hover:text-primary-700 mt-4 font-medium\"\n              >\n                متابعة التسوق\n              </Link>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAQA,iBAAiB;AACjB,MAAM,gBAAgB;IACpB;QACE,IAAI;QACJ,WAAW;QACX,SAAS,kHAAA,CAAA,iBAAc,CAAC,EAAE;QAC1B,UAAU;QACV,OAAO,kHAAA,CAAA,iBAAc,CAAC,EAAE,CAAC,KAAK;IAChC;IACA;QACE,IAAI;QACJ,WAAW;QACX,SAAS,kHAAA,CAAA,iBAAc,CAAC,EAAE;QAC1B,UAAU;QACV,OAAO,kHAAA,CAAA,iBAAc,CAAC,EAAE,CAAC,KAAK;IAChC;CACD;AAEc,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,iBAAiB,CAAC,QAAgB;QACtC,IAAI,gBAAgB,GAAG;YACrB,WAAW;YACX;QACF;QAEA,aAAa,CAAA,QACX,MAAM,GAAG,CAAC,CAAA,OACR,KAAK,EAAE,KAAK,SAAS;oBAAE,GAAG,IAAI;oBAAE,UAAU;gBAAY,IAAI;IAGhE;IAEA,MAAM,aAAa,CAAC;QAClB,aAAa,CAAA,QAAS,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;IACzD;IAEA,MAAM,WAAW,UAAU,MAAM,CAAC,CAAC,KAAK,OAAS,MAAO,KAAK,KAAK,GAAG,KAAK,QAAQ,EAAG;IACrF,MAAM,WAAW,IAAI,sBAAsB;IAC3C,MAAM,QAAQ,WAAW;IAEzB,MAAM,iBAAiB;QACrB,IAAI,UAAU,MAAM,KAAK,GAAG;YAC1B,MAAM;YACN;QACF;QACA,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,UAAU,MAAM,KAAK,GAAG;QAC1B,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC,4HAAA,CAAA,UAAM;;;;;8BACP,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CAAgB;;;;;;0CAC/B,8OAAC;gCAAG,WAAU;0CAAwC;;;;;;0CAGtD,8OAAC;gCAAE,WAAU;0CAAqB;;;;;;0CAGlC,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;IAOX;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,4HAAA,CAAA,UAAM;;;;;0BAEP,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAwC;;;;;;kCAEtD,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAG,WAAU;;oDAAsC;oDACvC,UAAU,MAAM;oDAAC;;;;;;;;;;;;sDAIhC,8OAAC;4CAAI,WAAU;sDACZ,UAAU,GAAG,CAAC,CAAC,qBACd,8OAAC;oDAAkB,WAAU;8DAC3B,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;;;;;0EAEf,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAG,WAAU;kFACX,KAAK,OAAO,CAAC,IAAI;;;;;;kFAEpB,8OAAC;wEAAE,WAAU;kFACV,KAAK,OAAO,CAAC,QAAQ;;;;;;kFAExB,8OAAC;wEAAE,WAAU;;4EACV,KAAK,KAAK,CAAC,cAAc,CAAC;4EAAS;;;;;;;;;;;;;0EAIxC,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEACC,SAAS,IAAM,eAAe,KAAK,EAAE,EAAE,KAAK,QAAQ,GAAG;wEACvD,WAAU;kFACX;;;;;;kFAGD,8OAAC;wEAAK,WAAU;kFACb,KAAK,QAAQ;;;;;;kFAEhB,8OAAC;wEACC,SAAS,IAAM,eAAe,KAAK,EAAE,EAAE,KAAK,QAAQ,GAAG;wEACvD,WAAU;kFACX;;;;;;;;;;;;0EAKH,8OAAC;gEACC,SAAS,IAAM,WAAW,KAAK,EAAE;gEACjC,WAAU;0EACX;;;;;;;;;;;;mDArCK,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;0CAgDzB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDAIzD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,8OAAC;4DAAK,WAAU;;gEACb,SAAS,cAAc,CAAC;gEAAS;;;;;;;;;;;;;8DAItC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,8OAAC;4DAAK,WAAU;;gEACb,SAAS,cAAc,CAAC;gEAAS;;;;;;;;;;;;;8DAItC,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EAAwB;;;;;;0EACxC,8OAAC;gEAAK,WAAU;;oEACb,MAAM,cAAc,CAAC;oEAAS;;;;;;;;;;;;;;;;;;;;;;;;sDAMvC,8OAAC;4CACC,SAAS;4CACT,WAAU;sDACX;;;;;;sDAID,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf"}}, {"offset": {"line": 707, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 717, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/New%20folder%20%288%29/arabic-ecommerce/node_modules/next/src/server/route-modules/app-page/vendored/contexts/app-router-context.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'contexts'\n].AppRouterContext\n"], "names": ["module", "exports", "require", "vendored", "AppRouterContext"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,0HAAyBC,QAAQ,CACxD,WACD,CAACC,gBAAgB", "ignoreList": [0]}}, {"offset": {"line": 719, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 724, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/New%20folder%20%288%29/arabic-ecommerce/node_modules/next/src/server/route-modules/app-page/vendored/contexts/hooks-client-context.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'contexts'\n].HooksClientContext\n"], "names": ["module", "exports", "require", "vendored", "HooksClientContext"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,0HAAyBC,QAAQ,CACxD,WACD,CAACC,kBAAkB", "ignoreList": [0]}}, {"offset": {"line": 726, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 731, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/New%20folder%20%288%29/arabic-ecommerce/node_modules/next/src/client/components/router-reducer/reducers/get-segment-value.ts"], "sourcesContent": ["import type { Segment } from '../../../../server/app-render/types'\n\nexport function getSegmentValue(segment: Segment) {\n  return Array.isArray(segment) ? segment[1] : segment\n}\n"], "names": ["getSegmentValue", "segment", "Array", "isArray"], "mappings": ";;;;+BAEgBA,mBAAAA;;;eAAAA;;;AAAT,SAASA,gBAAgBC,OAAgB;IAC9C,OAAOC,MAAMC,OAAO,CAACF,WAAWA,OAAO,CAAC,EAAE,GAAGA;AAC/C", "ignoreList": [0]}}, {"offset": {"line": 751, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 756, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/New%20folder%20%288%29/arabic-ecommerce/node_modules/next/src/shared/lib/segment.ts"], "sourcesContent": ["import type { Segment } from '../../server/app-render/types'\n\nexport function isGroupSegment(segment: string) {\n  // Use array[0] for performant purpose\n  return segment[0] === '(' && segment.endsWith(')')\n}\n\nexport function isParallelRouteSegment(segment: string) {\n  return segment.startsWith('@') && segment !== '@children'\n}\n\nexport function addSearchParamsIfPageSegment(\n  segment: Segment,\n  searchParams: Record<string, string | string[] | undefined>\n) {\n  const isPageSegment = segment.includes(PAGE_SEGMENT_KEY)\n\n  if (isPageSegment) {\n    const stringifiedQuery = JSON.stringify(searchParams)\n    return stringifiedQuery !== '{}'\n      ? PAGE_SEGMENT_KEY + '?' + stringifiedQuery\n      : PAGE_SEGMENT_KEY\n  }\n\n  return segment\n}\n\nexport const PAGE_SEGMENT_KEY = '__PAGE__'\nexport const DEFAULT_SEGMENT_KEY = '__DEFAULT__'\n"], "names": ["DEFAULT_SEGMENT_KEY", "PAGE_SEGMENT_KEY", "addSearchParamsIfPageSegment", "isGroupSegment", "isParallelRouteSegment", "segment", "endsWith", "startsWith", "searchParams", "isPageSegment", "includes", "stringified<PERSON><PERSON>y", "JSON", "stringify"], "mappings": ";;;;;;;;;;;;;;;;;;IA4BaA,mBAAmB,EAAA;eAAnBA;;IADAC,gBAAgB,EAAA;eAAhBA;;IAhBGC,4BAA4B,EAAA;eAA5BA;;IATAC,cAAc,EAAA;eAAdA;;IAKAC,sBAAsB,EAAA;eAAtBA;;;AALT,SAASD,eAAeE,OAAe;IAC5C,sCAAsC;IACtC,OAAOA,OAAO,CAAC,EAAE,KAAK,OAAOA,QAAQC,QAAQ,CAAC;AAChD;AAEO,SAASF,uBAAuBC,OAAe;IACpD,OAAOA,QAAQE,UAAU,CAAC,QAAQF,YAAY;AAChD;AAEO,SAASH,6BACdG,OAAgB,EAChBG,YAA2D;IAE3D,MAAMC,gBAAgBJ,QAAQK,QAAQ,CAACT;IAEvC,IAAIQ,eAAe;QACjB,MAAME,mBAAmBC,KAAKC,SAAS,CAACL;QACxC,OAAOG,qBAAqB,OACxBV,mBAAmB,MAAMU,mBACzBV;IACN;IAEA,OAAOI;AACT;AAEO,MAAMJ,mBAAmB;AACzB,MAAMD,sBAAsB", "ignoreList": [0]}}, {"offset": {"line": 807, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 812, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/New%20folder%20%288%29/arabic-ecommerce/node_modules/next/src/client/components/redirect-status-code.ts"], "sourcesContent": ["export enum RedirectStatusCode {\n  SeeOther = 303,\n  TemporaryRedirect = 307,\n  PermanentRedirect = 308,\n}\n"], "names": ["RedirectStatusCode"], "mappings": ";;;;+BAAYA,sBAAAA;;;eAAAA;;;AAAL,IAAKA,qBAAAA,WAAAA,GAAAA,SAAAA,kBAAAA;;;;WAAAA", "ignoreList": [0]}}, {"offset": {"line": 835, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 840, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/New%20folder%20%288%29/arabic-ecommerce/node_modules/next/src/client/components/redirect-error.ts"], "sourcesContent": ["import { RedirectStatusCode } from './redirect-status-code'\n\nexport const REDIRECT_ERROR_CODE = 'NEXT_REDIRECT'\n\nexport enum RedirectType {\n  push = 'push',\n  replace = 'replace',\n}\n\nexport type RedirectError = Error & {\n  digest: `${typeof REDIRECT_ERROR_CODE};${RedirectType};${string};${RedirectStatusCode};`\n}\n\n/**\n * Checks an error to determine if it's an error generated by the\n * `redirect(url)` helper.\n *\n * @param error the error that may reference a redirect error\n * @returns true if the error is a redirect error\n */\nexport function isRedirectError(error: unknown): error is RedirectError {\n  if (\n    typeof error !== 'object' ||\n    error === null ||\n    !('digest' in error) ||\n    typeof error.digest !== 'string'\n  ) {\n    return false\n  }\n\n  const digest = error.digest.split(';')\n  const [errorCode, type] = digest\n  const destination = digest.slice(2, -2).join(';')\n  const status = digest.at(-2)\n\n  const statusCode = Number(status)\n\n  return (\n    errorCode === REDIRECT_ERROR_CODE &&\n    (type === 'replace' || type === 'push') &&\n    typeof destination === 'string' &&\n    !isNaN(statusCode) &&\n    statusCode in RedirectStatusCode\n  )\n}\n"], "names": ["REDIRECT_ERROR_CODE", "RedirectType", "isRedirectError", "error", "digest", "split", "errorCode", "type", "destination", "slice", "join", "status", "at", "statusCode", "Number", "isNaN", "RedirectStatusCode"], "mappings": ";;;;;;;;;;;;;;;;IAEaA,mBAAmB,EAAA;eAAnBA;;IAEDC,YAAY,EAAA;eAAZA;;IAgBIC,eAAe,EAAA;eAAfA;;;oCApBmB;AAE5B,MAAMF,sBAAsB;AAE5B,IAAKC,eAAAA,WAAAA,GAAAA,SAAAA,YAAAA;;;WAAAA;;AAgBL,SAASC,gBAAgBC,KAAc;IAC5C,IACE,OAAOA,UAAU,YACjBA,UAAU,QACV,CAAE,CAAA,YAAYA,KAAI,KAClB,OAAOA,MAAMC,MAAM,KAAK,UACxB;QACA,OAAO;IACT;IAEA,MAAMA,SAASD,MAAMC,MAAM,CAACC,KAAK,CAAC;IAClC,MAAM,CAACC,WAAWC,KAAK,GAAGH;IAC1B,MAAMI,cAAcJ,OAAOK,KAAK,CAAC,GAAG,CAAC,GAAGC,IAAI,CAAC;IAC7C,MAAMC,SAASP,OAAOQ,EAAE,CAAC,CAAC;IAE1B,MAAMC,aAAaC,OAAOH;IAE1B,OACEL,cAAcN,uBACbO,CAAAA,SAAS,aAAaA,SAAS,MAAK,KACrC,OAAOC,gBAAgB,YACvB,CAACO,MAAMF,eACPA,cAAcG,oBAAAA,kBAAkB;AAEpC", "ignoreList": [0]}}, {"offset": {"line": 891, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 896, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/New%20folder%20%288%29/arabic-ecommerce/node_modules/next/src/client/components/redirect.ts"], "sourcesContent": ["import { actionAsyncStorage } from '../../server/app-render/action-async-storage.external'\nimport { RedirectStatusCode } from './redirect-status-code'\nimport {\n  RedirectType,\n  type RedirectError,\n  isRedirectError,\n  REDIRECT_ERROR_CODE,\n} from './redirect-error'\n\nexport function getRedirectError(\n  url: string,\n  type: RedirectType,\n  statusCode: RedirectStatusCode = RedirectStatusCode.TemporaryRedirect\n): RedirectError {\n  const error = new Error(REDIRECT_ERROR_CODE) as RedirectError\n  error.digest = `${REDIRECT_ERROR_CODE};${type};${url};${statusCode};`\n  return error\n}\n\n/**\n * This function allows you to redirect the user to another URL. It can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n * - In a Server Component, this will insert a meta tag to redirect the user to the target page.\n * - In a Route Handler or Server Action, it will serve a 307/303 to the caller.\n * - In a Server Action, type defaults to 'push' and 'replace' elsewhere.\n *\n * Read more: [Next.js Docs: `redirect`](https://nextjs.org/docs/app/api-reference/functions/redirect)\n */\nexport function redirect(\n  /** The URL to redirect to */\n  url: string,\n  type?: RedirectType\n): never {\n  const actionStore = actionAsyncStorage.getStore()\n  const redirectType =\n    type || (actionStore?.isAction ? RedirectType.push : RedirectType.replace)\n  throw getRedirectError(\n    url,\n    redirectType,\n    RedirectStatusCode.TemporaryRedirect\n  )\n}\n\n/**\n * This function allows you to redirect the user to another URL. It can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n * - In a Server Component, this will insert a meta tag to redirect the user to the target page.\n * - In a Route Handler or Server Action, it will serve a 308/303 to the caller.\n *\n * Read more: [Next.js Docs: `redirect`](https://nextjs.org/docs/app/api-reference/functions/redirect)\n */\nexport function permanentRedirect(\n  /** The URL to redirect to */\n  url: string,\n  type: RedirectType = RedirectType.replace\n): never {\n  throw getRedirectError(url, type, RedirectStatusCode.PermanentRedirect)\n}\n\n/**\n * Returns the encoded URL from the error if it's a RedirectError, null\n * otherwise. Note that this does not validate the URL returned.\n *\n * @param error the error that may be a redirect error\n * @return the url if the error was a redirect error\n */\nexport function getURLFromRedirectError(error: RedirectError): string\nexport function getURLFromRedirectError(error: unknown): string | null {\n  if (!isRedirectError(error)) return null\n\n  // Slices off the beginning of the digest that contains the code and the\n  // separating ';'.\n  return error.digest.split(';').slice(2, -2).join(';')\n}\n\nexport function getRedirectTypeFromError(error: RedirectError): RedirectType {\n  if (!isRedirectError(error)) {\n    throw new Error('Not a redirect error')\n  }\n\n  return error.digest.split(';', 2)[1] as RedirectType\n}\n\nexport function getRedirectStatusCodeFromError(error: RedirectError): number {\n  if (!isRedirectError(error)) {\n    throw new Error('Not a redirect error')\n  }\n\n  return Number(error.digest.split(';').at(-2))\n}\n"], "names": ["getRedirectError", "getRedirectStatusCodeFromError", "getRedirectTypeFromError", "getURLFromRedirectError", "permanentRedirect", "redirect", "url", "type", "statusCode", "RedirectStatusCode", "TemporaryRedirect", "error", "Error", "REDIRECT_ERROR_CODE", "digest", "actionStore", "actionAsyncStorage", "getStore", "redirectType", "isAction", "RedirectType", "push", "replace", "PermanentRedirect", "isRedirectError", "split", "slice", "join", "Number", "at"], "mappings": ";;;;;;;;;;;;;;;;;;;IASgBA,gBAAgB,EAAA;eAAhBA;;IAgFAC,8BAA8B,EAAA;eAA9BA;;IARAC,wBAAwB,EAAA;eAAxBA;;IARAC,uBAAuB,EAAA;eAAvBA;;IAhBAC,iBAAiB,EAAA;eAAjBA;;IA1BAC,QAAQ,EAAA;eAARA;;;4CA/BmB;oCACA;+BAM5B;AAEA,SAASL,iBACdM,GAAW,EACXC,IAAkB,EAClBC,UAAqE;IAArEA,IAAAA,eAAAA,KAAAA,GAAAA,aAAiCC,oBAAAA,kBAAkB,CAACC,iBAAiB;IAErE,MAAMC,QAAQ,IAAIC,MAAMC,eAAAA,mBAAmB;IAC3CF,MAAMG,MAAM,GAAMD,eAAAA,mBAAmB,GAAC,MAAGN,OAAK,MAAGD,MAAI,MAAGE,aAAW;IACnE,OAAOG;AACT;AAcO,SAASN,SACd,2BAA2B,GAC3BC,GAAW,EACXC,IAAmB;IAEnB,MAAMQ,cAAcC,4BAAAA,kBAAkB,CAACC,QAAQ;IAC/C,MAAMC,eACJX,QAASQ,CAAAA,CAAAA,eAAAA,OAAAA,KAAAA,IAAAA,YAAaI,QAAQ,IAAGC,eAAAA,YAAY,CAACC,IAAI,GAAGD,eAAAA,YAAY,CAACE,OAAM;IAC1E,MAAMtB,iBACJM,KACAY,cACAT,oBAAAA,kBAAkB,CAACC,iBAAiB;AAExC;AAaO,SAASN,kBACd,2BAA2B,GAC3BE,GAAW,EACXC,IAAyC;IAAzCA,IAAAA,SAAAA,KAAAA,GAAAA,OAAqBa,eAAAA,YAAY,CAACE,OAAO;IAEzC,MAAMtB,iBAAiBM,KAAKC,MAAME,oBAAAA,kBAAkB,CAACc,iBAAiB;AACxE;AAUO,SAASpB,wBAAwBQ,KAAc;IACpD,IAAI,CAACa,CAAAA,GAAAA,eAAAA,eAAe,EAACb,QAAQ,OAAO;IAEpC,wEAAwE;IACxE,kBAAkB;IAClB,OAAOA,MAAMG,MAAM,CAACW,KAAK,CAAC,KAAKC,KAAK,CAAC,GAAG,CAAC,GAAGC,IAAI,CAAC;AACnD;AAEO,SAASzB,yBAAyBS,KAAoB;IAC3D,IAAI,CAACa,CAAAA,GAAAA,eAAAA,eAAe,EAACb,QAAQ;QAC3B,MAAM,IAAIC,MAAM;IAClB;IAEA,OAAOD,MAAMG,MAAM,CAACW,KAAK,CAAC,KAAK,EAAE,CAAC,EAAE;AACtC;AAEO,SAASxB,+BAA+BU,KAAoB;IACjE,IAAI,CAACa,CAAAA,GAAAA,eAAAA,eAAe,EAACb,QAAQ;QAC3B,MAAM,IAAIC,MAAM;IAClB;IAEA,OAAOgB,OAAOjB,MAAMG,MAAM,CAACW,KAAK,CAAC,KAAKI,EAAE,CAAC,CAAC;AAC5C", "ignoreList": [0]}}, {"offset": {"line": 977, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 982, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/New%20folder%20%288%29/arabic-ecommerce/node_modules/next/src/client/components/http-access-fallback/http-access-fallback.ts"], "sourcesContent": ["export const HTTPAccessErrorStatus = {\n  NOT_FOUND: 404,\n  FORBIDDEN: 403,\n  UNAUTHORIZED: 401,\n}\n\nconst ALLOWED_CODES = new Set(Object.values(HTTPAccessErrorStatus))\n\nexport const HTTP_ERROR_FALLBACK_ERROR_CODE = 'NEXT_HTTP_ERROR_FALLBACK'\n\nexport type HTTPAccessFallbackError = Error & {\n  digest: `${typeof HTTP_ERROR_FALLBACK_ERROR_CODE};${string}`\n}\n\n/**\n * Checks an error to determine if it's an error generated by\n * the HTTP navigation APIs `notFound()`, `forbidden()` or `unauthorized()`.\n *\n * @param error the error that may reference a HTTP access error\n * @returns true if the error is a HTTP access error\n */\nexport function isHTTPAccessFallbackError(\n  error: unknown\n): error is HTTPAccessFallbackError {\n  if (\n    typeof error !== 'object' ||\n    error === null ||\n    !('digest' in error) ||\n    typeof error.digest !== 'string'\n  ) {\n    return false\n  }\n  const [prefix, httpStatus] = error.digest.split(';')\n\n  return (\n    prefix === HTTP_ERROR_FALLBACK_ERROR_CODE &&\n    ALLOWED_CODES.has(Number(httpStatus))\n  )\n}\n\nexport function getAccessFallbackHTTPStatus(\n  error: HTTPAccessFallbackError\n): number {\n  const httpStatus = error.digest.split(';')[1]\n  return Number(httpStatus)\n}\n\nexport function getAccessFallbackErrorTypeByStatus(\n  status: number\n): 'not-found' | 'forbidden' | 'unauthorized' | undefined {\n  switch (status) {\n    case 401:\n      return 'unauthorized'\n    case 403:\n      return 'forbidden'\n    case 404:\n      return 'not-found'\n    default:\n      return\n  }\n}\n"], "names": ["HTTPAccessErrorStatus", "HTTP_ERROR_FALLBACK_ERROR_CODE", "getAccessFallbackErrorTypeByStatus", "getAccessFallbackHTTPStatus", "isHTTPAccessFallbackError", "NOT_FOUND", "FORBIDDEN", "UNAUTHORIZED", "ALLOWED_CODES", "Set", "Object", "values", "error", "digest", "prefix", "httpStatus", "split", "has", "Number", "status"], "mappings": ";;;;;;;;;;;;;;;;;;IAAaA,qBAAqB,EAAA;eAArBA;;IAQAC,8BAA8B,EAAA;eAA9BA;;IAuCGC,kCAAkC,EAAA;eAAlCA;;IAPAC,2BAA2B,EAAA;eAA3BA;;IAnBAC,yBAAyB,EAAA;eAAzBA;;;AArBT,MAAMJ,wBAAwB;IACnCK,WAAW;IACXC,WAAW;IACXC,cAAc;AAChB;AAEA,MAAMC,gBAAgB,IAAIC,IAAIC,OAAOC,MAAM,CAACX;AAErC,MAAMC,iCAAiC;AAavC,SAASG,0BACdQ,KAAc;IAEd,IACE,OAAOA,UAAU,YACjBA,UAAU,QACV,CAAE,CAAA,YAAYA,KAAI,KAClB,OAAOA,MAAMC,MAAM,KAAK,UACxB;QACA,OAAO;IACT;IACA,MAAM,CAACC,QAAQC,WAAW,GAAGH,MAAMC,MAAM,CAACG,KAAK,CAAC;IAEhD,OACEF,WAAWb,kCACXO,cAAcS,GAAG,CAACC,OAAOH;AAE7B;AAEO,SAASZ,4BACdS,KAA8B;IAE9B,MAAMG,aAAaH,MAAMC,MAAM,CAACG,KAAK,CAAC,IAAI,CAAC,EAAE;IAC7C,OAAOE,OAAOH;AAChB;AAEO,SAASb,mCACdiB,MAAc;IAEd,OAAQA;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE;IACJ;AACF", "ignoreList": [0]}}, {"offset": {"line": 1053, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1058, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/New%20folder%20%288%29/arabic-ecommerce/node_modules/next/src/client/components/not-found.ts"], "sourcesContent": ["import {\n  HTTP_ERROR_FALLBACK_ERROR_CODE,\n  type HTTPAccessFallbackError,\n} from './http-access-fallback/http-access-fallback'\n\n/**\n * This function allows you to render the [not-found.js file](https://nextjs.org/docs/app/api-reference/file-conventions/not-found)\n * within a route segment as well as inject a tag.\n *\n * `notFound()` can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n * - In a Server Component, this will insert a `<meta name=\"robots\" content=\"noindex\" />` meta tag and set the status code to 404.\n * - In a Route Handler or Server Action, it will serve a 404 to the caller.\n *\n * Read more: [Next.js Docs: `notFound`](https://nextjs.org/docs/app/api-reference/functions/not-found)\n */\n\nconst DIGEST = `${HTTP_ERROR_FALLBACK_ERROR_CODE};404`\n\nexport function notFound(): never {\n  // eslint-disable-next-line no-throw-literal\n  const error = new Error(DIGEST) as HTTPAccessFallbackError\n  ;(error as HTTPAccessFallbackError).digest = DIGEST\n\n  throw error\n}\n"], "names": ["notFound", "DIGEST", "HTTP_ERROR_FALLBACK_ERROR_CODE", "error", "Error", "digest"], "mappings": ";;;;+BAsBgBA,YAAAA;;;eAAAA;;;oCAnBT;AAEP;;;;;;;;;;;;;CAaC,GAED,MAAMC,SAAU,KAAEC,oBAAAA,8BAA8B,GAAC;AAE1C,SAASF;IACd,4CAA4C;IAC5C,MAAMG,QAAQ,IAAIC,MAAMH;IACtBE,MAAkCE,MAAM,GAAGJ;IAE7C,MAAME;AACR", "ignoreList": [0]}}, {"offset": {"line": 1096, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1101, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/New%20folder%20%288%29/arabic-ecommerce/node_modules/next/src/client/components/forbidden.ts"], "sourcesContent": ["import {\n  HTTP_ERROR_FALLBACK_ERROR_CODE,\n  type HTTPAccessFallbackError,\n} from './http-access-fallback/http-access-fallback'\n\n// TODO: Add `forbidden` docs\n/**\n * @experimental\n * This function allows you to render the [forbidden.js file](https://nextjs.org/docs/app/api-reference/file-conventions/forbidden)\n * within a route segment as well as inject a tag.\n *\n * `forbidden()` can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n * Read more: [Next.js Docs: `forbidden`](https://nextjs.org/docs/app/api-reference/functions/forbidden)\n */\n\nconst DIGEST = `${HTTP_ERROR_FALLBACK_ERROR_CODE};403`\n\nexport function forbidden(): never {\n  if (!process.env.__NEXT_EXPERIMENTAL_AUTH_INTERRUPTS) {\n    throw new Error(\n      `\\`forbidden()\\` is experimental and only allowed to be enabled when \\`experimental.authInterrupts\\` is enabled.`\n    )\n  }\n\n  // eslint-disable-next-line no-throw-literal\n  const error = new Error(DIGEST) as HTTPAccessFallbackError\n  ;(error as HTTPAccessFallbackError).digest = DIGEST\n  throw error\n}\n"], "names": ["forbidden", "DIGEST", "HTTP_ERROR_FALLBACK_ERROR_CODE", "process", "env", "__NEXT_EXPERIMENTAL_AUTH_INTERRUPTS", "Error", "error", "digest"], "mappings": ";;;;+BAqBgBA,aAAAA;;;eAAAA;;;oCAlBT;AAEP,6BAA6B;AAC7B;;;;;;;;;;;CAWC,GAED,MAAMC,SAAU,KAAEC,oBAAAA,8BAA8B,GAAC;AAE1C,SAASF;IACd,IAAI,CAACG,QAAQC,GAAG,CAACC,uBAAqC,YAAF;QAClD,MAAM,IAAIC,MACP;IAEL;IAEA,4CAA4C;IAC5C,MAAMC,QAAQ,IAAID,MAAML;IACtBM,MAAkCC,MAAM,GAAGP;IAC7C,MAAMM;AACR", "ignoreList": [0]}}, {"offset": {"line": 1141, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1146, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/New%20folder%20%288%29/arabic-ecommerce/node_modules/next/src/client/components/unauthorized.ts"], "sourcesContent": ["import {\n  HTTP_ERROR_FALLBACK_ERROR_CODE,\n  type HTTPAccessFallbackError,\n} from './http-access-fallback/http-access-fallback'\n\n// TODO: Add `unauthorized` docs\n/**\n * @experimental\n * This function allows you to render the [unauthorized.js file](https://nextjs.org/docs/app/api-reference/file-conventions/unauthorized)\n * within a route segment as well as inject a tag.\n *\n * `unauthorized()` can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n *\n * Read more: [Next.js Docs: `unauthorized`](https://nextjs.org/docs/app/api-reference/functions/unauthorized)\n */\n\nconst DIGEST = `${HTTP_ERROR_FALLBACK_ERROR_CODE};401`\n\nexport function unauthorized(): never {\n  if (!process.env.__NEXT_EXPERIMENTAL_AUTH_INTERRUPTS) {\n    throw new Error(\n      `\\`unauthorized()\\` is experimental and only allowed to be used when \\`experimental.authInterrupts\\` is enabled.`\n    )\n  }\n\n  // eslint-disable-next-line no-throw-literal\n  const error = new Error(DIGEST) as HTTPAccessFallbackError\n  ;(error as HTTPAccessFallbackError).digest = DIGEST\n  throw error\n}\n"], "names": ["unauthorized", "DIGEST", "HTTP_ERROR_FALLBACK_ERROR_CODE", "process", "env", "__NEXT_EXPERIMENTAL_AUTH_INTERRUPTS", "Error", "error", "digest"], "mappings": ";;;;+BAsBgBA,gBAAAA;;;eAAAA;;;oCAnBT;AAEP,gCAAgC;AAChC;;;;;;;;;;;;CAYC,GAED,MAAMC,SAAU,KAAEC,oBAAAA,8BAA8B,GAAC;AAE1C,SAASF;IACd,IAAI,CAACG,QAAQC,GAAG,CAACC,uBAAqC,YAAF;QAClD,MAAM,IAAIC,MACP;IAEL;IAEA,4CAA4C;IAC5C,MAAMC,QAAQ,IAAID,MAAML;IACtBM,MAAkCC,MAAM,GAAGP;IAC7C,MAAMM;AACR", "ignoreList": [0]}}, {"offset": {"line": 1187, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1192, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/New%20folder%20%288%29/arabic-ecommerce/node_modules/next/src/client/components/hooks-server-context.ts"], "sourcesContent": ["const DYNAMIC_ERROR_CODE = 'DYNAMIC_SERVER_USAGE'\n\nexport class DynamicServerError extends Error {\n  digest: typeof DYNAMIC_ERROR_CODE = DYNAMIC_ERROR_CODE\n\n  constructor(public readonly description: string) {\n    super(`Dynamic server usage: ${description}`)\n  }\n}\n\nexport function isDynamicServerError(err: unknown): err is DynamicServerError {\n  if (\n    typeof err !== 'object' ||\n    err === null ||\n    !('digest' in err) ||\n    typeof err.digest !== 'string'\n  ) {\n    return false\n  }\n\n  return err.digest === DYNAMIC_ERROR_CODE\n}\n"], "names": ["DynamicServerError", "isDynamicServerError", "DYNAMIC_ERROR_CODE", "Error", "constructor", "description", "digest", "err"], "mappings": ";;;;;;;;;;;;;;;IAEaA,kBAAkB,EAAA;eAAlBA;;IAQGC,oBAAoB,EAAA;eAApBA;;;AAVhB,MAAMC,qBAAqB;AAEpB,MAAMF,2BAA2BG;IAGtCC,YAA4BC,WAAmB,CAAE;QAC/C,KAAK,CAAE,2BAAwBA,cAAAA,IAAAA,CADLA,WAAAA,GAAAA,aAAAA,IAAAA,CAF5BC,MAAAA,GAAoCJ;IAIpC;AACF;AAEO,SAASD,qBAAqBM,GAAY;IAC/C,IACE,OAAOA,QAAQ,YACfA,QAAQ,QACR,CAAE,CAAA,YAAYA,GAAE,KAChB,OAAOA,IAAID,MAAM,KAAK,UACtB;QACA,OAAO;IACT;IAEA,OAAOC,IAAID,MAAM,KAAKJ;AACxB", "ignoreList": [0]}}, {"offset": {"line": 1233, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1238, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/New%20folder%20%288%29/arabic-ecommerce/node_modules/next/src/shared/lib/lazy-dynamic/bailout-to-csr.ts"], "sourcesContent": ["// This has to be a shared module which is shared between client component error boundary and dynamic component\nconst BAILOUT_TO_CSR = 'BAILOUT_TO_CLIENT_SIDE_RENDERING'\n\n/** An error that should be thrown when we want to bail out to client-side rendering. */\nexport class BailoutToCSRError extends Error {\n  public readonly digest = BAILOUT_TO_CSR\n\n  constructor(public readonly reason: string) {\n    super(`Bail out to client-side rendering: ${reason}`)\n  }\n}\n\n/** Checks if a passed argument is an error that is thrown if we want to bail out to client-side rendering. */\nexport function isBailoutToCSRError(err: unknown): err is BailoutToCSRError {\n  if (typeof err !== 'object' || err === null || !('digest' in err)) {\n    return false\n  }\n\n  return err.digest === BAILOUT_TO_CSR\n}\n"], "names": ["BailoutToCSRError", "isBailoutToCSRError", "BAILOUT_TO_CSR", "Error", "constructor", "reason", "digest", "err"], "mappings": "AAAA,+GAA+G;;;;;;;;;;;;;;;;IAIlGA,iBAAiB,EAAA;eAAjBA;;IASGC,mBAAmB,EAAA;eAAnBA;;;AAZhB,MAAMC,iBAAiB;AAGhB,MAAMF,0BAA0BG;IAGrCC,YAA4BC,MAAc,CAAE;QAC1C,KAAK,CAAE,wCAAqCA,SAAAA,IAAAA,CADlBA,MAAAA,GAAAA,QAAAA,IAAAA,CAFZC,MAAAA,GAASJ;IAIzB;AACF;AAGO,SAASD,oBAAoBM,GAAY;IAC9C,IAAI,OAAOA,QAAQ,YAAYA,QAAQ,QAAQ,CAAE,CAAA,YAAYA,GAAE,GAAI;QACjE,OAAO;IACT;IAEA,OAAOA,IAAID,MAAM,KAAKJ;AACxB", "ignoreList": [0]}}, {"offset": {"line": 1273, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1278, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/New%20folder%20%288%29/arabic-ecommerce/node_modules/next/src/client/components/is-next-router-error.ts"], "sourcesContent": ["import {\n  isHTTPAccessFallbackError,\n  type HTTPAccessFallbackError,\n} from './http-access-fallback/http-access-fallback'\nimport { isRedirectError, type RedirectError } from './redirect-error'\n\n/**\n * Returns true if the error is a navigation signal error. These errors are\n * thrown by user code to perform navigation operations and interrupt the React\n * render.\n */\nexport function isNextRouterError(\n  error: unknown\n): error is RedirectError | HTTPAccessFallbackError {\n  return isRedirectError(error) || isHTTPAccessFallbackError(error)\n}\n"], "names": ["isNextRouterError", "error", "isRedirectError", "isHTTPAccessFallbackError"], "mappings": ";;;;+BAWgBA,qBAAAA;;;eAAAA;;;oCART;+BAC6C;AAO7C,SAASA,kBACdC,KAAc;IAEd,OAAOC,CAAAA,GAAAA,eAAAA,eAAe,EAACD,UAAUE,CAAAA,GAAAA,oBAAAA,yBAAyB,EAACF;AAC7D", "ignoreList": [0]}}, {"offset": {"line": 1300, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1305, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/New%20folder%20%288%29/arabic-ecommerce/node_modules/next/src/client/components/static-generation-bailout.ts"], "sourcesContent": ["const NEXT_STATIC_GEN_BAILOUT = 'NEXT_STATIC_GEN_BAILOUT'\n\nexport class StaticGenBailoutError extends Error {\n  public readonly code = NEXT_STATIC_GEN_BAILOUT\n}\n\nexport function isStaticGenBailoutError(\n  error: unknown\n): error is StaticGenBailoutError {\n  if (typeof error !== 'object' || error === null || !('code' in error)) {\n    return false\n  }\n\n  return error.code === NEXT_STATIC_GEN_BAILOUT\n}\n"], "names": ["StaticGenBailoutError", "isStaticGenBailoutError", "NEXT_STATIC_GEN_BAILOUT", "Error", "code", "error"], "mappings": ";;;;;;;;;;;;;;;IAEaA,qBAAqB,EAAA;eAArBA;;IAIGC,uBAAuB,EAAA;eAAvBA;;;AANhB,MAAMC,0BAA0B;AAEzB,MAAMF,8BAA8BG;;QAApC,KAAA,IAAA,OAAA,IAAA,CACWC,IAAAA,GAAOF;;AACzB;AAEO,SAASD,wBACdI,KAAc;IAEd,IAAI,OAAOA,UAAU,YAAYA,UAAU,QAAQ,CAAE,CAAA,UAAUA,KAAI,GAAI;QACrE,OAAO;IACT;IAEA,OAAOA,MAAMD,IAAI,KAAKF;AACxB", "ignoreList": [0]}}, {"offset": {"line": 1346, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1351, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/New%20folder%20%288%29/arabic-ecommerce/node_modules/next/src/server/dynamic-rendering-utils.ts"], "sourcesContent": ["/**\n * This function constructs a promise that will never resolve. This is primarily\n * useful for dynamicIO where we use promise resolution timing to determine which\n * parts of a render can be included in a prerender.\n *\n * @internal\n */\nexport function makeHangingPromise<T>(\n  signal: AbortSignal,\n  expression: string\n): Promise<T> {\n  const hangingPromise = new Promise<T>((_, reject) => {\n    signal.addEventListener(\n      'abort',\n      () => {\n        reject(\n          new Error(\n            `During prerendering, ${expression} rejects when the prerender is complete. Typically these errors are handled by React but if you move ${expression} to a different context by using \\`setTimeout\\`, \\`after\\`, or similar functions you may observe this error and you should handle it in that context.`\n          )\n        )\n      },\n      { once: true }\n    )\n  })\n  // We are fine if no one actually awaits this promise. We shouldn't consider this an unhandled rejection so\n  // we attach a noop catch handler here to suppress this warning. If you actually await somewhere or construct\n  // your own promise out of it you'll need to ensure you handle the error when it rejects.\n  hangingPromise.catch(ignoreReject)\n  return hangingPromise\n}\n\nfunction ignoreReject() {}\n"], "names": ["makeHangingPromise", "signal", "expression", "hanging<PERSON>romise", "Promise", "_", "reject", "addEventListener", "Error", "once", "catch", "ignoreReject"], "mappings": "AAAA;;;;;;CAMC,GAAA;;;;+BACe<PERSON>,sBAAAA;;;eAAAA;;;AAAT,SAASA,mBACdC,MAAmB,EACnBC,UAAkB;IAElB,MAAMC,iBAAiB,IAAIC,QAAW,CAACC,GAAGC;QACxCL,OAAOM,gBAAgB,CACrB,SACA;YACED,OACE,IAAIE,MACF,CAAC,qBAAqB,EAAEN,WAAW,qGAAqG,EAAEA,WAAW,qJAAqJ,CAAC;QAGjT,GACA;YAAEO,MAAM;QAAK;IAEjB;IACA,2GAA2G;IAC3G,6GAA6G;IAC7G,yFAAyF;IACzFN,eAAeO,KAAK,CAACC;IACrB,OAAOR;AACT;AAEA,SAASQ,gBAAgB", "ignoreList": [0]}}, {"offset": {"line": 1382, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1387, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/New%20folder%20%288%29/arabic-ecommerce/node_modules/next/src/lib/metadata/metadata-constants.tsx"], "sourcesContent": ["export const METADATA_BOUNDARY_NAME = '__next_metadata_boundary__'\nexport const VIEWPORT_BOUNDARY_NAME = '__next_viewport_boundary__'\nexport const OUTLET_BOUNDARY_NAME = '__next_outlet_boundary__'\n"], "names": ["METADATA_BOUNDARY_NAME", "OUTLET_BOUNDARY_NAME", "VIEWPORT_BOUNDARY_NAME"], "mappings": ";;;;;;;;;;;;;;;;IAAaA,sBAAsB,EAAA;eAAtBA;;IAEAC,oBAAoB,EAAA;eAApBA;;IADAC,sBAAsB,EAAA;eAAtBA;;;AADN,MAAMF,yBAAyB;AAC/B,MAAME,yBAAyB;AAC/B,MAAMD,uBAAuB", "ignoreList": [0]}}, {"offset": {"line": 1416, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1421, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/New%20folder%20%288%29/arabic-ecommerce/node_modules/next/src/server/app-render/dynamic-rendering.ts"], "sourcesContent": ["/**\n * The functions provided by this module are used to communicate certain properties\n * about the currently running code so that Next.js can make decisions on how to handle\n * the current execution in different rendering modes such as pre-rendering, resuming, and SSR.\n *\n * Today Next.js treats all code as potentially static. Certain APIs may only make sense when dynamically rendering.\n * Traditionally this meant deopting the entire render to dynamic however with PPR we can now deopt parts\n * of a React tree as dynamic while still keeping other parts static. There are really two different kinds of\n * Dynamic indications.\n *\n * The first is simply an intention to be dynamic. unstable_noStore is an example of this where\n * the currently executing code simply declares that the current scope is dynamic but if you use it\n * inside unstable_cache it can still be cached. This type of indication can be removed if we ever\n * make the default dynamic to begin with because the only way you would ever be static is inside\n * a cache scope which this indication does not affect.\n *\n * The second is an indication that a dynamic data source was read. This is a stronger form of dynamic\n * because it means that it is inappropriate to cache this at all. using a dynamic data source inside\n * unstable_cache should error. If you want to use some dynamic data inside unstable_cache you should\n * read that data outside the cache and pass it in as an argument to the cached function.\n */\n\nimport type { WorkStore } from '../app-render/work-async-storage.external'\nimport type {\n  WorkUnitStore,\n  RequestStore,\n  PrerenderStoreLegacy,\n  PrerenderStoreModern,\n} from '../app-render/work-unit-async-storage.external'\n\n// Once postpone is in stable we should switch to importing the postpone export directly\nimport React from 'react'\n\nimport { DynamicServerError } from '../../client/components/hooks-server-context'\nimport { StaticGenBailoutError } from '../../client/components/static-generation-bailout'\nimport { workUnitAsyncStorage } from './work-unit-async-storage.external'\nimport { workAsyncStorage } from '../app-render/work-async-storage.external'\nimport { makeHangingPromise } from '../dynamic-rendering-utils'\nimport {\n  METADATA_BOUNDARY_NAME,\n  VIEWPORT_BOUNDARY_NAME,\n  OUTLET_BOUNDARY_NAME,\n} from '../../lib/metadata/metadata-constants'\n\nconst hasPostpone = typeof React.unstable_postpone === 'function'\n\nexport type DynamicAccess = {\n  /**\n   * If debugging, this will contain the stack trace of where the dynamic access\n   * occurred. This is used to provide more information to the user about why\n   * their page is being rendered dynamically.\n   */\n  stack?: string\n\n  /**\n   * The expression that was accessed dynamically.\n   */\n  expression: string\n}\n\n// Stores dynamic reasons used during an RSC render.\nexport type DynamicTrackingState = {\n  /**\n   * When true, stack information will also be tracked during dynamic access.\n   */\n  readonly isDebugDynamicAccesses: boolean | undefined\n\n  /**\n   * The dynamic accesses that occurred during the render.\n   */\n  readonly dynamicAccesses: Array<DynamicAccess>\n\n  syncDynamicExpression: undefined | string\n  syncDynamicErrorWithStack: null | Error\n  // Dev only\n  syncDynamicLogged?: boolean\n}\n\n// Stores dynamic reasons used during an SSR render.\nexport type DynamicValidationState = {\n  hasSuspendedDynamic: boolean\n  hasDynamicMetadata: boolean\n  hasDynamicViewport: boolean\n  hasSyncDynamicErrors: boolean\n  dynamicErrors: Array<Error>\n}\n\nexport function createDynamicTrackingState(\n  isDebugDynamicAccesses: boolean | undefined\n): DynamicTrackingState {\n  return {\n    isDebugDynamicAccesses,\n    dynamicAccesses: [],\n    syncDynamicExpression: undefined,\n    syncDynamicErrorWithStack: null,\n  }\n}\n\nexport function createDynamicValidationState(): DynamicValidationState {\n  return {\n    hasSuspendedDynamic: false,\n    hasDynamicMetadata: false,\n    hasDynamicViewport: false,\n    hasSyncDynamicErrors: false,\n    dynamicErrors: [],\n  }\n}\n\nexport function getFirstDynamicReason(\n  trackingState: DynamicTrackingState\n): undefined | string {\n  return trackingState.dynamicAccesses[0]?.expression\n}\n\n/**\n * This function communicates that the current scope should be treated as dynamic.\n *\n * In most cases this function is a no-op but if called during\n * a PPR prerender it will postpone the current sub-tree and calling\n * it during a normal prerender will cause the entire prerender to abort\n */\nexport function markCurrentScopeAsDynamic(\n  store: WorkStore,\n  workUnitStore: undefined | Exclude<WorkUnitStore, PrerenderStoreModern>,\n  expression: string\n): void {\n  if (workUnitStore) {\n    if (\n      workUnitStore.type === 'cache' ||\n      workUnitStore.type === 'unstable-cache'\n    ) {\n      // inside cache scopes marking a scope as dynamic has no effect because the outer cache scope\n      // creates a cache boundary. This is subtly different from reading a dynamic data source which is\n      // forbidden inside a cache scope.\n      return\n    }\n  }\n\n  // If we're forcing dynamic rendering or we're forcing static rendering, we\n  // don't need to do anything here because the entire page is already dynamic\n  // or it's static and it should not throw or postpone here.\n  if (store.forceDynamic || store.forceStatic) return\n\n  if (store.dynamicShouldError) {\n    throw new StaticGenBailoutError(\n      `Route ${store.route} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`\n    )\n  }\n\n  if (workUnitStore) {\n    if (workUnitStore.type === 'prerender-ppr') {\n      postponeWithTracking(\n        store.route,\n        expression,\n        workUnitStore.dynamicTracking\n      )\n    } else if (workUnitStore.type === 'prerender-legacy') {\n      workUnitStore.revalidate = 0\n\n      // We aren't prerendering but we are generating a static page. We need to bail out of static generation\n      const err = new DynamicServerError(\n        `Route ${store.route} couldn't be rendered statically because it used ${expression}. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`\n      )\n      store.dynamicUsageDescription = expression\n      store.dynamicUsageStack = err.stack\n\n      throw err\n    } else if (\n      process.env.NODE_ENV === 'development' &&\n      workUnitStore &&\n      workUnitStore.type === 'request'\n    ) {\n      workUnitStore.usedDynamic = true\n    }\n  }\n}\n\n/**\n * This function communicates that some dynamic path parameter was read. This\n * differs from the more general `trackDynamicDataAccessed` in that it is will\n * not error when `dynamic = \"error\"` is set.\n *\n * @param store The static generation store\n * @param expression The expression that was accessed dynamically\n */\nexport function trackFallbackParamAccessed(\n  store: WorkStore,\n  expression: string\n): void {\n  const prerenderStore = workUnitAsyncStorage.getStore()\n  if (!prerenderStore || prerenderStore.type !== 'prerender-ppr') return\n\n  postponeWithTracking(store.route, expression, prerenderStore.dynamicTracking)\n}\n\n/**\n * This function is meant to be used when prerendering without dynamicIO or PPR.\n * When called during a build it will cause Next.js to consider the route as dynamic.\n *\n * @internal\n */\nexport function throwToInterruptStaticGeneration(\n  expression: string,\n  store: WorkStore,\n  prerenderStore: PrerenderStoreLegacy\n): never {\n  // We aren't prerendering but we are generating a static page. We need to bail out of static generation\n  const err = new DynamicServerError(\n    `Route ${store.route} couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`\n  )\n\n  prerenderStore.revalidate = 0\n\n  store.dynamicUsageDescription = expression\n  store.dynamicUsageStack = err.stack\n\n  throw err\n}\n\n/**\n * This function should be used to track whether something dynamic happened even when\n * we are in a dynamic render. This is useful for Dev where all renders are dynamic but\n * we still track whether dynamic APIs were accessed for helpful messaging\n *\n * @internal\n */\nexport function trackDynamicDataInDynamicRender(\n  _store: WorkStore,\n  workUnitStore: void | WorkUnitStore\n) {\n  if (workUnitStore) {\n    if (\n      workUnitStore.type === 'cache' ||\n      workUnitStore.type === 'unstable-cache'\n    ) {\n      // inside cache scopes marking a scope as dynamic has no effect because the outer cache scope\n      // creates a cache boundary. This is subtly different from reading a dynamic data source which is\n      // forbidden inside a cache scope.\n      return\n    }\n    if (\n      workUnitStore.type === 'prerender' ||\n      workUnitStore.type === 'prerender-legacy'\n    ) {\n      workUnitStore.revalidate = 0\n    }\n    if (\n      process.env.NODE_ENV === 'development' &&\n      workUnitStore.type === 'request'\n    ) {\n      workUnitStore.usedDynamic = true\n    }\n  }\n}\n\n// Despite it's name we don't actually abort unless we have a controller to call abort on\n// There are times when we let a prerender run long to discover caches where we want the semantics\n// of tracking dynamic access without terminating the prerender early\nfunction abortOnSynchronousDynamicDataAccess(\n  route: string,\n  expression: string,\n  prerenderStore: PrerenderStoreModern\n): void {\n  const reason = `Route ${route} needs to bail out of prerendering at this point because it used ${expression}.`\n\n  const error = createPrerenderInterruptedError(reason)\n\n  prerenderStore.controller.abort(error)\n\n  const dynamicTracking = prerenderStore.dynamicTracking\n  if (dynamicTracking) {\n    dynamicTracking.dynamicAccesses.push({\n      // When we aren't debugging, we don't need to create another error for the\n      // stack trace.\n      stack: dynamicTracking.isDebugDynamicAccesses\n        ? new Error().stack\n        : undefined,\n      expression,\n    })\n  }\n}\n\nexport function abortOnSynchronousPlatformIOAccess(\n  route: string,\n  expression: string,\n  errorWithStack: Error,\n  prerenderStore: PrerenderStoreModern\n): void {\n  const dynamicTracking = prerenderStore.dynamicTracking\n  if (dynamicTracking) {\n    if (dynamicTracking.syncDynamicErrorWithStack === null) {\n      dynamicTracking.syncDynamicExpression = expression\n      dynamicTracking.syncDynamicErrorWithStack = errorWithStack\n    }\n  }\n  return abortOnSynchronousDynamicDataAccess(route, expression, prerenderStore)\n}\n\nexport function trackSynchronousPlatformIOAccessInDev(\n  requestStore: RequestStore\n): void {\n  // We don't actually have a controller to abort but we do the semantic equivalent by\n  // advancing the request store out of prerender mode\n  requestStore.prerenderPhase = false\n}\n\n/**\n * use this function when prerendering with dynamicIO. If we are doing a\n * prospective prerender we don't actually abort because we want to discover\n * all caches for the shell. If this is the actual prerender we do abort.\n *\n * This function accepts a prerenderStore but the caller should ensure we're\n * actually running in dynamicIO mode.\n *\n * @internal\n */\nexport function abortAndThrowOnSynchronousRequestDataAccess(\n  route: string,\n  expression: string,\n  errorWithStack: Error,\n  prerenderStore: PrerenderStoreModern\n): never {\n  const dynamicTracking = prerenderStore.dynamicTracking\n  if (dynamicTracking) {\n    if (dynamicTracking.syncDynamicErrorWithStack === null) {\n      dynamicTracking.syncDynamicExpression = expression\n      dynamicTracking.syncDynamicErrorWithStack = errorWithStack\n      if (prerenderStore.validating === true) {\n        // We always log Request Access in dev at the point of calling the function\n        // So we mark the dynamic validation as not requiring it to be printed\n        dynamicTracking.syncDynamicLogged = true\n      }\n    }\n  }\n  abortOnSynchronousDynamicDataAccess(route, expression, prerenderStore)\n  throw createPrerenderInterruptedError(\n    `Route ${route} needs to bail out of prerendering at this point because it used ${expression}.`\n  )\n}\n\n// For now these implementations are the same so we just reexport\nexport const trackSynchronousRequestDataAccessInDev =\n  trackSynchronousPlatformIOAccessInDev\n\n/**\n * This component will call `React.postpone` that throws the postponed error.\n */\ntype PostponeProps = {\n  reason: string\n  route: string\n}\nexport function Postpone({ reason, route }: PostponeProps): never {\n  const prerenderStore = workUnitAsyncStorage.getStore()\n  const dynamicTracking =\n    prerenderStore && prerenderStore.type === 'prerender-ppr'\n      ? prerenderStore.dynamicTracking\n      : null\n  postponeWithTracking(route, reason, dynamicTracking)\n}\n\nexport function postponeWithTracking(\n  route: string,\n  expression: string,\n  dynamicTracking: null | DynamicTrackingState\n): never {\n  assertPostpone()\n  if (dynamicTracking) {\n    dynamicTracking.dynamicAccesses.push({\n      // When we aren't debugging, we don't need to create another error for the\n      // stack trace.\n      stack: dynamicTracking.isDebugDynamicAccesses\n        ? new Error().stack\n        : undefined,\n      expression,\n    })\n  }\n\n  React.unstable_postpone(createPostponeReason(route, expression))\n}\n\nfunction createPostponeReason(route: string, expression: string) {\n  return (\n    `Route ${route} needs to bail out of prerendering at this point because it used ${expression}. ` +\n    `React throws this special object to indicate where. It should not be caught by ` +\n    `your own try/catch. Learn more: https://nextjs.org/docs/messages/ppr-caught-error`\n  )\n}\n\nexport function isDynamicPostpone(err: unknown) {\n  if (\n    typeof err === 'object' &&\n    err !== null &&\n    typeof (err as any).message === 'string'\n  ) {\n    return isDynamicPostponeReason((err as any).message)\n  }\n  return false\n}\n\nfunction isDynamicPostponeReason(reason: string) {\n  return (\n    reason.includes(\n      'needs to bail out of prerendering at this point because it used'\n    ) &&\n    reason.includes(\n      'Learn more: https://nextjs.org/docs/messages/ppr-caught-error'\n    )\n  )\n}\n\nif (isDynamicPostponeReason(createPostponeReason('%%%', '^^^')) === false) {\n  throw new Error(\n    'Invariant: isDynamicPostpone misidentified a postpone reason. This is a bug in Next.js'\n  )\n}\n\nconst NEXT_PRERENDER_INTERRUPTED = 'NEXT_PRERENDER_INTERRUPTED'\n\nfunction createPrerenderInterruptedError(message: string): Error {\n  const error = new Error(message)\n  ;(error as any).digest = NEXT_PRERENDER_INTERRUPTED\n  return error\n}\n\ntype DigestError = Error & {\n  digest: string\n}\n\nexport function isPrerenderInterruptedError(\n  error: unknown\n): error is DigestError {\n  return (\n    typeof error === 'object' &&\n    error !== null &&\n    (error as any).digest === NEXT_PRERENDER_INTERRUPTED &&\n    'name' in error &&\n    'message' in error &&\n    error instanceof Error\n  )\n}\n\nexport function accessedDynamicData(\n  dynamicAccesses: Array<DynamicAccess>\n): boolean {\n  return dynamicAccesses.length > 0\n}\n\nexport function consumeDynamicAccess(\n  serverDynamic: DynamicTrackingState,\n  clientDynamic: DynamicTrackingState\n): DynamicTrackingState['dynamicAccesses'] {\n  // We mutate because we only call this once we are no longer writing\n  // to the dynamicTrackingState and it's more efficient than creating a new\n  // array.\n  serverDynamic.dynamicAccesses.push(...clientDynamic.dynamicAccesses)\n  return serverDynamic.dynamicAccesses\n}\n\nexport function formatDynamicAPIAccesses(\n  dynamicAccesses: Array<DynamicAccess>\n): string[] {\n  return dynamicAccesses\n    .filter(\n      (access): access is Required<DynamicAccess> =>\n        typeof access.stack === 'string' && access.stack.length > 0\n    )\n    .map(({ expression, stack }) => {\n      stack = stack\n        .split('\\n')\n        // Remove the \"Error: \" prefix from the first line of the stack trace as\n        // well as the first 4 lines of the stack trace which is the distance\n        // from the user code and the `new Error().stack` call.\n        .slice(4)\n        .filter((line) => {\n          // Exclude Next.js internals from the stack trace.\n          if (line.includes('node_modules/next/')) {\n            return false\n          }\n\n          // Exclude anonymous functions from the stack trace.\n          if (line.includes(' (<anonymous>)')) {\n            return false\n          }\n\n          // Exclude Node.js internals from the stack trace.\n          if (line.includes(' (node:')) {\n            return false\n          }\n\n          return true\n        })\n        .join('\\n')\n      return `Dynamic API Usage Debug - ${expression}:\\n${stack}`\n    })\n}\n\nfunction assertPostpone() {\n  if (!hasPostpone) {\n    throw new Error(\n      `Invariant: React.unstable_postpone is not defined. This suggests the wrong version of React was loaded. This is a bug in Next.js`\n    )\n  }\n}\n\n/**\n * This is a bit of a hack to allow us to abort a render using a Postpone instance instead of an Error which changes React's\n * abort semantics slightly.\n */\nexport function createPostponedAbortSignal(reason: string): AbortSignal {\n  assertPostpone()\n  const controller = new AbortController()\n  // We get our hands on a postpone instance by calling postpone and catching the throw\n  try {\n    React.unstable_postpone(reason)\n  } catch (x: unknown) {\n    controller.abort(x)\n  }\n  return controller.signal\n}\n\nexport function annotateDynamicAccess(\n  expression: string,\n  prerenderStore: PrerenderStoreModern\n) {\n  const dynamicTracking = prerenderStore.dynamicTracking\n  if (dynamicTracking) {\n    dynamicTracking.dynamicAccesses.push({\n      stack: dynamicTracking.isDebugDynamicAccesses\n        ? new Error().stack\n        : undefined,\n      expression,\n    })\n  }\n}\n\nexport function useDynamicRouteParams(expression: string) {\n  if (typeof window === 'undefined') {\n    const workStore = workAsyncStorage.getStore()\n\n    if (\n      workStore &&\n      workStore.isStaticGeneration &&\n      workStore.fallbackRouteParams &&\n      workStore.fallbackRouteParams.size > 0\n    ) {\n      // There are fallback route params, we should track these as dynamic\n      // accesses.\n      const workUnitStore = workUnitAsyncStorage.getStore()\n      if (workUnitStore) {\n        // We're prerendering with dynamicIO or PPR or both\n        if (workUnitStore.type === 'prerender') {\n          // We are in a prerender with dynamicIO semantics\n          // We are going to hang here and never resolve. This will cause the currently\n          // rendering component to effectively be a dynamic hole\n          React.use(makeHangingPromise(workUnitStore.renderSignal, expression))\n        } else if (workUnitStore.type === 'prerender-ppr') {\n          // We're prerendering with PPR\n          postponeWithTracking(\n            workStore.route,\n            expression,\n            workUnitStore.dynamicTracking\n          )\n        } else if (workUnitStore.type === 'prerender-legacy') {\n          throwToInterruptStaticGeneration(expression, workStore, workUnitStore)\n        }\n      }\n    }\n  }\n}\n\nconst hasSuspenseRegex = /\\n\\s+at Suspense \\(<anonymous>\\)/\nconst hasMetadataRegex = new RegExp(\n  `\\\\n\\\\s+at ${METADATA_BOUNDARY_NAME}[\\\\n\\\\s]`\n)\nconst hasViewportRegex = new RegExp(\n  `\\\\n\\\\s+at ${VIEWPORT_BOUNDARY_NAME}[\\\\n\\\\s]`\n)\nconst hasOutletRegex = new RegExp(`\\\\n\\\\s+at ${OUTLET_BOUNDARY_NAME}[\\\\n\\\\s]`)\n\nexport function trackAllowedDynamicAccess(\n  route: string,\n  componentStack: string,\n  dynamicValidation: DynamicValidationState,\n  serverDynamic: DynamicTrackingState,\n  clientDynamic: DynamicTrackingState\n) {\n  if (hasOutletRegex.test(componentStack)) {\n    // We don't need to track that this is dynamic. It is only so when something else is also dynamic.\n    return\n  } else if (hasMetadataRegex.test(componentStack)) {\n    dynamicValidation.hasDynamicMetadata = true\n    return\n  } else if (hasViewportRegex.test(componentStack)) {\n    dynamicValidation.hasDynamicViewport = true\n    return\n  } else if (hasSuspenseRegex.test(componentStack)) {\n    dynamicValidation.hasSuspendedDynamic = true\n    return\n  } else if (\n    serverDynamic.syncDynamicErrorWithStack ||\n    clientDynamic.syncDynamicErrorWithStack\n  ) {\n    dynamicValidation.hasSyncDynamicErrors = true\n    return\n  } else {\n    const message = `Route \"${route}\": A component accessed data, headers, params, searchParams, or a short-lived cache without a Suspense boundary nor a \"use cache\" above it. We don't have the exact line number added to error messages yet but you can see which component in the stack below. See more info: https://nextjs.org/docs/messages/next-prerender-missing-suspense`\n    const error = createErrorWithComponentStack(message, componentStack)\n    dynamicValidation.dynamicErrors.push(error)\n    return\n  }\n}\n\nfunction createErrorWithComponentStack(\n  message: string,\n  componentStack: string\n) {\n  const error = new Error(message)\n  error.stack = 'Error: ' + message + componentStack\n  return error\n}\n\nexport function throwIfDisallowedDynamic(\n  route: string,\n  dynamicValidation: DynamicValidationState,\n  serverDynamic: DynamicTrackingState,\n  clientDynamic: DynamicTrackingState\n): void {\n  let syncError: null | Error\n  let syncExpression: undefined | string\n  let syncLogged: boolean\n  if (serverDynamic.syncDynamicErrorWithStack) {\n    syncError = serverDynamic.syncDynamicErrorWithStack\n    syncExpression = serverDynamic.syncDynamicExpression!\n    syncLogged = serverDynamic.syncDynamicLogged === true\n  } else if (clientDynamic.syncDynamicErrorWithStack) {\n    syncError = clientDynamic.syncDynamicErrorWithStack\n    syncExpression = clientDynamic.syncDynamicExpression!\n    syncLogged = clientDynamic.syncDynamicLogged === true\n  } else {\n    syncError = null\n    syncExpression = undefined\n    syncLogged = false\n  }\n\n  if (dynamicValidation.hasSyncDynamicErrors && syncError) {\n    if (!syncLogged) {\n      // In dev we already log errors about sync dynamic access. But during builds we need to ensure\n      // the offending sync error is logged before we exit the build\n      console.error(syncError)\n    }\n    // The actual error should have been logged when the sync access ocurred\n    throw new StaticGenBailoutError()\n  }\n\n  const dynamicErrors = dynamicValidation.dynamicErrors\n  if (dynamicErrors.length) {\n    for (let i = 0; i < dynamicErrors.length; i++) {\n      console.error(dynamicErrors[i])\n    }\n\n    throw new StaticGenBailoutError()\n  }\n\n  if (!dynamicValidation.hasSuspendedDynamic) {\n    if (dynamicValidation.hasDynamicMetadata) {\n      if (syncError) {\n        console.error(syncError)\n        throw new StaticGenBailoutError(\n          `Route \"${route}\" has a \\`generateMetadata\\` that could not finish rendering before ${syncExpression} was used. Follow the instructions in the error for this expression to resolve.`\n        )\n      }\n      throw new StaticGenBailoutError(\n        `Route \"${route}\" has a \\`generateMetadata\\` that depends on Request data (\\`cookies()\\`, etc...) or external data (\\`fetch(...)\\`, etc...) but the rest of the route was static or only used cached data (\\`\"use cache\"\\`). If you expected this route to be prerenderable update your \\`generateMetadata\\` to not use Request data and only use cached external data. Otherwise, add \\`await connection()\\` somewhere within this route to indicate explicitly it should not be prerendered.`\n      )\n    } else if (dynamicValidation.hasDynamicViewport) {\n      if (syncError) {\n        console.error(syncError)\n        throw new StaticGenBailoutError(\n          `Route \"${route}\" has a \\`generateViewport\\` that could not finish rendering before ${syncExpression} was used. Follow the instructions in the error for this expression to resolve.`\n        )\n      }\n      throw new StaticGenBailoutError(\n        `Route \"${route}\" has a \\`generateViewport\\` that depends on Request data (\\`cookies()\\`, etc...) or external data (\\`fetch(...)\\`, etc...) but the rest of the route was static or only used cached data (\\`\"use cache\"\\`). If you expected this route to be prerenderable update your \\`generateViewport\\` to not use Request data and only use cached external data. Otherwise, add \\`await connection()\\` somewhere within this route to indicate explicitly it should not be prerendered.`\n      )\n    }\n  }\n}\n"], "names": ["Postpone", "abortAndThrowOnSynchronousRequestDataAccess", "abortOnSynchronousPlatformIOAccess", "accessedDynamicData", "annotateDynamicAccess", "consumeDynamicAccess", "createDynamicTrackingState", "createDynamicValidationState", "createPostponedAbortSignal", "formatDynamicAPIAccesses", "getFirstDynamicReason", "isDynamicPostpone", "isPrerenderInterruptedError", "markCurrentScopeAsDynamic", "postponeWithTracking", "throwIfDisallowedDynamic", "throwToInterruptStaticGeneration", "trackAllowedDynamicAccess", "trackDynamicDataInDynamicRender", "trackFallbackParamAccessed", "trackSynchronousPlatformIOAccessInDev", "trackSynchronousRequestDataAccessInDev", "useDynamicRouteParams", "hasPostpone", "React", "unstable_postpone", "isDebugDynamicAccesses", "dynamicAccesses", "syncDynamicExpression", "undefined", "syncDynamicErrorWithStack", "hasSuspendedDynamic", "hasDynamicMetadata", "hasDynamicViewport", "hasSyncDynamicErrors", "dynamicErrors", "trackingState", "expression", "store", "workUnitStore", "type", "forceDynamic", "forceStatic", "dynamicShouldError", "StaticGenBailoutError", "route", "dynamicTracking", "revalidate", "err", "DynamicServerError", "dynamicUsageDescription", "dynamicUsageStack", "stack", "process", "env", "NODE_ENV", "usedDynamic", "prerenderStore", "workUnitAsyncStorage", "getStore", "_store", "abortOnSynchronousDynamicDataAccess", "reason", "error", "createPrerenderInterruptedError", "controller", "abort", "push", "Error", "errorWithStack", "requestStore", "prerenderPhase", "validating", "syncDynamicLogged", "assertPostpone", "createPostponeReason", "message", "isDynamicPostponeReason", "includes", "NEXT_PRERENDER_INTERRUPTED", "digest", "length", "serverDynamic", "clientDynamic", "filter", "access", "map", "split", "slice", "line", "join", "AbortController", "x", "signal", "window", "workStore", "workAsyncStorage", "isStaticGeneration", "fallbackRouteParams", "size", "use", "makeHangingPromise", "renderSignal", "hasSuspenseRegex", "hasMetadataRegex", "RegExp", "METADATA_BOUNDARY_NAME", "hasViewportRegex", "VIEWPORT_BOUNDARY_NAME", "hasOutletRegex", "OUTLET_BOUNDARY_NAME", "componentStack", "dynamicValidation", "test", "createErrorWithComponentStack", "syncError", "syncExpression", "syncLogged", "console", "i"], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;CAoBC,GAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IA2UeA,QAAQ,EAAA;eAARA;;IAnCAC,2CAA2C,EAAA;eAA3CA;;IAlCAC,kCAAkC,EAAA;eAAlCA;;IA+JAC,mBAAmB,EAAA;eAAnBA;;IA+EAC,qBAAqB,EAAA;eAArBA;;IAzEAC,oBAAoB,EAAA;eAApBA;;IAxWAC,0BAA0B,EAAA;eAA1BA;;IAWAC,4BAA4B,EAAA;eAA5BA;;IA0ZAC,0BAA0B,EAAA;eAA1BA;;IAlDAC,wBAAwB,EAAA;eAAxBA;;IA9VAC,qBAAqB,EAAA;eAArBA;;IAwRAC,iBAAiB,EAAA;eAAjBA;;IAwCAC,2BAA2B,EAAA;eAA3BA;;IAnTAC,yBAAyB,EAAA;eAAzBA;;IA+OAC,oBAAoB,EAAA;eAApBA;;IAqQAC,wBAAwB,EAAA;eAAxBA;;IApaAC,gCAAgC,EAAA;eAAhCA;;IA0XAC,yBAAyB,EAAA;eAAzBA;;IAjWAC,+BAA+B,EAAA;eAA/BA;;IAzCAC,0BAA0B,EAAA;eAA1BA;;IAiHAC,qCAAqC,EAAA;eAArCA;;IA2CHC,sCAAsC,EAAA;eAAtCA;;IAkMGC,qBAAqB,EAAA;eAArBA;;;8DAxfE;oCAEiB;yCACG;8CACD;0CACJ;uCACE;mCAK5B;;;;;;AAEP,MAAMC,cAAc,OAAOC,OAAAA,OAAK,CAACC,iBAAiB,KAAK;AA2ChD,SAASnB,2BACdoB,sBAA2C;IAE3C,OAAO;QACLA;QACAC,iBAAiB,EAAE;QACnBC,uBAAuBC;QACvBC,2BAA2B;IAC7B;AACF;AAEO,SAASvB;IACd,OAAO;QACLwB,qBAAqB;QACrBC,oBAAoB;QACpBC,oBAAoB;QACpBC,sBAAsB;QACtBC,eAAe,EAAE;IACnB;AACF;AAEO,SAASzB,sBACd0B,aAAmC;QAE5BA;IAAP,OAAA,CAAOA,kCAAAA,cAAcT,eAAe,CAAC,EAAE,KAAA,OAAA,KAAA,IAAhCS,gCAAkCC,UAAU;AACrD;AASO,SAASxB,0BACdyB,KAAgB,EAChBC,aAAuE,EACvEF,UAAkB;IAElB,IAAIE,eAAe;QACjB,IACEA,cAAcC,IAAI,KAAK,WACvBD,cAAcC,IAAI,KAAK,kBACvB;YACA,6FAA6F;YAC7F,iGAAiG;YACjG,kCAAkC;YAClC;QACF;IACF;IAEA,2EAA2E;IAC3E,4EAA4E;IAC5E,2DAA2D;IAC3D,IAAIF,MAAMG,YAAY,IAAIH,MAAMI,WAAW,EAAE;IAE7C,IAAIJ,MAAMK,kBAAkB,EAAE;QAC5B,MAAM,IAAIC,yBAAAA,qBAAqB,CAC7B,CAAC,MAAM,EAAEN,MAAMO,KAAK,CAAC,8EAA8E,EAAER,WAAW,4HAA4H,CAAC;IAEjP;IAEA,IAAIE,eAAe;QACjB,IAAIA,cAAcC,IAAI,KAAK,iBAAiB;YAC1C1B,qBACEwB,MAAMO,KAAK,EACXR,YACAE,cAAcO,eAAe;QAEjC,OAAO,IAAIP,cAAcC,IAAI,KAAK,oBAAoB;YACpDD,cAAcQ,UAAU,GAAG;YAE3B,uGAAuG;YACvG,MAAMC,MAAM,IAAIC,oBAAAA,kBAAkB,CAChC,CAAC,MAAM,EAAEX,MAAMO,KAAK,CAAC,iDAAiD,EAAER,WAAW,2EAA2E,CAAC;YAEjKC,MAAMY,uBAAuB,GAAGb;YAChCC,MAAMa,iBAAiB,GAAGH,IAAII,KAAK;YAEnC,MAAMJ;QACR,OAAO,IACLK,QAAQC,GAAG,CAACC,QAAQ,gCAAK,iBACzBhB,iBACAA,cAAcC,IAAI,KAAK,WACvB;YACAD,cAAciB,WAAW,GAAG;QAC9B;IACF;AACF;AAUO,SAASrC,2BACdmB,KAAgB,EAChBD,UAAkB;IAElB,MAAMoB,iBAAiBC,8BAAAA,oBAAoB,CAACC,QAAQ;IACpD,IAAI,CAACF,kBAAkBA,eAAejB,IAAI,KAAK,iBAAiB;IAEhE1B,qBAAqBwB,MAAMO,KAAK,EAAER,YAAYoB,eAAeX,eAAe;AAC9E;AAQO,SAAS9B,iCACdqB,UAAkB,EAClBC,KAAgB,EAChBmB,cAAoC;IAEpC,uGAAuG;IACvG,MAAMT,MAAM,IAAIC,oBAAAA,kBAAkB,CAChC,CAAC,MAAM,EAAEX,MAAMO,KAAK,CAAC,mDAAmD,EAAER,WAAW,6EAA6E,CAAC;IAGrKoB,eAAeV,UAAU,GAAG;IAE5BT,MAAMY,uBAAuB,GAAGb;IAChCC,MAAMa,iBAAiB,GAAGH,IAAII,KAAK;IAEnC,MAAMJ;AACR;AASO,SAAS9B,gCACd0C,MAAiB,EACjBrB,aAAmC;IAEnC,IAAIA,eAAe;QACjB,IACEA,cAAcC,IAAI,KAAK,WACvBD,cAAcC,IAAI,KAAK,kBACvB;YACA,6FAA6F;YAC7F,iGAAiG;YACjG,kCAAkC;YAClC;QACF;QACA,IACED,cAAcC,IAAI,KAAK,eACvBD,cAAcC,IAAI,KAAK,oBACvB;YACAD,cAAcQ,UAAU,GAAG;QAC7B;QACA,IACEM,QAAQC,GAAG,CAACC,QAAQ,gCAAK,iBACzBhB,cAAcC,IAAI,KAAK,WACvB;YACAD,cAAciB,WAAW,GAAG;QAC9B;IACF;AACF;AAEA,yFAAyF;AACzF,kGAAkG;AAClG,qEAAqE;AACrE,SAASK,oCACPhB,KAAa,EACbR,UAAkB,EAClBoB,cAAoC;IAEpC,MAAMK,SAAS,CAAC,MAAM,EAAEjB,MAAM,iEAAiE,EAAER,WAAW,CAAC,CAAC;IAE9G,MAAM0B,QAAQC,gCAAgCF;IAE9CL,eAAeQ,UAAU,CAACC,KAAK,CAACH;IAEhC,MAAMjB,kBAAkBW,eAAeX,eAAe;IACtD,IAAIA,iBAAiB;QACnBA,gBAAgBnB,eAAe,CAACwC,IAAI,CAAC;YACnC,0EAA0E;YAC1E,eAAe;YACff,OAAON,gBAAgBpB,sBAAsB,GACzC,IAAI0C,QAAQhB,KAAK,GACjBvB;YACJQ;QACF;IACF;AACF;AAEO,SAASnC,mCACd2C,KAAa,EACbR,UAAkB,EAClBgC,cAAqB,EACrBZ,cAAoC;IAEpC,MAAMX,kBAAkBW,eAAeX,eAAe;IACtD,IAAIA,iBAAiB;QACnB,IAAIA,gBAAgBhB,yBAAyB,KAAK,MAAM;YACtDgB,gBAAgBlB,qBAAqB,GAAGS;YACxCS,gBAAgBhB,yBAAyB,GAAGuC;QAC9C;IACF;IACA,OAAOR,oCAAoChB,OAAOR,YAAYoB;AAChE;AAEO,SAASrC,sCACdkD,YAA0B;IAE1B,oFAAoF;IACpF,oDAAoD;IACpDA,aAAaC,cAAc,GAAG;AAChC;AAYO,SAAStE,4CACd4C,KAAa,EACbR,UAAkB,EAClBgC,cAAqB,EACrBZ,cAAoC;IAEpC,MAAMX,kBAAkBW,eAAeX,eAAe;IACtD,IAAIA,iBAAiB;QACnB,IAAIA,gBAAgBhB,yBAAyB,KAAK,MAAM;YACtDgB,gBAAgBlB,qBAAqB,GAAGS;YACxCS,gBAAgBhB,yBAAyB,GAAGuC;YAC5C,IAAIZ,eAAee,UAAU,KAAK,MAAM;gBACtC,2EAA2E;gBAC3E,sEAAsE;gBACtE1B,gBAAgB2B,iBAAiB,GAAG;YACtC;QACF;IACF;IACAZ,oCAAoChB,OAAOR,YAAYoB;IACvD,MAAMO,gCACJ,CAAC,MAAM,EAAEnB,MAAM,iEAAiE,EAAER,WAAW,CAAC,CAAC;AAEnG;AAGO,MAAMhB,yCACXD;AASK,SAASpB,SAAS,EAAE8D,MAAM,EAAEjB,KAAK,EAAiB;IACvD,MAAMY,iBAAiBC,8BAAAA,oBAAoB,CAACC,QAAQ;IACpD,MAAMb,kBACJW,kBAAkBA,eAAejB,IAAI,KAAK,kBACtCiB,eAAeX,eAAe,GAC9B;IACNhC,qBAAqB+B,OAAOiB,QAAQhB;AACtC;AAEO,SAAShC,qBACd+B,KAAa,EACbR,UAAkB,EAClBS,eAA4C;IAE5C4B;IACA,IAAI5B,iBAAiB;QACnBA,gBAAgBnB,eAAe,CAACwC,IAAI,CAAC;YACnC,0EAA0E;YAC1E,eAAe;YACff,OAAON,gBAAgBpB,sBAAsB,GACzC,IAAI0C,QAAQhB,KAAK,GACjBvB;YACJQ;QACF;IACF;IAEAb,OAAAA,OAAK,CAACC,iBAAiB,CAACkD,qBAAqB9B,OAAOR;AACtD;AAEA,SAASsC,qBAAqB9B,KAAa,EAAER,UAAkB;IAC7D,OACE,CAAC,MAAM,EAAEQ,MAAM,iEAAiE,EAAER,WAAW,EAAE,CAAC,GAChG,CAAC,+EAA+E,CAAC,GACjF,CAAC,iFAAiF,CAAC;AAEvF;AAEO,SAAS1B,kBAAkBqC,GAAY;IAC5C,IACE,OAAOA,QAAQ,YACfA,QAAQ,QACR,OAAQA,IAAY4B,OAAO,KAAK,UAChC;QACA,OAAOC,wBAAyB7B,IAAY4B,OAAO;IACrD;IACA,OAAO;AACT;AAEA,SAASC,wBAAwBf,MAAc;IAC7C,OACEA,OAAOgB,QAAQ,CACb,sEAEFhB,OAAOgB,QAAQ,CACb;AAGN;AAEA,IAAID,wBAAwBF,qBAAqB,OAAO,YAAY,OAAO;IACzE,MAAM,IAAIP,MACR;AAEJ;AAEA,MAAMW,6BAA6B;AAEnC,SAASf,gCAAgCY,OAAe;IACtD,MAAMb,QAAQ,IAAIK,MAAMQ;IACtBb,MAAciB,MAAM,GAAGD;IACzB,OAAOhB;AACT;AAMO,SAASnD,4BACdmD,KAAc;IAEd,OACE,OAAOA,UAAU,YACjBA,UAAU,QACTA,MAAciB,MAAM,KAAKD,8BAC1B,UAAUhB,SACV,aAAaA,SACbA,iBAAiBK;AAErB;AAEO,SAASjE,oBACdwB,eAAqC;IAErC,OAAOA,gBAAgBsD,MAAM,GAAG;AAClC;AAEO,SAAS5E,qBACd6E,aAAmC,EACnCC,aAAmC;IAEnC,oEAAoE;IACpE,0EAA0E;IAC1E,SAAS;IACTD,cAAcvD,eAAe,CAACwC,IAAI,IAAIgB,cAAcxD,eAAe;IACnE,OAAOuD,cAAcvD,eAAe;AACtC;AAEO,SAASlB,yBACdkB,eAAqC;IAErC,OAAOA,gBACJyD,MAAM,CACL,CAACC,SACC,OAAOA,OAAOjC,KAAK,KAAK,YAAYiC,OAAOjC,KAAK,CAAC6B,MAAM,GAAG,GAE7DK,GAAG,CAAC,CAAC,EAAEjD,UAAU,EAAEe,KAAK,EAAE;QACzBA,QAAQA,MACLmC,KAAK,CAAC,MACP,wEAAwE;QACxE,qEAAqE;QACrE,uDAAuD;SACtDC,KAAK,CAAC,GACNJ,MAAM,CAAC,CAACK;YACP,kDAAkD;YAClD,IAAIA,KAAKX,QAAQ,CAAC,uBAAuB;gBACvC,OAAO;YACT;YAEA,oDAAoD;YACpD,IAAIW,KAAKX,QAAQ,CAAC,mBAAmB;gBACnC,OAAO;YACT;YAEA,kDAAkD;YAClD,IAAIW,KAAKX,QAAQ,CAAC,YAAY;gBAC5B,OAAO;YACT;YAEA,OAAO;QACT,GACCY,IAAI,CAAC;QACR,OAAO,CAAC,0BAA0B,EAAErD,WAAW,GAAG,EAAEe,OAAO;IAC7D;AACJ;AAEA,SAASsB;IACP,IAAI,CAACnD,aAAa;QAChB,MAAM,IAAI6C,MACR,CAAC,gIAAgI,CAAC;IAEtI;AACF;AAMO,SAAS5D,2BAA2BsD,MAAc;IACvDY;IACA,MAAMT,aAAa,IAAI0B;IACvB,qFAAqF;IACrF,IAAI;QACFnE,OAAAA,OAAK,CAACC,iBAAiB,CAACqC;IAC1B,EAAE,OAAO8B,GAAY;QACnB3B,WAAWC,KAAK,CAAC0B;IACnB;IACA,OAAO3B,WAAW4B,MAAM;AAC1B;AAEO,SAASzF,sBACdiC,UAAkB,EAClBoB,cAAoC;IAEpC,MAAMX,kBAAkBW,eAAeX,eAAe;IACtD,IAAIA,iBAAiB;QACnBA,gBAAgBnB,eAAe,CAACwC,IAAI,CAAC;YACnCf,OAAON,gBAAgBpB,sBAAsB,GACzC,IAAI0C,QAAQhB,KAAK,GACjBvB;YACJQ;QACF;IACF;AACF;AAEO,SAASf,sBAAsBe,UAAkB;IACtD,IAAI,OAAOyD,WAAW,aAAa;QACjC,MAAMC,YAAYC,0BAAAA,gBAAgB,CAACrC,QAAQ;QAE3C,IACEoC,aACAA,UAAUE,kBAAkB,IAC5BF,UAAUG,mBAAmB,IAC7BH,UAAUG,mBAAmB,CAACC,IAAI,GAAG,GACrC;YACA,oEAAoE;YACpE,YAAY;YACZ,MAAM5D,gBAAgBmB,8BAAAA,oBAAoB,CAACC,QAAQ;YACnD,IAAIpB,eAAe;gBACjB,mDAAmD;gBACnD,IAAIA,cAAcC,IAAI,KAAK,aAAa;oBACtC,iDAAiD;oBACjD,6EAA6E;oBAC7E,uDAAuD;oBACvDhB,OAAAA,OAAK,CAAC4E,GAAG,CAACC,CAAAA,GAAAA,uBAAAA,kBAAkB,EAAC9D,cAAc+D,YAAY,EAAEjE;gBAC3D,OAAO,IAAIE,cAAcC,IAAI,KAAK,iBAAiB;oBACjD,8BAA8B;oBAC9B1B,qBACEiF,UAAUlD,KAAK,EACfR,YACAE,cAAcO,eAAe;gBAEjC,OAAO,IAAIP,cAAcC,IAAI,KAAK,oBAAoB;oBACpDxB,iCAAiCqB,YAAY0D,WAAWxD;gBAC1D;YACF;QACF;IACF;AACF;AAEA,MAAMgE,mBAAmB;AACzB,MAAMC,mBAAmB,IAAIC,OAC3B,CAAC,UAAU,EAAEC,mBAAAA,sBAAsB,CAAC,QAAQ,CAAC;AAE/C,MAAMC,mBAAmB,IAAIF,OAC3B,CAAC,UAAU,EAAEG,mBAAAA,sBAAsB,CAAC,QAAQ,CAAC;AAE/C,MAAMC,iBAAiB,IAAIJ,OAAO,CAAC,UAAU,EAAEK,mBAAAA,oBAAoB,CAAC,QAAQ,CAAC;AAEtE,SAAS7F,0BACd4B,KAAa,EACbkE,cAAsB,EACtBC,iBAAyC,EACzC9B,aAAmC,EACnCC,aAAmC;IAEnC,IAAI0B,eAAeI,IAAI,CAACF,iBAAiB;QACvC,kGAAkG;QAClG;IACF,OAAO,IAAIP,iBAAiBS,IAAI,CAACF,iBAAiB;QAChDC,kBAAkBhF,kBAAkB,GAAG;QACvC;IACF,OAAO,IAAI2E,iBAAiBM,IAAI,CAACF,iBAAiB;QAChDC,kBAAkB/E,kBAAkB,GAAG;QACvC;IACF,OAAO,IAAIsE,iBAAiBU,IAAI,CAACF,iBAAiB;QAChDC,kBAAkBjF,mBAAmB,GAAG;QACxC;IACF,OAAO,IACLmD,cAAcpD,yBAAyB,IACvCqD,cAAcrD,yBAAyB,EACvC;QACAkF,kBAAkB9E,oBAAoB,GAAG;QACzC;IACF,OAAO;QACL,MAAM0C,UAAU,CAAC,OAAO,EAAE/B,MAAM,+UAA+U,CAAC;QAChX,MAAMkB,QAAQmD,8BAA8BtC,SAASmC;QACrDC,kBAAkB7E,aAAa,CAACgC,IAAI,CAACJ;QACrC;IACF;AACF;AAEA,SAASmD,8BACPtC,OAAe,EACfmC,cAAsB;IAEtB,MAAMhD,QAAQ,IAAIK,MAAMQ;IACxBb,MAAMX,KAAK,GAAG,YAAYwB,UAAUmC;IACpC,OAAOhD;AACT;AAEO,SAAShD,yBACd8B,KAAa,EACbmE,iBAAyC,EACzC9B,aAAmC,EACnCC,aAAmC;IAEnC,IAAIgC;IACJ,IAAIC;IACJ,IAAIC;IACJ,IAAInC,cAAcpD,yBAAyB,EAAE;QAC3CqF,YAAYjC,cAAcpD,yBAAyB;QACnDsF,iBAAiBlC,cAActD,qBAAqB;QACpDyF,aAAanC,cAAcT,iBAAiB,KAAK;IACnD,OAAO,IAAIU,cAAcrD,yBAAyB,EAAE;QAClDqF,YAAYhC,cAAcrD,yBAAyB;QACnDsF,iBAAiBjC,cAAcvD,qBAAqB;QACpDyF,aAAalC,cAAcV,iBAAiB,KAAK;IACnD,OAAO;QACL0C,YAAY;QACZC,iBAAiBvF;QACjBwF,aAAa;IACf;IAEA,IAAIL,kBAAkB9E,oBAAoB,IAAIiF,WAAW;QACvD,IAAI,CAACE,YAAY;YACf,8FAA8F;YAC9F,8DAA8D;YAC9DC,QAAQvD,KAAK,CAACoD;QAChB;QACA,wEAAwE;QACxE,MAAM,IAAIvE,yBAAAA,qBAAqB;IACjC;IAEA,MAAMT,gBAAgB6E,kBAAkB7E,aAAa;IACrD,IAAIA,cAAc8C,MAAM,EAAE;QACxB,IAAK,IAAIsC,IAAI,GAAGA,IAAIpF,cAAc8C,MAAM,EAAEsC,IAAK;YAC7CD,QAAQvD,KAAK,CAAC5B,aAAa,CAACoF,EAAE;QAChC;QAEA,MAAM,IAAI3E,yBAAAA,qBAAqB;IACjC;IAEA,IAAI,CAACoE,kBAAkBjF,mBAAmB,EAAE;QAC1C,IAAIiF,kBAAkBhF,kBAAkB,EAAE;YACxC,IAAImF,WAAW;gBACbG,QAAQvD,KAAK,CAACoD;gBACd,MAAM,IAAIvE,yBAAAA,qBAAqB,CAC7B,CAAC,OAAO,EAAEC,MAAM,oEAAoE,EAAEuE,eAAe,+EAA+E,CAAC;YAEzL;YACA,MAAM,IAAIxE,yBAAAA,qBAAqB,CAC7B,CAAC,OAAO,EAAEC,MAAM,8cAA8c,CAAC;QAEne,OAAO,IAAImE,kBAAkB/E,kBAAkB,EAAE;YAC/C,IAAIkF,WAAW;gBACbG,QAAQvD,KAAK,CAACoD;gBACd,MAAM,IAAIvE,yBAAAA,qBAAqB,CAC7B,CAAC,OAAO,EAAEC,MAAM,oEAAoE,EAAEuE,eAAe,+EAA+E,CAAC;YAEzL;YACA,MAAM,IAAIxE,yBAAAA,qBAAqB,CAC7B,CAAC,OAAO,EAAEC,MAAM,8cAA8c,CAAC;QAEne;IACF;AACF", "ignoreList": [0]}}, {"offset": {"line": 1894, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1899, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/New%20folder%20%288%29/arabic-ecommerce/node_modules/next/src/export/helpers/is-dynamic-usage-error.ts"], "sourcesContent": ["import { isDynamicServerError } from '../../client/components/hooks-server-context'\nimport { isBailoutToCSRError } from '../../shared/lib/lazy-dynamic/bailout-to-csr'\nimport { isNextRouterError } from '../../client/components/is-next-router-error'\nimport { isDynamicPostpone } from '../../server/app-render/dynamic-rendering'\n\nexport const isDynamicUsageError = (err: unknown) =>\n  isDynamicServerError(err) ||\n  isBailoutToCSRError(err) ||\n  isNextRouterError(err) ||\n  isDynamicPostpone(err)\n"], "names": ["isDynamicUsageError", "err", "isDynamicServerError", "isBailoutToCSRError", "isNextRouterError", "isDynamicPostpone"], "mappings": ";;;;+BAKaA,uBAAAA;;;eAAAA;;;oCALwB;8BACD;mCACF;kCACA;AAE3B,MAAMA,sBAAsB,CAACC,MAClCC,CAAAA,GAAAA,oBAAAA,oBAAoB,EAACD,QACrBE,CAAAA,GAAAA,cAAAA,mBAAmB,EAACF,QACpBG,CAAAA,GAAAA,mBAAAA,iBAAiB,EAACH,QAClBI,CAAAA,GAAAA,kBAAAA,iBAAiB,EAACJ", "ignoreList": [0]}}, {"offset": {"line": 1914, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1919, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/New%20folder%20%288%29/arabic-ecommerce/node_modules/next/src/server/lib/router-utils/is-postpone.ts"], "sourcesContent": ["const REACT_POSTPONE_TYPE: symbol = Symbol.for('react.postpone')\n\nexport function isPostpone(error: any): boolean {\n  return (\n    typeof error === 'object' &&\n    error !== null &&\n    error.$$typeof === REACT_POSTPONE_TYPE\n  )\n}\n"], "names": ["isPostpone", "REACT_POSTPONE_TYPE", "Symbol", "for", "error", "$$typeof"], "mappings": ";;;;+BAEgBA,cAAAA;;;eAAAA;;;AAFhB,MAAMC,sBAA8BC,OAAOC,GAAG,CAAC;AAExC,SAASH,WAAWI,KAAU;IACnC,OACE,OAAOA,UAAU,YACjBA,UAAU,QACVA,MAAMC,QAAQ,KAAKJ;AAEvB", "ignoreList": [0]}}, {"offset": {"line": 1933, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1938, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/New%20folder%20%288%29/arabic-ecommerce/node_modules/next/src/client/components/unstable-rethrow.ts"], "sourcesContent": ["import { isDynamicUsageError } from '../../export/helpers/is-dynamic-usage-error'\nimport { isPostpone } from '../../server/lib/router-utils/is-postpone'\nimport { isBailoutToCSRError } from '../../shared/lib/lazy-dynamic/bailout-to-csr'\nimport { isNextRouterError } from './is-next-router-error'\n\n/**\n * This function should be used to rethrow internal Next.js errors so that they can be handled by the framework.\n * When wrapping an API that uses errors to interrupt control flow, you should use this function before you do any error handling.\n * This function will rethrow the error if it is a Next.js error so it can be handled, otherwise it will do nothing.\n *\n * Read more: [Next.js Docs: `unstable_rethrow`](https://nextjs.org/docs/app/api-reference/functions/unstable_rethrow)\n */\nexport function unstable_rethrow(error: unknown): void {\n  if (\n    isNextRouterError(error) ||\n    isBailoutToCSRError(error) ||\n    isDynamicUsageError(error) ||\n    isPostpone(error)\n  ) {\n    throw error\n  }\n\n  if (error instanceof Error && 'cause' in error) {\n    unstable_rethrow(error.cause)\n  }\n}\n"], "names": ["unstable_rethrow", "error", "isNextRouterError", "isBailoutToCSRError", "isDynamicUsageError", "isPostpone", "Error", "cause"], "mappings": ";;;;+BAYgBA,oBAAAA;;;eAAAA;;;qCAZoB;4BACT;8BACS;mCACF;AAS3B,SAASA,iBAAiBC,KAAc;IAC7C,IACEC,CAAAA,GAAAA,mBAAAA,iBAAiB,EAACD,UAClBE,CAAAA,GAAAA,cAAAA,mBAAmB,EAACF,UACpBG,CAAAA,GAAAA,qBAAAA,mBAAmB,EAACH,UACpBI,CAAAA,GAAAA,YAAAA,UAAU,EAACJ,QACX;QACA,MAAMA;IACR;IAEA,IAAIA,iBAAiBK,SAAS,WAAWL,OAAO;QAC9CD,iBAAiBC,MAAMM,KAAK;IAC9B;AACF", "ignoreList": [0]}}, {"offset": {"line": 1967, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1972, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/New%20folder%20%288%29/arabic-ecommerce/node_modules/next/src/client/components/navigation.react-server.ts"], "sourcesContent": ["/** @internal */\nclass ReadonlyURLSearchParamsError extends <PERSON>rror {\n  constructor() {\n    super(\n      'Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams'\n    )\n  }\n}\n\nclass ReadonlyURLSearchParams extends URLSearchParams {\n  /** @deprecated Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams */\n  append() {\n    throw new ReadonlyURLSearchParamsError()\n  }\n  /** @deprecated Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams */\n  delete() {\n    throw new ReadonlyURLSearchParamsError()\n  }\n  /** @deprecated Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams */\n  set() {\n    throw new ReadonlyURLSearchParamsError()\n  }\n  /** @deprecated Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams */\n  sort() {\n    throw new ReadonlyURLSearchParamsError()\n  }\n}\n\nexport { redirect, permanentRedirect } from './redirect'\nexport { RedirectType } from './redirect-error'\nexport { notFound } from './not-found'\nexport { forbidden } from './forbidden'\nexport { unauthorized } from './unauthorized'\nexport { unstable_rethrow } from './unstable-rethrow'\nexport { ReadonlyURLSearchParams }\n"], "names": ["ReadonlyURLSearchParams", "RedirectType", "forbidden", "notFound", "permanentRedirect", "redirect", "unauthorized", "unstable_rethrow", "ReadonlyURLSearchParamsError", "Error", "constructor", "URLSearchParams", "append", "delete", "set", "sort"], "mappings": "AAAA,cAAc,GAAA;;;;;;;;;;;;;;;;;;;;;IAkCLA,uBAAuB,EAAA;eAAvBA;;IALAC,YAAY,EAAA;eAAZA,eAAAA,YAAY;;IAEZC,SAAS,EAAA;eAATA,WAAAA,SAAS;;IADTC,QAAQ,EAAA;eAARA,UAAAA,QAAQ;;IAFEC,iBAAiB,EAAA;eAAjBA,UAAAA,iBAAiB;;IAA3BC,QAAQ,EAAA;eAARA,UAAAA,QAAQ;;IAIRC,YAAY,EAAA;eAAZA,cAAAA,YAAY;;IACZC,gBAAgB,EAAA;eAAhBA,iBAAAA,gBAAgB;;;0BALmB;+BACf;0BACJ;2BACC;8BACG;iCACI;AAhCjC,MAAMC,qCAAqCC;IACzCC,aAAc;QACZ,KAAK,CACH;IAEJ;AACF;AAEA,MAAMV,gCAAgCW;IACpC,wKAAwK,GACxKC,SAAS;QACP,MAAM,IAAIJ;IACZ;IACA,wKAAwK,GACxKK,SAAS;QACP,MAAM,IAAIL;IACZ;IACA,wKAAwK,GACxKM,MAAM;QACJ,MAAM,IAAIN;IACZ;IACA,wKAAwK,GACxKO,OAAO;QACL,MAAM,IAAIP;IACZ;AACF", "ignoreList": [0]}}, {"offset": {"line": 2050, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2055, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/New%20folder%20%288%29/arabic-ecommerce/node_modules/next/src/server/route-modules/app-page/vendored/contexts/server-inserted-html.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'contexts'\n].ServerInsertedHtml\n"], "names": ["module", "exports", "require", "vendored", "ServerInsertedHtml"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,0HAAyBC,QAAQ,CACxD,WACD,CAACC,kBAAkB", "ignoreList": [0]}}, {"offset": {"line": 2057, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2062, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/New%20folder%20%288%29/arabic-ecommerce/node_modules/next/src/client/components/bailout-to-client-rendering.ts"], "sourcesContent": ["import { BailoutToCSRError } from '../../shared/lib/lazy-dynamic/bailout-to-csr'\nimport { workAsyncStorage } from '../../server/app-render/work-async-storage.external'\n\nexport function bailoutToClientRendering(reason: string): void | never {\n  const workStore = workAsyncStorage.getStore()\n\n  if (workStore?.forceStatic) return\n\n  if (workStore?.isStaticGeneration) throw new BailoutToCSRError(reason)\n}\n"], "names": ["bailoutToClientRendering", "reason", "workStore", "workAsyncStorage", "getStore", "forceStatic", "isStaticGeneration", "BailoutToCSRError"], "mappings": ";;;;+BAGgBA,4BAAAA;;;eAAAA;;;8BAHkB;0CACD;AAE1B,SAASA,yBAAyBC,MAAc;IACrD,MAAMC,YAAYC,0BAAAA,gBAAgB,CAACC,QAAQ;IAE3C,IAAIF,aAAAA,OAAAA,KAAAA,IAAAA,UAAWG,WAAW,EAAE;IAE5B,IAAIH,aAAAA,OAAAA,KAAAA,IAAAA,UAAWI,kBAAkB,EAAE,MAAM,IAAIC,cAAAA,iBAAiB,CAACN;AACjE", "ignoreList": [0]}}, {"offset": {"line": 2086, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2091, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/New%20folder%20%288%29/arabic-ecommerce/node_modules/next/src/client/components/navigation.ts"], "sourcesContent": ["import type { Flight<PERSON>outerState } from '../../server/app-render/types'\nimport type { Params } from '../../server/request/params'\n\nimport { useContext, useMemo } from 'react'\nimport {\n  AppRouterContext,\n  LayoutRouterContext,\n  type AppRouterInstance,\n} from '../../shared/lib/app-router-context.shared-runtime'\nimport {\n  SearchParamsContext,\n  PathnameContext,\n  PathParamsContext,\n} from '../../shared/lib/hooks-client-context.shared-runtime'\nimport { getSegmentValue } from './router-reducer/reducers/get-segment-value'\nimport { PAGE_SEGMENT_KEY, DEFAULT_SEGMENT_KEY } from '../../shared/lib/segment'\nimport { ReadonlyURLSearchParams } from './navigation.react-server'\nimport { useDynamicRouteParams } from '../../server/app-render/dynamic-rendering'\n\n/**\n * A [Client Component](https://nextjs.org/docs/app/building-your-application/rendering/client-components) hook\n * that lets you *read* the current URL's search parameters.\n *\n * Learn more about [`URLSearchParams` on MDN](https://developer.mozilla.org/docs/Web/API/URLSearchParams)\n *\n * @example\n * ```ts\n * \"use client\"\n * import { useSearchParams } from 'next/navigation'\n *\n * export default function Page() {\n *   const searchParams = useSearchParams()\n *   searchParams.get('foo') // returns 'bar' when ?foo=bar\n *   // ...\n * }\n * ```\n *\n * Read more: [Next.js Docs: `useSearchParams`](https://nextjs.org/docs/app/api-reference/functions/use-search-params)\n */\n// Client components API\nexport function useSearchParams(): ReadonlyURLSearchParams {\n  const searchParams = useContext(SearchParamsContext)\n\n  // In the case where this is `null`, the compat types added in\n  // `next-env.d.ts` will add a new overload that changes the return type to\n  // include `null`.\n  const readonlySearchParams = useMemo(() => {\n    if (!searchParams) {\n      // When the router is not ready in pages, we won't have the search params\n      // available.\n      return null\n    }\n\n    return new ReadonlyURLSearchParams(searchParams)\n  }, [searchParams]) as ReadonlyURLSearchParams\n\n  if (typeof window === 'undefined') {\n    // AsyncLocalStorage should not be included in the client bundle.\n    const { bailoutToClientRendering } =\n      require('./bailout-to-client-rendering') as typeof import('./bailout-to-client-rendering')\n    // TODO-APP: handle dynamic = 'force-static' here and on the client\n    bailoutToClientRendering('useSearchParams()')\n  }\n\n  return readonlySearchParams\n}\n\n/**\n * A [Client Component](https://nextjs.org/docs/app/building-your-application/rendering/client-components) hook\n * that lets you read the current URL's pathname.\n *\n * @example\n * ```ts\n * \"use client\"\n * import { usePathname } from 'next/navigation'\n *\n * export default function Page() {\n *  const pathname = usePathname() // returns \"/dashboard\" on /dashboard?foo=bar\n *  // ...\n * }\n * ```\n *\n * Read more: [Next.js Docs: `usePathname`](https://nextjs.org/docs/app/api-reference/functions/use-pathname)\n */\n// Client components API\nexport function usePathname(): string {\n  useDynamicRouteParams('usePathname()')\n\n  // In the case where this is `null`, the compat types added in `next-env.d.ts`\n  // will add a new overload that changes the return type to include `null`.\n  return useContext(PathnameContext) as string\n}\n\n// Client components API\nexport {\n  ServerInsertedHTMLContext,\n  useServerInsertedHTML,\n} from '../../shared/lib/server-inserted-html.shared-runtime'\n\n/**\n *\n * This hook allows you to programmatically change routes inside [Client Component](https://nextjs.org/docs/app/building-your-application/rendering/client-components).\n *\n * @example\n * ```ts\n * \"use client\"\n * import { useRouter } from 'next/navigation'\n *\n * export default function Page() {\n *  const router = useRouter()\n *  // ...\n *  router.push('/dashboard') // Navigate to /dashboard\n * }\n * ```\n *\n * Read more: [Next.js Docs: `useRouter`](https://nextjs.org/docs/app/api-reference/functions/use-router)\n */\n// Client components API\nexport function useRouter(): AppRouterInstance {\n  const router = useContext(AppRouterContext)\n  if (router === null) {\n    throw new Error('invariant expected app router to be mounted')\n  }\n\n  return router\n}\n\n/**\n * A [Client Component](https://nextjs.org/docs/app/building-your-application/rendering/client-components) hook\n * that lets you read a route's dynamic params filled in by the current URL.\n *\n * @example\n * ```ts\n * \"use client\"\n * import { useParams } from 'next/navigation'\n *\n * export default function Page() {\n *   // on /dashboard/[team] where pathname is /dashboard/nextjs\n *   const { team } = useParams() // team === \"nextjs\"\n * }\n * ```\n *\n * Read more: [Next.js Docs: `useParams`](https://nextjs.org/docs/app/api-reference/functions/use-params)\n */\n// Client components API\nexport function useParams<T extends Params = Params>(): T {\n  useDynamicRouteParams('useParams()')\n\n  return useContext(PathParamsContext) as T\n}\n\n/** Get the canonical parameters from the current level to the leaf node. */\n// Client components API\nfunction getSelectedLayoutSegmentPath(\n  tree: FlightRouterState,\n  parallelRouteKey: string,\n  first = true,\n  segmentPath: string[] = []\n): string[] {\n  let node: FlightRouterState\n  if (first) {\n    // Use the provided parallel route key on the first parallel route\n    node = tree[1][parallelRouteKey]\n  } else {\n    // After first parallel route prefer children, if there's no children pick the first parallel route.\n    const parallelRoutes = tree[1]\n    node = parallelRoutes.children ?? Object.values(parallelRoutes)[0]\n  }\n\n  if (!node) return segmentPath\n  const segment = node[0]\n\n  let segmentValue = getSegmentValue(segment)\n\n  if (!segmentValue || segmentValue.startsWith(PAGE_SEGMENT_KEY)) {\n    return segmentPath\n  }\n\n  segmentPath.push(segmentValue)\n\n  return getSelectedLayoutSegmentPath(\n    node,\n    parallelRouteKey,\n    false,\n    segmentPath\n  )\n}\n\n/**\n * A [Client Component](https://nextjs.org/docs/app/building-your-application/rendering/client-components) hook\n * that lets you read the active route segments **below** the Layout it is called from.\n *\n * @example\n * ```ts\n * 'use client'\n *\n * import { useSelectedLayoutSegments } from 'next/navigation'\n *\n * export default function ExampleClientComponent() {\n *   const segments = useSelectedLayoutSegments()\n *\n *   return (\n *     <ul>\n *       {segments.map((segment, index) => (\n *         <li key={index}>{segment}</li>\n *       ))}\n *     </ul>\n *   )\n * }\n * ```\n *\n * Read more: [Next.js Docs: `useSelectedLayoutSegments`](https://nextjs.org/docs/app/api-reference/functions/use-selected-layout-segments)\n */\n// Client components API\nexport function useSelectedLayoutSegments(\n  parallelRouteKey: string = 'children'\n): string[] {\n  useDynamicRouteParams('useSelectedLayoutSegments()')\n\n  const context = useContext(LayoutRouterContext)\n  // @ts-expect-error This only happens in `pages`. Type is overwritten in navigation.d.ts\n  if (!context) return null\n\n  return getSelectedLayoutSegmentPath(context.tree, parallelRouteKey)\n}\n\n/**\n * A [Client Component](https://nextjs.org/docs/app/building-your-application/rendering/client-components) hook\n * that lets you read the active route segment **one level below** the Layout it is called from.\n *\n * @example\n * ```ts\n * 'use client'\n * import { useSelectedLayoutSegment } from 'next/navigation'\n *\n * export default function ExampleClientComponent() {\n *   const segment = useSelectedLayoutSegment()\n *\n *   return <p>Active segment: {segment}</p>\n * }\n * ```\n *\n * Read more: [Next.js Docs: `useSelectedLayoutSegment`](https://nextjs.org/docs/app/api-reference/functions/use-selected-layout-segment)\n */\n// Client components API\nexport function useSelectedLayoutSegment(\n  parallelRouteKey: string = 'children'\n): string | null {\n  useDynamicRouteParams('useSelectedLayoutSegment()')\n\n  const selectedLayoutSegments = useSelectedLayoutSegments(parallelRouteKey)\n\n  if (!selectedLayoutSegments || selectedLayoutSegments.length === 0) {\n    return null\n  }\n\n  const selectedLayoutSegment =\n    parallelRouteKey === 'children'\n      ? selectedLayoutSegments[0]\n      : selectedLayoutSegments[selectedLayoutSegments.length - 1]\n\n  // if the default slot is showing, we return null since it's not technically \"selected\" (it's a fallback)\n  // and returning an internal value like `__DEFAULT__` would be confusing.\n  return selectedLayoutSegment === DEFAULT_SEGMENT_KEY\n    ? null\n    : selectedLayoutSegment\n}\n\n// Shared components APIs\nexport {\n  notFound,\n  forbidden,\n  unauthorized,\n  redirect,\n  permanentRedirect,\n  RedirectType,\n  ReadonlyURLSearchParams,\n  unstable_rethrow,\n} from './navigation.react-server'\n"], "names": ["ReadonlyURLSearchParams", "RedirectType", "ServerInsertedHTMLContext", "forbidden", "notFound", "permanentRedirect", "redirect", "unauthorized", "unstable_rethrow", "useParams", "usePathname", "useRouter", "useSearchParams", "useSelectedLayoutSegment", "useSelectedLayoutSegments", "useServerInsertedHTML", "searchParams", "useContext", "SearchParamsContext", "readonlySearchParams", "useMemo", "window", "bailoutToClientRendering", "require", "useDynamicRouteParams", "PathnameContext", "router", "AppRouterContext", "Error", "PathParamsContext", "getSelectedLayoutSegmentPath", "tree", "parallelRouteKey", "first", "segmentPath", "node", "parallelRoutes", "children", "Object", "values", "segment", "segmentValue", "getSegmentValue", "startsWith", "PAGE_SEGMENT_KEY", "push", "context", "LayoutRouterContext", "selectedLayoutSegments", "length", "selectedLayoutSegment", "DEFAULT_SEGMENT_KEY"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAoREA,uBAAuB,EAAA;eAAvBA,uBAAAA,uBAAuB;;IADvBC,YAAY,EAAA;eAAZA,uBAAAA,YAAY;;IApLZC,yBAAyB,EAAA;eAAzBA,iCAAAA,yBAAyB;;IAgLzBC,SAAS,EAAA;eAATA,uBAAAA,SAAS;;IADTC,QAAQ,EAAA;eAARA,uBAAAA,QAAQ;;IAIRC,iBAAiB,EAAA;eAAjBA,uBAAAA,iBAAiB;;IADjBC,QAAQ,EAAA;eAARA,uBAAAA,QAAQ;;IADRC,YAAY,EAAA;eAAZA,uBAAAA,YAAY;;IAKZC,gBAAgB,EAAA;eAAhBA,uBAAAA,gBAAgB;;IApIFC,SAAS,EAAA;eAATA;;IA5DAC,WAAW,EAAA;eAAXA;;IAiCAC,SAAS,EAAA;eAATA;;IA9EAC,eAAe,EAAA;eAAfA;;IA6MAC,wBAAwB,EAAA;eAAxBA;;IA/BAC,yBAAyB,EAAA;eAAzBA;;IAtHdC,qBAAqB,EAAA;eAArBA,iCAAAA,qBAAqB;;;uBA7Fa;+CAK7B;iDAKA;iCACyB;yBACsB;uCACd;kCACF;iDAgF/B;AAzDA,SAASH;IACd,MAAMI,eAAeC,CAAAA,GAAAA,OAAAA,UAAU,EAACC,iCAAAA,mBAAmB;IAEnD,8DAA8D;IAC9D,0EAA0E;IAC1E,kBAAkB;IAClB,MAAMC,uBAAuBC,CAAAA,GAAAA,OAAAA,OAAO,EAAC;QACnC,IAAI,CAACJ,cAAc;YACjB,yEAAyE;YACzE,aAAa;YACb,OAAO;QACT;QAEA,OAAO,IAAIhB,uBAAAA,uBAAuB,CAACgB;IACrC,GAAG;QAACA;KAAa;IAEjB,IAAI,OAAOK,WAAW,aAAa;QACjC,iEAAiE;QACjE,MAAM,EAAEC,wBAAwB,EAAE,GAChCC,QAAQ;QACV,mEAAmE;QACnED,yBAAyB;IAC3B;IAEA,OAAOH;AACT;AAoBO,SAAST;IACdc,CAAAA,GAAAA,kBAAAA,qBAAqB,EAAC;IAEtB,8EAA8E;IAC9E,0EAA0E;IAC1E,OAAOP,CAAAA,GAAAA,OAAAA,UAAU,EAACQ,iCAAAA,eAAe;AACnC;AA2BO,SAASd;IACd,MAAMe,SAAST,CAAAA,GAAAA,OAAAA,UAAU,EAACU,+BAAAA,gBAAgB;IAC1C,IAAID,WAAW,MAAM;QACnB,MAAM,IAAIE,MAAM;IAClB;IAEA,OAAOF;AACT;AAoBO,SAASjB;IACde,CAAAA,GAAAA,kBAAAA,qBAAqB,EAAC;IAEtB,OAAOP,CAAAA,GAAAA,OAAAA,UAAU,EAACY,iCAAAA,iBAAiB;AACrC;AAEA,0EAA0E,GAC1E,wBAAwB;AACxB,SAASC,6BACPC,IAAuB,EACvBC,gBAAwB,EACxBC,KAAY,EACZC,WAA0B;IAD1BD,IAAAA,UAAAA,KAAAA,GAAAA,QAAQ;IACRC,IAAAA,gBAAAA,KAAAA,GAAAA,cAAwB,EAAE;IAE1B,IAAIC;IACJ,IAAIF,OAAO;QACT,kEAAkE;QAClEE,OAAOJ,IAAI,CAAC,EAAE,CAACC,iBAAiB;IAClC,OAAO;QACL,oGAAoG;QACpG,MAAMI,iBAAiBL,IAAI,CAAC,EAAE;YACvBK;QAAPD,OAAOC,CAAAA,2BAAAA,eAAeC,QAAQ,KAAA,OAAvBD,2BAA2BE,OAAOC,MAAM,CAACH,eAAe,CAAC,EAAE;IACpE;IAEA,IAAI,CAACD,MAAM,OAAOD;IAClB,MAAMM,UAAUL,IAAI,CAAC,EAAE;IAEvB,IAAIM,eAAeC,CAAAA,GAAAA,iBAAAA,eAAe,EAACF;IAEnC,IAAI,CAACC,gBAAgBA,aAAaE,UAAU,CAACC,SAAAA,gBAAgB,GAAG;QAC9D,OAAOV;IACT;IAEAA,YAAYW,IAAI,CAACJ;IAEjB,OAAOX,6BACLK,MACAH,kBACA,OACAE;AAEJ;AA4BO,SAASpB,0BACdkB,gBAAqC;IAArCA,IAAAA,qBAAAA,KAAAA,GAAAA,mBAA2B;IAE3BR,CAAAA,GAAAA,kBAAAA,qBAAqB,EAAC;IAEtB,MAAMsB,UAAU7B,CAAAA,GAAAA,OAAAA,UAAU,EAAC8B,+BAAAA,mBAAmB;IAC9C,wFAAwF;IACxF,IAAI,CAACD,SAAS,OAAO;IAErB,OAAOhB,6BAA6BgB,QAAQf,IAAI,EAAEC;AACpD;AAqBO,SAASnB,yBACdmB,gBAAqC;IAArCA,IAAAA,qBAAAA,KAAAA,GAAAA,mBAA2B;IAE3BR,CAAAA,GAAAA,kBAAAA,qBAAqB,EAAC;IAEtB,MAAMwB,yBAAyBlC,0BAA0BkB;IAEzD,IAAI,CAACgB,0BAA0BA,uBAAuBC,MAAM,KAAK,GAAG;QAClE,OAAO;IACT;IAEA,MAAMC,wBACJlB,qBAAqB,aACjBgB,sBAAsB,CAAC,EAAE,GACzBA,sBAAsB,CAACA,uBAAuBC,MAAM,GAAG,EAAE;IAE/D,yGAAyG;IACzG,yEAAyE;IACzE,OAAOC,0BAA0BC,SAAAA,mBAAmB,GAChD,OACAD;AACN", "ignoreList": [0]}}, {"offset": {"line": 2267, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2272, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/New%20folder%20%288%29/arabic-ecommerce/node_modules/next/navigation.js"], "sourcesContent": ["module.exports = require('./dist/client/components/navigation')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0]}}, {"offset": {"line": 2273, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2278, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/New%20folder%20%288%29/arabic-ecommerce/node_modules/%40swc/helpers/cjs/_interop_require_default.cjs"], "sourcesContent": ["\"use strict\";\n\nfunction _interop_require_default(obj) {\n    return obj && obj.__esModule ? obj : { default: obj };\n}\nexports._ = _interop_require_default;\n"], "names": [], "mappings": "AAAA;AAEA,SAAS,yBAAyB,GAAG;IACjC,OAAO,OAAO,IAAI,UAAU,GAAG,MAAM;QAAE,SAAS;IAAI;AACxD;AACA,QAAQ,CAAC,GAAG", "ignoreList": [0]}}, {"offset": {"line": 2285, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2290, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/New%20folder%20%288%29/arabic-ecommerce/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-ssr'\n].ReactJsxRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,0HAAyBC,QAAQ,CACxD,YACD,CAACC,eAAe", "ignoreList": [0]}}, {"offset": {"line": 2292, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2297, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/New%20folder%20%288%29/arabic-ecommerce/node_modules/%40swc/helpers/cjs/_interop_require_wildcard.cjs"], "sourcesContent": ["\"use strict\";\n\nfunction _getRequireWildcardCache(nodeInterop) {\n    if (typeof WeakMap !== \"function\") return null;\n\n    var cacheBabelInterop = new WeakMap();\n    var cacheNodeInterop = new WeakMap();\n\n    return (_getRequireWildcardCache = function(nodeInterop) {\n        return nodeInterop ? cacheNodeInterop : cacheBabelInterop;\n    })(nodeInterop);\n}\nfunction _interop_require_wildcard(obj, nodeInterop) {\n    if (!nodeInterop && obj && obj.__esModule) return obj;\n    if (obj === null || typeof obj !== \"object\" && typeof obj !== \"function\") return { default: obj };\n\n    var cache = _getRequireWildcardCache(nodeInterop);\n\n    if (cache && cache.has(obj)) return cache.get(obj);\n\n    var newObj = { __proto__: null };\n    var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;\n\n    for (var key in obj) {\n        if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) {\n            var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;\n            if (desc && (desc.get || desc.set)) Object.defineProperty(newObj, key, desc);\n            else newObj[key] = obj[key];\n        }\n    }\n\n    newObj.default = obj;\n\n    if (cache) cache.set(obj, newObj);\n\n    return newObj;\n}\nexports._ = _interop_require_wildcard;\n"], "names": [], "mappings": "AAAA;AAEA,SAAS,yBAAyB,WAAW;IACzC,IAAI,OAAO,YAAY,YAAY,OAAO;IAE1C,IAAI,oBAAoB,IAAI;IAC5B,IAAI,mBAAmB,IAAI;IAE3B,OAAO,CAAC,2BAA2B,SAAS,WAAW;QACnD,OAAO,cAAc,mBAAmB;IAC5C,CAAC,EAAE;AACP;AACA,SAAS,0BAA0B,GAAG,EAAE,WAAW;IAC/C,IAAI,CAAC,eAAe,OAAO,IAAI,UAAU,EAAE,OAAO;IAClD,IAAI,QAAQ,QAAQ,OAAO,QAAQ,YAAY,OAAO,QAAQ,YAAY,OAAO;QAAE,SAAS;IAAI;IAEhG,IAAI,QAAQ,yBAAyB;IAErC,IAAI,SAAS,MAAM,GAAG,CAAC,MAAM,OAAO,MAAM,GAAG,CAAC;IAE9C,IAAI,SAAS;QAAE,WAAW;IAAK;IAC/B,IAAI,wBAAwB,OAAO,cAAc,IAAI,OAAO,wBAAwB;IAEpF,IAAK,IAAI,OAAO,IAAK;QACjB,IAAI,QAAQ,aAAa,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,MAAM;YACrE,IAAI,OAAO,wBAAwB,OAAO,wBAAwB,CAAC,KAAK,OAAO;YAC/E,IAAI,QAAQ,CAAC,KAAK,GAAG,IAAI,KAAK,GAAG,GAAG,OAAO,cAAc,CAAC,QAAQ,KAAK;iBAClE,MAAM,CAAC,IAAI,GAAG,GAAG,CAAC,IAAI;QAC/B;IACJ;IAEA,OAAO,OAAO,GAAG;IAEjB,IAAI,OAAO,MAAM,GAAG,CAAC,KAAK;IAE1B,OAAO;AACX;AACA,QAAQ,CAAC,GAAG", "ignoreList": [0]}}, {"offset": {"line": 2329, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2334, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/New%20folder%20%288%29/arabic-ecommerce/node_modules/next/src/shared/lib/router/utils/querystring.ts"], "sourcesContent": ["import type { ParsedUrlQuery } from 'querystring'\n\nexport function searchParamsToUrlQuery(\n  searchParams: URLSearchParams\n): ParsedUrlQuery {\n  const query: ParsedUrlQuery = {}\n  searchParams.forEach((value, key) => {\n    if (typeof query[key] === 'undefined') {\n      query[key] = value\n    } else if (Array.isArray(query[key])) {\n      ;(query[key] as string[]).push(value)\n    } else {\n      query[key] = [query[key] as string, value]\n    }\n  })\n  return query\n}\n\nfunction stringifyUrlQueryParam(param: unknown): string {\n  if (\n    typeof param === 'string' ||\n    (typeof param === 'number' && !isNaN(param)) ||\n    typeof param === 'boolean'\n  ) {\n    return String(param)\n  } else {\n    return ''\n  }\n}\n\nexport function urlQueryToSearchParams(\n  urlQuery: ParsedUrlQuery\n): URLSearchParams {\n  const result = new URLSearchParams()\n  Object.entries(urlQuery).forEach(([key, value]) => {\n    if (Array.isArray(value)) {\n      value.forEach((item) => result.append(key, stringifyUrlQueryParam(item)))\n    } else {\n      result.set(key, stringifyUrlQueryParam(value))\n    }\n  })\n  return result\n}\n\nexport function assign(\n  target: URLSearchParams,\n  ...searchParamsList: URLSearchParams[]\n): URLSearchParams {\n  searchParamsList.forEach((searchParams) => {\n    Array.from(searchParams.keys()).forEach((key) => target.delete(key))\n    searchParams.forEach((value, key) => target.append(key, value))\n  })\n  return target\n}\n"], "names": ["assign", "searchParamsToUrlQuery", "urlQueryToSearchParams", "searchParams", "query", "for<PERSON>ach", "value", "key", "Array", "isArray", "push", "stringifyUrlQueryParam", "param", "isNaN", "String", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "result", "URLSearchParams", "Object", "entries", "item", "append", "set", "target", "searchParamsList", "from", "keys", "delete"], "mappings": ";;;;;;;;;;;;;;;;IA4CgBA,MAAM,EAAA;eAANA;;IA1CAC,sBAAsB,EAAA;eAAtBA;;IA4BAC,sBAAsB,EAAA;eAAtBA;;;AA5BT,SAASD,uBACdE,YAA6B;IAE7B,MAAMC,QAAwB,CAAC;IAC/BD,aAAaE,OAAO,CAAC,CAACC,OAAOC;QAC3B,IAAI,OAAOH,KAAK,CAACG,IAAI,KAAK,aAAa;YACrCH,KAAK,CAACG,IAAI,GAAGD;QACf,OAAO,IAAIE,MAAMC,OAAO,CAACL,KAAK,CAACG,IAAI,GAAG;;YAClCH,KAAK,CAACG,IAAI,CAAcG,IAAI,CAACJ;QACjC,OAAO;YACLF,KAAK,CAACG,IAAI,GAAG;gBAACH,KAAK,CAACG,IAAI;gBAAYD;aAAM;QAC5C;IACF;IACA,OAAOF;AACT;AAEA,SAASO,uBAAuBC,KAAc;IAC5C,IACE,OAAOA,UAAU,YAChB,OAAOA,UAAU,YAAY,CAACC,MAAMD,UACrC,OAAOA,UAAU,WACjB;QACA,OAAOE,OAAOF;IAChB,OAAO;QACL,OAAO;IACT;AACF;AAEO,SAASV,uBACda,QAAwB;IAExB,MAAMC,SAAS,IAAIC;IACnBC,OAAOC,OAAO,CAACJ,UAAUV,OAAO,CAAC,CAAA;YAAC,CAACE,KAAKD,MAAM,GAAA;QAC5C,IAAIE,MAAMC,OAAO,CAACH,QAAQ;YACxBA,MAAMD,OAAO,CAAC,CAACe,OAASJ,OAAOK,MAAM,CAACd,KAAKI,uBAAuBS;QACpE,OAAO;YACLJ,OAAOM,GAAG,CAACf,KAAKI,uBAAuBL;QACzC;IACF;IACA,OAAOU;AACT;AAEO,SAAShB,OACduB,MAAuB;IACvB,IAAA,IAAA,OAAA,UAAA,MAAA,EAAGC,mBAAH,IAAA,MAAA,OAAA,IAAA,OAAA,IAAA,IAAA,OAAA,GAAA,OAAA,MAAA,OAAA;QAAGA,gBAAAA,CAAH,OAAA,EAAA,GAAA,SAAA,CAAA,KAAsC;;IAEtCA,iBAAiBnB,OAAO,CAAC,CAACF;QACxBK,MAAMiB,IAAI,CAACtB,aAAauB,IAAI,IAAIrB,OAAO,CAAC,CAACE,MAAQgB,OAAOI,MAAM,CAACpB;QAC/DJ,aAAaE,OAAO,CAAC,CAACC,OAAOC,MAAQgB,OAAOF,MAAM,CAACd,KAAKD;IAC1D;IACA,OAAOiB;AACT", "ignoreList": [0]}}, {"offset": {"line": 2406, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2411, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/New%20folder%20%288%29/arabic-ecommerce/node_modules/next/src/shared/lib/router/utils/format-url.ts"], "sourcesContent": ["// Format function modified from nodejs\n// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\nimport type { UrlObject } from 'url'\nimport type { ParsedUrlQuery } from 'querystring'\nimport * as querystring from './querystring'\n\nconst slashedProtocols = /https?|ftp|gopher|file/\n\nexport function formatUrl(urlObj: UrlObject) {\n  let { auth, hostname } = urlObj\n  let protocol = urlObj.protocol || ''\n  let pathname = urlObj.pathname || ''\n  let hash = urlObj.hash || ''\n  let query = urlObj.query || ''\n  let host: string | false = false\n\n  auth = auth ? encodeURIComponent(auth).replace(/%3A/i, ':') + '@' : ''\n\n  if (urlObj.host) {\n    host = auth + urlObj.host\n  } else if (hostname) {\n    host = auth + (~hostname.indexOf(':') ? `[${hostname}]` : hostname)\n    if (urlObj.port) {\n      host += ':' + urlObj.port\n    }\n  }\n\n  if (query && typeof query === 'object') {\n    query = String(querystring.urlQueryToSearchParams(query as ParsedUrlQuery))\n  }\n\n  let search = urlObj.search || (query && `?${query}`) || ''\n\n  if (protocol && !protocol.endsWith(':')) protocol += ':'\n\n  if (\n    urlObj.slashes ||\n    ((!protocol || slashedProtocols.test(protocol)) && host !== false)\n  ) {\n    host = '//' + (host || '')\n    if (pathname && pathname[0] !== '/') pathname = '/' + pathname\n  } else if (!host) {\n    host = ''\n  }\n\n  if (hash && hash[0] !== '#') hash = '#' + hash\n  if (search && search[0] !== '?') search = '?' + search\n\n  pathname = pathname.replace(/[?#]/g, encodeURIComponent)\n  search = search.replace('#', '%23')\n\n  return `${protocol}${host}${pathname}${search}${hash}`\n}\n\nexport const urlObjectKeys = [\n  'auth',\n  'hash',\n  'host',\n  'hostname',\n  'href',\n  'path',\n  'pathname',\n  'port',\n  'protocol',\n  'query',\n  'search',\n  'slashes',\n]\n\nexport function formatWithValidation(url: UrlObject): string {\n  if (process.env.NODE_ENV === 'development') {\n    if (url !== null && typeof url === 'object') {\n      Object.keys(url).forEach((key) => {\n        if (!urlObjectKeys.includes(key)) {\n          console.warn(\n            `Unknown key passed via urlObject into url.format: ${key}`\n          )\n        }\n      })\n    }\n  }\n\n  return formatUrl(url)\n}\n"], "names": ["formatUrl", "formatWithValidation", "urlObjectKeys", "slashedProtocols", "url<PERSON>bj", "auth", "hostname", "protocol", "pathname", "hash", "query", "host", "encodeURIComponent", "replace", "indexOf", "port", "String", "querystring", "urlQueryToSearchParams", "search", "endsWith", "slashes", "test", "url", "process", "env", "NODE_ENV", "Object", "keys", "for<PERSON>ach", "key", "includes", "console", "warn"], "mappings": "AAAA,uCAAuC;AACvC,sDAAsD;AACtD,EAAE;AACF,0EAA0E;AAC1E,gEAAgE;AAChE,sEAAsE;AACtE,sEAAsE;AACtE,4EAA4E;AAC5E,qEAAqE;AACrE,wBAAwB;AACxB,EAAE;AACF,0EAA0E;AAC1E,yDAAyD;AACzD,EAAE;AACF,0EAA0E;AAC1E,6DAA6D;AAC7D,4EAA4E;AAC5E,2EAA2E;AAC3E,wEAAwE;AACxE,4EAA4E;AAC5E,yCAAyC;;;;;;;;;;;;;;;;;IAQzBA,SAAS,EAAA;eAATA;;IA6DAC,oBAAoB,EAAA;eAApBA;;IAfHC,aAAa,EAAA;eAAbA;;;;uEAlDgB;AAE7B,MAAMC,mBAAmB;AAElB,SAASH,UAAUI,MAAiB;IACzC,IAAI,EAAEC,IAAI,EAAEC,QAAQ,EAAE,GAAGF;IACzB,IAAIG,WAAWH,OAAOG,QAAQ,IAAI;IAClC,IAAIC,WAAWJ,OAAOI,QAAQ,IAAI;IAClC,IAAIC,OAAOL,OAAOK,IAAI,IAAI;IAC1B,IAAIC,QAAQN,OAAOM,KAAK,IAAI;IAC5B,IAAIC,OAAuB;IAE3BN,OAAOA,OAAOO,mBAAmBP,MAAMQ,OAAO,CAAC,QAAQ,OAAO,MAAM;IAEpE,IAAIT,OAAOO,IAAI,EAAE;QACfA,OAAON,OAAOD,OAAOO,IAAI;IAC3B,OAAO,IAAIL,UAAU;QACnBK,OAAON,OAAQ,CAAA,CAACC,SAASQ,OAAO,CAAC,OAAQ,MAAGR,WAAS,MAAKA,QAAO;QACjE,IAAIF,OAAOW,IAAI,EAAE;YACfJ,QAAQ,MAAMP,OAAOW,IAAI;QAC3B;IACF;IAEA,IAAIL,SAAS,OAAOA,UAAU,UAAU;QACtCA,QAAQM,OAAOC,aAAYC,sBAAsB,CAACR;IACpD;IAEA,IAAIS,SAASf,OAAOe,MAAM,IAAKT,SAAU,MAAGA,SAAY;IAExD,IAAIH,YAAY,CAACA,SAASa,QAAQ,CAAC,MAAMb,YAAY;IAErD,IACEH,OAAOiB,OAAO,IACZ,CAAA,CAACd,YAAYJ,iBAAiBmB,IAAI,CAACf,SAAQ,KAAMI,SAAS,OAC5D;QACAA,OAAO,OAAQA,CAAAA,QAAQ,EAAC;QACxB,IAAIH,YAAYA,QAAQ,CAAC,EAAE,KAAK,KAAKA,WAAW,MAAMA;IACxD,OAAO,IAAI,CAACG,MAAM;QAChBA,OAAO;IACT;IAEA,IAAIF,QAAQA,IAAI,CAAC,EAAE,KAAK,KAAKA,OAAO,MAAMA;IAC1C,IAAIU,UAAUA,MAAM,CAAC,EAAE,KAAK,KAAKA,SAAS,MAAMA;IAEhDX,WAAWA,SAASK,OAAO,CAAC,SAASD;IACrCO,SAASA,OAAON,OAAO,CAAC,KAAK;IAE7B,OAAQ,KAAEN,WAAWI,OAAOH,WAAWW,SAASV;AAClD;AAEO,MAAMP,gBAAgB;IAC3B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAEM,SAASD,qBAAqBsB,GAAc;IACjD,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAe;QAC1C,IAAIH,QAAQ,QAAQ,OAAOA,QAAQ,UAAU;YAC3CI,OAAOC,IAAI,CAACL,KAAKM,OAAO,CAAC,CAACC;gBACxB,IAAI,CAAC5B,cAAc6B,QAAQ,CAACD,MAAM;oBAChCE,QAAQC,IAAI,CACT,uDAAoDH;gBAEzD;YACF;QACF;IACF;IAEA,OAAO9B,UAAUuB;AACnB", "ignoreList": [0]}}, {"offset": {"line": 2520, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2525, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/New%20folder%20%288%29/arabic-ecommerce/node_modules/next/src/client/request-idle-callback.ts"], "sourcesContent": ["export const requestIdleCallback =\n  (typeof self !== 'undefined' &&\n    self.requestIdleCallback &&\n    self.requestIdleCallback.bind(window)) ||\n  function (cb: IdleRequestCallback): number {\n    let start = Date.now()\n    return self.setTimeout(function () {\n      cb({\n        didTimeout: false,\n        timeRemaining: function () {\n          return Math.max(0, 50 - (Date.now() - start))\n        },\n      })\n    }, 1)\n  }\n\nexport const cancelIdleCallback =\n  (typeof self !== 'undefined' &&\n    self.cancelIdleCallback &&\n    self.cancelIdleCallback.bind(window)) ||\n  function (id: number) {\n    return clearTimeout(id)\n  }\n"], "names": ["cancelIdleCallback", "requestIdleCallback", "self", "bind", "window", "cb", "start", "Date", "now", "setTimeout", "didTimeout", "timeRemaining", "Math", "max", "id", "clearTimeout"], "mappings": ";;;;;;;;;;;;;;;IAgBaA,kBAAkB,EAAA;eAAlBA;;IAhBAC,mBAAmB,EAAA;eAAnBA;;;AAAN,MAAMA,sBACV,OAAOC,SAAS,eACfA,KAAKD,mBAAmB,IACxBC,KAAKD,mBAAmB,CAACE,IAAI,CAACC,WAChC,SAAUC,EAAuB;IAC/B,IAAIC,QAAQC,KAAKC,GAAG;IACpB,OAAON,KAAKO,UAAU,CAAC;QACrBJ,GAAG;YACDK,YAAY;YACZC,eAAe;gBACb,OAAOC,KAAKC,GAAG,CAAC,GAAG,KAAMN,CAAAA,KAAKC,GAAG,KAAKF,KAAI;YAC5C;QACF;IACF,GAAG;AACL;AAEK,MAAMN,qBACV,OAAOE,SAAS,eACfA,KAAKF,kBAAkB,IACvBE,KAAKF,kBAAkB,CAACG,IAAI,CAACC,WAC/B,SAAUU,EAAU;IAClB,OAAOC,aAAaD;AACtB", "ignoreList": [0]}}, {"offset": {"line": 2568, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2573, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/New%20folder%20%288%29/arabic-ecommerce/node_modules/next/src/client/use-intersection.tsx"], "sourcesContent": ["import { useCallback, useEffect, useRef, useState } from 'react'\nimport {\n  requestIdleCallback,\n  cancelIdleCallback,\n} from './request-idle-callback'\n\ntype UseIntersectionObserverInit = Pick<\n  IntersectionObserverInit,\n  'rootMargin' | 'root'\n>\n\ntype UseIntersection = { disabled?: boolean } & UseIntersectionObserverInit & {\n    rootRef?: React.RefObject<HTMLElement | null> | null\n  }\ntype ObserveCallback = (isVisible: boolean) => void\ntype Identifier = {\n  root: Element | Document | null\n  margin: string\n}\ntype Observer = {\n  id: Identifier\n  observer: IntersectionObserver\n  elements: Map<Element, ObserveCallback>\n}\n\nconst hasIntersectionObserver = typeof IntersectionObserver === 'function'\n\nconst observers = new Map<Identifier, Observer>()\nconst idList: Identifier[] = []\n\nfunction createObserver(options: UseIntersectionObserverInit): Observer {\n  const id = {\n    root: options.root || null,\n    margin: options.rootMargin || '',\n  }\n  const existing = idList.find(\n    (obj) => obj.root === id.root && obj.margin === id.margin\n  )\n  let instance: Observer | undefined\n\n  if (existing) {\n    instance = observers.get(existing)\n    if (instance) {\n      return instance\n    }\n  }\n\n  const elements = new Map<Element, ObserveCallback>()\n  const observer = new IntersectionObserver((entries) => {\n    entries.forEach((entry) => {\n      const callback = elements.get(entry.target)\n      const isVisible = entry.isIntersecting || entry.intersectionRatio > 0\n      if (callback && isVisible) {\n        callback(isVisible)\n      }\n    })\n  }, options)\n  instance = {\n    id,\n    observer,\n    elements,\n  }\n\n  idList.push(id)\n  observers.set(id, instance)\n  return instance\n}\n\nfunction observe(\n  element: Element,\n  callback: ObserveCallback,\n  options: UseIntersectionObserverInit\n): () => void {\n  const { id, observer, elements } = createObserver(options)\n  elements.set(element, callback)\n\n  observer.observe(element)\n  return function unobserve(): void {\n    elements.delete(element)\n    observer.unobserve(element)\n\n    // Destroy observer when there's nothing left to watch:\n    if (elements.size === 0) {\n      observer.disconnect()\n      observers.delete(id)\n      const index = idList.findIndex(\n        (obj) => obj.root === id.root && obj.margin === id.margin\n      )\n      if (index > -1) {\n        idList.splice(index, 1)\n      }\n    }\n  }\n}\n\nexport function useIntersection<T extends Element>({\n  rootRef,\n  rootMargin,\n  disabled,\n}: UseIntersection): [(element: T | null) => void, boolean, () => void] {\n  const isDisabled: boolean = disabled || !hasIntersectionObserver\n\n  const [visible, setVisible] = useState(false)\n  const elementRef = useRef<T | null>(null)\n  const setElement = useCallback((element: T | null) => {\n    elementRef.current = element\n  }, [])\n\n  useEffect(() => {\n    if (hasIntersectionObserver) {\n      if (isDisabled || visible) return\n\n      const element = elementRef.current\n      if (element && element.tagName) {\n        const unobserve = observe(\n          element,\n          (isVisible) => isVisible && setVisible(isVisible),\n          { root: rootRef?.current, rootMargin }\n        )\n\n        return unobserve\n      }\n    } else {\n      if (!visible) {\n        const idleCallback = requestIdleCallback(() => setVisible(true))\n        return () => cancelIdleCallback(idleCallback)\n      }\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [isDisabled, rootMargin, rootRef, visible, elementRef.current])\n\n  const resetVisible = useCallback(() => {\n    setVisible(false)\n  }, [])\n\n  return [setElement, visible, resetVisible]\n}\n"], "names": ["useIntersection", "hasIntersectionObserver", "IntersectionObserver", "observers", "Map", "idList", "createObserver", "options", "id", "root", "margin", "rootMargin", "existing", "find", "obj", "instance", "get", "elements", "observer", "entries", "for<PERSON>ach", "entry", "callback", "target", "isVisible", "isIntersecting", "intersectionRatio", "push", "set", "observe", "element", "unobserve", "delete", "size", "disconnect", "index", "findIndex", "splice", "rootRef", "disabled", "isDisabled", "visible", "setVisible", "useState", "elementRef", "useRef", "setElement", "useCallback", "current", "useEffect", "tagName", "idleCallback", "requestIdleCallback", "cancelIdleCallback", "resetVisible"], "mappings": ";;;;+BA+FgBA,mBAAAA;;;eAAAA;;;uBA/FyC;qCAIlD;AAqBP,MAAMC,0BAA0B,OAAOC,yBAAyB;AAEhE,MAAMC,YAAY,IAAIC;AACtB,MAAMC,SAAuB,EAAE;AAE/B,SAASC,eAAeC,OAAoC;IAC1D,MAAMC,KAAK;QACTC,MAAMF,QAAQE,IAAI,IAAI;QACtBC,QAAQH,QAAQI,UAAU,IAAI;IAChC;IACA,MAAMC,WAAWP,OAAOQ,IAAI,CAC1B,CAACC,MAAQA,IAAIL,IAAI,KAAKD,GAAGC,IAAI,IAAIK,IAAIJ,MAAM,KAAKF,GAAGE,MAAM;IAE3D,IAAIK;IAEJ,IAAIH,UAAU;QACZG,WAAWZ,UAAUa,GAAG,CAACJ;QACzB,IAAIG,UAAU;YACZ,OAAOA;QACT;IACF;IAEA,MAAME,WAAW,IAAIb;IACrB,MAAMc,WAAW,IAAIhB,qBAAqB,CAACiB;QACzCA,QAAQC,OAAO,CAAC,CAACC;YACf,MAAMC,WAAWL,SAASD,GAAG,CAACK,MAAME,MAAM;YAC1C,MAAMC,YAAYH,MAAMI,cAAc,IAAIJ,MAAMK,iBAAiB,GAAG;YACpE,IAAIJ,YAAYE,WAAW;gBACzBF,SAASE;YACX;QACF;IACF,GAAGjB;IACHQ,WAAW;QACTP;QACAU;QACAD;IACF;IAEAZ,OAAOsB,IAAI,CAACnB;IACZL,UAAUyB,GAAG,CAACpB,IAAIO;IAClB,OAAOA;AACT;AAEA,SAASc,QACPC,OAAgB,EAChBR,QAAyB,EACzBf,OAAoC;IAEpC,MAAM,EAAEC,EAAE,EAAEU,QAAQ,EAAED,QAAQ,EAAE,GAAGX,eAAeC;IAClDU,SAASW,GAAG,CAACE,SAASR;IAEtBJ,SAASW,OAAO,CAACC;IACjB,OAAO,SAASC;QACdd,SAASe,MAAM,CAACF;QAChBZ,SAASa,SAAS,CAACD;QAEnB,uDAAuD;QACvD,IAAIb,SAASgB,IAAI,KAAK,GAAG;YACvBf,SAASgB,UAAU;YACnB/B,UAAU6B,MAAM,CAACxB;YACjB,MAAM2B,QAAQ9B,OAAO+B,SAAS,CAC5B,CAACtB,MAAQA,IAAIL,IAAI,KAAKD,GAAGC,IAAI,IAAIK,IAAIJ,MAAM,KAAKF,GAAGE,MAAM;YAE3D,IAAIyB,QAAQ,CAAC,GAAG;gBACd9B,OAAOgC,MAAM,CAACF,OAAO;YACvB;QACF;IACF;AACF;AAEO,SAASnC,gBAAmC,KAIjC;IAJiC,IAAA,EACjDsC,OAAO,EACP3B,UAAU,EACV4B,QAAQ,EACQ,GAJiC;IAKjD,MAAMC,aAAsBD,YAAY,CAACtC;IAEzC,MAAM,CAACwC,SAASC,WAAW,GAAGC,CAAAA,GAAAA,OAAAA,QAAQ,EAAC;IACvC,MAAMC,aAAaC,CAAAA,GAAAA,OAAAA,MAAM,EAAW;IACpC,MAAMC,aAAaC,CAAAA,GAAAA,OAAAA,WAAW,EAAC,CAACjB;QAC9Bc,WAAWI,OAAO,GAAGlB;IACvB,GAAG,EAAE;IAELmB,CAAAA,GAAAA,OAAAA,SAAS,EAAC;QACR,IAAIhD,yBAAyB;YAC3B,IAAIuC,cAAcC,SAAS;YAE3B,MAAMX,UAAUc,WAAWI,OAAO;YAClC,IAAIlB,WAAWA,QAAQoB,OAAO,EAAE;gBAC9B,MAAMnB,YAAYF,QAChBC,SACA,CAACN,YAAcA,aAAakB,WAAWlB,YACvC;oBAAEf,IAAI,EAAE6B,WAAAA,OAAAA,KAAAA,IAAAA,QAASU,OAAO;oBAAErC;gBAAW;gBAGvC,OAAOoB;YACT;QACF,OAAO;YACL,IAAI,CAACU,SAAS;gBACZ,MAAMU,eAAeC,CAAAA,GAAAA,qBAAAA,mBAAmB,EAAC,IAAMV,WAAW;gBAC1D,OAAO,IAAMW,CAAAA,GAAAA,qBAAAA,kBAAkB,EAACF;YAClC;QACF;IACA,uDAAuD;IACzD,GAAG;QAACX;QAAY7B;QAAY2B;QAASG;QAASG,WAAWI,OAAO;KAAC;IAEjE,MAAMM,eAAeP,CAAAA,GAAAA,OAAAA,WAAW,EAAC;QAC/BL,WAAW;IACb,GAAG,EAAE;IAEL,OAAO;QAACI;QAAYL;QAASa;KAAa;AAC5C", "ignoreList": [0]}}, {"offset": {"line": 2687, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2692, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/New%20folder%20%288%29/arabic-ecommerce/node_modules/next/src/client/components/router-reducer/router-reducer-types.ts"], "sourcesContent": ["import type { CacheNode } from '../../../shared/lib/app-router-context.shared-runtime'\nimport type {\n  FlightRouterState,\n  FlightSegmentPath,\n} from '../../../server/app-render/types'\nimport type { FetchServerResponseResult } from './fetch-server-response'\n\nexport const ACTION_REFRESH = 'refresh'\nexport const ACTION_NAVIGATE = 'navigate'\nexport const ACTION_RESTORE = 'restore'\nexport const ACTION_SERVER_PATCH = 'server-patch'\nexport const ACTION_PREFETCH = 'prefetch'\nexport const ACTION_HMR_REFRESH = 'hmr-refresh'\nexport const ACTION_SERVER_ACTION = 'server-action'\n\nexport type RouterChangeByServerResponse = ({\n  previousTree,\n  serverResponse,\n}: {\n  previousTree: FlightRouterState\n  serverResponse: FetchServerResponseResult\n}) => void\n\nexport type RouterNavigate = (\n  href: string,\n  navigateType: 'push' | 'replace',\n  shouldScroll: boolean\n) => void\n\nexport interface Mutable {\n  mpaNavigation?: boolean\n  patchedTree?: FlightRouterState\n  canonicalUrl?: string\n  scrollableSegments?: FlightSegmentPath[]\n  pendingPush?: boolean\n  cache?: CacheNode\n  prefetchCache?: AppRouterState['prefetchCache']\n  hashFragment?: string\n  shouldScroll?: boolean\n  preserveCustomHistoryState?: boolean\n  onlyHashChange?: boolean\n}\n\nexport interface ServerActionMutable extends Mutable {\n  inFlightServerAction?: Promise<any> | null\n}\n\n/**\n * Refresh triggers a refresh of the full page data.\n * - fetches the Flight data and fills rsc at the root of the cache.\n * - The router state is updated at the root.\n */\nexport interface RefreshAction {\n  type: typeof ACTION_REFRESH\n  origin: Location['origin']\n}\n\nexport interface HmrRefreshAction {\n  type: typeof ACTION_HMR_REFRESH\n  origin: Location['origin']\n}\n\nexport type ServerActionDispatcher = (\n  args: Omit<\n    ServerActionAction,\n    'type' | 'mutable' | 'navigate' | 'changeByServerResponse' | 'cache'\n  >\n) => void\n\nexport interface ServerActionAction {\n  type: typeof ACTION_SERVER_ACTION\n  actionId: string\n  actionArgs: any[]\n  resolve: (value: any) => void\n  reject: (reason?: any) => void\n}\n\n/**\n * Navigate triggers a navigation to the provided url. It supports two types: `push` and `replace`.\n *\n * `navigateType`:\n * - `push` - pushes a new history entry in the browser history\n * - `replace` - replaces the current history entry in the browser history\n *\n * Navigate has multiple cache heuristics:\n * - page was prefetched\n *  - Apply router state tree from prefetch\n *  - Apply Flight data from prefetch to the cache\n *  - If Flight data is a string, it's a redirect and the state is updated to trigger a redirect\n *  - Check if hard navigation is needed\n *    - Hard navigation happens when a dynamic parameter below the common layout changed\n *    - When hard navigation is needed the cache is invalidated below the flightSegmentPath\n *    - The missing cache nodes of the page will be fetched in layout-router and trigger the SERVER_PATCH action\n *  - If hard navigation is not needed\n *    - The cache is reused\n *    - If any cache nodes are missing they'll be fetched in layout-router and trigger the SERVER_PATCH action\n * - page was not prefetched\n *  - The navigate was called from `next/router` (`router.push()` / `router.replace()`) / `next/link` without prefetched data available (e.g. the prefetch didn't come back from the server before clicking the link)\n *    - Flight data is fetched in the reducer (suspends the reducer)\n *    - Router state tree is created based on Flight data\n *    - Cache is filled based on the Flight data\n *\n * Above steps explain 3 cases:\n * - `soft` - Reuses the existing cache and fetches missing nodes in layout-router.\n * - `hard` - Creates a new cache where cache nodes are removed below the common layout and fetches missing nodes in layout-router.\n * - `optimistic` (explicit no prefetch) - Creates a new cache and kicks off the data fetch in the reducer. The data fetch is awaited in the layout-router.\n */\nexport interface NavigateAction {\n  type: typeof ACTION_NAVIGATE\n  url: URL\n  isExternalUrl: boolean\n  locationSearch: Location['search']\n  navigateType: 'push' | 'replace'\n  shouldScroll: boolean\n  allowAliasing: boolean\n}\n\n/**\n * Restore applies the provided router state.\n * - Used for `popstate` (back/forward navigation) where a known router state has to be applied.\n * - Also used when syncing the router state with `pushState`/`replaceState` calls.\n * - Router state is applied as-is from the history state, if available.\n * - If the history state does not contain the router state, the existing router state is used.\n * - If any cache node is missing it will be fetched in layout-router during rendering and the server-patch case.\n * - If existing cache nodes match these are used.\n */\nexport interface RestoreAction {\n  type: typeof ACTION_RESTORE\n  url: URL\n  tree: FlightRouterState | undefined\n}\n\n/**\n * Server-patch applies the provided Flight data to the cache and router tree.\n * - Only triggered in layout-router.\n * - Creates a new cache and router state with the Flight data applied.\n */\nexport interface ServerPatchAction {\n  type: typeof ACTION_SERVER_PATCH\n  serverResponse: FetchServerResponseResult\n  previousTree: FlightRouterState\n}\n\n/**\n * PrefetchKind defines the type of prefetching that should be done.\n * - `auto` - if the page is dynamic, prefetch the page data partially, if static prefetch the page data fully.\n * - `full` - prefetch the page data fully.\n * - `temporary` - a temporary prefetch entry is added to the cache, this is used when prefetch={false} is used in next/link or when you push a route programmatically.\n */\n\nexport enum PrefetchKind {\n  AUTO = 'auto',\n  FULL = 'full',\n  TEMPORARY = 'temporary',\n}\n\n/**\n * Prefetch adds the provided FlightData to the prefetch cache\n * - Creates the router state tree based on the patch in FlightData\n * - Adds the FlightData to the prefetch cache\n * - In ACTION_NAVIGATE the prefetch cache is checked and the router state tree and FlightData are applied.\n */\nexport interface PrefetchAction {\n  type: typeof ACTION_PREFETCH\n  url: URL\n  kind: PrefetchKind\n}\n\nexport interface PushRef {\n  /**\n   * If the app-router should push a new history entry in app-router's useEffect()\n   */\n  pendingPush: boolean\n  /**\n   * Multi-page navigation through location.href.\n   */\n  mpaNavigation: boolean\n  /**\n   * Skip applying the router state to the browser history state.\n   */\n  preserveCustomHistoryState: boolean\n}\n\nexport type FocusAndScrollRef = {\n  /**\n   * If focus and scroll should be set in the layout-router's useEffect()\n   */\n  apply: boolean\n  /**\n   * The hash fragment that should be scrolled to.\n   */\n  hashFragment: string | null\n  /**\n   * The paths of the segments that should be focused.\n   */\n  segmentPaths: FlightSegmentPath[]\n  /**\n   * If only the URLs hash fragment changed\n   */\n  onlyHashChange: boolean\n}\n\nexport type PrefetchCacheEntry = {\n  treeAtTimeOfPrefetch: FlightRouterState\n  data: Promise<FetchServerResponseResult>\n  kind: PrefetchKind\n  prefetchTime: number\n  staleTime: number\n  lastUsedTime: number | null\n  key: string\n  status: PrefetchCacheEntryStatus\n  url: URL\n}\n\nexport enum PrefetchCacheEntryStatus {\n  fresh = 'fresh',\n  reusable = 'reusable',\n  expired = 'expired',\n  stale = 'stale',\n}\n\n/**\n * Handles keeping the state of app-router.\n */\nexport type AppRouterState = {\n  /**\n   * The router state, this is written into the history state in app-router using replaceState/pushState.\n   * - Has to be serializable as it is written into the history state.\n   * - Holds which segments and parallel routes are shown on the screen.\n   */\n  tree: FlightRouterState\n  /**\n   * The cache holds React nodes for every segment that is shown on screen as well as previously shown segments.\n   * It also holds in-progress data requests.\n   * Prefetched data is stored separately in `prefetchCache`, that is applied during ACTION_NAVIGATE.\n   */\n  cache: CacheNode\n  /**\n   * Cache that holds prefetched Flight responses keyed by url.\n   */\n  prefetchCache: Map<string, PrefetchCacheEntry>\n  /**\n   * Decides if the update should create a new history entry and if the navigation has to trigger a browser navigation.\n   */\n  pushRef: PushRef\n  /**\n   * Decides if the update should apply scroll and focus management.\n   */\n  focusAndScrollRef: FocusAndScrollRef\n  /**\n   * The canonical url that is pushed/replaced.\n   * - This is the url you see in the browser.\n   */\n  canonicalUrl: string\n  /**\n   * The underlying \"url\" representing the UI state, which is used for intercepting routes.\n   */\n  nextUrl: string | null\n}\n\nexport type ReadonlyReducerState = Readonly<AppRouterState>\nexport type ReducerState = Promise<AppRouterState> | AppRouterState\nexport type ReducerActions = Readonly<\n  | RefreshAction\n  | NavigateAction\n  | RestoreAction\n  | ServerPatchAction\n  | PrefetchAction\n  | HmrRefreshAction\n  | ServerActionAction\n>\n"], "names": ["ACTION_HMR_REFRESH", "ACTION_NAVIGATE", "ACTION_PREFETCH", "ACTION_REFRESH", "ACTION_RESTORE", "ACTION_SERVER_ACTION", "ACTION_SERVER_PATCH", "PrefetchCacheEntryStatus", "PrefetchKind"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;IAYaA,kBAAkB,EAAA;eAAlBA;;IAJAC,eAAe,EAAA;eAAfA;;IAGAC,eAAe,EAAA;eAAfA;;IAJAC,cAAc,EAAA;eAAdA;;IAEAC,cAAc,EAAA;eAAdA;;IAIAC,oBAAoB,EAAA;eAApBA;;IAHAC,mBAAmB,EAAA;eAAnBA;;IA4MDC,wBAAwB,EAAA;eAAxBA;;IAhEAC,YAAY,EAAA;eAAZA;;;AA/IL,MAAML,iBAAiB;AACvB,MAAMF,kBAAkB;AACxB,MAAMG,iBAAiB;AACvB,MAAME,sBAAsB;AAC5B,MAAMJ,kBAAkB;AACxB,MAAMF,qBAAqB;AAC3B,MAAMK,uBAAuB;AAyI7B,IAAKG,eAAAA,WAAAA,GAAAA,SAAAA,YAAAA;;;;WAAAA;;AAgEL,IAAKD,2BAAAA,WAAAA,GAAAA,SAAAA,wBAAAA;;;;;WAAAA", "ignoreList": [0]}}, {"offset": {"line": 2769, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2774, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/New%20folder%20%288%29/arabic-ecommerce/node_modules/next/src/client/use-merged-ref.ts"], "sourcesContent": ["import { useMemo, useRef, type Ref } from 'react'\n\n// This is a compatibility hook to support React 18 and 19 refs.\n// In 19, a cleanup function from refs may be returned.\n// In 18, returning a cleanup function creates a warning.\n// Since we take userspace refs, we don't know ahead of time if a cleanup function will be returned.\n// This implements cleanup functions with the old behavior in 18.\n// We know refs are always called alternating with `null` and then `T`.\n// So a call with `null` means we need to call the previous cleanup functions.\nexport function useMergedRef<TElement>(\n  refA: Ref<TElement>,\n  refB: Ref<TElement>\n): Ref<TElement> {\n  const cleanupA = useRef<() => void>(() => {})\n  const cleanupB = useRef<() => void>(() => {})\n\n  return useMemo(() => {\n    if (!refA || !refB) {\n      return refA || refB\n    }\n\n    return (current: TElement | null): void => {\n      if (current === null) {\n        cleanupA.current()\n        cleanupB.current()\n      } else {\n        cleanupA.current = applyRef(refA, current)\n        cleanupB.current = applyRef(refB, current)\n      }\n    }\n  }, [refA, refB])\n}\n\nfunction applyRef<TElement>(\n  refA: NonNullable<Ref<TElement>>,\n  current: TElement\n) {\n  if (typeof refA === 'function') {\n    const cleanup = refA(current)\n    if (typeof cleanup === 'function') {\n      return cleanup\n    } else {\n      return () => refA(null)\n    }\n  } else {\n    refA.current = current\n    return () => {\n      refA.current = null\n    }\n  }\n}\n"], "names": ["useMergedRef", "refA", "refB", "cleanupA", "useRef", "cleanupB", "useMemo", "current", "applyRef", "cleanup"], "mappings": ";;;;+BASgBA,gBAAAA;;;eAAAA;;;uBAT0B;AASnC,SAASA,aACdC,IAAmB,EACnBC,IAAmB;IAEnB,MAAMC,WAAWC,CAAAA,GAAAA,OAAAA,MAAM,EAAa,KAAO;IAC3C,MAAMC,WAAWD,CAAAA,GAAAA,OAAAA,MAAM,EAAa,KAAO;IAE3C,OAAOE,CAAAA,GAAAA,OAAAA,OAAO,EAAC;QACb,IAAI,CAACL,QAAQ,CAACC,MAAM;YAClB,OAAOD,QAAQC;QACjB;QAEA,OAAO,CAACK;YACN,IAAIA,YAAY,MAAM;gBACpBJ,SAASI,OAAO;gBAChBF,SAASE,OAAO;YAClB,OAAO;gBACLJ,SAASI,OAAO,GAAGC,SAASP,MAAMM;gBAClCF,SAASE,OAAO,GAAGC,SAASN,MAAMK;YACpC;QACF;IACF,GAAG;QAACN;QAAMC;KAAK;AACjB;AAEA,SAASM,SACPP,IAAgC,EAChCM,OAAiB;IAEjB,IAAI,OAAON,SAAS,YAAY;QAC9B,MAAMQ,UAAUR,KAAKM;QACrB,IAAI,OAAOE,YAAY,YAAY;YACjC,OAAOA;QACT,OAAO;YACL,OAAO,IAAMR,KAAK;QACpB;IACF,OAAO;QACLA,KAAKM,OAAO,GAAGA;QACf,OAAO;YACLN,KAAKM,OAAO,GAAG;QACjB;IACF;AACF", "ignoreList": [0]}}, {"offset": {"line": 2828, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2833, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/New%20folder%20%288%29/arabic-ecommerce/node_modules/next/src/shared/lib/utils.ts"], "sourcesContent": ["import type { HtmlProps } from './html-context.shared-runtime'\nimport type { ComponentType, JSX } from 'react'\nimport type { DomainLocale } from '../../server/config'\nimport type { Env } from '@next/env'\nimport type { IncomingMessage, ServerResponse } from 'http'\nimport type { NextRouter } from './router/router'\nimport type { ParsedUrlQuery } from 'querystring'\nimport type { PreviewData } from '../../types'\nimport type { COMPILER_NAMES } from './constants'\nimport type fs from 'fs'\n\nexport type NextComponentType<\n  Context extends BaseContext = NextPageContext,\n  InitialProps = {},\n  Props = {},\n> = ComponentType<Props> & {\n  /**\n   * Used for initial page load data population. Data returned from `getInitialProps` is serialized when server rendered.\n   * Make sure to return plain `Object` without using `Date`, `Map`, `Set`.\n   * @param context Context of `page`\n   */\n  getInitialProps?(context: Context): InitialProps | Promise<InitialProps>\n}\n\nexport type DocumentType = NextComponentType<\n  DocumentContext,\n  DocumentInitialProps,\n  DocumentProps\n>\n\nexport type AppType<P = {}> = NextComponentType<\n  AppContextType,\n  P,\n  AppPropsType<any, P>\n>\n\nexport type AppTreeType = ComponentType<\n  AppInitialProps & { [name: string]: any }\n>\n\n/**\n * Web vitals provided to _app.reportWebVitals by Core Web Vitals plugin developed by Google Chrome team.\n * https://nextjs.org/blog/next-9-4#integrated-web-vitals-reporting\n */\nexport const WEB_VITALS = ['CLS', 'FCP', 'FID', 'INP', 'LCP', 'TTFB'] as const\nexport type NextWebVitalsMetric = {\n  id: string\n  startTime: number\n  value: number\n  attribution?: { [key: string]: unknown }\n} & (\n  | {\n      label: 'web-vital'\n      name: (typeof WEB_VITALS)[number]\n    }\n  | {\n      label: 'custom'\n      name:\n        | 'Next.js-hydration'\n        | 'Next.js-route-change-to-render'\n        | 'Next.js-render'\n    }\n)\n\nexport type Enhancer<C> = (Component: C) => C\n\nexport type ComponentsEnhancer =\n  | {\n      enhanceApp?: Enhancer<AppType>\n      enhanceComponent?: Enhancer<NextComponentType>\n    }\n  | Enhancer<NextComponentType>\n\nexport type RenderPageResult = {\n  html: string\n  head?: Array<JSX.Element | null>\n}\n\nexport type RenderPage = (\n  options?: ComponentsEnhancer\n) => DocumentInitialProps | Promise<DocumentInitialProps>\n\nexport type BaseContext = {\n  res?: ServerResponse\n  [k: string]: any\n}\n\nexport type NEXT_DATA = {\n  props: Record<string, any>\n  page: string\n  query: ParsedUrlQuery\n  buildId: string\n  assetPrefix?: string\n  runtimeConfig?: { [key: string]: any }\n  nextExport?: boolean\n  autoExport?: boolean\n  isFallback?: boolean\n  isExperimentalCompile?: boolean\n  dynamicIds?: (string | number)[]\n  err?: Error & {\n    statusCode?: number\n    source?: typeof COMPILER_NAMES.server | typeof COMPILER_NAMES.edgeServer\n  }\n  gsp?: boolean\n  gssp?: boolean\n  customServer?: boolean\n  gip?: boolean\n  appGip?: boolean\n  locale?: string\n  locales?: string[]\n  defaultLocale?: string\n  domainLocales?: DomainLocale[]\n  scriptLoader?: any[]\n  isPreview?: boolean\n  notFoundSrcPage?: string\n}\n\n/**\n * `Next` context\n */\nexport interface NextPageContext {\n  /**\n   * Error object if encountered during rendering\n   */\n  err?: (Error & { statusCode?: number }) | null\n  /**\n   * `HTTP` request object.\n   */\n  req?: IncomingMessage\n  /**\n   * `HTTP` response object.\n   */\n  res?: ServerResponse\n  /**\n   * Path section of `URL`.\n   */\n  pathname: string\n  /**\n   * Query string section of `URL` parsed as an object.\n   */\n  query: ParsedUrlQuery\n  /**\n   * `String` of the actual path including query.\n   */\n  asPath?: string\n  /**\n   * The currently active locale\n   */\n  locale?: string\n  /**\n   * All configured locales\n   */\n  locales?: string[]\n  /**\n   * The configured default locale\n   */\n  defaultLocale?: string\n  /**\n   * `Component` the tree of the App to use if needing to render separately\n   */\n  AppTree: AppTreeType\n}\n\nexport type AppContextType<Router extends NextRouter = NextRouter> = {\n  Component: NextComponentType<NextPageContext>\n  AppTree: AppTreeType\n  ctx: NextPageContext\n  router: Router\n}\n\nexport type AppInitialProps<PageProps = any> = {\n  pageProps: PageProps\n}\n\nexport type AppPropsType<\n  Router extends NextRouter = NextRouter,\n  PageProps = {},\n> = AppInitialProps<PageProps> & {\n  Component: NextComponentType<NextPageContext, any, any>\n  router: Router\n  __N_SSG?: boolean\n  __N_SSP?: boolean\n}\n\nexport type DocumentContext = NextPageContext & {\n  renderPage: RenderPage\n  defaultGetInitialProps(\n    ctx: DocumentContext,\n    options?: { nonce?: string }\n  ): Promise<DocumentInitialProps>\n}\n\nexport type DocumentInitialProps = RenderPageResult & {\n  styles?: React.ReactElement[] | Iterable<React.ReactNode> | JSX.Element\n}\n\nexport type DocumentProps = DocumentInitialProps & HtmlProps\n\n/**\n * Next `API` route request\n */\nexport interface NextApiRequest extends IncomingMessage {\n  /**\n   * Object of `query` values from url\n   */\n  query: Partial<{\n    [key: string]: string | string[]\n  }>\n  /**\n   * Object of `cookies` from header\n   */\n  cookies: Partial<{\n    [key: string]: string\n  }>\n\n  body: any\n\n  env: Env\n\n  draftMode?: boolean\n\n  preview?: boolean\n  /**\n   * Preview data set on the request, if any\n   * */\n  previewData?: PreviewData\n}\n\n/**\n * Send body of response\n */\ntype Send<T> = (body: T) => void\n\n/**\n * Next `API` route response\n */\nexport type NextApiResponse<Data = any> = ServerResponse & {\n  /**\n   * Send data `any` data in response\n   */\n  send: Send<Data>\n  /**\n   * Send data `json` data in response\n   */\n  json: Send<Data>\n  status: (statusCode: number) => NextApiResponse<Data>\n  redirect(url: string): NextApiResponse<Data>\n  redirect(status: number, url: string): NextApiResponse<Data>\n\n  /**\n   * Set draft mode\n   */\n  setDraftMode: (options: { enable: boolean }) => NextApiResponse<Data>\n\n  /**\n   * Set preview data for Next.js' prerender mode\n   */\n  setPreviewData: (\n    data: object | string,\n    options?: {\n      /**\n       * Specifies the number (in seconds) for the preview session to last for.\n       * The given number will be converted to an integer by rounding down.\n       * By default, no maximum age is set and the preview session finishes\n       * when the client shuts down (browser is closed).\n       */\n      maxAge?: number\n      /**\n       * Specifies the path for the preview session to work under. By default,\n       * the path is considered the \"default path\", i.e., any pages under \"/\".\n       */\n      path?: string\n    }\n  ) => NextApiResponse<Data>\n\n  /**\n   * Clear preview data for Next.js' prerender mode\n   */\n  clearPreviewData: (options?: { path?: string }) => NextApiResponse<Data>\n\n  /**\n   * Revalidate a specific page and regenerate it using On-Demand Incremental\n   * Static Regeneration.\n   * The path should be an actual path, not a rewritten path. E.g. for\n   * \"/blog/[slug]\" this should be \"/blog/post-1\".\n   * @link https://nextjs.org/docs/app/building-your-application/data-fetching/incremental-static-regeneration#on-demand-revalidation-with-revalidatepath\n   */\n  revalidate: (\n    urlPath: string,\n    opts?: {\n      unstable_onlyGenerated?: boolean\n    }\n  ) => Promise<void>\n}\n\n/**\n * Next `API` route handler\n */\nexport type NextApiHandler<T = any> = (\n  req: NextApiRequest,\n  res: NextApiResponse<T>\n) => unknown | Promise<unknown>\n\n/**\n * Utils\n */\nexport function execOnce<T extends (...args: any[]) => ReturnType<T>>(\n  fn: T\n): T {\n  let used = false\n  let result: ReturnType<T>\n\n  return ((...args: any[]) => {\n    if (!used) {\n      used = true\n      result = fn(...args)\n    }\n    return result\n  }) as T\n}\n\n// Scheme: https://tools.ietf.org/html/rfc3986#section-3.1\n// Absolute URL: https://tools.ietf.org/html/rfc3986#section-4.3\nconst ABSOLUTE_URL_REGEX = /^[a-zA-Z][a-zA-Z\\d+\\-.]*?:/\nexport const isAbsoluteUrl = (url: string) => ABSOLUTE_URL_REGEX.test(url)\n\nexport function getLocationOrigin() {\n  const { protocol, hostname, port } = window.location\n  return `${protocol}//${hostname}${port ? ':' + port : ''}`\n}\n\nexport function getURL() {\n  const { href } = window.location\n  const origin = getLocationOrigin()\n  return href.substring(origin.length)\n}\n\nexport function getDisplayName<P>(Component: ComponentType<P>) {\n  return typeof Component === 'string'\n    ? Component\n    : Component.displayName || Component.name || 'Unknown'\n}\n\nexport function isResSent(res: ServerResponse) {\n  return res.finished || res.headersSent\n}\n\nexport function normalizeRepeatedSlashes(url: string) {\n  const urlParts = url.split('?')\n  const urlNoQuery = urlParts[0]\n\n  return (\n    urlNoQuery\n      // first we replace any non-encoded backslashes with forward\n      // then normalize repeated forward slashes\n      .replace(/\\\\/g, '/')\n      .replace(/\\/\\/+/g, '/') +\n    (urlParts[1] ? `?${urlParts.slice(1).join('?')}` : '')\n  )\n}\n\nexport async function loadGetInitialProps<\n  C extends BaseContext,\n  IP = {},\n  P = {},\n>(App: NextComponentType<C, IP, P>, ctx: C): Promise<IP> {\n  if (process.env.NODE_ENV !== 'production') {\n    if (App.prototype?.getInitialProps) {\n      const message = `\"${getDisplayName(\n        App\n      )}.getInitialProps()\" is defined as an instance method - visit https://nextjs.org/docs/messages/get-initial-props-as-an-instance-method for more information.`\n      throw new Error(message)\n    }\n  }\n  // when called from _app `ctx` is nested in `ctx`\n  const res = ctx.res || (ctx.ctx && ctx.ctx.res)\n\n  if (!App.getInitialProps) {\n    if (ctx.ctx && ctx.Component) {\n      // @ts-ignore pageProps default\n      return {\n        pageProps: await loadGetInitialProps(ctx.Component, ctx.ctx),\n      }\n    }\n    return {} as IP\n  }\n\n  const props = await App.getInitialProps(ctx)\n\n  if (res && isResSent(res)) {\n    return props\n  }\n\n  if (!props) {\n    const message = `\"${getDisplayName(\n      App\n    )}.getInitialProps()\" should resolve to an object. But found \"${props}\" instead.`\n    throw new Error(message)\n  }\n\n  if (process.env.NODE_ENV !== 'production') {\n    if (Object.keys(props).length === 0 && !ctx.ctx) {\n      console.warn(\n        `${getDisplayName(\n          App\n        )} returned an empty object from \\`getInitialProps\\`. This de-optimizes and prevents automatic static optimization. https://nextjs.org/docs/messages/empty-object-getInitialProps`\n      )\n    }\n  }\n\n  return props\n}\n\nexport const SP = typeof performance !== 'undefined'\nexport const ST =\n  SP &&\n  (['mark', 'measure', 'getEntriesByName'] as const).every(\n    (method) => typeof performance[method] === 'function'\n  )\n\nexport class DecodeError extends Error {}\nexport class NormalizeError extends Error {}\nexport class PageNotFoundError extends Error {\n  code: string\n\n  constructor(page: string) {\n    super()\n    this.code = 'ENOENT'\n    this.name = 'PageNotFoundError'\n    this.message = `Cannot find module for page: ${page}`\n  }\n}\n\nexport class MissingStaticPage extends Error {\n  constructor(page: string, message: string) {\n    super()\n    this.message = `Failed to load static file for page: ${page} ${message}`\n  }\n}\n\nexport class MiddlewareNotFoundError extends Error {\n  code: string\n  constructor() {\n    super()\n    this.code = 'ENOENT'\n    this.message = `Cannot find the middleware module`\n  }\n}\n\nexport interface CacheFs {\n  existsSync: typeof fs.existsSync\n  readFile: typeof fs.promises.readFile\n  readFileSync: typeof fs.readFileSync\n  writeFile(f: string, d: any): Promise<void>\n  mkdir(dir: string): Promise<void | string>\n  stat(f: string): Promise<{ mtime: Date }>\n}\n\nexport function stringifyError(error: Error) {\n  return JSON.stringify({ message: error.message, stack: error.stack })\n}\n"], "names": ["DecodeError", "MiddlewareNotFoundError", "MissingStaticPage", "NormalizeError", "PageNotFoundError", "SP", "ST", "WEB_VITALS", "execOnce", "getDisplayName", "getLocationOrigin", "getURL", "isAbsoluteUrl", "isResSent", "loadGetInitialProps", "normalizeRepeatedSlashes", "stringifyError", "fn", "used", "result", "args", "ABSOLUTE_URL_REGEX", "url", "test", "protocol", "hostname", "port", "window", "location", "href", "origin", "substring", "length", "Component", "displayName", "name", "res", "finished", "headersSent", "urlParts", "split", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "replace", "slice", "join", "App", "ctx", "process", "env", "NODE_ENV", "prototype", "getInitialProps", "message", "Error", "pageProps", "props", "Object", "keys", "console", "warn", "performance", "every", "method", "constructor", "page", "code", "error", "JSON", "stringify", "stack"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAoaaA,WAAW,EAAA;eAAXA;;IAoBAC,uBAAuB,EAAA;eAAvBA;;IAPAC,iBAAiB,EAAA;eAAjBA;;IAZAC,cAAc,EAAA;eAAdA;;IACAC,iBAAiB,EAAA;eAAjBA;;IATAC,EAAE,EAAA;eAAFA;;IACAC,EAAE,EAAA;eAAFA;;IAlXAC,UAAU,EAAA;eAAVA;;IAsQGC,QAAQ,EAAA;eAARA;;IA+BAC,cAAc,EAAA;eAAdA;;IAXAC,iBAAiB,EAAA;eAAjBA;;IAKAC,MAAM,EAAA;eAANA;;IAPHC,aAAa,EAAA;eAAbA;;IAmBGC,SAAS,EAAA;eAATA;;IAkBMC,mBAAmB,EAAA;eAAnBA;;IAdNC,wBAAwB,EAAA;eAAxBA;;IA+GAC,cAAc,EAAA;eAAdA;;;AA9ZT,MAAMT,aAAa;IAAC;IAAO;IAAO;IAAO;IAAO;IAAO;CAAO;AAsQ9D,SAASC,SACdS,EAAK;IAEL,IAAIC,OAAO;IACX,IAAIC;IAEJ,OAAQ;yCAAIC,OAAAA,IAAAA,MAAAA,OAAAA,OAAAA,GAAAA,OAAAA,MAAAA,OAAAA;YAAAA,IAAAA,CAAAA,KAAAA,GAAAA,SAAAA,CAAAA,KAAAA;;QACV,IAAI,CAACF,MAAM;YACTA,OAAO;YACPC,SAASF,MAAMG;QACjB;QACA,OAAOD;IACT;AACF;AAEA,0DAA0D;AAC1D,gEAAgE;AAChE,MAAME,qBAAqB;AACpB,MAAMT,gBAAgB,CAACU,MAAgBD,mBAAmBE,IAAI,CAACD;AAE/D,SAASZ;IACd,MAAM,EAAEc,QAAQ,EAAEC,QAAQ,EAAEC,IAAI,EAAE,GAAGC,OAAOC,QAAQ;IACpD,OAAUJ,WAAS,OAAIC,WAAWC,CAAAA,OAAO,MAAMA,OAAO,EAAC;AACzD;AAEO,SAASf;IACd,MAAM,EAAEkB,IAAI,EAAE,GAAGF,OAAOC,QAAQ;IAChC,MAAME,SAASpB;IACf,OAAOmB,KAAKE,SAAS,CAACD,OAAOE,MAAM;AACrC;AAEO,SAASvB,eAAkBwB,SAA2B;IAC3D,OAAO,OAAOA,cAAc,WACxBA,YACAA,UAAUC,WAAW,IAAID,UAAUE,IAAI,IAAI;AACjD;AAEO,SAAStB,UAAUuB,GAAmB;IAC3C,OAAOA,IAAIC,QAAQ,IAAID,IAAIE,WAAW;AACxC;AAEO,SAASvB,yBAAyBO,GAAW;IAClD,MAAMiB,WAAWjB,IAAIkB,KAAK,CAAC;IAC3B,MAAMC,aAAaF,QAAQ,CAAC,EAAE;IAE9B,OACEE,WACE,4DAA4D;IAC5D,0CAA0C;KACzCC,OAAO,CAAC,OAAO,KACfA,OAAO,CAAC,UAAU,OACpBH,CAAAA,QAAQ,CAAC,EAAE,GAAI,MAAGA,SAASI,KAAK,CAAC,GAAGC,IAAI,CAAC,OAAS,EAAC;AAExD;AAEO,eAAe9B,oBAIpB+B,GAAgC,EAAEC,GAAM;IACxC,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAc;YACrCJ;QAAJ,IAAA,CAAIA,iBAAAA,IAAIK,SAAS,KAAA,OAAA,KAAA,IAAbL,eAAeM,eAAe,EAAE;YAClC,MAAMC,UAAW,MAAG3C,eAClBoC,OACA;YACF,MAAM,IAAIQ,MAAMD;QAClB;IACF;IACA,iDAAiD;IACjD,MAAMhB,MAAMU,IAAIV,GAAG,IAAKU,IAAIA,GAAG,IAAIA,IAAIA,GAAG,CAACV,GAAG;IAE9C,IAAI,CAACS,IAAIM,eAAe,EAAE;QACxB,IAAIL,IAAIA,GAAG,IAAIA,IAAIb,SAAS,EAAE;YAC5B,+BAA+B;YAC/B,OAAO;gBACLqB,WAAW,MAAMxC,oBAAoBgC,IAAIb,SAAS,EAAEa,IAAIA,GAAG;YAC7D;QACF;QACA,OAAO,CAAC;IACV;IAEA,MAAMS,QAAQ,MAAMV,IAAIM,eAAe,CAACL;IAExC,IAAIV,OAAOvB,UAAUuB,MAAM;QACzB,OAAOmB;IACT;IAEA,IAAI,CAACA,OAAO;QACV,MAAMH,UAAW,MAAG3C,eAClBoC,OACA,iEAA8DU,QAAM;QACtE,MAAM,IAAIF,MAAMD;IAClB;IAEA,IAAIL,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAc;QACzC,IAAIO,OAAOC,IAAI,CAACF,OAAOvB,MAAM,KAAK,KAAK,CAACc,IAAIA,GAAG,EAAE;YAC/CY,QAAQC,IAAI,CACT,KAAElD,eACDoC,OACA;QAEN;IACF;IAEA,OAAOU;AACT;AAEO,MAAMlD,KAAK,OAAOuD,gBAAgB;AAClC,MAAMtD,KACXD,MACC;IAAC;IAAQ;IAAW;CAAmB,CAAWwD,KAAK,CACtD,CAACC,SAAW,OAAOF,WAAW,CAACE,OAAO,KAAK;AAGxC,MAAM9D,oBAAoBqD;AAAO;AACjC,MAAMlD,uBAAuBkD;AAAO;AACpC,MAAMjD,0BAA0BiD;IAGrCU,YAAYC,IAAY,CAAE;QACxB,KAAK;QACL,IAAI,CAACC,IAAI,GAAG;QACZ,IAAI,CAAC9B,IAAI,GAAG;QACZ,IAAI,CAACiB,OAAO,GAAI,kCAA+BY;IACjD;AACF;AAEO,MAAM9D,0BAA0BmD;IACrCU,YAAYC,IAAY,EAAEZ,OAAe,CAAE;QACzC,KAAK;QACL,IAAI,CAACA,OAAO,GAAI,0CAAuCY,OAAK,MAAGZ;IACjE;AACF;AAEO,MAAMnD,gCAAgCoD;IAE3CU,aAAc;QACZ,KAAK;QACL,IAAI,CAACE,IAAI,GAAG;QACZ,IAAI,CAACb,OAAO,GAAI;IAClB;AACF;AAWO,SAASpC,eAAekD,KAAY;IACzC,OAAOC,KAAKC,SAAS,CAAC;QAAEhB,SAASc,MAAMd,OAAO;QAAEiB,OAAOH,MAAMG,KAAK;IAAC;AACrE", "ignoreList": [0]}}, {"offset": {"line": 3034, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3039, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/New%20folder%20%288%29/arabic-ecommerce/node_modules/next/src/shared/lib/router/utils/parse-path.ts"], "sourcesContent": ["/**\n * Given a path this function will find the pathname, query and hash and return\n * them. This is useful to parse full paths on the client side.\n * @param path A path to parse e.g. /foo/bar?id=1#hash\n */\nexport function parsePath(path: string) {\n  const hashIndex = path.indexOf('#')\n  const queryIndex = path.indexOf('?')\n  const hasQuery = queryIndex > -1 && (hashIndex < 0 || queryIndex < hashIndex)\n\n  if (hasQuery || hashIndex > -1) {\n    return {\n      pathname: path.substring(0, hasQuery ? queryIndex : hashIndex),\n      query: hasQuery\n        ? path.substring(queryIndex, hashIndex > -1 ? hashIndex : undefined)\n        : '',\n      hash: hashIndex > -1 ? path.slice(hashIndex) : '',\n    }\n  }\n\n  return { pathname: path, query: '', hash: '' }\n}\n"], "names": ["parsePath", "path", "hashIndex", "indexOf", "queryIndex", "<PERSON><PERSON><PERSON><PERSON>", "pathname", "substring", "query", "undefined", "hash", "slice"], "mappings": "AAAA;;;;CAIC,GAAA;;;;+BACeA,aAAAA;;;eAAAA;;;AAAT,SAASA,UAAUC,IAAY;IACpC,MAAMC,YAAYD,KAAKE,OAAO,CAAC;IAC/B,MAAMC,aAAaH,KAAKE,OAAO,CAAC;IAChC,MAAME,WAAWD,aAAa,CAAC,KAAMF,CAAAA,YAAY,KAAKE,aAAaF,SAAQ;IAE3E,IAAIG,YAAYH,YAAY,CAAC,GAAG;QAC9B,OAAO;YACLI,UAAUL,KAAKM,SAAS,CAAC,GAAGF,WAAWD,aAAaF;YACpDM,OAAOH,WACHJ,KAAKM,SAAS,CAACH,YAAYF,YAAY,CAAC,IAAIA,YAAYO,aACxD;YACJC,MAAMR,YAAY,CAAC,IAAID,KAAKU,KAAK,CAACT,aAAa;QACjD;IACF;IAEA,OAAO;QAAEI,UAAUL;QAAMO,OAAO;QAAIE,MAAM;IAAG;AAC/C", "ignoreList": [0]}}, {"offset": {"line": 3070, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3075, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/New%20folder%20%288%29/arabic-ecommerce/node_modules/next/src/shared/lib/router/utils/add-path-prefix.ts"], "sourcesContent": ["import { parsePath } from './parse-path'\n\n/**\n * Adds the provided prefix to the given path. It first ensures that the path\n * is indeed starting with a slash.\n */\nexport function addPathPrefix(path: string, prefix?: string) {\n  if (!path.startsWith('/') || !prefix) {\n    return path\n  }\n\n  const { pathname, query, hash } = parsePath(path)\n  return `${prefix}${pathname}${query}${hash}`\n}\n"], "names": ["addPathPrefix", "path", "prefix", "startsWith", "pathname", "query", "hash", "parsePath"], "mappings": ";;;;+BAMgBA,iBAAAA;;;eAAAA;;;2BANU;AAMnB,SAASA,cAAcC,IAAY,EAAEC,MAAe;IACzD,IAAI,CAACD,KAAKE,UAAU,CAAC,QAAQ,CAACD,QAAQ;QACpC,OAAOD;IACT;IAEA,MAAM,EAAEG,QAAQ,EAAEC,KAAK,EAAEC,IAAI,EAAE,GAAGC,CAAAA,GAAAA,WAAAA,SAAS,EAACN;IAC5C,OAAQ,KAAEC,SAASE,WAAWC,QAAQC;AACxC", "ignoreList": [0]}}, {"offset": {"line": 3093, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3098, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/New%20folder%20%288%29/arabic-ecommerce/node_modules/next/src/shared/lib/router/utils/remove-trailing-slash.ts"], "sourcesContent": ["/**\n * Removes the trailing slash for a given route or page path. Preserves the\n * root page. Examples:\n *   - `/foo/bar/` -> `/foo/bar`\n *   - `/foo/bar` -> `/foo/bar`\n *   - `/` -> `/`\n */\nexport function removeTrailingSlash(route: string) {\n  return route.replace(/\\/$/, '') || '/'\n}\n"], "names": ["removeTrailingSlash", "route", "replace"], "mappings": "AAAA;;;;;;CAMC,GAAA;;;;+BACeA,uBAAAA;;;eAAAA;;;AAAT,SAASA,oBAAoBC,KAAa;IAC/C,OAAOA,MAAMC,OAAO,CAAC,OAAO,OAAO;AACrC", "ignoreList": [0]}}, {"offset": {"line": 3117, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3122, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/New%20folder%20%288%29/arabic-ecommerce/node_modules/next/src/client/normalize-trailing-slash.ts"], "sourcesContent": ["import { removeTrailingSlash } from '../shared/lib/router/utils/remove-trailing-slash'\nimport { parsePath } from '../shared/lib/router/utils/parse-path'\n\n/**\n * Normalizes the trailing slash of a path according to the `trailingSlash` option\n * in `next.config.js`.\n */\nexport const normalizePathTrailingSlash = (path: string) => {\n  if (!path.startsWith('/') || process.env.__NEXT_MANUAL_TRAILING_SLASH) {\n    return path\n  }\n\n  const { pathname, query, hash } = parsePath(path)\n  if (process.env.__NEXT_TRAILING_SLASH) {\n    if (/\\.[^/]+\\/?$/.test(pathname)) {\n      return `${removeTrailingSlash(pathname)}${query}${hash}`\n    } else if (pathname.endsWith('/')) {\n      return `${pathname}${query}${hash}`\n    } else {\n      return `${pathname}/${query}${hash}`\n    }\n  }\n\n  return `${removeTrailingSlash(pathname)}${query}${hash}`\n}\n"], "names": ["normalizePathTrailingSlash", "path", "startsWith", "process", "env", "__NEXT_MANUAL_TRAILING_SLASH", "pathname", "query", "hash", "parsePath", "__NEXT_TRAILING_SLASH", "test", "removeTrailingSlash", "endsWith"], "mappings": ";;;;+BAOaA,8BAAAA;;;eAAAA;;;qCAPuB;2BACV;AAMnB,MAAMA,6BAA6B,CAACC;IACzC,IAAI,CAACA,KAAKC,UAAU,CAAC,QAAQC,QAAQC,GAAG,CAACC,4BAA4B,EAAE;QACrE,OAAOJ;IACT;IAEA,MAAM,EAAEK,QAAQ,EAAEC,KAAK,EAAEC,IAAI,EAAE,GAAGC,CAAAA,GAAAA,WAAAA,SAAS,EAACR;IAC5C,IAAIE,QAAQC,GAAG,CAACM,qBAAqB,EAAE;;IAQvC;IAEA,OAAQ,KAAEE,CAAAA,GAAAA,qBAAAA,mBAAmB,EAACN,YAAYC,QAAQC;AACpD", "ignoreList": [0]}}, {"offset": {"line": 3151, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3156, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/New%20folder%20%288%29/arabic-ecommerce/node_modules/next/src/client/add-base-path.ts"], "sourcesContent": ["import { addPathPrefix } from '../shared/lib/router/utils/add-path-prefix'\nimport { normalizePathTrailingSlash } from './normalize-trailing-slash'\n\nconst basePath = (process.env.__NEXT_ROUTER_BASEPATH as string) || ''\n\nexport function addBasePath(path: string, required?: boolean): string {\n  return normalizePathTrailingSlash(\n    process.env.__NEXT_MANUAL_CLIENT_BASE_PATH && !required\n      ? path\n      : addPathPrefix(path, basePath)\n  )\n}\n"], "names": ["addBasePath", "basePath", "process", "env", "__NEXT_ROUTER_BASEPATH", "path", "required", "normalizePathTrailingSlash", "__NEXT_MANUAL_CLIENT_BASE_PATH", "addPathPrefix"], "mappings": ";;;;+BAKg<PERSON>,eAAAA;;;eAAAA;;;+BALc;wCACa;AAE3C,MAAMC,WAAYC,QAAQC,GAAG,CAACC,sBAAsB,MAAe;AAE5D,SAASJ,YAAYK,IAAY,EAAEC,QAAkB;IAC1D,OAAOC,CAAAA,GAAAA,wBAAAA,0BAA0B,EAC/BL,QAAQC,GAAG,CAACK,8BAA8B,IAAI,CAACF,WAC3CD,mBACAI,CAAAA,GAAAA,eAAAA,aAAa,EAACJ,MAAMJ;AAE5B", "ignoreList": [0]}}, {"offset": {"line": 3179, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3184, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/New%20folder%20%288%29/arabic-ecommerce/node_modules/next/src/shared/lib/utils/warn-once.ts"], "sourcesContent": ["let warnOnce = (_: string) => {}\nif (process.env.NODE_ENV !== 'production') {\n  const warnings = new Set<string>()\n  warnOnce = (msg: string) => {\n    if (!warnings.has(msg)) {\n      console.warn(msg)\n    }\n    warnings.add(msg)\n  }\n}\n\nexport { warnOnce }\n"], "names": ["warnOnce", "_", "process", "env", "NODE_ENV", "warnings", "Set", "msg", "has", "console", "warn", "add"], "mappings": ";;;;+BAWS<PERSON>,YAAAA;;;eAAAA;;;AAXT,IAAIA,WAAW,CAACC,KAAe;AAC/B,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAc;IACzC,MAAMC,WAAW,IAAIC;IACrBN,WAAW,CAACO;QACV,IAAI,CAACF,SAASG,GAAG,CAACD,MAAM;YACtBE,QAAQC,IAAI,CAACH;QACf;QACAF,SAASM,GAAG,CAACJ;IACf;AACF", "ignoreList": [0]}}, {"offset": {"line": 3204, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3209, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/New%20folder%20%288%29/arabic-ecommerce/node_modules/next/src/client/app-dir/link.tsx"], "sourcesContent": ["'use client'\n\nimport type { NextRouter } from '../../shared/lib/router/router'\n\nimport React from 'react'\nimport type { UrlObject } from 'url'\nimport { formatUrl } from '../../shared/lib/router/utils/format-url'\nimport { AppRouterContext } from '../../shared/lib/app-router-context.shared-runtime'\nimport type { AppRouterInstance } from '../../shared/lib/app-router-context.shared-runtime'\nimport type { PrefetchOptions } from '../../shared/lib/app-router-context.shared-runtime'\nimport { useIntersection } from '../use-intersection'\nimport { PrefetchKind } from '../components/router-reducer/router-reducer-types'\nimport { useMergedRef } from '../use-merged-ref'\nimport { isAbsoluteUrl } from '../../shared/lib/utils'\nimport { addBasePath } from '../add-base-path'\nimport { warnOnce } from '../../shared/lib/utils/warn-once'\n\ntype Url = string | UrlObject\ntype RequiredKeys<T> = {\n  [K in keyof T]-?: {} extends Pick<T, K> ? never : K\n}[keyof T]\ntype OptionalKeys<T> = {\n  [K in keyof T]-?: {} extends Pick<T, K> ? K : never\n}[keyof T]\n\ntype InternalLinkProps = {\n  /**\n   * The path or URL to navigate to. It can also be an object.\n   *\n   * @example https://nextjs.org/docs/api-reference/next/link#with-url-object\n   */\n  href: Url\n  /**\n   * Optional decorator for the path that will be shown in the browser URL bar. Before Next.js 9.5.3 this was used for dynamic routes, check our [previous docs](https://github.com/vercel/next.js/blob/v9.5.2/docs/api-reference/next/link.md#dynamic-routes) to see how it worked. Note: when this path differs from the one provided in `href` the previous `href`/`as` behavior is used as shown in the [previous docs](https://github.com/vercel/next.js/blob/v9.5.2/docs/api-reference/next/link.md#dynamic-routes).\n   */\n  as?: Url\n  /**\n   * Replace the current `history` state instead of adding a new url into the stack.\n   *\n   * @defaultValue `false`\n   */\n  replace?: boolean\n  /**\n   * Whether to override the default scroll behavior\n   *\n   * @example https://nextjs.org/docs/api-reference/next/link#disable-scrolling-to-the-top-of-the-page\n   *\n   * @defaultValue `true`\n   */\n  scroll?: boolean\n  /**\n   * Update the path of the current page without rerunning [`getStaticProps`](https://nextjs.org/docs/pages/building-your-application/data-fetching/get-static-props), [`getServerSideProps`](https://nextjs.org/docs/pages/building-your-application/data-fetching/get-server-side-props) or [`getInitialProps`](/docs/pages/api-reference/functions/get-initial-props).\n   *\n   * @defaultValue `false`\n   */\n  shallow?: boolean\n  /**\n   * Forces `Link` to send the `href` property to its child.\n   *\n   * @defaultValue `false`\n   */\n  passHref?: boolean\n  /**\n   * Prefetch the page in the background.\n   * Any `<Link />` that is in the viewport (initially or through scroll) will be prefetched.\n   * Prefetch can be disabled by passing `prefetch={false}`. Prefetching is only enabled in production.\n   *\n   * In App Router:\n   * - `null` (default): For statically generated pages, this will prefetch the full React Server Component data. For dynamic pages, this will prefetch up to the nearest route segment with a [`loading.js`](https://nextjs.org/docs/app/api-reference/file-conventions/loading) file. If there is no loading file, it will not fetch the full tree to avoid fetching too much data.\n   * - `true`: This will prefetch the full React Server Component data for all route segments, regardless of whether they contain a segment with `loading.js`.\n   * - `false`: This will not prefetch any data, even on hover.\n   *\n   * In Pages Router:\n   * - `true` (default): The full route & its data will be prefetched.\n   * - `false`: Prefetching will not happen when entering the viewport, but will still happen on hover.\n   * @defaultValue `true` (pages router) or `null` (app router)\n   */\n  prefetch?: boolean | null\n  /**\n   * The active locale is automatically prepended. `locale` allows for providing a different locale.\n   * When `false` `href` has to include the locale as the default behavior is disabled.\n   * Note: This is only available in the Pages Router.\n   */\n  locale?: string | false\n  /**\n   * Enable legacy link behavior.\n   * @defaultValue `false`\n   * @see https://github.com/vercel/next.js/commit/489e65ed98544e69b0afd7e0cfc3f9f6c2b803b7\n   */\n  legacyBehavior?: boolean\n  /**\n   * Optional event handler for when the mouse pointer is moved onto Link\n   */\n  onMouseEnter?: React.MouseEventHandler<HTMLAnchorElement>\n  /**\n   * Optional event handler for when Link is touched.\n   */\n  onTouchStart?: React.TouchEventHandler<HTMLAnchorElement>\n  /**\n   * Optional event handler for when Link is clicked.\n   */\n  onClick?: React.MouseEventHandler<HTMLAnchorElement>\n}\n\n// TODO-APP: Include the full set of Anchor props\n// adding this to the publicly exported type currently breaks existing apps\n\n// `RouteInferType` is a stub here to avoid breaking `typedRoutes` when the type\n// isn't generated yet. It will be replaced when the webpack plugin runs.\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nexport type LinkProps<RouteInferType = any> = InternalLinkProps\ntype LinkPropsRequired = RequiredKeys<LinkProps>\ntype LinkPropsOptional = OptionalKeys<Omit<InternalLinkProps, 'locale'>>\n\nfunction prefetch(\n  router: AppRouterInstance,\n  href: string,\n  options: PrefetchOptions\n): void {\n  if (typeof window === 'undefined') {\n    return\n  }\n\n  const doPrefetch = async () => {\n    // note that `appRouter.prefetch()` is currently sync,\n    // so we have to wrap this call in an async function to be able to catch() errors below.\n    return router.prefetch(href, options)\n  }\n\n  // Prefetch the page if asked (only in the client)\n  // We need to handle a prefetch error here since we may be\n  // loading with priority which can reject but we don't\n  // want to force navigation since this is only a prefetch\n  doPrefetch().catch((err) => {\n    if (process.env.NODE_ENV !== 'production') {\n      // rethrow to show invalid URL errors\n      throw err\n    }\n  })\n}\n\nfunction isModifiedEvent(event: React.MouseEvent): boolean {\n  const eventTarget = event.currentTarget as HTMLAnchorElement | SVGAElement\n  const target = eventTarget.getAttribute('target')\n  return (\n    (target && target !== '_self') ||\n    event.metaKey ||\n    event.ctrlKey ||\n    event.shiftKey ||\n    event.altKey || // triggers resource download\n    (event.nativeEvent && event.nativeEvent.which === 2)\n  )\n}\n\nfunction linkClicked(\n  e: React.MouseEvent,\n  router: NextRouter | AppRouterInstance,\n  href: string,\n  as: string,\n  replace?: boolean,\n  shallow?: boolean,\n  scroll?: boolean\n): void {\n  const { nodeName } = e.currentTarget\n\n  // anchors inside an svg have a lowercase nodeName\n  const isAnchorNodeName = nodeName.toUpperCase() === 'A'\n\n  if (isAnchorNodeName && isModifiedEvent(e)) {\n    // ignore click for browser’s default behavior\n    return\n  }\n\n  e.preventDefault()\n\n  const navigate = () => {\n    // If the router is an NextRouter instance it will have `beforePopState`\n    const routerScroll = scroll ?? true\n    if ('beforePopState' in router) {\n      router[replace ? 'replace' : 'push'](href, as, {\n        shallow,\n        scroll: routerScroll,\n      })\n    } else {\n      router[replace ? 'replace' : 'push'](as || href, {\n        scroll: routerScroll,\n      })\n    }\n  }\n\n  React.startTransition(navigate)\n}\n\ntype LinkPropsReal = React.PropsWithChildren<\n  Omit<React.AnchorHTMLAttributes<HTMLAnchorElement>, keyof LinkProps> &\n    LinkProps\n>\n\nfunction formatStringOrUrl(urlObjOrString: UrlObject | string): string {\n  if (typeof urlObjOrString === 'string') {\n    return urlObjOrString\n  }\n\n  return formatUrl(urlObjOrString)\n}\n\n/**\n * A React component that extends the HTML `<a>` element to provide [prefetching](https://nextjs.org/docs/app/building-your-application/routing/linking-and-navigating#2-prefetching)\n * and client-side navigation between routes.\n *\n * It is the primary way to navigate between routes in Next.js.\n *\n * Read more: [Next.js docs: `<Link>`](https://nextjs.org/docs/app/api-reference/components/link)\n */\nconst Link = React.forwardRef<HTMLAnchorElement, LinkPropsReal>(\n  function LinkComponent(props, forwardedRef) {\n    let children: React.ReactNode\n\n    const {\n      href: hrefProp,\n      as: asProp,\n      children: childrenProp,\n      prefetch: prefetchProp = null,\n      passHref,\n      replace,\n      shallow,\n      scroll,\n      onClick,\n      onMouseEnter: onMouseEnterProp,\n      onTouchStart: onTouchStartProp,\n      legacyBehavior = false,\n      ...restProps\n    } = props\n\n    children = childrenProp\n\n    if (\n      legacyBehavior &&\n      (typeof children === 'string' || typeof children === 'number')\n    ) {\n      children = <a>{children}</a>\n    }\n\n    const router = React.useContext(AppRouterContext)\n\n    const prefetchEnabled = prefetchProp !== false\n    /**\n     * The possible states for prefetch are:\n     * - null: this is the default \"auto\" mode, where we will prefetch partially if the link is in the viewport\n     * - true: we will prefetch if the link is visible and prefetch the full page, not just partially\n     * - false: we will not prefetch if in the viewport at all\n     */\n    const appPrefetchKind =\n      prefetchProp === null ? PrefetchKind.AUTO : PrefetchKind.FULL\n\n    if (process.env.NODE_ENV !== 'production') {\n      function createPropError(args: {\n        key: string\n        expected: string\n        actual: string\n      }) {\n        return new Error(\n          `Failed prop type: The prop \\`${args.key}\\` expects a ${args.expected} in \\`<Link>\\`, but got \\`${args.actual}\\` instead.` +\n            (typeof window !== 'undefined'\n              ? \"\\nOpen your browser's console to view the Component stack trace.\"\n              : '')\n        )\n      }\n\n      // TypeScript trick for type-guarding:\n      const requiredPropsGuard: Record<LinkPropsRequired, true> = {\n        href: true,\n      } as const\n      const requiredProps: LinkPropsRequired[] = Object.keys(\n        requiredPropsGuard\n      ) as LinkPropsRequired[]\n      requiredProps.forEach((key: LinkPropsRequired) => {\n        if (key === 'href') {\n          if (\n            props[key] == null ||\n            (typeof props[key] !== 'string' && typeof props[key] !== 'object')\n          ) {\n            throw createPropError({\n              key,\n              expected: '`string` or `object`',\n              actual: props[key] === null ? 'null' : typeof props[key],\n            })\n          }\n        } else {\n          // TypeScript trick for type-guarding:\n          // eslint-disable-next-line @typescript-eslint/no-unused-vars\n          const _: never = key\n        }\n      })\n\n      // TypeScript trick for type-guarding:\n      const optionalPropsGuard: Record<LinkPropsOptional, true> = {\n        as: true,\n        replace: true,\n        scroll: true,\n        shallow: true,\n        passHref: true,\n        prefetch: true,\n        onClick: true,\n        onMouseEnter: true,\n        onTouchStart: true,\n        legacyBehavior: true,\n      } as const\n      const optionalProps: LinkPropsOptional[] = Object.keys(\n        optionalPropsGuard\n      ) as LinkPropsOptional[]\n      optionalProps.forEach((key: LinkPropsOptional) => {\n        const valType = typeof props[key]\n\n        if (key === 'as') {\n          if (props[key] && valType !== 'string' && valType !== 'object') {\n            throw createPropError({\n              key,\n              expected: '`string` or `object`',\n              actual: valType,\n            })\n          }\n        } else if (\n          key === 'onClick' ||\n          key === 'onMouseEnter' ||\n          key === 'onTouchStart'\n        ) {\n          if (props[key] && valType !== 'function') {\n            throw createPropError({\n              key,\n              expected: '`function`',\n              actual: valType,\n            })\n          }\n        } else if (\n          key === 'replace' ||\n          key === 'scroll' ||\n          key === 'shallow' ||\n          key === 'passHref' ||\n          key === 'prefetch' ||\n          key === 'legacyBehavior'\n        ) {\n          if (props[key] != null && valType !== 'boolean') {\n            throw createPropError({\n              key,\n              expected: '`boolean`',\n              actual: valType,\n            })\n          }\n        } else {\n          // TypeScript trick for type-guarding:\n          // eslint-disable-next-line @typescript-eslint/no-unused-vars\n          const _: never = key\n        }\n      })\n    }\n\n    if (process.env.NODE_ENV !== 'production') {\n      if (props.locale) {\n        warnOnce(\n          'The `locale` prop is not supported in `next/link` while using the `app` router. Read more about app router internalization: https://nextjs.org/docs/app/building-your-application/routing/internationalization'\n        )\n      }\n      if (!asProp) {\n        let href: string | undefined\n        if (typeof hrefProp === 'string') {\n          href = hrefProp\n        } else if (\n          typeof hrefProp === 'object' &&\n          typeof hrefProp.pathname === 'string'\n        ) {\n          href = hrefProp.pathname\n        }\n\n        if (href) {\n          const hasDynamicSegment = href\n            .split('/')\n            .some((segment) => segment.startsWith('[') && segment.endsWith(']'))\n\n          if (hasDynamicSegment) {\n            throw new Error(\n              `Dynamic href \\`${href}\\` found in <Link> while using the \\`/app\\` router, this is not supported. Read more: https://nextjs.org/docs/messages/app-dir-dynamic-href`\n            )\n          }\n        }\n      }\n    }\n\n    const { href, as } = React.useMemo(() => {\n      const resolvedHref = formatStringOrUrl(hrefProp)\n      return {\n        href: resolvedHref,\n        as: asProp ? formatStringOrUrl(asProp) : resolvedHref,\n      }\n    }, [hrefProp, asProp])\n\n    const previousHref = React.useRef<string>(href)\n    const previousAs = React.useRef<string>(as)\n\n    // This will return the first child, if multiple are provided it will throw an error\n    let child: any\n    if (legacyBehavior) {\n      if (process.env.NODE_ENV === 'development') {\n        if (onClick) {\n          console.warn(\n            `\"onClick\" was passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but \"legacyBehavior\" was set. The legacy behavior requires onClick be set on the child of next/link`\n          )\n        }\n        if (onMouseEnterProp) {\n          console.warn(\n            `\"onMouseEnter\" was passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but \"legacyBehavior\" was set. The legacy behavior requires onMouseEnter be set on the child of next/link`\n          )\n        }\n        try {\n          child = React.Children.only(children)\n        } catch (err) {\n          if (!children) {\n            throw new Error(\n              `No children were passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but one child is required https://nextjs.org/docs/messages/link-no-children`\n            )\n          }\n          throw new Error(\n            `Multiple children were passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but only one child is supported https://nextjs.org/docs/messages/link-multiple-children` +\n              (typeof window !== 'undefined'\n                ? \" \\nOpen your browser's console to view the Component stack trace.\"\n                : '')\n          )\n        }\n      } else {\n        child = React.Children.only(children)\n      }\n    } else {\n      if (process.env.NODE_ENV === 'development') {\n        if ((children as any)?.type === 'a') {\n          throw new Error(\n            'Invalid <Link> with <a> child. Please remove <a> or use <Link legacyBehavior>.\\nLearn more: https://nextjs.org/docs/messages/invalid-new-link-with-extra-anchor'\n          )\n        }\n      }\n    }\n\n    const childRef: any = legacyBehavior\n      ? child && typeof child === 'object' && child.ref\n      : forwardedRef\n\n    const [setIntersectionRef, isVisible, resetVisible] = useIntersection({\n      rootMargin: '200px',\n    })\n\n    const setIntersectionWithResetRef = React.useCallback(\n      (el: Element) => {\n        // Before the link getting observed, check if visible state need to be reset\n        if (previousAs.current !== as || previousHref.current !== href) {\n          resetVisible()\n          previousAs.current = as\n          previousHref.current = href\n        }\n\n        setIntersectionRef(el)\n      },\n      [as, href, resetVisible, setIntersectionRef]\n    )\n\n    const setRef = useMergedRef(setIntersectionWithResetRef, childRef)\n\n    // Prefetch the URL if we haven't already and it's visible.\n    React.useEffect(() => {\n      // in dev, we only prefetch on hover to avoid wasting resources as the prefetch will trigger compiling the page.\n      if (process.env.NODE_ENV !== 'production') {\n        return\n      }\n\n      if (!router) {\n        return\n      }\n\n      // If we don't need to prefetch the URL, don't do prefetch.\n      if (!isVisible || !prefetchEnabled) {\n        return\n      }\n\n      // Prefetch the URL.\n      prefetch(router, href, {\n        kind: appPrefetchKind,\n      })\n    }, [as, href, isVisible, prefetchEnabled, router, appPrefetchKind])\n\n    const childProps: {\n      onTouchStart?: React.TouchEventHandler<HTMLAnchorElement>\n      onMouseEnter: React.MouseEventHandler<HTMLAnchorElement>\n      onClick: React.MouseEventHandler<HTMLAnchorElement>\n      href?: string\n      ref?: any\n    } = {\n      ref: setRef,\n      onClick(e) {\n        if (process.env.NODE_ENV !== 'production') {\n          if (!e) {\n            throw new Error(\n              `Component rendered inside next/link has to pass click event to \"onClick\" prop.`\n            )\n          }\n        }\n\n        if (!legacyBehavior && typeof onClick === 'function') {\n          onClick(e)\n        }\n\n        if (\n          legacyBehavior &&\n          child.props &&\n          typeof child.props.onClick === 'function'\n        ) {\n          child.props.onClick(e)\n        }\n\n        if (!router) {\n          return\n        }\n\n        if (e.defaultPrevented) {\n          return\n        }\n\n        linkClicked(e, router, href, as, replace, shallow, scroll)\n      },\n      onMouseEnter(e) {\n        if (!legacyBehavior && typeof onMouseEnterProp === 'function') {\n          onMouseEnterProp(e)\n        }\n\n        if (\n          legacyBehavior &&\n          child.props &&\n          typeof child.props.onMouseEnter === 'function'\n        ) {\n          child.props.onMouseEnter(e)\n        }\n\n        if (!router) {\n          return\n        }\n\n        if (!prefetchEnabled || process.env.NODE_ENV === 'development') {\n          return\n        }\n\n        prefetch(router, href, {\n          kind: appPrefetchKind,\n        })\n      },\n      onTouchStart: process.env.__NEXT_LINK_NO_TOUCH_START\n        ? undefined\n        : function onTouchStart(e) {\n            if (!legacyBehavior && typeof onTouchStartProp === 'function') {\n              onTouchStartProp(e)\n            }\n\n            if (\n              legacyBehavior &&\n              child.props &&\n              typeof child.props.onTouchStart === 'function'\n            ) {\n              child.props.onTouchStart(e)\n            }\n\n            if (!router) {\n              return\n            }\n\n            if (!prefetchEnabled) {\n              return\n            }\n\n            prefetch(router, href, {\n              kind: appPrefetchKind,\n            })\n          },\n    }\n\n    // If child is an <a> tag and doesn't have a href attribute, or if the 'passHref' property is\n    // defined, we specify the current 'href', so that repetition is not needed by the user.\n    // If the url is absolute, we can bypass the logic to prepend the basePath.\n    if (isAbsoluteUrl(as)) {\n      childProps.href = as\n    } else if (\n      !legacyBehavior ||\n      passHref ||\n      (child.type === 'a' && !('href' in child.props))\n    ) {\n      childProps.href = addBasePath(as)\n    }\n\n    return legacyBehavior ? (\n      React.cloneElement(child, childProps)\n    ) : (\n      <a {...restProps} {...childProps}>\n        {children}\n      </a>\n    )\n  }\n)\n\nexport default Link\n"], "names": ["prefetch", "router", "href", "options", "window", "doPrefetch", "catch", "err", "process", "env", "NODE_ENV", "isModifiedEvent", "event", "eventTarget", "currentTarget", "target", "getAttribute", "metaKey", "ctrl<PERSON>ey", "shift<PERSON>ey", "altKey", "nativeEvent", "which", "linkClicked", "e", "as", "replace", "shallow", "scroll", "nodeName", "isAnchorNodeName", "toUpperCase", "preventDefault", "navigate", "routerScroll", "React", "startTransition", "formatStringOrUrl", "urlObjOrString", "formatUrl", "Link", "forwardRef", "LinkComponent", "props", "forwardedRef", "children", "hrefProp", "asProp", "childrenProp", "prefetchProp", "passHref", "onClick", "onMouseEnter", "onMouseEnterProp", "onTouchStart", "onTouchStartProp", "legacyBeh<PERSON>or", "restProps", "a", "useContext", "AppRouterContext", "prefetchEnabled", "appPrefetchKind", "PrefetchKind", "AUTO", "FULL", "createPropError", "args", "Error", "key", "expected", "actual", "requiredPropsGuard", "requiredProps", "Object", "keys", "for<PERSON>ach", "_", "optionalPropsGuard", "optionalProps", "valType", "locale", "warnOnce", "pathname", "hasDynamicSegment", "split", "some", "segment", "startsWith", "endsWith", "useMemo", "resolvedHref", "previousHref", "useRef", "previousAs", "child", "console", "warn", "Children", "only", "type", "childRef", "ref", "setIntersectionRef", "isVisible", "resetVisible", "useIntersection", "rootMargin", "setIntersectionWithResetRef", "useCallback", "el", "current", "setRef", "useMergedRef", "useEffect", "kind", "childProps", "defaultPrevented", "__NEXT_LINK_NO_TOUCH_START", "undefined", "isAbsoluteUrl", "addBasePath", "cloneElement"], "mappings": "AAAA;;;;;+BA2l<PERSON>,WAAA;;;eAAA;;;;;gEAvlBkB;2BAEQ;+CACO;iCAGD;oCACH;8BACA;uBACC;6BACF;0BACH;AAmGzB,SAASA,SACPC,MAAyB,EACzBC,IAAY,EACZC,OAAwB;IAExB,IAAI,OAAOC,WAAW,aAAa;QACjC;IACF;IAEA,MAAMC,aAAa;QACjB,sDAAsD;QACtD,wFAAwF;QACxF,OAAOJ,OAAOD,QAAQ,CAACE,MAAMC;IAC/B;IAEA,kDAAkD;IAClD,0DAA0D;IAC1D,sDAAsD;IACtD,yDAAyD;IACzDE,aAAaC,KAAK,CAAC,CAACC;QAClB,IAAIC,QAAQC,GAAG,CAACC,QAAQ,gCAAK,cAAc;YACzC,qCAAqC;YACrC,MAAMH;QACR;IACF;AACF;AAEA,SAASI,gBAAgBC,KAAuB;IAC9C,MAAMC,cAAcD,MAAME,aAAa;IACvC,MAAMC,SAASF,YAAYG,YAAY,CAAC;IACxC,OACGD,UAAUA,WAAW,WACtBH,MAAMK,OAAO,IACbL,MAAMM,OAAO,IACbN,MAAMO,QAAQ,IACdP,MAAMQ,MAAM,IAAI,6BAA6B;IAC5CR,MAAMS,WAAW,IAAIT,MAAMS,WAAW,CAACC,KAAK,KAAK;AAEtD;AAEA,SAASC,YACPC,CAAmB,EACnBvB,MAAsC,EACtCC,IAAY,EACZuB,EAAU,EACVC,OAAiB,EACjBC,OAAiB,EACjBC,MAAgB;IAEhB,MAAM,EAAEC,QAAQ,EAAE,GAAGL,EAAEV,aAAa;IAEpC,kDAAkD;IAClD,MAAMgB,mBAAmBD,SAASE,WAAW,OAAO;IAEpD,IAAID,oBAAoBnB,gBAAgBa,IAAI;QAC1C,8CAA8C;QAC9C;IACF;IAEAA,EAAEQ,cAAc;IAEhB,MAAMC,WAAW;QACf,wEAAwE;QACxE,MAAMC,eAAeN,UAAAA,OAAAA,SAAU;QAC/B,IAAI,oBAAoB3B,QAAQ;YAC9BA,MAAM,CAACyB,UAAU,YAAY,OAAO,CAACxB,MAAMuB,IAAI;gBAC7CE;gBACAC,QAAQM;YACV;QACF,OAAO;YACLjC,MAAM,CAACyB,UAAU,YAAY,OAAO,CAACD,MAAMvB,MAAM;gBAC/C0B,QAAQM;YACV;QACF;IACF;IAEAC,OAAAA,OAAK,CAACC,eAAe,CAACH;AACxB;AAOA,SAASI,kBAAkBC,cAAkC;IAC3D,IAAI,OAAOA,mBAAmB,UAAU;QACtC,OAAOA;IACT;IAEA,OAAOC,CAAAA,GAAAA,WAAAA,SAAS,EAACD;AACnB;AAEA;;;;;;;CAOC,GACD,MAAME,OAAAA,WAAAA,GAAOL,OAAAA,OAAK,CAACM,UAAU,CAC3B,SAASC,cAAcC,KAAK,EAAEC,YAAY;IACxC,IAAIC;IAEJ,MAAM,EACJ3C,MAAM4C,QAAQ,EACdrB,IAAIsB,MAAM,EACVF,UAAUG,YAAY,EACtBhD,UAAUiD,eAAe,IAAI,EAC7BC,QAAQ,EACRxB,OAAO,EACPC,OAAO,EACPC,MAAM,EACNuB,OAAO,EACPC,cAAcC,gBAAgB,EAC9BC,cAAcC,gBAAgB,EAC9BC,iBAAiB,KAAK,EACtB,GAAGC,WACJ,GAAGd;IAEJE,WAAWG;IAEX,IACEQ,kBACC,CAAA,OAAOX,aAAa,YAAY,OAAOA,aAAa,QAAO,GAC5D;QACAA,WAAAA,WAAAA,GAAW,CAAA,GAAA,YAAA,GAAA,EAACa,KAAAA;sBAAGb;;IACjB;IAEA,MAAM5C,SAASkC,OAAAA,OAAK,CAACwB,UAAU,CAACC,+BAAAA,gBAAgB;IAEhD,MAAMC,kBAAkBZ,iBAAiB;IACzC;;;;;KAKC,GACD,MAAMa,kBACJb,iBAAiB,OAAOc,oBAAAA,YAAY,CAACC,IAAI,GAAGD,oBAAAA,YAAY,CAACE,IAAI;IAE/D,IAAIzD,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAc;QACzC,SAASwD,gBAAgBC,IAIxB;YACC,OAAO,IAAIC,MACR,iCAA+BD,KAAKE,GAAG,GAAC,iBAAeF,KAAKG,QAAQ,GAAC,4BAA4BH,KAAKI,MAAM,GAAC,eAC3G,CAAA,OAAOnE,WAAW,cACf,qEACA,EAAC;QAEX;QAEA,sCAAsC;QACtC,MAAMoE,qBAAsD;YAC1DtE,MAAM;QACR;QACA,MAAMuE,gBAAqCC,OAAOC,IAAI,CACpDH;QAEFC,cAAcG,OAAO,CAAC,CAACP;YACrB,IAAIA,QAAQ,QAAQ;gBAClB,IACE1B,KAAK,CAAC0B,IAAI,IAAI,QACb,OAAO1B,KAAK,CAAC0B,IAAI,KAAK,YAAY,OAAO1B,KAAK,CAAC0B,IAAI,KAAK,UACzD;oBACA,MAAMH,gBAAgB;wBACpBG;wBACAC,UAAU;wBACVC,QAAQ5B,KAAK,CAAC0B,IAAI,KAAK,OAAO,SAAS,OAAO1B,KAAK,CAAC0B,IAAI;oBAC1D;gBACF;YACF,OAAO;gBACL,sCAAsC;gBACtC,6DAA6D;gBAC7D,MAAMQ,IAAWR;YACnB;QACF;QAEA,sCAAsC;QACtC,MAAMS,qBAAsD;YAC1DrD,IAAI;YACJC,SAAS;YACTE,QAAQ;YACRD,SAAS;YACTuB,UAAU;YACVlD,UAAU;YACVmD,SAAS;YACTC,cAAc;YACdE,cAAc;YACdE,gBAAgB;QAClB;QACA,MAAMuB,gBAAqCL,OAAOC,IAAI,CACpDG;QAEFC,cAAcH,OAAO,CAAC,CAACP;YACrB,MAAMW,UAAU,OAAOrC,KAAK,CAAC0B,IAAI;YAEjC,IAAIA,QAAQ,MAAM;gBAChB,IAAI1B,KAAK,CAAC0B,IAAI,IAAIW,YAAY,YAAYA,YAAY,UAAU;oBAC9D,MAAMd,gBAAgB;wBACpBG;wBACAC,UAAU;wBACVC,QAAQS;oBACV;gBACF;YACF,OAAO,IACLX,QAAQ,aACRA,QAAQ,kBACRA,QAAQ,gBACR;gBACA,IAAI1B,KAAK,CAAC0B,IAAI,IAAIW,YAAY,YAAY;oBACxC,MAAMd,gBAAgB;wBACpBG;wBACAC,UAAU;wBACVC,QAAQS;oBACV;gBACF;YACF,OAAO,IACLX,QAAQ,aACRA,QAAQ,YACRA,QAAQ,aACRA,QAAQ,cACRA,QAAQ,cACRA,QAAQ,kBACR;gBACA,IAAI1B,KAAK,CAAC0B,IAAI,IAAI,QAAQW,YAAY,WAAW;oBAC/C,MAAMd,gBAAgB;wBACpBG;wBACAC,UAAU;wBACVC,QAAQS;oBACV;gBACF;YACF,OAAO;gBACL,sCAAsC;gBACtC,6DAA6D;gBAC7D,MAAMH,IAAWR;YACnB;QACF;IACF;IAEA,IAAI7D,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAc;QACzC,IAAIiC,MAAMsC,MAAM,EAAE;YAChBC,CAAAA,GAAAA,UAAAA,QAAQ,EACN;QAEJ;QACA,IAAI,CAACnC,QAAQ;YACX,IAAI7C;YACJ,IAAI,OAAO4C,aAAa,UAAU;gBAChC5C,OAAO4C;YACT,OAAO,IACL,OAAOA,aAAa,YACpB,OAAOA,SAASqC,QAAQ,KAAK,UAC7B;gBACAjF,OAAO4C,SAASqC,QAAQ;YAC1B;YAEA,IAAIjF,MAAM;gBACR,MAAMkF,oBAAoBlF,KACvBmF,KAAK,CAAC,KACNC,IAAI,CAAC,CAACC,UAAYA,QAAQC,UAAU,CAAC,QAAQD,QAAQE,QAAQ,CAAC;gBAEjE,IAAIL,mBAAmB;oBACrB,MAAM,IAAIhB,MACP,mBAAiBlE,OAAK;gBAE3B;YACF;QACF;IACF;IAEA,MAAM,EAAEA,IAAI,EAAEuB,EAAE,EAAE,GAAGU,OAAAA,OAAK,CAACuD,OAAO,CAAC;QACjC,MAAMC,eAAetD,kBAAkBS;QACvC,OAAO;YACL5C,MAAMyF;YACNlE,IAAIsB,SAASV,kBAAkBU,UAAU4C;QAC3C;IACF,GAAG;QAAC7C;QAAUC;KAAO;IAErB,MAAM6C,eAAezD,OAAAA,OAAK,CAAC0D,MAAM,CAAS3F;IAC1C,MAAM4F,aAAa3D,OAAAA,OAAK,CAAC0D,MAAM,CAASpE;IAExC,oFAAoF;IACpF,IAAIsE;IACJ,IAAIvC,gBAAgB;QAClB,IAAIhD,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAe;YAC1C,IAAIyC,SAAS;gBACX6C,QAAQC,IAAI,CACT,oDAAoDnD,WAAS;YAElE;YACA,IAAIO,kBAAkB;gBACpB2C,QAAQC,IAAI,CACT,yDAAyDnD,WAAS;YAEvE;YACA,IAAI;gBACFiD,QAAQ5D,OAAAA,OAAK,CAAC+D,QAAQ,CAACC,IAAI,CAACtD;YAC9B,EAAE,OAAOtC,KAAK;gBACZ,IAAI,CAACsC,UAAU;oBACb,MAAM,IAAIuB,MACP,uDAAuDtB,WAAS;gBAErE;gBACA,MAAM,IAAIsB,MACP,6DAA6DtB,WAAS,8FACpE,CAAA,OAAO1C,WAAW,cACf,sEACA,EAAC;YAEX;QACF,OAAO;;QAEP;IACF,OAAO;QACL,IAAII,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAe;YAC1C,IAAI,CAACmC,YAAAA,OAAAA,KAAAA,IAAAA,SAAkBuD,IAAI,MAAK,KAAK;gBACnC,MAAM,IAAIhC,MACR;YAEJ;QACF;IACF;IAEA,MAAMiC,WAAgB7C,iBAClBuC,SAAS,OAAOA,UAAU,YAAYA,MAAMO,GAAG,GAC/C1D;IAEJ,MAAM,CAAC2D,oBAAoBC,WAAWC,aAAa,GAAGC,CAAAA,GAAAA,iBAAAA,eAAe,EAAC;QACpEC,YAAY;IACd;IAEA,MAAMC,8BAA8BzE,OAAAA,OAAK,CAAC0E,WAAW,CACnD,CAACC;QACC,4EAA4E;QAC5E,IAAIhB,WAAWiB,OAAO,KAAKtF,MAAMmE,aAAamB,OAAO,KAAK7G,MAAM;YAC9DuG;YACAX,WAAWiB,OAAO,GAAGtF;YACrBmE,aAAamB,OAAO,GAAG7G;QACzB;QAEAqG,mBAAmBO;IACrB,GACA;QAACrF;QAAIvB;QAAMuG;QAAcF;KAAmB;IAG9C,MAAMS,SAASC,CAAAA,GAAAA,cAAAA,YAAY,EAACL,6BAA6BP;IAEzD,2DAA2D;IAC3DlE,OAAAA,OAAK,CAAC+E,SAAS,CAAC;QACd,gHAAgH;QAChH,IAAI1G,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAc;YACzC;QACF;;IAeF,GAAG;QAACe;QAAIvB;QAAMsG;QAAW3C;QAAiB5D;QAAQ6D;KAAgB;IAElE,MAAMsD,aAMF;QACFd,KAAKU;QACL7D,SAAQ3B,CAAC;YACP,IAAIhB,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAc;gBACzC,IAAI,CAACc,GAAG;oBACN,MAAM,IAAI4C,MACP;gBAEL;YACF;YAEA,IAAI,CAACZ,kBAAkB,OAAOL,YAAY,YAAY;gBACpDA,QAAQ3B;YACV;YAEA,IACEgC,kBACAuC,MAAMpD,KAAK,IACX,OAAOoD,MAAMpD,KAAK,CAACQ,OAAO,KAAK,YAC/B;gBACA4C,MAAMpD,KAAK,CAACQ,OAAO,CAAC3B;YACtB;YAEA,IAAI,CAACvB,QAAQ;gBACX;YACF;YAEA,IAAIuB,EAAE6F,gBAAgB,EAAE;gBACtB;YACF;YAEA9F,YAAYC,GAAGvB,QAAQC,MAAMuB,IAAIC,SAASC,SAASC;QACrD;QACAwB,cAAa5B,CAAC;YACZ,IAAI,CAACgC,kBAAkB,OAAOH,qBAAqB,YAAY;gBAC7DA,iBAAiB7B;YACnB;YAEA,IACEgC,kBACAuC,MAAMpD,KAAK,IACX,OAAOoD,MAAMpD,KAAK,CAACS,YAAY,KAAK,YACpC;gBACA2C,MAAMpD,KAAK,CAACS,YAAY,CAAC5B;YAC3B;YAEA,IAAI,CAACvB,QAAQ;gBACX;YACF;YAEA,IAAI,CAAC4D,mBAAmBrD,QAAQC,GAAG,CAACC,IAA4B,IAApB,KAAK;gBAC/C;YACF;;QAKF;QACA4C,cAAc9C,QAAQC,GAAG,CAAC6G,0BAA0B,GAChDC,oCACA,SAASjE,aAAa9B,CAAC;YACrB,IAAI,CAACgC,kBAAkB,OAAOD,qBAAqB,YAAY;gBAC7DA,iBAAiB/B;YACnB;YAEA,IACEgC,kBACAuC,MAAMpD,KAAK,IACX,OAAOoD,MAAMpD,KAAK,CAACW,YAAY,KAAK,YACpC;gBACAyC,MAAMpD,KAAK,CAACW,YAAY,CAAC9B;YAC3B;YAEA,IAAI,CAACvB,QAAQ;gBACX;YACF;YAEA,IAAI,CAAC4D,iBAAiB;gBACpB;YACF;YAEA7D,SAASC,QAAQC,MAAM;gBACrBiH,MAAMrD;YACR;QACF;IACN;IAEA,6FAA6F;IAC7F,wFAAwF;IACxF,2EAA2E;IAC3E,IAAI0D,CAAAA,GAAAA,OAAAA,aAAa,EAAC/F,KAAK;QACrB2F,WAAWlH,IAAI,GAAGuB;IACpB,OAAO,IACL,CAAC+B,kBACDN,YACC6C,MAAMK,IAAI,KAAK,OAAO,CAAE,CAAA,UAAUL,MAAMpD,KAAI,GAC7C;QACAyE,WAAWlH,IAAI,GAAGuH,CAAAA,GAAAA,aAAAA,WAAW,EAAChG;IAChC;IAEA,OAAO+B,iBAAAA,WAAAA,GACLrB,OAAAA,OAAK,CAACuF,YAAY,CAAC3B,OAAOqB,cAAAA,WAAAA,GAE1B,CAAA,GAAA,YAAA,GAAA,EAAC1D,KAAAA;QAAG,GAAGD,SAAS;QAAG,GAAG2D,UAAU;kBAC7BvE;;AAGP;MAGF,WAAeL", "ignoreList": [0]}}, {"offset": {"line": 3553, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}