<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار السلة المحدث</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            direction: rtl;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .product-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .product-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .cart-section {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .btn {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn-primary {
            background-color: #3b82f6;
            color: white;
        }
        .btn-success {
            background-color: #10b981;
            color: white;
        }
        .btn-danger {
            background-color: #ef4444;
            color: white;
        }
        .btn-warning {
            background-color: #f59e0b;
            color: white;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .status.success {
            background-color: #d1fae5;
            color: #065f46;
            border: 1px solid #10b981;
        }
        .status.error {
            background-color: #fee2e2;
            color: #991b1b;
            border: 1px solid #ef4444;
        }
        .cart-item {
            border-bottom: 1px solid #e5e7eb;
            padding: 10px 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .cart-total {
            font-size: 18px;
            font-weight: bold;
            color: #1f2937;
            margin-top: 15px;
            padding-top: 15px;
            border-top: 2px solid #e5e7eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>اختبار السلة المحدث - متوافق مع CartContext</h1>
        
        <div id="status"></div>

        <h2>المنتجات المتاحة</h2>
        <div class="product-grid" id="products"></div>

        <div class="cart-section">
            <h2>سلة التسوق</h2>
            <div id="cart-items"></div>
            <div id="cart-total"></div>
            
            <div style="margin-top: 20px;">
                <button class="btn btn-danger" onclick="clearCart()">مسح السلة</button>
                <button class="btn btn-success" onclick="goToCheckout()">متابعة للدفع</button>
                <button class="btn btn-warning" onclick="testCartSync()">اختبار مزامنة السلة</button>
            </div>
        </div>

        <div class="cart-section">
            <h3>أدوات الاختبار</h3>
            <button class="btn btn-primary" onclick="loadSampleData()">تحميل البيانات الوهمية</button>
            <button class="btn btn-primary" onclick="showCartData()">عرض بيانات السلة</button>
            <button class="btn btn-primary" onclick="window.open('http://localhost:3001/cart', '_blank')">فتح صفحة السلة</button>
            <button class="btn btn-primary" onclick="window.open('http://localhost:3001/checkout', '_blank')">فتح صفحة الدفع</button>
        </div>
    </div>

    <script>
        // البيانات الوهمية - متوافقة مع CartContext
        const sampleProducts = [
            {
                id: '1',
                name: 'هاتف ذكي متطور',
                description: 'هاتف ذكي بمواصفات عالية وكاميرا متقدمة',
                price: 2500,
                image: '/images/phone.jpg',
                category: 'إلكترونيات',
                stock: 15,
                featured: true,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString(),
            },
            {
                id: '2',
                name: 'لابتوب للألعاب',
                description: 'لابتوب قوي مخصص للألعاب والتصميم',
                price: 4500,
                image: '/images/laptop.jpg',
                category: 'إلكترونيات',
                stock: 8,
                featured: true,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString(),
            },
            {
                id: '3',
                name: 'ساعة ذكية',
                description: 'ساعة ذكية لتتبع اللياقة البدنية',
                price: 800,
                image: '/images/watch.jpg',
                category: 'إكسسوارات',
                stock: 25,
                featured: false,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString(),
            },
            {
                id: '4',
                name: 'سماعات لاسلكية',
                description: 'سماعات بلوتوث عالية الجودة',
                price: 350,
                image: '/images/headphones.jpg',
                category: 'إكسسوارات',
                stock: 30,
                featured: true,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString(),
            }
        ];

        let cart = [];

        function loadSampleData() {
            displayProducts();
            loadCart();
            updateStatus('تم تحميل البيانات الوهمية', 'success');
        }

        function displayProducts() {
            const productsDiv = document.getElementById('products');
            let html = '';
            
            sampleProducts.forEach(product => {
                html += `
                    <div class="product-card">
                        <h3>${product.name}</h3>
                        <p>${product.description}</p>
                        <p><strong>السعر:</strong> ${product.price} ريال</p>
                        <p><strong>التصنيف:</strong> ${product.category}</p>
                        <p><strong>المخزون:</strong> ${product.stock}</p>
                        <button class="btn btn-primary" onclick="addToCart('${product.id}')">
                            إضافة للسلة
                        </button>
                    </div>
                `;
            });
            
            productsDiv.innerHTML = html;
        }

        function addToCart(productId) {
            const product = sampleProducts.find(p => p.id === productId);
            if (!product) return;

            const existingItem = cart.find(item => item.product.id === productId);

            if (existingItem) {
                existingItem.quantity += 1;
            } else {
                // تنسيق متوافق مع CartContext
                cart.push({
                    id: `${productId}-${Date.now()}`,
                    product: product,
                    quantity: 1,
                    selectedSize: undefined,
                    selectedColor: undefined
                });
            }

            saveCart();
            updateCartDisplay();
            updateStatus('تم إضافة ' + product.name + ' إلى السلة!', 'success');
        }

        function removeFromCart(itemId) {
            cart = cart.filter(item => item.id !== itemId);
            saveCart();
            updateCartDisplay();
            updateStatus('تم حذف المنتج من السلة', 'success');
        }

        function updateQuantity(itemId, newQuantity) {
            if (newQuantity <= 0) {
                removeFromCart(itemId);
                return;
            }

            const item = cart.find(item => item.id === itemId);
            if (item) {
                item.quantity = newQuantity;
                saveCart();
                updateCartDisplay();
            }
        }

        function clearCart() {
            cart = [];
            saveCart();
            updateCartDisplay();
            updateStatus('تم مسح السلة', 'success');
        }

        function saveCart() {
            localStorage.setItem('cart', JSON.stringify(cart));
        }

        function loadCart() {
            const savedCart = localStorage.getItem('cart');
            if (savedCart) {
                try {
                    cart = JSON.parse(savedCart);
                    updateCartDisplay();
                } catch (error) {
                    console.error('خطأ في تحميل السلة:', error);
                    cart = [];
                }
            }
        }

        function updateCartDisplay() {
            const cartItems = document.getElementById('cart-items');
            const cartTotal = document.getElementById('cart-total');

            if (cart.length === 0) {
                cartItems.innerHTML = '<p>السلة فارغة</p>';
                cartTotal.innerHTML = '';
                return;
            }

            let html = '';
            let total = 0;

            cart.forEach(item => {
                const itemTotal = item.product.price * item.quantity;
                total += itemTotal;
                
                html += `
                    <div class="cart-item">
                        <div>
                            <strong>${item.product.name}</strong><br>
                            <small>${item.product.price} ريال × ${item.quantity} = ${itemTotal} ريال</small>
                        </div>
                        <div>
                            <button class="btn btn-primary" onclick="updateQuantity('${item.id}', ${item.quantity - 1})">-</button>
                            <span style="margin: 0 10px;">${item.quantity}</span>
                            <button class="btn btn-primary" onclick="updateQuantity('${item.id}', ${item.quantity + 1})">+</button>
                            <button class="btn btn-danger" onclick="removeFromCart('${item.id}')">حذف</button>
                        </div>
                    </div>
                `;
            });

            cartItems.innerHTML = html;
            cartTotal.innerHTML = `<div class="cart-total">المجموع: ${total} ريال</div>`;
        }

        function goToCheckout() {
            if (cart.length === 0) {
                updateStatus('السلة فارغة! أضف منتجات أولاً', 'error');
                return;
            }
            
            updateStatus('جاري الانتقال لصفحة الدفع...', 'success');
            setTimeout(() => {
                window.open('http://localhost:3001/checkout', '_blank');
            }, 1000);
        }

        function testCartSync() {
            const cartData = localStorage.getItem('cart');
            if (cartData) {
                updateStatus('بيانات السلة موجودة في localStorage - جاهزة للمزامنة', 'success');
                console.log('Cart data:', JSON.parse(cartData));
            } else {
                updateStatus('لا توجد بيانات في السلة', 'error');
            }
        }

        function showCartData() {
            const cartData = localStorage.getItem('cart');
            if (cartData) {
                console.log('Cart data:', JSON.parse(cartData));
                alert('تم عرض بيانات السلة في console');
            } else {
                alert('السلة فارغة');
            }
        }

        function updateStatus(message, type) {
            const statusDiv = document.getElementById('status');
            statusDiv.innerHTML = `<div class="status ${type}">${message}</div>`;
            setTimeout(() => {
                statusDiv.innerHTML = '';
            }, 3000);
        }

        // تحميل البيانات عند تحميل الصفحة
        window.onload = function() {
            loadSampleData();
        };
    </script>
</body>
</html>
