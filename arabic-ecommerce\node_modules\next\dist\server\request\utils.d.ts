export declare function describeStringPropertyAccess(target: string, prop: string): string;
export declare function describeHasCheckingStringProperty(target: string, prop: string): string;
export declare function throwWithStaticGenerationBailoutError(route: string, expression: string): never;
export declare function throwWithStaticGenerationBailoutErrorWithDynamicError(route: string, expression: string): never;
export declare function isRequestAPICallableInsideAfter(): boolean;
export declare const wellKnownProperties: Set<string>;
