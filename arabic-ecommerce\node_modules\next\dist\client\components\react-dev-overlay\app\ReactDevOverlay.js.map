{"version": 3, "sources": ["../../../../../src/client/components/react-dev-overlay/app/ReactDevOverlay.tsx"], "sourcesContent": ["import React from 'react'\nimport type { OverlayState } from '../shared'\nimport { ShadowPortal } from '../internal/components/ShadowPortal'\nimport { BuildError } from '../internal/container/BuildError'\nimport { Errors } from '../internal/container/Errors'\nimport { StaticIndicator } from '../internal/container/StaticIndicator'\nimport { Base } from '../internal/styles/Base'\nimport { ComponentStyles } from '../internal/styles/ComponentStyles'\nimport { CssReset } from '../internal/styles/CssReset'\nimport { RootLayoutMissingTagsError } from '../internal/container/root-layout-missing-tags-error'\nimport type { Dispatcher } from './hot-reloader-client'\nimport { RuntimeErrorHandler } from '../internal/helpers/runtime-error-handler'\n\ninterface ReactDevOverlayState {\n  isReactError: boolean\n}\nexport default class ReactDevOverlay extends React.PureComponent<\n  {\n    state: OverlayState\n    dispatcher?: Dispatcher\n    children: React.ReactNode\n  },\n  ReactDevOverlayState\n> {\n  state = { isReactError: false }\n\n  static getDerivedStateFromError(error: Error): ReactDevOverlayState {\n    if (!error.stack) return { isReactError: false }\n\n    RuntimeErrorHandler.hadRuntimeError = true\n    return {\n      isReactError: true,\n    }\n  }\n\n  render() {\n    const { state, children, dispatcher } = this.props\n    const { isReactError } = this.state\n\n    const hasBuildError = state.buildError != null\n    const hasRuntimeErrors = Boolean(state.errors.length)\n    const hasStaticIndicator = state.staticIndicator\n    const debugInfo = state.debugInfo\n\n    return (\n      <>\n        {isReactError ? (\n          <html>\n            <head></head>\n            <body></body>\n          </html>\n        ) : (\n          children\n        )}\n        <ShadowPortal>\n          <CssReset />\n          <Base />\n          <ComponentStyles />\n          {state.rootLayoutMissingTags?.length ? (\n            <RootLayoutMissingTagsError\n              missingTags={state.rootLayoutMissingTags}\n            />\n          ) : hasBuildError ? (\n            <BuildError\n              message={state.buildError!}\n              versionInfo={state.versionInfo}\n            />\n          ) : (\n            <>\n              {hasRuntimeErrors ? (\n                <Errors\n                  isAppDir={true}\n                  initialDisplayState={\n                    isReactError ? 'fullscreen' : 'minimized'\n                  }\n                  errors={state.errors}\n                  versionInfo={state.versionInfo}\n                  hasStaticIndicator={hasStaticIndicator}\n                  debugInfo={debugInfo}\n                />\n              ) : null}\n\n              {hasStaticIndicator && (\n                <StaticIndicator dispatcher={dispatcher} />\n              )}\n            </>\n          )}\n        </ShadowPortal>\n      </>\n    )\n  }\n}\n"], "names": ["ReactDevOverlay", "React", "PureComponent", "getDerivedStateFromError", "error", "stack", "isReactError", "RuntimeError<PERSON>andler", "hadRuntimeError", "render", "state", "children", "dispatcher", "props", "hasBuildError", "buildError", "hasRuntimeErrors", "Boolean", "errors", "length", "hasStaticIndicator", "staticIndicator", "debugInfo", "html", "head", "body", "ShadowPort<PERSON>", "CssReset", "Base", "ComponentStyles", "rootLayoutMissingTags", "RootLayoutMissingTagsError", "missingTags", "BuildError", "message", "versionInfo", "Errors", "isAppDir", "initialDisplayState", "StaticIndicator"], "mappings": ";;;;;;;eAgBqBA;;;;;gEAhBH;8BAEW;4BACF;wBACJ;iCACS;sBACX;iCACW;0BACP;4CACkB;qCAEP;AAKrB,MAAMA,wBAAwBC,cAAK,CAACC,aAAa;IAU9D,OAAOC,yBAAyBC,KAAY,EAAwB;QAClE,IAAI,CAACA,MAAMC,KAAK,EAAE,OAAO;YAAEC,cAAc;QAAM;QAE/CC,wCAAmB,CAACC,eAAe,GAAG;QACtC,OAAO;YACLF,cAAc;QAChB;IACF;IAEAG,SAAS;YAuBAC;QAtBP,MAAM,EAAEA,KAAK,EAAEC,QAAQ,EAAEC,UAAU,EAAE,GAAG,IAAI,CAACC,KAAK;QAClD,MAAM,EAAEP,YAAY,EAAE,GAAG,IAAI,CAACI,KAAK;QAEnC,MAAMI,gBAAgBJ,MAAMK,UAAU,IAAI;QAC1C,MAAMC,mBAAmBC,QAAQP,MAAMQ,MAAM,CAACC,MAAM;QACpD,MAAMC,qBAAqBV,MAAMW,eAAe;QAChD,MAAMC,YAAYZ,MAAMY,SAAS;QAEjC,qBACE;;gBACGhB,6BACC,sBAACiB;;sCACC,qBAACC;sCACD,qBAACC;;qBAGHd;8BAEF,sBAACe,0BAAY;;sCACX,qBAACC,kBAAQ;sCACT,qBAACC,UAAI;sCACL,qBAACC,gCAAe;wBACfnB,EAAAA,+BAAAA,MAAMoB,qBAAqB,qBAA3BpB,6BAA6BS,MAAM,kBAClC,qBAACY,sDAA0B;4BACzBC,aAAatB,MAAMoB,qBAAqB;6BAExChB,8BACF,qBAACmB,sBAAU;4BACTC,SAASxB,MAAMK,UAAU;4BACzBoB,aAAazB,MAAMyB,WAAW;2CAGhC;;gCACGnB,iCACC,qBAACoB,cAAM;oCACLC,UAAU;oCACVC,qBACEhC,eAAe,eAAe;oCAEhCY,QAAQR,MAAMQ,MAAM;oCACpBiB,aAAazB,MAAMyB,WAAW;oCAC9Bf,oBAAoBA;oCACpBE,WAAWA;qCAEX;gCAEHF,oCACC,qBAACmB,gCAAe;oCAAC3B,YAAYA;;;;;;;;IAO3C;;QA1Ea,qBAQbF,QAAQ;YAAEJ,cAAc;QAAM;;AAmEhC"}