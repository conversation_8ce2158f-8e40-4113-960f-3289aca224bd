{"version": 3, "sources": ["../../../src/lib/metadata/resolve-metadata.ts"], "sourcesContent": ["import type {\n  <PERSON>ada<PERSON>,\n  ResolvedMetadata,\n  ResolvedViewport,\n  ResolvingMetadata,\n  ResolvingViewport,\n  Viewport,\n} from './types/metadata-interface'\nimport type { MetadataImageModule } from '../../build/webpack/loaders/metadata/types'\nimport type { GetDynamicParamFromSegment } from '../../server/app-render/app-render'\nimport type { Twitter } from './types/twitter-types'\nimport type { OpenGraph } from './types/opengraph-types'\nimport type { AppDirModules } from '../../build/webpack/loaders/next-app-loader'\nimport type { MetadataContext } from './types/resolvers'\nimport type { LoaderTree } from '../../server/lib/app-dir-module'\nimport type {\n  AbsoluteTemplateString,\n  IconDescriptor,\n  ResolvedIcons,\n} from './types/metadata-types'\nimport type { ParsedUrlQuery } from 'querystring'\nimport type { StaticMetadata } from './types/icons'\n\n// eslint-disable-next-line import/no-extraneous-dependencies\nimport 'server-only'\n\nimport { cache } from 'react'\nimport {\n  createDefaultMetadata,\n  createDefaultViewport,\n} from './default-metadata'\nimport { resolveOpenGraph, resolveTwitter } from './resolvers/resolve-opengraph'\nimport { resolveTitle } from './resolvers/resolve-title'\nimport { resolveAsArrayOrUndefined } from './generate/utils'\nimport {\n  getComponentTypeModule,\n  getLayoutOrPageModule,\n} from '../../server/lib/app-dir-module'\nimport { interopDefault } from '../interop-default'\nimport {\n  resolveAlternates,\n  resolveAppleWebApp,\n  resolveAppLinks,\n  resolveRobots,\n  resolveThemeColor,\n  resolveVerification,\n  resolveItunes,\n  resolveFacebook,\n} from './resolvers/resolve-basics'\nimport { resolveIcons } from './resolvers/resolve-icons'\nimport { getTracer } from '../../server/lib/trace/tracer'\nimport { ResolveMetadataSpan } from '../../server/lib/trace/constants'\nimport { PAGE_SEGMENT_KEY } from '../../shared/lib/segment'\nimport * as Log from '../../build/output/log'\nimport type { WorkStore } from '../../server/app-render/work-async-storage.external'\nimport type {\n  Params,\n  CreateServerParamsForMetadata,\n} from '../../server/request/params'\n\ntype StaticIcons = Pick<ResolvedIcons, 'icon' | 'apple'>\n\ntype MetadataResolver = (\n  parent: ResolvingMetadata\n) => Metadata | Promise<Metadata>\ntype ViewportResolver = (\n  parent: ResolvingViewport\n) => Viewport | Promise<Viewport>\n\nexport type MetadataErrorType = 'not-found' | 'forbidden' | 'unauthorized'\n\nexport type MetadataItems = [\n  Metadata | MetadataResolver | null,\n  StaticMetadata,\n  Viewport | ViewportResolver | null,\n][]\n\ntype TitleTemplates = {\n  title: string | null\n  twitter: string | null\n  openGraph: string | null\n}\n\ntype BuildState = {\n  warnings: Set<string>\n}\n\ntype LayoutProps = {\n  params: { [key: string]: any }\n}\ntype PageProps = {\n  params: { [key: string]: any }\n  searchParams: { [key: string]: any }\n}\n\nfunction isFavicon(icon: IconDescriptor | undefined): boolean {\n  if (!icon) {\n    return false\n  }\n\n  // turbopack appends a hash to all images\n  return (\n    (icon.url === '/favicon.ico' ||\n      icon.url.toString().startsWith('/favicon.ico?')) &&\n    icon.type === 'image/x-icon'\n  )\n}\n\nfunction mergeStaticMetadata(\n  source: Metadata | null,\n  target: ResolvedMetadata,\n  staticFilesMetadata: StaticMetadata,\n  metadataContext: MetadataContext,\n  titleTemplates: TitleTemplates,\n  leafSegmentStaticIcons: StaticIcons\n) {\n  if (!staticFilesMetadata) return\n  const { icon, apple, openGraph, twitter, manifest } = staticFilesMetadata\n\n  // Keep updating the static icons in the most leaf node\n\n  if (icon) {\n    leafSegmentStaticIcons.icon = icon\n  }\n  if (apple) {\n    leafSegmentStaticIcons.apple = apple\n  }\n\n  // file based metadata is specified and current level metadata twitter.images is not specified\n  if (twitter && !source?.twitter?.hasOwnProperty('images')) {\n    const resolvedTwitter = resolveTwitter(\n      { ...target.twitter, images: twitter } as Twitter,\n      target.metadataBase,\n      { ...metadataContext, isStaticMetadataRouteFile: true },\n      titleTemplates.twitter\n    )\n    target.twitter = resolvedTwitter\n  }\n\n  // file based metadata is specified and current level metadata openGraph.images is not specified\n  if (openGraph && !source?.openGraph?.hasOwnProperty('images')) {\n    const resolvedOpenGraph = resolveOpenGraph(\n      { ...target.openGraph, images: openGraph } as OpenGraph,\n      target.metadataBase,\n      { ...metadataContext, isStaticMetadataRouteFile: true },\n      titleTemplates.openGraph\n    )\n    target.openGraph = resolvedOpenGraph\n  }\n  if (manifest) {\n    target.manifest = manifest\n  }\n\n  return target\n}\n\n// Merge the source metadata into the resolved target metadata.\nfunction mergeMetadata({\n  source,\n  target,\n  staticFilesMetadata,\n  titleTemplates,\n  metadataContext,\n  buildState,\n  leafSegmentStaticIcons,\n}: {\n  source: Metadata | null\n  target: ResolvedMetadata\n  staticFilesMetadata: StaticMetadata\n  titleTemplates: TitleTemplates\n  metadataContext: MetadataContext\n  buildState: BuildState\n  leafSegmentStaticIcons: StaticIcons\n}): void {\n  // If there's override metadata, prefer it otherwise fallback to the default metadata.\n  const metadataBase =\n    typeof source?.metadataBase !== 'undefined'\n      ? source.metadataBase\n      : target.metadataBase\n  for (const key_ in source) {\n    const key = key_ as keyof Metadata\n\n    switch (key) {\n      case 'title': {\n        target.title = resolveTitle(source.title, titleTemplates.title)\n        break\n      }\n      case 'alternates': {\n        target.alternates = resolveAlternates(\n          source.alternates,\n          metadataBase,\n          metadataContext\n        )\n        break\n      }\n      case 'openGraph': {\n        target.openGraph = resolveOpenGraph(\n          source.openGraph,\n          metadataBase,\n          metadataContext,\n          titleTemplates.openGraph\n        )\n        break\n      }\n      case 'twitter': {\n        target.twitter = resolveTwitter(\n          source.twitter,\n          metadataBase,\n          metadataContext,\n          titleTemplates.twitter\n        )\n        break\n      }\n      case 'facebook':\n        target.facebook = resolveFacebook(source.facebook)\n        break\n\n      case 'verification':\n        target.verification = resolveVerification(source.verification)\n        break\n\n      case 'icons': {\n        target.icons = resolveIcons(source.icons)\n        break\n      }\n      case 'appleWebApp':\n        target.appleWebApp = resolveAppleWebApp(source.appleWebApp)\n        break\n      case 'appLinks':\n        target.appLinks = resolveAppLinks(source.appLinks)\n        break\n      case 'robots': {\n        target.robots = resolveRobots(source.robots)\n        break\n      }\n      case 'archives':\n      case 'assets':\n      case 'bookmarks':\n      case 'keywords': {\n        target[key] = resolveAsArrayOrUndefined(source[key])\n        break\n      }\n      case 'authors': {\n        target[key] = resolveAsArrayOrUndefined(source.authors)\n        break\n      }\n      case 'itunes': {\n        target[key] = resolveItunes(\n          source.itunes,\n          metadataBase,\n          metadataContext\n        )\n        break\n      }\n      // directly assign fields that fallback to null\n      case 'applicationName':\n      case 'description':\n      case 'generator':\n      case 'creator':\n      case 'publisher':\n      case 'category':\n      case 'classification':\n      case 'referrer':\n      case 'formatDetection':\n      case 'manifest':\n        // @ts-ignore TODO: support inferring\n        target[key] = source[key] || null\n        break\n      case 'other':\n        target.other = Object.assign({}, target.other, source.other)\n        break\n      case 'metadataBase':\n        target.metadataBase = metadataBase\n        break\n\n      default: {\n        if (\n          (key === 'viewport' ||\n            key === 'themeColor' ||\n            key === 'colorScheme') &&\n          source[key] != null\n        ) {\n          buildState.warnings.add(\n            `Unsupported metadata ${key} is configured in metadata export in ${metadataContext.pathname}. Please move it to viewport export instead.\\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport`\n          )\n        }\n        break\n      }\n    }\n  }\n  mergeStaticMetadata(\n    source,\n    target,\n    staticFilesMetadata,\n    metadataContext,\n    titleTemplates,\n    leafSegmentStaticIcons\n  )\n}\n\nfunction mergeViewport({\n  target,\n  source,\n}: {\n  target: ResolvedViewport\n  source: Viewport | null\n}): void {\n  if (!source) return\n  for (const key_ in source) {\n    const key = key_ as keyof Viewport\n\n    switch (key) {\n      case 'themeColor': {\n        target.themeColor = resolveThemeColor(source.themeColor)\n        break\n      }\n      case 'colorScheme':\n        target.colorScheme = source.colorScheme || null\n        break\n      default:\n        if (typeof source[key] !== 'undefined') {\n          // @ts-ignore viewport properties\n          target[key] = source[key]\n        }\n        break\n    }\n  }\n}\n\nasync function getDefinedViewport(\n  mod: any,\n  props: any,\n  tracingProps: { route: string }\n): Promise<Viewport | ViewportResolver | null> {\n  if (typeof mod.generateViewport === 'function') {\n    const { route } = tracingProps\n    return (parent: ResolvingViewport) =>\n      getTracer().trace(\n        ResolveMetadataSpan.generateViewport,\n        {\n          spanName: `generateViewport ${route}`,\n          attributes: {\n            'next.page': route,\n          },\n        },\n        () => mod.generateViewport(props, parent)\n      )\n  }\n  return mod.viewport || null\n}\n\nasync function getDefinedMetadata(\n  mod: any,\n  props: any,\n  tracingProps: { route: string }\n): Promise<Metadata | MetadataResolver | null> {\n  if (typeof mod.generateMetadata === 'function') {\n    const { route } = tracingProps\n    return (parent: ResolvingMetadata) =>\n      getTracer().trace(\n        ResolveMetadataSpan.generateMetadata,\n        {\n          spanName: `generateMetadata ${route}`,\n          attributes: {\n            'next.page': route,\n          },\n        },\n        () => mod.generateMetadata(props, parent)\n      )\n  }\n  return mod.metadata || null\n}\n\nasync function collectStaticImagesFiles(\n  metadata: AppDirModules['metadata'],\n  props: any,\n  type: keyof NonNullable<AppDirModules['metadata']>\n) {\n  if (!metadata?.[type]) return undefined\n\n  const iconPromises = metadata[type as 'icon' | 'apple'].map(\n    async (imageModule: (p: any) => Promise<MetadataImageModule[]>) =>\n      interopDefault(await imageModule(props))\n  )\n\n  return iconPromises?.length > 0\n    ? (await Promise.all(iconPromises))?.flat()\n    : undefined\n}\n\nasync function resolveStaticMetadata(\n  modules: AppDirModules,\n  props: any\n): Promise<StaticMetadata> {\n  const { metadata } = modules\n  if (!metadata) return null\n\n  const [icon, apple, openGraph, twitter] = await Promise.all([\n    collectStaticImagesFiles(metadata, props, 'icon'),\n    collectStaticImagesFiles(metadata, props, 'apple'),\n    collectStaticImagesFiles(metadata, props, 'openGraph'),\n    collectStaticImagesFiles(metadata, props, 'twitter'),\n  ])\n\n  const staticMetadata = {\n    icon,\n    apple,\n    openGraph,\n    twitter,\n    manifest: metadata.manifest,\n  }\n\n  return staticMetadata\n}\n\n// [layout.metadata, static files metadata] -> ... -> [page.metadata, static files metadata]\nasync function collectMetadata({\n  tree,\n  metadataItems,\n  errorMetadataItem,\n  props,\n  route,\n  errorConvention,\n}: {\n  tree: LoaderTree\n  metadataItems: MetadataItems\n  errorMetadataItem: MetadataItems[number]\n  props: any\n  route: string\n  errorConvention?: MetadataErrorType\n}) {\n  let mod\n  let modType\n  const hasErrorConventionComponent = Boolean(\n    errorConvention && tree[2][errorConvention]\n  )\n  if (errorConvention) {\n    mod = await getComponentTypeModule(tree, 'layout')\n    modType = errorConvention\n  } else {\n    const { mod: layoutOrPageMod, modType: layoutOrPageModType } =\n      await getLayoutOrPageModule(tree)\n    mod = layoutOrPageMod\n    modType = layoutOrPageModType\n  }\n\n  if (modType) {\n    route += `/${modType}`\n  }\n\n  const staticFilesMetadata = await resolveStaticMetadata(tree[2], props)\n  const metadataExport = mod\n    ? await getDefinedMetadata(mod, props, { route })\n    : null\n\n  const viewportExport = mod\n    ? await getDefinedViewport(mod, props, { route })\n    : null\n\n  metadataItems.push([metadataExport, staticFilesMetadata, viewportExport])\n\n  if (hasErrorConventionComponent && errorConvention) {\n    const errorMod = await getComponentTypeModule(tree, errorConvention)\n    const errorViewportExport = errorMod\n      ? await getDefinedViewport(errorMod, props, { route })\n      : null\n    const errorMetadataExport = errorMod\n      ? await getDefinedMetadata(errorMod, props, { route })\n      : null\n\n    errorMetadataItem[0] = errorMetadataExport\n    errorMetadataItem[1] = staticFilesMetadata\n    errorMetadataItem[2] = errorViewportExport\n  }\n}\n\nconst cachedResolveMetadataItems = cache(resolveMetadataItems)\nexport { cachedResolveMetadataItems as resolveMetadataItems }\nasync function resolveMetadataItems(\n  tree: LoaderTree,\n  searchParams: Promise<ParsedUrlQuery>,\n  errorConvention: MetadataErrorType | undefined,\n  getDynamicParamFromSegment: GetDynamicParamFromSegment,\n  createServerParamsForMetadata: CreateServerParamsForMetadata,\n  workStore: WorkStore\n) {\n  const parentParams = {}\n  const metadataItems: MetadataItems = []\n  const errorMetadataItem: MetadataItems[number] = [null, null, null]\n  const treePrefix = undefined\n  return resolveMetadataItemsImpl(\n    metadataItems,\n    tree,\n    treePrefix,\n    parentParams,\n    searchParams,\n    errorConvention,\n    errorMetadataItem,\n    getDynamicParamFromSegment,\n    createServerParamsForMetadata,\n    workStore\n  )\n}\n\nasync function resolveMetadataItemsImpl(\n  metadataItems: MetadataItems,\n  tree: LoaderTree,\n  /** Provided tree can be nested subtree, this argument says what is the path of such subtree */\n  treePrefix: undefined | string[],\n  parentParams: Params,\n  searchParams: Promise<ParsedUrlQuery>,\n  errorConvention: MetadataErrorType | undefined,\n  errorMetadataItem: MetadataItems[number],\n  getDynamicParamFromSegment: GetDynamicParamFromSegment,\n  createServerParamsForMetadata: CreateServerParamsForMetadata,\n  workStore: WorkStore\n): Promise<MetadataItems> {\n  const [segment, parallelRoutes, { page }] = tree\n  const currentTreePrefix =\n    treePrefix && treePrefix.length ? [...treePrefix, segment] : [segment]\n  const isPage = typeof page !== 'undefined'\n\n  // Handle dynamic segment params.\n  const segmentParam = getDynamicParamFromSegment(segment)\n  /**\n   * Create object holding the parent params and current params\n   */\n  let currentParams = parentParams\n  if (segmentParam && segmentParam.value !== null) {\n    currentParams = {\n      ...parentParams,\n      [segmentParam.param]: segmentParam.value,\n    }\n  }\n\n  const params = createServerParamsForMetadata(currentParams, workStore)\n\n  let layerProps: LayoutProps | PageProps\n  if (isPage) {\n    layerProps = {\n      params,\n      searchParams,\n    }\n  } else {\n    layerProps = {\n      params,\n    }\n  }\n\n  await collectMetadata({\n    tree,\n    metadataItems,\n    errorMetadataItem,\n    errorConvention,\n    props: layerProps,\n    route: currentTreePrefix\n      // __PAGE__ shouldn't be shown in a route\n      .filter((s) => s !== PAGE_SEGMENT_KEY)\n      .join('/'),\n  })\n\n  for (const key in parallelRoutes) {\n    const childTree = parallelRoutes[key]\n    await resolveMetadataItemsImpl(\n      metadataItems,\n      childTree,\n      currentTreePrefix,\n      currentParams,\n      searchParams,\n      errorConvention,\n      errorMetadataItem,\n      getDynamicParamFromSegment,\n      createServerParamsForMetadata,\n      workStore\n    )\n  }\n\n  if (Object.keys(parallelRoutes).length === 0 && errorConvention) {\n    // If there are no parallel routes, place error metadata as the last item.\n    // e.g. layout -> layout -> not-found\n    metadataItems.push(errorMetadataItem)\n  }\n\n  return metadataItems\n}\n\ntype WithTitle = { title?: AbsoluteTemplateString | null }\ntype WithDescription = { description?: string | null }\n\nconst isTitleTruthy = (title: AbsoluteTemplateString | null | undefined) =>\n  !!title?.absolute\nconst hasTitle = (metadata: WithTitle | null) => isTitleTruthy(metadata?.title)\n\nfunction inheritFromMetadata(\n  target: (WithTitle & WithDescription) | null,\n  metadata: ResolvedMetadata\n) {\n  if (target) {\n    if (!hasTitle(target) && hasTitle(metadata)) {\n      target.title = metadata.title\n    }\n    if (!target.description && metadata.description) {\n      target.description = metadata.description\n    }\n  }\n}\n\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nconst commonOgKeys = ['title', 'description', 'images'] as const\nfunction postProcessMetadata(\n  metadata: ResolvedMetadata,\n  favicon: any,\n  titleTemplates: TitleTemplates,\n  metadataContext: MetadataContext\n): ResolvedMetadata {\n  const { openGraph, twitter } = metadata\n\n  if (openGraph) {\n    // If there's openGraph information but not configured in twitter,\n    // inherit them from openGraph metadata.\n    let autoFillProps: Partial<{\n      [Key in (typeof commonOgKeys)[number]]: NonNullable<\n        ResolvedMetadata['openGraph']\n      >[Key]\n    }> = {}\n    const hasTwTitle = hasTitle(twitter)\n    const hasTwDescription = twitter?.description\n    const hasTwImages = Boolean(\n      twitter?.hasOwnProperty('images') && twitter.images\n    )\n    if (!hasTwTitle) {\n      if (isTitleTruthy(openGraph.title)) {\n        autoFillProps.title = openGraph.title\n      } else if (metadata.title && isTitleTruthy(metadata.title)) {\n        autoFillProps.title = metadata.title\n      }\n    }\n    if (!hasTwDescription)\n      autoFillProps.description =\n        openGraph.description || metadata.description || undefined\n    if (!hasTwImages) autoFillProps.images = openGraph.images\n\n    if (Object.keys(autoFillProps).length > 0) {\n      const partialTwitter = resolveTwitter(\n        autoFillProps,\n        metadata.metadataBase,\n        metadataContext,\n        titleTemplates.twitter\n      )\n      if (metadata.twitter) {\n        metadata.twitter = Object.assign({}, metadata.twitter, {\n          ...(!hasTwTitle && { title: partialTwitter?.title }),\n          ...(!hasTwDescription && {\n            description: partialTwitter?.description,\n          }),\n          ...(!hasTwImages && { images: partialTwitter?.images }),\n        })\n      } else {\n        metadata.twitter = partialTwitter\n      }\n    }\n  }\n\n  // If there's no title and description configured in openGraph or twitter,\n  // use the title and description from metadata.\n  inheritFromMetadata(openGraph, metadata)\n  inheritFromMetadata(twitter, metadata)\n\n  if (favicon) {\n    if (!metadata.icons) {\n      metadata.icons = {\n        icon: [],\n        apple: [],\n      }\n    }\n\n    metadata.icons.icon.unshift(favicon)\n  }\n\n  return metadata\n}\n\ntype DataResolver<Data, ResolvedData> = (\n  parent: Promise<ResolvedData>\n) => Data | Promise<Data>\n\nfunction collectMetadataExportPreloading<Data, ResolvedData>(\n  results: (Data | Promise<Data>)[],\n  dynamicMetadataExportFn: DataResolver<Data, ResolvedData>,\n  resolvers: ((value: ResolvedData) => void)[]\n) {\n  const result = dynamicMetadataExportFn(\n    new Promise<any>((resolve) => {\n      resolvers.push(resolve)\n    })\n  )\n\n  if (result instanceof Promise) {\n    // since we eager execute generateMetadata and\n    // they can reject at anytime we need to ensure\n    // we attach the catch handler right away to\n    // prevent unhandled rejections crashing the process\n    result.catch((err) => {\n      return {\n        __nextError: err,\n      }\n    })\n  }\n  results.push(result)\n}\n\nasync function getMetadataFromExport<Data, ResolvedData>(\n  getPreloadMetadataExport: (\n    metadataItem: NonNullable<MetadataItems[number]>\n  ) => Data | DataResolver<Data, ResolvedData> | null,\n  dynamicMetadataResolveState: {\n    resolvers: ((value: ResolvedData) => void)[]\n    resolvingIndex: number\n  },\n  metadataItems: MetadataItems,\n  currentIndex: number,\n  resolvedMetadata: ResolvedData,\n  metadataResults: (Data | Promise<Data>)[]\n) {\n  const metadataExport = getPreloadMetadataExport(metadataItems[currentIndex])\n  const dynamicMetadataResolvers = dynamicMetadataResolveState.resolvers\n  let metadata: Data | null = null\n  if (typeof metadataExport === 'function') {\n    // Only preload at the beginning when resolves are empty\n    if (!dynamicMetadataResolvers.length) {\n      for (let j = currentIndex; j < metadataItems.length; j++) {\n        const preloadMetadataExport = getPreloadMetadataExport(metadataItems[j])\n        // call each `generateMetadata function concurrently and stash their resolver\n        if (typeof preloadMetadataExport === 'function') {\n          collectMetadataExportPreloading<Data, ResolvedData>(\n            metadataResults,\n            preloadMetadataExport as DataResolver<Data, ResolvedData>,\n            dynamicMetadataResolvers\n          )\n        }\n      }\n    }\n\n    const resolveParent =\n      dynamicMetadataResolvers[dynamicMetadataResolveState.resolvingIndex]\n    const metadataResult =\n      metadataResults[dynamicMetadataResolveState.resolvingIndex++]\n\n    // In dev we clone and freeze to prevent relying on mutating resolvedMetadata directly.\n    // In prod we just pass resolvedMetadata through without any copying.\n    const currentResolvedMetadata =\n      process.env.NODE_ENV === 'development'\n        ? Object.freeze(\n            require('./clone-metadata').cloneMetadata(resolvedMetadata)\n          )\n        : resolvedMetadata\n\n    // This resolve should unblock the generateMetadata function if it awaited the parent\n    // argument. If it didn't await the parent argument it might already have a value since it was\n    // called concurrently. Regardless we await the return value before continuing on to the next layer\n    resolveParent(currentResolvedMetadata)\n    metadata =\n      metadataResult instanceof Promise ? await metadataResult : metadataResult\n\n    if (metadata && typeof metadata === 'object' && '__nextError' in metadata) {\n      // re-throw caught metadata error from preloading\n      throw metadata['__nextError']\n    }\n  } else if (metadataExport !== null && typeof metadataExport === 'object') {\n    // This metadataExport is the object form\n    metadata = metadataExport\n  }\n\n  return metadata\n}\n\nexport async function accumulateMetadata(\n  metadataItems: MetadataItems,\n  metadataContext: MetadataContext\n): Promise<ResolvedMetadata> {\n  const resolvedMetadata = createDefaultMetadata()\n  const metadataResults: (Metadata | Promise<Metadata>)[] = []\n\n  let titleTemplates: TitleTemplates = {\n    title: null,\n    twitter: null,\n    openGraph: null,\n  }\n\n  // Loop over all metadata items again, merging synchronously any static object exports,\n  // awaiting any static promise exports, and resolving parent metadata and awaiting any generated metadata\n  const dynamicMetadataResolvers = {\n    resolvers: [],\n    resolvingIndex: 0,\n  }\n  const buildState = {\n    warnings: new Set<string>(),\n  }\n\n  let favicon\n\n  // Collect the static icons in the most leaf node,\n  // since we don't collect all the static metadata icons in the parent segments.\n  const leafSegmentStaticIcons = {\n    icon: [],\n    apple: [],\n  }\n  for (let i = 0; i < metadataItems.length; i++) {\n    const staticFilesMetadata = metadataItems[i][1]\n\n    // Treat favicon as special case, it should be the first icon in the list\n    // i <= 1 represents root layout, and if current page is also at root\n    if (i <= 1 && isFavicon(staticFilesMetadata?.icon?.[0])) {\n      const iconMod = staticFilesMetadata?.icon?.shift()\n      if (i === 0) favicon = iconMod\n    }\n\n    const metadata = await getMetadataFromExport<Metadata, ResolvedMetadata>(\n      (metadataItem) => metadataItem[0],\n      dynamicMetadataResolvers,\n      metadataItems,\n      i,\n      resolvedMetadata,\n      metadataResults\n    )\n\n    mergeMetadata({\n      target: resolvedMetadata,\n      source: metadata,\n      metadataContext,\n      staticFilesMetadata,\n      titleTemplates,\n      buildState,\n      leafSegmentStaticIcons,\n    })\n\n    // If the layout is the same layer with page, skip the leaf layout and leaf page\n    // The leaf layout and page are the last two items\n    if (i < metadataItems.length - 2) {\n      titleTemplates = {\n        title: resolvedMetadata.title?.template || null,\n        openGraph: resolvedMetadata.openGraph?.title.template || null,\n        twitter: resolvedMetadata.twitter?.title.template || null,\n      }\n    }\n  }\n\n  if (\n    leafSegmentStaticIcons.icon.length > 0 ||\n    leafSegmentStaticIcons.apple.length > 0\n  ) {\n    if (!resolvedMetadata.icons) {\n      resolvedMetadata.icons = {\n        icon: [],\n        apple: [],\n      }\n      if (leafSegmentStaticIcons.icon.length > 0) {\n        resolvedMetadata.icons.icon.unshift(...leafSegmentStaticIcons.icon)\n      }\n      if (leafSegmentStaticIcons.apple.length > 0) {\n        resolvedMetadata.icons.apple.unshift(...leafSegmentStaticIcons.apple)\n      }\n    }\n  }\n\n  // Only log warnings if there are any, and only once after the metadata resolving process is finished\n  if (buildState.warnings.size > 0) {\n    for (const warning of buildState.warnings) {\n      Log.warn(warning)\n    }\n  }\n\n  return postProcessMetadata(\n    resolvedMetadata,\n    favicon,\n    titleTemplates,\n    metadataContext\n  )\n}\n\nexport async function accumulateViewport(\n  metadataItems: MetadataItems\n): Promise<ResolvedViewport> {\n  const resolvedViewport: ResolvedViewport = createDefaultViewport()\n\n  const viewportResults: (Viewport | Promise<Viewport>)[] = []\n  const dynamicMetadataResolvers = {\n    resolvers: [],\n    resolvingIndex: 0,\n  }\n  for (let i = 0; i < metadataItems.length; i++) {\n    const viewport = await getMetadataFromExport<Viewport, ResolvedViewport>(\n      (metadataItem) => metadataItem[2],\n      dynamicMetadataResolvers,\n      metadataItems,\n      i,\n      resolvedViewport,\n      viewportResults\n    )\n\n    mergeViewport({\n      target: resolvedViewport,\n      source: viewport,\n    })\n  }\n  return resolvedViewport\n}\n"], "names": ["cache", "createDefaultMetadata", "createDefaultViewport", "resolveOpenGraph", "resolveTwitter", "resolveTitle", "resolveAsArrayOrUndefined", "getComponentTypeModule", "getLayoutOrPageModule", "interopDefault", "resolveAlternates", "resolveAppleWebApp", "resolveAppLinks", "resolveRobots", "resolveThemeColor", "resolveVerification", "resolveItunes", "resolveFacebook", "resolveIcons", "getTracer", "ResolveMetadataSpan", "PAGE_SEGMENT_KEY", "Log", "isFavicon", "icon", "url", "toString", "startsWith", "type", "mergeStaticMetadata", "source", "target", "staticFilesMetadata", "metadataContext", "titleTemplates", "leafSegmentStaticIcons", "apple", "openGraph", "twitter", "manifest", "hasOwnProperty", "resolvedTwitter", "images", "metadataBase", "isStaticMetadataRouteFile", "resolvedOpenGraph", "mergeMetadata", "buildState", "key_", "key", "title", "alternates", "facebook", "verification", "icons", "appleWebApp", "appLinks", "robots", "authors", "itunes", "other", "Object", "assign", "warnings", "add", "pathname", "mergeViewport", "themeColor", "colorScheme", "getDefinedViewport", "mod", "props", "tracingProps", "generateViewport", "route", "parent", "trace", "spanName", "attributes", "viewport", "getDefinedMetadata", "generateMetadata", "metadata", "collectStaticImagesFiles", "undefined", "iconPromises", "map", "imageModule", "length", "Promise", "all", "flat", "resolveStaticMetadata", "modules", "staticMetadata", "collectMetadata", "tree", "metadataItems", "errorMetadataItem", "errorConvention", "modType", "hasErrorConventionComponent", "Boolean", "layoutOrPageMod", "layoutOrPageModType", "metadataExport", "viewportExport", "push", "errorMod", "errorViewportExport", "errorMetadataExport", "cachedResolveMetadataItems", "resolveMetadataItems", "searchParams", "getDynamicParamFromSegment", "createServerParamsForMetadata", "workStore", "parentParams", "treePrefix", "resolveMetadataItemsImpl", "segment", "parallelRoutes", "page", "currentTreePrefix", "isPage", "segmentParam", "currentParams", "value", "param", "params", "layerProps", "filter", "s", "join", "childTree", "keys", "isTitleTruthy", "absolute", "hasTitle", "inheritFromMetadata", "description", "commonOgKeys", "postProcessMetadata", "favicon", "autoFillProps", "hasTwTitle", "hasTwDescription", "hasTwImages", "partialTwitter", "unshift", "collectMetadataExportPreloading", "results", "dynamicMetadataExportFn", "resolvers", "result", "resolve", "catch", "err", "__nextError", "getMetadataFromExport", "getPreloadMetadataExport", "dynamicMetadataResolveState", "currentIndex", "resolvedMetadata", "metadataResults", "dynamicMetadataResolvers", "j", "preloadMetadataExport", "resolveParent", "resolvingIndex", "metadataResult", "currentResolvedMetadata", "process", "env", "NODE_ENV", "freeze", "require", "cloneMetadata", "accumulateMetadata", "Set", "i", "iconMod", "shift", "metadataItem", "template", "size", "warning", "warn", "accumulateViewport", "resolvedViewport", "viewportResults"], "mappings": "AAuBA,6DAA6D;AAC7D,OAAO,cAAa;AAEpB,SAASA,KAAK,QAAQ,QAAO;AAC7B,SACEC,qBAAqB,EACrBC,qBAAqB,QAChB,qBAAoB;AAC3B,SAASC,gBAAgB,EAAEC,cAAc,QAAQ,gCAA+B;AAChF,SAASC,YAAY,QAAQ,4BAA2B;AACxD,SAASC,yBAAyB,QAAQ,mBAAkB;AAC5D,SACEC,sBAAsB,EACtBC,qBAAqB,QAChB,kCAAiC;AACxC,SAASC,cAAc,QAAQ,qBAAoB;AACnD,SACEC,iBAAiB,EACjBC,kBAAkB,EAClBC,eAAe,EACfC,aAAa,EACbC,iBAAiB,EACjBC,mBAAmB,EACnBC,aAAa,EACbC,eAAe,QACV,6BAA4B;AACnC,SAASC,YAAY,QAAQ,4BAA2B;AACxD,SAASC,SAAS,QAAQ,gCAA+B;AACzD,SAASC,mBAAmB,QAAQ,mCAAkC;AACtE,SAASC,gBAAgB,QAAQ,2BAA0B;AAC3D,YAAYC,SAAS,yBAAwB;AA0C7C,SAASC,UAAUC,IAAgC;IACjD,IAAI,CAACA,MAAM;QACT,OAAO;IACT;IAEA,yCAAyC;IACzC,OACE,AAACA,CAAAA,KAAKC,GAAG,KAAK,kBACZD,KAAKC,GAAG,CAACC,QAAQ,GAAGC,UAAU,CAAC,gBAAe,KAChDH,KAAKI,IAAI,KAAK;AAElB;AAEA,SAASC,oBACPC,MAAuB,EACvBC,MAAwB,EACxBC,mBAAmC,EACnCC,eAAgC,EAChCC,cAA8B,EAC9BC,sBAAmC;QAenBL,iBAWEA;IAxBlB,IAAI,CAACE,qBAAqB;IAC1B,MAAM,EAAER,IAAI,EAAEY,KAAK,EAAEC,SAAS,EAAEC,OAAO,EAAEC,QAAQ,EAAE,GAAGP;IAEtD,uDAAuD;IAEvD,IAAIR,MAAM;QACRW,uBAAuBX,IAAI,GAAGA;IAChC;IACA,IAAIY,OAAO;QACTD,uBAAuBC,KAAK,GAAGA;IACjC;IAEA,8FAA8F;IAC9F,IAAIE,WAAW,EAACR,2BAAAA,kBAAAA,OAAQQ,OAAO,qBAAfR,gBAAiBU,cAAc,CAAC,YAAW;QACzD,MAAMC,kBAAkBrC,eACtB;YAAE,GAAG2B,OAAOO,OAAO;YAAEI,QAAQJ;QAAQ,GACrCP,OAAOY,YAAY,EACnB;YAAE,GAAGV,eAAe;YAAEW,2BAA2B;QAAK,GACtDV,eAAeI,OAAO;QAExBP,OAAOO,OAAO,GAAGG;IACnB;IAEA,gGAAgG;IAChG,IAAIJ,aAAa,EAACP,2BAAAA,oBAAAA,OAAQO,SAAS,qBAAjBP,kBAAmBU,cAAc,CAAC,YAAW;QAC7D,MAAMK,oBAAoB1C,iBACxB;YAAE,GAAG4B,OAAOM,SAAS;YAAEK,QAAQL;QAAU,GACzCN,OAAOY,YAAY,EACnB;YAAE,GAAGV,eAAe;YAAEW,2BAA2B;QAAK,GACtDV,eAAeG,SAAS;QAE1BN,OAAOM,SAAS,GAAGQ;IACrB;IACA,IAAIN,UAAU;QACZR,OAAOQ,QAAQ,GAAGA;IACpB;IAEA,OAAOR;AACT;AAEA,+DAA+D;AAC/D,SAASe,cAAc,EACrBhB,MAAM,EACNC,MAAM,EACNC,mBAAmB,EACnBE,cAAc,EACdD,eAAe,EACfc,UAAU,EACVZ,sBAAsB,EASvB;IACC,sFAAsF;IACtF,MAAMQ,eACJ,QAAOb,0BAAAA,OAAQa,YAAY,MAAK,cAC5Bb,OAAOa,YAAY,GACnBZ,OAAOY,YAAY;IACzB,IAAK,MAAMK,QAAQlB,OAAQ;QACzB,MAAMmB,MAAMD;QAEZ,OAAQC;YACN,KAAK;gBAAS;oBACZlB,OAAOmB,KAAK,GAAG7C,aAAayB,OAAOoB,KAAK,EAAEhB,eAAegB,KAAK;oBAC9D;gBACF;YACA,KAAK;gBAAc;oBACjBnB,OAAOoB,UAAU,GAAGzC,kBAClBoB,OAAOqB,UAAU,EACjBR,cACAV;oBAEF;gBACF;YACA,KAAK;gBAAa;oBAChBF,OAAOM,SAAS,GAAGlC,iBACjB2B,OAAOO,SAAS,EAChBM,cACAV,iBACAC,eAAeG,SAAS;oBAE1B;gBACF;YACA,KAAK;gBAAW;oBACdN,OAAOO,OAAO,GAAGlC,eACf0B,OAAOQ,OAAO,EACdK,cACAV,iBACAC,eAAeI,OAAO;oBAExB;gBACF;YACA,KAAK;gBACHP,OAAOqB,QAAQ,GAAGnC,gBAAgBa,OAAOsB,QAAQ;gBACjD;YAEF,KAAK;gBACHrB,OAAOsB,YAAY,GAAGtC,oBAAoBe,OAAOuB,YAAY;gBAC7D;YAEF,KAAK;gBAAS;oBACZtB,OAAOuB,KAAK,GAAGpC,aAAaY,OAAOwB,KAAK;oBACxC;gBACF;YACA,KAAK;gBACHvB,OAAOwB,WAAW,GAAG5C,mBAAmBmB,OAAOyB,WAAW;gBAC1D;YACF,KAAK;gBACHxB,OAAOyB,QAAQ,GAAG5C,gBAAgBkB,OAAO0B,QAAQ;gBACjD;YACF,KAAK;gBAAU;oBACbzB,OAAO0B,MAAM,GAAG5C,cAAciB,OAAO2B,MAAM;oBAC3C;gBACF;YACA,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBAAY;oBACf1B,MAAM,CAACkB,IAAI,GAAG3C,0BAA0BwB,MAAM,CAACmB,IAAI;oBACnD;gBACF;YACA,KAAK;gBAAW;oBACdlB,MAAM,CAACkB,IAAI,GAAG3C,0BAA0BwB,OAAO4B,OAAO;oBACtD;gBACF;YACA,KAAK;gBAAU;oBACb3B,MAAM,CAACkB,IAAI,GAAGjC,cACZc,OAAO6B,MAAM,EACbhB,cACAV;oBAEF;gBACF;YACA,+CAA+C;YAC/C,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH,qCAAqC;gBACrCF,MAAM,CAACkB,IAAI,GAAGnB,MAAM,CAACmB,IAAI,IAAI;gBAC7B;YACF,KAAK;gBACHlB,OAAO6B,KAAK,GAAGC,OAAOC,MAAM,CAAC,CAAC,GAAG/B,OAAO6B,KAAK,EAAE9B,OAAO8B,KAAK;gBAC3D;YACF,KAAK;gBACH7B,OAAOY,YAAY,GAAGA;gBACtB;YAEF;gBAAS;oBACP,IACE,AAACM,CAAAA,QAAQ,cACPA,QAAQ,gBACRA,QAAQ,aAAY,KACtBnB,MAAM,CAACmB,IAAI,IAAI,MACf;wBACAF,WAAWgB,QAAQ,CAACC,GAAG,CACrB,CAAC,qBAAqB,EAAEf,IAAI,qCAAqC,EAAEhB,gBAAgBgC,QAAQ,CAAC,8HAA8H,CAAC;oBAE/N;oBACA;gBACF;QACF;IACF;IACApC,oBACEC,QACAC,QACAC,qBACAC,iBACAC,gBACAC;AAEJ;AAEA,SAAS+B,cAAc,EACrBnC,MAAM,EACND,MAAM,EAIP;IACC,IAAI,CAACA,QAAQ;IACb,IAAK,MAAMkB,QAAQlB,OAAQ;QACzB,MAAMmB,MAAMD;QAEZ,OAAQC;YACN,KAAK;gBAAc;oBACjBlB,OAAOoC,UAAU,GAAGrD,kBAAkBgB,OAAOqC,UAAU;oBACvD;gBACF;YACA,KAAK;gBACHpC,OAAOqC,WAAW,GAAGtC,OAAOsC,WAAW,IAAI;gBAC3C;YACF;gBACE,IAAI,OAAOtC,MAAM,CAACmB,IAAI,KAAK,aAAa;oBACtC,iCAAiC;oBACjClB,MAAM,CAACkB,IAAI,GAAGnB,MAAM,CAACmB,IAAI;gBAC3B;gBACA;QACJ;IACF;AACF;AAEA,eAAeoB,mBACbC,GAAQ,EACRC,KAAU,EACVC,YAA+B;IAE/B,IAAI,OAAOF,IAAIG,gBAAgB,KAAK,YAAY;QAC9C,MAAM,EAAEC,KAAK,EAAE,GAAGF;QAClB,OAAO,CAACG,SACNxD,YAAYyD,KAAK,CACfxD,oBAAoBqD,gBAAgB,EACpC;gBACEI,UAAU,CAAC,iBAAiB,EAAEH,OAAO;gBACrCI,YAAY;oBACV,aAAaJ;gBACf;YACF,GACA,IAAMJ,IAAIG,gBAAgB,CAACF,OAAOI;IAExC;IACA,OAAOL,IAAIS,QAAQ,IAAI;AACzB;AAEA,eAAeC,mBACbV,GAAQ,EACRC,KAAU,EACVC,YAA+B;IAE/B,IAAI,OAAOF,IAAIW,gBAAgB,KAAK,YAAY;QAC9C,MAAM,EAAEP,KAAK,EAAE,GAAGF;QAClB,OAAO,CAACG,SACNxD,YAAYyD,KAAK,CACfxD,oBAAoB6D,gBAAgB,EACpC;gBACEJ,UAAU,CAAC,iBAAiB,EAAEH,OAAO;gBACrCI,YAAY;oBACV,aAAaJ;gBACf;YACF,GACA,IAAMJ,IAAIW,gBAAgB,CAACV,OAAOI;IAExC;IACA,OAAOL,IAAIY,QAAQ,IAAI;AACzB;AAEA,eAAeC,yBACbD,QAAmC,EACnCX,KAAU,EACV3C,IAAkD;QAU7C;IARL,IAAI,EAACsD,4BAAAA,QAAU,CAACtD,KAAK,GAAE,OAAOwD;IAE9B,MAAMC,eAAeH,QAAQ,CAACtD,KAAyB,CAAC0D,GAAG,CACzD,OAAOC,cACL9E,eAAe,MAAM8E,YAAYhB;IAGrC,OAAOc,CAAAA,gCAAAA,aAAcG,MAAM,IAAG,KACzB,QAAA,MAAMC,QAAQC,GAAG,CAACL,kCAAnB,AAAC,MAAkCM,IAAI,KACvCP;AACN;AAEA,eAAeQ,sBACbC,OAAsB,EACtBtB,KAAU;IAEV,MAAM,EAAEW,QAAQ,EAAE,GAAGW;IACrB,IAAI,CAACX,UAAU,OAAO;IAEtB,MAAM,CAAC1D,MAAMY,OAAOC,WAAWC,QAAQ,GAAG,MAAMmD,QAAQC,GAAG,CAAC;QAC1DP,yBAAyBD,UAAUX,OAAO;QAC1CY,yBAAyBD,UAAUX,OAAO;QAC1CY,yBAAyBD,UAAUX,OAAO;QAC1CY,yBAAyBD,UAAUX,OAAO;KAC3C;IAED,MAAMuB,iBAAiB;QACrBtE;QACAY;QACAC;QACAC;QACAC,UAAU2C,SAAS3C,QAAQ;IAC7B;IAEA,OAAOuD;AACT;AAEA,4FAA4F;AAC5F,eAAeC,gBAAgB,EAC7BC,IAAI,EACJC,aAAa,EACbC,iBAAiB,EACjB3B,KAAK,EACLG,KAAK,EACLyB,eAAe,EAQhB;IACC,IAAI7B;IACJ,IAAI8B;IACJ,MAAMC,8BAA8BC,QAClCH,mBAAmBH,IAAI,CAAC,EAAE,CAACG,gBAAgB;IAE7C,IAAIA,iBAAiB;QACnB7B,MAAM,MAAM/D,uBAAuByF,MAAM;QACzCI,UAAUD;IACZ,OAAO;QACL,MAAM,EAAE7B,KAAKiC,eAAe,EAAEH,SAASI,mBAAmB,EAAE,GAC1D,MAAMhG,sBAAsBwF;QAC9B1B,MAAMiC;QACNH,UAAUI;IACZ;IAEA,IAAIJ,SAAS;QACX1B,SAAS,CAAC,CAAC,EAAE0B,SAAS;IACxB;IAEA,MAAMpE,sBAAsB,MAAM4D,sBAAsBI,IAAI,CAAC,EAAE,EAAEzB;IACjE,MAAMkC,iBAAiBnC,MACnB,MAAMU,mBAAmBV,KAAKC,OAAO;QAAEG;IAAM,KAC7C;IAEJ,MAAMgC,iBAAiBpC,MACnB,MAAMD,mBAAmBC,KAAKC,OAAO;QAAEG;IAAM,KAC7C;IAEJuB,cAAcU,IAAI,CAAC;QAACF;QAAgBzE;QAAqB0E;KAAe;IAExE,IAAIL,+BAA+BF,iBAAiB;QAClD,MAAMS,WAAW,MAAMrG,uBAAuByF,MAAMG;QACpD,MAAMU,sBAAsBD,WACxB,MAAMvC,mBAAmBuC,UAAUrC,OAAO;YAAEG;QAAM,KAClD;QACJ,MAAMoC,sBAAsBF,WACxB,MAAM5B,mBAAmB4B,UAAUrC,OAAO;YAAEG;QAAM,KAClD;QAEJwB,iBAAiB,CAAC,EAAE,GAAGY;QACvBZ,iBAAiB,CAAC,EAAE,GAAGlE;QACvBkE,iBAAiB,CAAC,EAAE,GAAGW;IACzB;AACF;AAEA,MAAME,6BAA6B/G,MAAMgH;AACzC,SAASD,8BAA8BC,oBAAoB,GAAE;AAC7D,eAAeA,qBACbhB,IAAgB,EAChBiB,YAAqC,EACrCd,eAA8C,EAC9Ce,0BAAsD,EACtDC,6BAA4D,EAC5DC,SAAoB;IAEpB,MAAMC,eAAe,CAAC;IACtB,MAAMpB,gBAA+B,EAAE;IACvC,MAAMC,oBAA2C;QAAC;QAAM;QAAM;KAAK;IACnE,MAAMoB,aAAalC;IACnB,OAAOmC,yBACLtB,eACAD,MACAsB,YACAD,cACAJ,cACAd,iBACAD,mBACAgB,4BACAC,+BACAC;AAEJ;AAEA,eAAeG,yBACbtB,aAA4B,EAC5BD,IAAgB,EAChB,6FAA6F,GAC7FsB,UAAgC,EAChCD,YAAoB,EACpBJ,YAAqC,EACrCd,eAA8C,EAC9CD,iBAAwC,EACxCgB,0BAAsD,EACtDC,6BAA4D,EAC5DC,SAAoB;IAEpB,MAAM,CAACI,SAASC,gBAAgB,EAAEC,IAAI,EAAE,CAAC,GAAG1B;IAC5C,MAAM2B,oBACJL,cAAcA,WAAW9B,MAAM,GAAG;WAAI8B;QAAYE;KAAQ,GAAG;QAACA;KAAQ;IACxE,MAAMI,SAAS,OAAOF,SAAS;IAE/B,iCAAiC;IACjC,MAAMG,eAAeX,2BAA2BM;IAChD;;GAEC,GACD,IAAIM,gBAAgBT;IACpB,IAAIQ,gBAAgBA,aAAaE,KAAK,KAAK,MAAM;QAC/CD,gBAAgB;YACd,GAAGT,YAAY;YACf,CAACQ,aAAaG,KAAK,CAAC,EAAEH,aAAaE,KAAK;QAC1C;IACF;IAEA,MAAME,SAASd,8BAA8BW,eAAeV;IAE5D,IAAIc;IACJ,IAAIN,QAAQ;QACVM,aAAa;YACXD;YACAhB;QACF;IACF,OAAO;QACLiB,aAAa;YACXD;QACF;IACF;IAEA,MAAMlC,gBAAgB;QACpBC;QACAC;QACAC;QACAC;QACA5B,OAAO2D;QACPxD,OAAOiD,iBACL,yCAAyC;SACxCQ,MAAM,CAAC,CAACC,IAAMA,MAAM/G,kBACpBgH,IAAI,CAAC;IACV;IAEA,IAAK,MAAMpF,OAAOwE,eAAgB;QAChC,MAAMa,YAAYb,cAAc,CAACxE,IAAI;QACrC,MAAMsE,yBACJtB,eACAqC,WACAX,mBACAG,eACAb,cACAd,iBACAD,mBACAgB,4BACAC,+BACAC;IAEJ;IAEA,IAAIvD,OAAO0E,IAAI,CAACd,gBAAgBjC,MAAM,KAAK,KAAKW,iBAAiB;QAC/D,0EAA0E;QAC1E,qCAAqC;QACrCF,cAAcU,IAAI,CAACT;IACrB;IAEA,OAAOD;AACT;AAKA,MAAMuC,gBAAgB,CAACtF,QACrB,CAAC,EAACA,yBAAAA,MAAOuF,QAAQ;AACnB,MAAMC,WAAW,CAACxD,WAA+BsD,cAActD,4BAAAA,SAAUhC,KAAK;AAE9E,SAASyF,oBACP5G,MAA4C,EAC5CmD,QAA0B;IAE1B,IAAInD,QAAQ;QACV,IAAI,CAAC2G,SAAS3G,WAAW2G,SAASxD,WAAW;YAC3CnD,OAAOmB,KAAK,GAAGgC,SAAShC,KAAK;QAC/B;QACA,IAAI,CAACnB,OAAO6G,WAAW,IAAI1D,SAAS0D,WAAW,EAAE;YAC/C7G,OAAO6G,WAAW,GAAG1D,SAAS0D,WAAW;QAC3C;IACF;AACF;AAEA,6DAA6D;AAC7D,MAAMC,eAAe;IAAC;IAAS;IAAe;CAAS;AACvD,SAASC,oBACP5D,QAA0B,EAC1B6D,OAAY,EACZ7G,cAA8B,EAC9BD,eAAgC;IAEhC,MAAM,EAAEI,SAAS,EAAEC,OAAO,EAAE,GAAG4C;IAE/B,IAAI7C,WAAW;QACb,kEAAkE;QAClE,wCAAwC;QACxC,IAAI2G,gBAIC,CAAC;QACN,MAAMC,aAAaP,SAASpG;QAC5B,MAAM4G,mBAAmB5G,2BAAAA,QAASsG,WAAW;QAC7C,MAAMO,cAAc7C,QAClBhE,CAAAA,2BAAAA,QAASE,cAAc,CAAC,cAAaF,QAAQI,MAAM;QAErD,IAAI,CAACuG,YAAY;YACf,IAAIT,cAAcnG,UAAUa,KAAK,GAAG;gBAClC8F,cAAc9F,KAAK,GAAGb,UAAUa,KAAK;YACvC,OAAO,IAAIgC,SAAShC,KAAK,IAAIsF,cAActD,SAAShC,KAAK,GAAG;gBAC1D8F,cAAc9F,KAAK,GAAGgC,SAAShC,KAAK;YACtC;QACF;QACA,IAAI,CAACgG,kBACHF,cAAcJ,WAAW,GACvBvG,UAAUuG,WAAW,IAAI1D,SAAS0D,WAAW,IAAIxD;QACrD,IAAI,CAAC+D,aAAaH,cAActG,MAAM,GAAGL,UAAUK,MAAM;QAEzD,IAAImB,OAAO0E,IAAI,CAACS,eAAexD,MAAM,GAAG,GAAG;YACzC,MAAM4D,iBAAiBhJ,eACrB4I,eACA9D,SAASvC,YAAY,EACrBV,iBACAC,eAAeI,OAAO;YAExB,IAAI4C,SAAS5C,OAAO,EAAE;gBACpB4C,SAAS5C,OAAO,GAAGuB,OAAOC,MAAM,CAAC,CAAC,GAAGoB,SAAS5C,OAAO,EAAE;oBACrD,GAAI,CAAC2G,cAAc;wBAAE/F,KAAK,EAAEkG,kCAAAA,eAAgBlG,KAAK;oBAAC,CAAC;oBACnD,GAAI,CAACgG,oBAAoB;wBACvBN,WAAW,EAAEQ,kCAAAA,eAAgBR,WAAW;oBAC1C,CAAC;oBACD,GAAI,CAACO,eAAe;wBAAEzG,MAAM,EAAE0G,kCAAAA,eAAgB1G,MAAM;oBAAC,CAAC;gBACxD;YACF,OAAO;gBACLwC,SAAS5C,OAAO,GAAG8G;YACrB;QACF;IACF;IAEA,0EAA0E;IAC1E,+CAA+C;IAC/CT,oBAAoBtG,WAAW6C;IAC/ByD,oBAAoBrG,SAAS4C;IAE7B,IAAI6D,SAAS;QACX,IAAI,CAAC7D,SAAS5B,KAAK,EAAE;YACnB4B,SAAS5B,KAAK,GAAG;gBACf9B,MAAM,EAAE;gBACRY,OAAO,EAAE;YACX;QACF;QAEA8C,SAAS5B,KAAK,CAAC9B,IAAI,CAAC6H,OAAO,CAACN;IAC9B;IAEA,OAAO7D;AACT;AAMA,SAASoE,gCACPC,OAAiC,EACjCC,uBAAyD,EACzDC,SAA4C;IAE5C,MAAMC,SAASF,wBACb,IAAI/D,QAAa,CAACkE;QAChBF,UAAU9C,IAAI,CAACgD;IACjB;IAGF,IAAID,kBAAkBjE,SAAS;QAC7B,8CAA8C;QAC9C,+CAA+C;QAC/C,4CAA4C;QAC5C,oDAAoD;QACpDiE,OAAOE,KAAK,CAAC,CAACC;YACZ,OAAO;gBACLC,aAAaD;YACf;QACF;IACF;IACAN,QAAQ5C,IAAI,CAAC+C;AACf;AAEA,eAAeK,sBACbC,wBAEmD,EACnDC,2BAGC,EACDhE,aAA4B,EAC5BiE,YAAoB,EACpBC,gBAA8B,EAC9BC,eAAyC;IAEzC,MAAM3D,iBAAiBuD,yBAAyB/D,aAAa,CAACiE,aAAa;IAC3E,MAAMG,2BAA2BJ,4BAA4BR,SAAS;IACtE,IAAIvE,WAAwB;IAC5B,IAAI,OAAOuB,mBAAmB,YAAY;QACxC,wDAAwD;QACxD,IAAI,CAAC4D,yBAAyB7E,MAAM,EAAE;YACpC,IAAK,IAAI8E,IAAIJ,cAAcI,IAAIrE,cAAcT,MAAM,EAAE8E,IAAK;gBACxD,MAAMC,wBAAwBP,yBAAyB/D,aAAa,CAACqE,EAAE;gBACvE,6EAA6E;gBAC7E,IAAI,OAAOC,0BAA0B,YAAY;oBAC/CjB,gCACEc,iBACAG,uBACAF;gBAEJ;YACF;QACF;QAEA,MAAMG,gBACJH,wBAAwB,CAACJ,4BAA4BQ,cAAc,CAAC;QACtE,MAAMC,iBACJN,eAAe,CAACH,4BAA4BQ,cAAc,GAAG;QAE/D,uFAAuF;QACvF,qEAAqE;QACrE,MAAME,0BACJC,QAAQC,GAAG,CAACC,QAAQ,KAAK,gBACrBjH,OAAOkH,MAAM,CACXC,QAAQ,oBAAoBC,aAAa,CAACd,qBAE5CA;QAEN,qFAAqF;QACrF,8FAA8F;QAC9F,mGAAmG;QACnGK,cAAcG;QACdzF,WACEwF,0BAA0BjF,UAAU,MAAMiF,iBAAiBA;QAE7D,IAAIxF,YAAY,OAAOA,aAAa,YAAY,iBAAiBA,UAAU;YACzE,iDAAiD;YACjD,MAAMA,QAAQ,CAAC,cAAc;QAC/B;IACF,OAAO,IAAIuB,mBAAmB,QAAQ,OAAOA,mBAAmB,UAAU;QACxE,yCAAyC;QACzCvB,WAAWuB;IACb;IAEA,OAAOvB;AACT;AAEA,OAAO,eAAegG,mBACpBjF,aAA4B,EAC5BhE,eAAgC;IAEhC,MAAMkI,mBAAmBlK;IACzB,MAAMmK,kBAAoD,EAAE;IAE5D,IAAIlI,iBAAiC;QACnCgB,OAAO;QACPZ,SAAS;QACTD,WAAW;IACb;IAEA,uFAAuF;IACvF,yGAAyG;IACzG,MAAMgI,2BAA2B;QAC/BZ,WAAW,EAAE;QACbgB,gBAAgB;IAClB;IACA,MAAM1H,aAAa;QACjBgB,UAAU,IAAIoH;IAChB;IAEA,IAAIpC;IAEJ,kDAAkD;IAClD,+EAA+E;IAC/E,MAAM5G,yBAAyB;QAC7BX,MAAM,EAAE;QACRY,OAAO,EAAE;IACX;IACA,IAAK,IAAIgJ,IAAI,GAAGA,IAAInF,cAAcT,MAAM,EAAE4F,IAAK;YAKrBpJ;QAJxB,MAAMA,sBAAsBiE,aAAa,CAACmF,EAAE,CAAC,EAAE;QAE/C,yEAAyE;QACzE,qEAAqE;QACrE,IAAIA,KAAK,KAAK7J,UAAUS,wCAAAA,4BAAAA,oBAAqBR,IAAI,qBAAzBQ,yBAA2B,CAAC,EAAE,GAAG;gBACvCA;YAAhB,MAAMqJ,UAAUrJ,wCAAAA,6BAAAA,oBAAqBR,IAAI,qBAAzBQ,2BAA2BsJ,KAAK;YAChD,IAAIF,MAAM,GAAGrC,UAAUsC;QACzB;QAEA,MAAMnG,WAAW,MAAM6E,sBACrB,CAACwB,eAAiBA,YAAY,CAAC,EAAE,EACjClB,0BACApE,eACAmF,GACAjB,kBACAC;QAGFtH,cAAc;YACZf,QAAQoI;YACRrI,QAAQoD;YACRjD;YACAD;YACAE;YACAa;YACAZ;QACF;QAEA,gFAAgF;QAChF,kDAAkD;QAClD,IAAIiJ,IAAInF,cAAcT,MAAM,GAAG,GAAG;gBAEvB2E,yBACIA,6BACFA;YAHXjI,iBAAiB;gBACfgB,OAAOiH,EAAAA,0BAAAA,iBAAiBjH,KAAK,qBAAtBiH,wBAAwBqB,QAAQ,KAAI;gBAC3CnJ,WAAW8H,EAAAA,8BAAAA,iBAAiB9H,SAAS,qBAA1B8H,4BAA4BjH,KAAK,CAACsI,QAAQ,KAAI;gBACzDlJ,SAAS6H,EAAAA,4BAAAA,iBAAiB7H,OAAO,qBAAxB6H,0BAA0BjH,KAAK,CAACsI,QAAQ,KAAI;YACvD;QACF;IACF;IAEA,IACErJ,uBAAuBX,IAAI,CAACgE,MAAM,GAAG,KACrCrD,uBAAuBC,KAAK,CAACoD,MAAM,GAAG,GACtC;QACA,IAAI,CAAC2E,iBAAiB7G,KAAK,EAAE;YAC3B6G,iBAAiB7G,KAAK,GAAG;gBACvB9B,MAAM,EAAE;gBACRY,OAAO,EAAE;YACX;YACA,IAAID,uBAAuBX,IAAI,CAACgE,MAAM,GAAG,GAAG;gBAC1C2E,iBAAiB7G,KAAK,CAAC9B,IAAI,CAAC6H,OAAO,IAAIlH,uBAAuBX,IAAI;YACpE;YACA,IAAIW,uBAAuBC,KAAK,CAACoD,MAAM,GAAG,GAAG;gBAC3C2E,iBAAiB7G,KAAK,CAAClB,KAAK,CAACiH,OAAO,IAAIlH,uBAAuBC,KAAK;YACtE;QACF;IACF;IAEA,qGAAqG;IACrG,IAAIW,WAAWgB,QAAQ,CAAC0H,IAAI,GAAG,GAAG;QAChC,KAAK,MAAMC,WAAW3I,WAAWgB,QAAQ,CAAE;YACzCzC,IAAIqK,IAAI,CAACD;QACX;IACF;IAEA,OAAO5C,oBACLqB,kBACApB,SACA7G,gBACAD;AAEJ;AAEA,OAAO,eAAe2J,mBACpB3F,aAA4B;IAE5B,MAAM4F,mBAAqC3L;IAE3C,MAAM4L,kBAAoD,EAAE;IAC5D,MAAMzB,2BAA2B;QAC/BZ,WAAW,EAAE;QACbgB,gBAAgB;IAClB;IACA,IAAK,IAAIW,IAAI,GAAGA,IAAInF,cAAcT,MAAM,EAAE4F,IAAK;QAC7C,MAAMrG,WAAW,MAAMgF,sBACrB,CAACwB,eAAiBA,YAAY,CAAC,EAAE,EACjClB,0BACApE,eACAmF,GACAS,kBACAC;QAGF5H,cAAc;YACZnC,QAAQ8J;YACR/J,QAAQiD;QACV;IACF;IACA,OAAO8G;AACT"}