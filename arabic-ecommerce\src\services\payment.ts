import Stripe from 'stripe';
import {
  PaymentMethod,
  PaymentStatus,
  Currency,
  CreatePaymentRequest,
  CreatePaymentResponse,
  ConfirmPaymentRequest,
  ConfirmPaymentResponse,
  PaymentInfo,
  StripePaymentData,
  TabbyPaymentData,
  STCPayPaymentData,
  PaymentOption
} from '@/types/payment';

// إعدادات بوابات الدفع
const PAYMENT_CONFIG = {
  stripe: {
    publicKey: process.env.NEXT_PUBLIC_STRIPE_PUBLIC_KEY || 'pk_test_placeholder',
    secretKey: process.env.STRIPE_SECRET_KEY || 'sk_test_placeholder',
    webhookSecret: process.env.STRIPE_WEBHOOK_SECRET || 'whsec_placeholder',
    currency: 'SAR' as Currency,
  },
  tabby: {
    publicKey: process.env.NEXT_PUBLIC_TABBY_PUBLIC_KEY || 'pk_test_placeholder',
    secretKey: process.env.TABBY_SECRET_KEY || 'sk_test_placeholder',
    merchantCode: process.env.TABBY_MERCHANT_CODE || 'merchant_placeholder',
    currency: 'SAR' as Currency,
    environment: (process.env.TABBY_ENVIRONMENT as 'sandbox' | 'production') || 'sandbox',
  },
  stcPay: {
    merchantId: process.env.STC_PAY_MERCHANT_ID || 'merchant_placeholder',
    apiKey: process.env.STC_PAY_API_KEY || 'api_key_placeholder',
    secretKey: process.env.STC_PAY_SECRET_KEY || 'secret_placeholder',
    environment: (process.env.STC_PAY_ENVIRONMENT as 'sandbox' | 'production') || 'sandbox',
  }
};

// خيارات الدفع المتاحة
export const PAYMENT_OPTIONS: PaymentOption[] = [
  {
    method: 'stripe',
    name: 'Credit/Debit Card',
    nameAr: 'بطاقة ائتمانية/مدينة',
    description: 'Pay securely with your credit or debit card',
    descriptionAr: 'ادفع بأمان باستخدام بطاقتك الائتمانية أو المدينة',
    icon: '💳',
    enabled: true,
    fees: 0,
    minAmount: 1,
    maxAmount: 100000,
    supportedCurrencies: ['SAR', 'USD', 'EUR'],
    processingTime: 'Instant',
    processingTimeAr: 'فوري',
  },
  {
    method: 'tabby',
    name: 'Buy Now, Pay Later',
    nameAr: 'اشتري الآن وادفع لاحقاً',
    description: 'Split your payment into 4 interest-free installments',
    descriptionAr: 'قسم دفعتك إلى 4 أقساط بدون فوائد',
    icon: '📅',
    enabled: true,
    fees: 0,
    minAmount: 100,
    maxAmount: 50000,
    supportedCurrencies: ['SAR'],
    processingTime: '1-2 business days',
    processingTimeAr: '1-2 يوم عمل',
  },
  {
    method: 'stc_pay',
    name: 'STC Pay',
    nameAr: 'STC Pay',
    description: 'Pay with your STC Pay digital wallet',
    descriptionAr: 'ادفع باستخدام محفظة STC Pay الرقمية',
    icon: '📱',
    enabled: true,
    fees: 0,
    minAmount: 1,
    maxAmount: 10000,
    supportedCurrencies: ['SAR'],
    processingTime: 'Instant',
    processingTimeAr: 'فوري',
  },
  {
    method: 'cash_on_delivery',
    name: 'Cash on Delivery',
    nameAr: 'الدفع عند الاستلام',
    description: 'Pay cash when your order is delivered',
    descriptionAr: 'ادفع نقداً عند استلام طلبك',
    icon: '💵',
    enabled: true,
    fees: 10, // رسوم إضافية 10 ريال
    minAmount: 50,
    maxAmount: 5000,
    supportedCurrencies: ['SAR'],
    processingTime: '3-5 business days',
    processingTimeAr: '3-5 أيام عمل',
  },
];

// خدمة Stripe
class StripeService {
  private stripe: Stripe | null = null;

  constructor() {
    if (this.isConfigured()) {
      this.stripe = new Stripe(PAYMENT_CONFIG.stripe.secretKey, {
        apiVersion: '2024-12-18.acacia',
      });
    }
  }

  private isConfigured(): boolean {
    return !!(
      PAYMENT_CONFIG.stripe.secretKey &&
      PAYMENT_CONFIG.stripe.secretKey !== 'sk_test_placeholder'
    );
  }

  async createPayment(request: CreatePaymentRequest): Promise<StripePaymentData> {
    if (!this.stripe || !this.isConfigured()) {
      throw new Error('Stripe is not configured');
    }

    try {
      const paymentIntent = await this.stripe.paymentIntents.create({
        amount: Math.round(request.amount * 100), // تحويل إلى هللات
        currency: request.currency.toLowerCase(),
        metadata: {
          orderId: request.orderId,
          customerEmail: request.customerInfo.email,
          ...request.metadata,
        },
        description: `Order #${request.orderId}`,
      });

      return {
        paymentIntentId: paymentIntent.id,
        clientSecret: paymentIntent.client_secret!,
        status: paymentIntent.status,
        amount: paymentIntent.amount / 100,
        currency: paymentIntent.currency.toUpperCase(),
      };
    } catch (error) {
      console.error('Stripe payment creation failed:', error);
      throw new Error('Failed to create Stripe payment');
    }
  }

  async confirmPayment(paymentIntentId: string): Promise<ConfirmPaymentResponse> {
    if (!this.stripe || !this.isConfigured()) {
      throw new Error('Stripe is not configured');
    }

    try {
      const paymentIntent = await this.stripe.paymentIntents.retrieve(paymentIntentId);
      
      return {
        success: paymentIntent.status === 'succeeded',
        status: this.mapStripeStatus(paymentIntent.status),
        transactionId: paymentIntent.id,
      };
    } catch (error) {
      console.error('Stripe payment confirmation failed:', error);
      return {
        success: false,
        status: 'failed',
        error: 'Failed to confirm payment',
      };
    }
  }

  private mapStripeStatus(stripeStatus: string): PaymentStatus {
    switch (stripeStatus) {
      case 'succeeded':
        return 'completed';
      case 'processing':
        return 'processing';
      case 'requires_payment_method':
      case 'requires_confirmation':
      case 'requires_action':
        return 'pending';
      case 'canceled':
        return 'cancelled';
      default:
        return 'failed';
    }
  }
}

// خدمة تابي (محاكاة)
class TabbyService {
  private isConfigured(): boolean {
    return !!(
      PAYMENT_CONFIG.tabby.secretKey &&
      PAYMENT_CONFIG.tabby.secretKey !== 'sk_test_placeholder'
    );
  }

  async createPayment(request: CreatePaymentRequest): Promise<TabbyPaymentData> {
    if (!this.isConfigured()) {
      // محاكاة للتطوير
      return {
        sessionId: `tabby_session_${Date.now()}`,
        checkoutUrl: `https://checkout.tabby.ai/session_${Date.now()}`,
        status: 'created',
        amount: request.amount,
        currency: request.currency,
        installments: 4,
      };
    }

    // هنا سيكون التكامل الحقيقي مع تابي
    try {
      // TODO: تكامل حقيقي مع Tabby API
      const response = await fetch('https://api.tabby.ai/api/v2/checkout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${PAYMENT_CONFIG.tabby.secretKey}`,
        },
        body: JSON.stringify({
          payment: {
            amount: request.amount.toString(),
            currency: request.currency,
            description: `Order #${request.orderId}`,
          },
          lang: 'ar',
          merchant_code: PAYMENT_CONFIG.tabby.merchantCode,
          merchant_urls: {
            success: `${process.env.NEXT_PUBLIC_APP_URL}/payment/success`,
            cancel: `${process.env.NEXT_PUBLIC_APP_URL}/payment/cancel`,
            failure: `${process.env.NEXT_PUBLIC_APP_URL}/payment/failure`,
          },
        }),
      });

      const data = await response.json();
      
      return {
        sessionId: data.id,
        checkoutUrl: data.configuration.available_products.installments[0].web_url,
        status: 'created',
        amount: request.amount,
        currency: request.currency,
        installments: 4,
      };
    } catch (error) {
      console.error('Tabby payment creation failed:', error);
      throw new Error('Failed to create Tabby payment');
    }
  }

  async confirmPayment(sessionId: string): Promise<ConfirmPaymentResponse> {
    // محاكاة للتطوير
    return {
      success: true,
      status: 'completed',
      transactionId: sessionId,
    };
  }
}

// خدمة STC Pay (محاكاة)
class STCPayService {
  private isConfigured(): boolean {
    return !!(
      PAYMENT_CONFIG.stcPay.apiKey &&
      PAYMENT_CONFIG.stcPay.apiKey !== 'api_key_placeholder'
    );
  }

  async createPayment(request: CreatePaymentRequest): Promise<STCPayPaymentData> {
    if (!this.isConfigured()) {
      // محاكاة للتطوير
      return {
        transactionId: `stc_${Date.now()}`,
        paymentUrl: `https://stcpay.com.sa/payment/${Date.now()}`,
        status: 'created',
        amount: request.amount,
        currency: request.currency,
      };
    }

    // هنا سيكون التكامل الحقيقي مع STC Pay
    try {
      // TODO: تكامل حقيقي مع STC Pay API
      const response = await fetch('https://api.stcpay.com.sa/payment/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${PAYMENT_CONFIG.stcPay.apiKey}`,
        },
        body: JSON.stringify({
          merchant_id: PAYMENT_CONFIG.stcPay.merchantId,
          amount: request.amount,
          currency: request.currency,
          order_id: request.orderId,
          return_url: `${process.env.NEXT_PUBLIC_APP_URL}/payment/return`,
        }),
      });

      const data = await response.json();
      
      return {
        transactionId: data.transaction_id,
        paymentUrl: data.payment_url,
        status: 'created',
        amount: request.amount,
        currency: request.currency,
      };
    } catch (error) {
      console.error('STC Pay payment creation failed:', error);
      throw new Error('Failed to create STC Pay payment');
    }
  }

  async confirmPayment(transactionId: string): Promise<ConfirmPaymentResponse> {
    // محاكاة للتطوير
    return {
      success: true,
      status: 'completed',
      transactionId: transactionId,
    };
  }
}

// الخدمة الرئيسية للدفع
export class PaymentService {
  private stripeService = new StripeService();
  private tabbyService = new TabbyService();
  private stcPayService = new STCPayService();

  async createPayment(request: CreatePaymentRequest): Promise<CreatePaymentResponse> {
    try {
      let data: StripePaymentData | TabbyPaymentData | STCPayPaymentData;
      
      switch (request.method) {
        case 'stripe':
          data = await this.stripeService.createPayment(request);
          break;
        case 'tabby':
          data = await this.tabbyService.createPayment(request);
          break;
        case 'stc_pay':
          data = await this.stcPayService.createPayment(request);
          break;
        case 'cash_on_delivery':
          // الدفع عند الاستلام لا يحتاج معالجة خاصة
          data = {
            paymentIntentId: `cod_${Date.now()}`,
            clientSecret: '',
            status: 'pending',
            amount: request.amount,
            currency: request.currency,
          } as StripePaymentData;
          break;
        default:
          throw new Error(`Unsupported payment method: ${request.method}`);
      }

      return {
        success: true,
        paymentId: this.extractPaymentId(data),
        data,
      };
    } catch (error) {
      console.error('Payment creation failed:', error);
      return {
        success: false,
        paymentId: '',
        data: {} as any,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  async confirmPayment(request: ConfirmPaymentRequest): Promise<ConfirmPaymentResponse> {
    try {
      switch (request.method) {
        case 'stripe':
          return await this.stripeService.confirmPayment(request.paymentId);
        case 'tabby':
          return await this.tabbyService.confirmPayment(request.paymentId);
        case 'stc_pay':
          return await this.stcPayService.confirmPayment(request.paymentId);
        case 'cash_on_delivery':
          return {
            success: true,
            status: 'pending',
            transactionId: request.paymentId,
          };
        default:
          throw new Error(`Unsupported payment method: ${request.method}`);
      }
    } catch (error) {
      console.error('Payment confirmation failed:', error);
      return {
        success: false,
        status: 'failed',
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  getAvailablePaymentMethods(amount: number, currency: Currency): PaymentOption[] {
    return PAYMENT_OPTIONS.filter(option => {
      return (
        option.enabled &&
        option.supportedCurrencies.includes(currency) &&
        amount >= (option.minAmount || 0) &&
        amount <= (option.maxAmount || Infinity)
      );
    });
  }

  private extractPaymentId(data: StripePaymentData | TabbyPaymentData | STCPayPaymentData): string {
    if ('paymentIntentId' in data) {
      return data.paymentIntentId;
    } else if ('sessionId' in data) {
      return data.sessionId;
    } else if ('transactionId' in data) {
      return data.transactionId;
    }
    return '';
  }
}

// إنشاء instance واحد للاستخدام
export const paymentService = new PaymentService();
