'use client';

import React, { useState, useEffect } from 'react';
import { loadStripe } from '@stripe/stripe-js';
import {
  Elements,
  CardElement,
  useStripe,
  useElements,
} from '@stripe/react-stripe-js';
import { StripePaymentData } from '@/types/payment';
import { usePayment } from '@/contexts/PaymentContext';

// تحميل Stripe
const stripePromise = loadStripe(
  process.env.NEXT_PUBLIC_STRIPE_PUBLIC_KEY || 'pk_test_placeholder'
);

interface StripePaymentProps {
  paymentData: StripePaymentData;
  onSuccess: (paymentIntentId: string) => void;
  onError: (error: string) => void;
  className?: string;
}

export default function StripePayment({
  paymentData,
  onSuccess,
  onError,
  className = '',
}: StripePaymentProps) {
  return (
    <Elements stripe={stripePromise}>
      <StripePaymentForm
        paymentData={paymentData}
        onSuccess={onSuccess}
        onError={onError}
        className={className}
      />
    </Elements>
  );
}

function StripePaymentForm({
  paymentData,
  onSuccess,
  onError,
  className = '',
}: StripePaymentProps) {
  const stripe = useStripe();
  const elements = useElements();
  const { state } = usePayment();
  
  const [processing, setProcessing] = useState(false);
  const [cardComplete, setCardComplete] = useState(false);
  const [cardError, setCardError] = useState<string | null>(null);

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    if (!stripe || !elements) {
      onError('Stripe لم يتم تحميل بعد');
      return;
    }

    const cardElement = elements.getElement(CardElement);
    if (!cardElement) {
      onError('عنصر البطاقة غير متاح');
      return;
    }

    if (!cardComplete) {
      onError('يرجى إدخال بيانات البطاقة كاملة');
      return;
    }

    setProcessing(true);

    try {
      const { error, paymentIntent } = await stripe.confirmCardPayment(
        paymentData.clientSecret,
        {
          payment_method: {
            card: cardElement,
          },
        }
      );

      if (error) {
        onError(error.message || 'فشل في معالجة الدفع');
      } else if (paymentIntent && paymentIntent.status === 'succeeded') {
        onSuccess(paymentIntent.id);
      } else {
        onError('حالة الدفع غير متوقعة');
      }
    } catch (error) {
      onError('خطأ في معالجة الدفع');
    } finally {
      setProcessing(false);
    }
  };

  const handleCardChange = (event: any) => {
    setCardComplete(event.complete);
    setCardError(event.error ? event.error.message : null);
  };

  return (
    <div className={`bg-white rounded-lg border border-gray-200 p-6 ${className}`}>
      <div className="mb-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          💳 الدفع بالبطاقة الائتمانية
        </h3>
        <p className="text-sm text-gray-600">
          أدخل بيانات بطاقتك الائتمانية أو المدينة للدفع الآمن
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* عنصر البطاقة */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            بيانات البطاقة
          </label>
          <div className="border border-gray-300 rounded-md p-3 bg-white">
            <CardElement
              onChange={handleCardChange}
              options={{
                style: {
                  base: {
                    fontSize: '16px',
                    color: '#374151',
                    fontFamily: 'Cairo, sans-serif',
                    '::placeholder': {
                      color: '#9CA3AF',
                    },
                  },
                  invalid: {
                    color: '#EF4444',
                  },
                },
                hidePostalCode: true,
              }}
            />
          </div>
          {cardError && (
            <p className="mt-2 text-sm text-red-600">{cardError}</p>
          )}
        </div>

        {/* معلومات الدفع */}
        <div className="bg-gray-50 rounded-lg p-4">
          <h4 className="font-medium text-gray-900 mb-3">ملخص الدفع</h4>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-600">المبلغ:</span>
              <span className="font-medium">
                {paymentData.amount} {paymentData.currency}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">طريقة الدفع:</span>
              <span className="font-medium">بطاقة ائتمانية/مدينة</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">الحالة:</span>
              <span className="font-medium text-blue-600">
                {paymentData.status === 'requires_payment_method' ? 'في انتظار الدفع' : paymentData.status}
              </span>
            </div>
          </div>
        </div>

        {/* أزرار الإجراءات */}
        <div className="flex space-x-3 space-x-reverse">
          <button
            type="submit"
            disabled={!stripe || processing || !cardComplete || state.processing}
            className={`
              flex-1 py-3 px-4 rounded-lg font-medium transition-colors duration-200
              ${processing || !cardComplete || state.processing
                ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                : 'bg-primary-600 hover:bg-primary-700 text-white'
              }
            `}
          >
            {processing || state.processing ? (
              <div className="flex items-center justify-center space-x-2 space-x-reverse">
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                <span>جاري المعالجة...</span>
              </div>
            ) : (
              `ادفع ${paymentData.amount} ${paymentData.currency}`
            )}
          </button>
        </div>

        {/* رسالة الأمان */}
        <div className="flex items-center space-x-2 space-x-reverse text-xs text-gray-500">
          <span>🔒</span>
          <span>
            دفعتك محمية بتشفير SSL وتتم معالجتها بواسطة Stripe
          </span>
        </div>
      </form>
    </div>
  );
}

// مكون لعرض حالة الدفع
export function StripePaymentStatus({ 
  status, 
  className = '' 
}: { 
  status: string; 
  className?: string; 
}) {
  const getStatusInfo = (status: string) => {
    switch (status) {
      case 'succeeded':
        return {
          icon: '✅',
          text: 'تم الدفع بنجاح',
          color: 'text-green-600',
          bgColor: 'bg-green-50',
        };
      case 'processing':
        return {
          icon: '⏳',
          text: 'جاري معالجة الدفع',
          color: 'text-blue-600',
          bgColor: 'bg-blue-50',
        };
      case 'requires_payment_method':
      case 'requires_confirmation':
      case 'requires_action':
        return {
          icon: '⏱️',
          text: 'في انتظار الدفع',
          color: 'text-yellow-600',
          bgColor: 'bg-yellow-50',
        };
      case 'canceled':
        return {
          icon: '❌',
          text: 'تم إلغاء الدفع',
          color: 'text-red-600',
          bgColor: 'bg-red-50',
        };
      default:
        return {
          icon: '❓',
          text: 'حالة غير معروفة',
          color: 'text-gray-600',
          bgColor: 'bg-gray-50',
        };
    }
  };

  const statusInfo = getStatusInfo(status);

  return (
    <div className={`inline-flex items-center space-x-2 space-x-reverse px-3 py-2 rounded-full ${statusInfo.bgColor} ${className}`}>
      <span>{statusInfo.icon}</span>
      <span className={`text-sm font-medium ${statusInfo.color}`}>
        {statusInfo.text}
      </span>
    </div>
  );
}

// مكون لعرض معلومات البطاقة (مخفية جزئياً)
export function StripeCardInfo({ 
  last4, 
  brand, 
  className = '' 
}: { 
  last4?: string; 
  brand?: string; 
  className?: string; 
}) {
  const getBrandIcon = (brand?: string) => {
    switch (brand?.toLowerCase()) {
      case 'visa':
        return '💳';
      case 'mastercard':
        return '💳';
      case 'amex':
        return '💳';
      default:
        return '💳';
    }
  };

  if (!last4) return null;

  return (
    <div className={`inline-flex items-center space-x-2 space-x-reverse text-sm text-gray-600 ${className}`}>
      <span>{getBrandIcon(brand)}</span>
      <span>•••• •••• •••• {last4}</span>
      {brand && (
        <span className="text-xs text-gray-500 uppercase">
          {brand}
        </span>
      )}
    </div>
  );
}
