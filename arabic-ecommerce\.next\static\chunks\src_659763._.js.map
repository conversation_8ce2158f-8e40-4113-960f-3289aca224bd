{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/New%20folder%20%288%29/arabic-ecommerce/src/components/Navbar.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { useState } from 'react';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { useCart } from '@/contexts/CartContext';\nimport { LogOut, User, Settings, ShoppingBag } from 'lucide-react';\n\nexport default function Navbar() {\n  const { isAuthenticated, isAdmin, appUser, signOut } = useAuth();\n  const { items } = useCart();\n  const [showUserMenu, setShowUserMenu] = useState(false);\n\n  const cartItemsCount = items.reduce((total, item) => total + item.quantity, 0);\n\n  const handleSignOut = async () => {\n    await signOut();\n    setShowUserMenu(false);\n  };\n\n  return (\n    <nav className=\"bg-white shadow-lg border-b border-gray-200\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center h-16\">\n          {/* Logo */}\n          <div className=\"flex-shrink-0\">\n            <Link href=\"/\" className=\"text-2xl font-bold text-primary-600\">\n              🛍️ المتجر العربي\n            </Link>\n          </div>\n\n          {/* Navigation Links */}\n          <div className=\"hidden md:block\">\n            <div className=\"mr-10 flex items-baseline space-x-4 space-x-reverse\">\n              <Link\n                href=\"/\"\n                className=\"text-gray-700 hover:text-primary-600 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 flex items-center\"\n              >\n                🏠 الصفحة الرئيسية\n              </Link>\n              <Link\n                href=\"/products\"\n                className=\"text-gray-700 hover:text-primary-600 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 flex items-center\"\n              >\n                📦 المنتجات\n              </Link>\n              {isAdmin && (\n                <Link\n                  href=\"/admin\"\n                  className=\"text-gray-700 hover:text-primary-600 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 flex items-center\"\n                >\n                  ⚙️ لوحة التحكم\n                </Link>\n              )}\n            </div>\n          </div>\n\n          {/* Right side - Cart and Auth */}\n          <div className=\"flex items-center space-x-4 space-x-reverse\">\n            {/* Cart */}\n            <Link\n              href=\"/cart\"\n              className=\"relative text-gray-700 hover:text-primary-600 transition-colors duration-200\"\n            >\n              <span className=\"text-2xl\">🛒</span>\n              {cartItemsCount > 0 && (\n                <span className=\"absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center\">\n                  {cartItemsCount}\n                </span>\n              )}\n            </Link>\n\n            {/* Auth Links */}\n            {isAuthenticated ? (\n              <div className=\"relative\">\n                <button\n                  onClick={() => setShowUserMenu(!showUserMenu)}\n                  className=\"flex items-center space-x-2 space-x-reverse text-gray-700 hover:text-primary-600 px-3 py-1 rounded text-sm transition-colors duration-200\"\n                >\n                  <User className=\"h-4 w-4\" />\n                  <span>مرحباً، {appUser?.name || 'المستخدم'}</span>\n                </button>\n\n                {showUserMenu && (\n                  <div className=\"absolute left-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 border border-gray-200\">\n                    <Link\n                      href=\"/profile\"\n                      className=\"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                      onClick={() => setShowUserMenu(false)}\n                    >\n                      <User className=\"h-4 w-4 ml-2\" />\n                      الملف الشخصي\n                    </Link>\n                    <Link\n                      href=\"/orders\"\n                      className=\"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                      onClick={() => setShowUserMenu(false)}\n                    >\n                      <ShoppingBag className=\"h-4 w-4 ml-2\" />\n                      طلباتي\n                    </Link>\n                    {isAdmin && (\n                      <Link\n                        href=\"/admin\"\n                        className=\"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                        onClick={() => setShowUserMenu(false)}\n                      >\n                        <Settings className=\"h-4 w-4 ml-2\" />\n                        لوحة التحكم\n                      </Link>\n                    )}\n                    <hr className=\"my-1\" />\n                    <button\n                      onClick={handleSignOut}\n                      className=\"flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50\"\n                    >\n                      <LogOut className=\"h-4 w-4 ml-2\" />\n                      تسجيل الخروج\n                    </button>\n                  </div>\n                )}\n              </div>\n            ) : (\n              <div className=\"flex items-center space-x-2 space-x-reverse\">\n                <Link\n                  href=\"/auth/login\"\n                  className=\"text-gray-700 hover:text-primary-600 px-3 py-1 rounded text-sm transition-colors duration-200\"\n                >\n                  تسجيل الدخول\n                </Link>\n                <Link\n                  href=\"/auth/register\"\n                  className=\"bg-primary-600 hover:bg-primary-700 text-white px-3 py-1 rounded text-sm transition-colors duration-200\"\n                >\n                  إنشاء حساب\n                </Link>\n              </div>\n            )}\n          </div>\n\n          {/* Mobile menu button */}\n          <div className=\"md:hidden\">\n            <button\n              type=\"button\"\n              className=\"text-gray-700 hover:text-primary-600 focus:outline-none focus:text-primary-600\"\n            >\n              <svg className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\n              </svg>\n            </button>\n          </div>\n        </div>\n      </div>\n    </nav>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;;;AANA;;;;;;AAQe,SAAS;;IACtB,MAAM,EAAE,eAAe,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAC7D,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACxB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,iBAAiB,MAAM,MAAM,CAAC,CAAC,OAAO,OAAS,QAAQ,KAAK,QAAQ,EAAE;IAE5E,MAAM,gBAAgB;QACpB,MAAM;QACN,gBAAgB;IAClB;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;sCAAsC;;;;;;;;;;;kCAMjE,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;gCAGA,yBACC,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;kCAQP,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;kDAEV,6LAAC;wCAAK,WAAU;kDAAW;;;;;;oCAC1B,iBAAiB,mBAChB,6LAAC;wCAAK,WAAU;kDACb;;;;;;;;;;;;4BAMN,gCACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,SAAS,IAAM,gBAAgB,CAAC;wCAChC,WAAU;;0DAEV,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,6LAAC;;oDAAK;oDAAS,SAAS,QAAQ;;;;;;;;;;;;;oCAGjC,8BACC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,gBAAgB;;kEAE/B,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGnC,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,gBAAgB;;kEAE/B,6LAAC,uNAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;4CAGzC,yBACC,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,gBAAgB;;kEAE/B,6LAAC,6MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAIzC,6LAAC;gDAAG,WAAU;;;;;;0DACd,6LAAC;gDACC,SAAS;gDACT,WAAU;;kEAEV,6LAAC,6MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;qDAO3C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;kCAQP,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,MAAK;4BACL,WAAU;sCAEV,cAAA,6LAAC;gCAAI,WAAU;gCAAU,MAAK;gCAAO,SAAQ;gCAAY,QAAO;0CAC9D,cAAA,6LAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQrF;GAnJwB;;QACiC,kIAAA,CAAA,UAAO;QAC5C,kIAAA,CAAA,UAAO;;;KAFH"}}, {"offset": {"line": 355, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 361, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/New%20folder%20%288%29/arabic-ecommerce/src/components/ProductCard.tsx"], "sourcesContent": ["'use client';\n\nimport { Product } from '@/types';\nimport Image from 'next/image';\nimport Link from 'next/link';\n\ninterface ProductCardProps {\n  product: Product;\n  onAddToCart?: (productId: string) => void;\n}\n\nexport default function ProductCard({ product, onAddToCart }: ProductCardProps) {\n  const handleAddToCart = () => {\n    if (onAddToCart) {\n      onAddToCart(product.id);\n    }\n  };\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 overflow-hidden\">\n      {/* Product Image */}\n      <div className=\"relative h-48 bg-gray-200\">\n        <Image\n          src={product.image}\n          alt={product.name}\n          fill\n          className=\"object-cover\"\n          onError={(e) => {\n            // Fallback image if the original fails to load\n            const target = e.target as HTMLImageElement;\n            target.src = '/images/placeholder.jpg';\n          }}\n        />\n        {product.featured && (\n          <div className=\"absolute top-2 right-2 bg-yellow-500 text-white px-2 py-1 rounded text-xs font-bold\">\n            مميز\n          </div>\n        )}\n        {product.stock === 0 && (\n          <div className=\"absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center\">\n            <span className=\"text-white font-bold text-lg\">نفد المخزون</span>\n          </div>\n        )}\n      </div>\n\n      {/* Product Info */}\n      <div className=\"p-4\">\n        <div className=\"mb-2\">\n          <span className=\"text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded\">\n            {product.category}\n          </span>\n        </div>\n        \n        <h3 className=\"text-lg font-semibold text-gray-800 mb-2 line-clamp-2\">\n          {product.name}\n        </h3>\n        \n        <p className=\"text-gray-600 text-sm mb-3 line-clamp-2\">\n          {product.description}\n        </p>\n\n        <div className=\"flex items-center justify-between mb-3\">\n          <span className=\"text-2xl font-bold text-primary-600\">\n            {product.price.toLocaleString('ar-SA')} ر.س\n          </span>\n          <span className=\"text-sm text-gray-500\">\n            متوفر: {product.stock}\n          </span>\n        </div>\n\n        {/* Action Buttons */}\n        <div className=\"flex space-x-2 space-x-reverse\">\n          <button\n            onClick={handleAddToCart}\n            disabled={product.stock === 0}\n            className={`flex-1 py-2 px-4 rounded-lg font-medium transition-colors duration-200 ${\n              product.stock === 0\n                ? 'bg-gray-300 text-gray-500 cursor-not-allowed'\n                : 'bg-primary-600 hover:bg-primary-700 text-white'\n            }`}\n          >\n            {product.stock === 0 ? 'نفد المخزون' : 'أضف للسلة'}\n          </button>\n          \n          <Link\n            href={`/products/${product.id}`}\n            className=\"px-4 py-2 border border-primary-600 text-primary-600 hover:bg-primary-600 hover:text-white rounded-lg font-medium transition-colors duration-200\"\n          >\n            عرض\n          </Link>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAJA;;;;AAWe,SAAS,YAAY,EAAE,OAAO,EAAE,WAAW,EAAoB;IAC5E,MAAM,kBAAkB;QACtB,IAAI,aAAa;YACf,YAAY,QAAQ,EAAE;QACxB;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,gIAAA,CAAA,UAAK;wBACJ,KAAK,QAAQ,KAAK;wBAClB,KAAK,QAAQ,IAAI;wBACjB,IAAI;wBACJ,WAAU;wBACV,SAAS,CAAC;4BACR,+CAA+C;4BAC/C,MAAM,SAAS,EAAE,MAAM;4BACvB,OAAO,GAAG,GAAG;wBACf;;;;;;oBAED,QAAQ,QAAQ,kBACf,6LAAC;wBAAI,WAAU;kCAAsF;;;;;;oBAItG,QAAQ,KAAK,KAAK,mBACjB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAK,WAAU;sCAA+B;;;;;;;;;;;;;;;;;0BAMrD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAK,WAAU;sCACb,QAAQ,QAAQ;;;;;;;;;;;kCAIrB,6LAAC;wBAAG,WAAU;kCACX,QAAQ,IAAI;;;;;;kCAGf,6LAAC;wBAAE,WAAU;kCACV,QAAQ,WAAW;;;;;;kCAGtB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;;oCACb,QAAQ,KAAK,CAAC,cAAc,CAAC;oCAAS;;;;;;;0CAEzC,6LAAC;gCAAK,WAAU;;oCAAwB;oCAC9B,QAAQ,KAAK;;;;;;;;;;;;;kCAKzB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS;gCACT,UAAU,QAAQ,KAAK,KAAK;gCAC5B,WAAW,CAAC,uEAAuE,EACjF,QAAQ,KAAK,KAAK,IACd,iDACA,kDACJ;0CAED,QAAQ,KAAK,KAAK,IAAI,gBAAgB;;;;;;0CAGzC,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAM,CAAC,UAAU,EAAE,QAAQ,EAAE,EAAE;gCAC/B,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;AAOX;KAnFwB"}}, {"offset": {"line": 539, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 545, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/New%20folder%20%288%29/arabic-ecommerce/src/hooks/useFilteredProducts.ts"], "sourcesContent": ["import React, { useState, useEffect, useMemo } from 'react';\nimport { Product } from '@/types';\nimport { useProducts } from '@/contexts/ProductContext';\n\nexport interface FilterOptions {\n  category?: string;\n  minPrice?: number;\n  maxPrice?: number;\n  searchTerm?: string;\n  sortBy?: 'name' | 'price' | 'date';\n  sortOrder?: 'asc' | 'desc';\n}\n\nexport function useFilteredProducts(initialFilters: FilterOptions = {}) {\n  const { products, loading } = useProducts();\n  const [filters, setFilters] = useState<FilterOptions>(initialFilters);\n\n  const filteredProducts = useMemo(() => {\n    if (!products) return [];\n\n    let filtered = [...products];\n\n    // تطبيق فلتر التصنيف\n    if (filters.category && filters.category !== 'all') {\n      filtered = filtered.filter(product => product.category === filters.category);\n    }\n\n    // تطبيق فلتر السعر\n    if (filters.minPrice !== undefined) {\n      filtered = filtered.filter(product => product.price >= filters.minPrice!);\n    }\n\n    if (filters.maxPrice !== undefined) {\n      filtered = filtered.filter(product => product.price <= filters.maxPrice!);\n    }\n\n    // تطبيق فلتر البحث\n    if (filters.searchTerm) {\n      const searchLower = filters.searchTerm.toLowerCase();\n      filtered = filtered.filter(product =>\n        product.name.toLowerCase().includes(searchLower) ||\n        product.description.toLowerCase().includes(searchLower)\n      );\n    }\n\n    // تطبيق الترتيب\n    if (filters.sortBy) {\n      filtered.sort((a, b) => {\n        let aValue: any;\n        let bValue: any;\n\n        switch (filters.sortBy) {\n          case 'name':\n            aValue = a.name.toLowerCase();\n            bValue = b.name.toLowerCase();\n            break;\n          case 'price':\n            aValue = a.price;\n            bValue = b.price;\n            break;\n          case 'date':\n            aValue = new Date(a.createdAt || 0);\n            bValue = new Date(b.createdAt || 0);\n            break;\n          default:\n            return 0;\n        }\n\n        if (aValue < bValue) {\n          return filters.sortOrder === 'desc' ? 1 : -1;\n        }\n        if (aValue > bValue) {\n          return filters.sortOrder === 'desc' ? -1 : 1;\n        }\n        return 0;\n      });\n    }\n\n    return filtered;\n  }, [products, filters]);\n\n  const updateFilters = (newFilters: Partial<FilterOptions>) => {\n    setFilters(prev => ({ ...prev, ...newFilters }));\n  };\n\n  const resetFilters = () => {\n    setFilters({});\n  };\n\n  return {\n    products: filteredProducts,\n    loading,\n    filters,\n    updateFilters,\n    resetFilters,\n    totalCount: filteredProducts.length,\n    allProducts: products || []\n  };\n}\n"], "names": [], "mappings": ";;;AAAA;AAEA;;;;AAWO,SAAS,oBAAoB,iBAAgC,CAAC,CAAC;;IACpE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IACxC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAEtD,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;yDAAE;YAC/B,IAAI,CAAC,UAAU,OAAO,EAAE;YAExB,IAAI,WAAW;mBAAI;aAAS;YAE5B,qBAAqB;YACrB,IAAI,QAAQ,QAAQ,IAAI,QAAQ,QAAQ,KAAK,OAAO;gBAClD,WAAW,SAAS,MAAM;qEAAC,CAAA,UAAW,QAAQ,QAAQ,KAAK,QAAQ,QAAQ;;YAC7E;YAEA,mBAAmB;YACnB,IAAI,QAAQ,QAAQ,KAAK,WAAW;gBAClC,WAAW,SAAS,MAAM;qEAAC,CAAA,UAAW,QAAQ,KAAK,IAAI,QAAQ,QAAQ;;YACzE;YAEA,IAAI,QAAQ,QAAQ,KAAK,WAAW;gBAClC,WAAW,SAAS,MAAM;qEAAC,CAAA,UAAW,QAAQ,KAAK,IAAI,QAAQ,QAAQ;;YACzE;YAEA,mBAAmB;YACnB,IAAI,QAAQ,UAAU,EAAE;gBACtB,MAAM,cAAc,QAAQ,UAAU,CAAC,WAAW;gBAClD,WAAW,SAAS,MAAM;qEAAC,CAAA,UACzB,QAAQ,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,gBACpC,QAAQ,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC;;YAE/C;YAEA,gBAAgB;YAChB,IAAI,QAAQ,MAAM,EAAE;gBAClB,SAAS,IAAI;qEAAC,CAAC,GAAG;wBAChB,IAAI;wBACJ,IAAI;wBAEJ,OAAQ,QAAQ,MAAM;4BACpB,KAAK;gCACH,SAAS,EAAE,IAAI,CAAC,WAAW;gCAC3B,SAAS,EAAE,IAAI,CAAC,WAAW;gCAC3B;4BACF,KAAK;gCACH,SAAS,EAAE,KAAK;gCAChB,SAAS,EAAE,KAAK;gCAChB;4BACF,KAAK;gCACH,SAAS,IAAI,KAAK,EAAE,SAAS,IAAI;gCACjC,SAAS,IAAI,KAAK,EAAE,SAAS,IAAI;gCACjC;4BACF;gCACE,OAAO;wBACX;wBAEA,IAAI,SAAS,QAAQ;4BACnB,OAAO,QAAQ,SAAS,KAAK,SAAS,IAAI,CAAC;wBAC7C;wBACA,IAAI,SAAS,QAAQ;4BACnB,OAAO,QAAQ,SAAS,KAAK,SAAS,CAAC,IAAI;wBAC7C;wBACA,OAAO;oBACT;;YACF;YAEA,OAAO;QACT;wDAAG;QAAC;QAAU;KAAQ;IAEtB,MAAM,gBAAgB,CAAC;QACrB,WAAW,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,GAAG,UAAU;YAAC,CAAC;IAChD;IAEA,MAAM,eAAe;QACnB,WAAW,CAAC;IACd;IAEA,OAAO;QACL,UAAU;QACV;QACA;QACA;QACA;QACA,YAAY,iBAAiB,MAAM;QACnC,aAAa,YAAY,EAAE;IAC7B;AACF;GArFgB;;QACgB,qIAAA,CAAA,cAAW"}}, {"offset": {"line": 652, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 658, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/New%20folder%20%288%29/arabic-ecommerce/src/app/products/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport Navbar from '@/components/Navbar';\nimport ProductCard from '@/components/ProductCard';\nimport { useProducts } from '@/contexts/ProductContext';\nimport { useCategories } from '@/contexts/CategoryContext';\nimport { useCart } from '@/contexts/CartContext';\nimport { useFilteredProducts } from '@/hooks/useFilteredProducts';\n\nexport default function ProductsPage() {\n  const [selectedCategory, setSelectedCategory] = useState<string>('');\n  const [priceRange, setPriceRange] = useState<string>('');\n  const [searchTerm, setSearchTerm] = useState<string>('');\n\n  const { products, loading: isLoading } = useProducts();\n  const { getActiveCategories } = useCategories();\n  const { addToCart } = useCart();\n  const activeCategories = getActiveCategories();\n\n  // تحويل priceRange إلى minPrice و maxPrice\n  const getPriceRange = (range: string) => {\n    switch (range) {\n      case 'under-500':\n        return { maxPrice: 500 };\n      case '500-1000':\n        return { minPrice: 500, maxPrice: 1000 };\n      case '1000-3000':\n        return { minPrice: 1000, maxPrice: 3000 };\n      case 'over-3000':\n        return { minPrice: 3000 };\n      default:\n        return {};\n    }\n  };\n\n  const { products: filteredProducts } = useFilteredProducts({\n    category: selectedCategory || undefined,\n    searchTerm: searchTerm || undefined,\n    ...getPriceRange(priceRange),\n  });\n\n  const handleAddToCart = (productId: string) => {\n    const product = products.find(p => p.id === productId);\n    if (product) {\n      addToCart(product);\n      alert('تم إضافة المنتج إلى السلة!');\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Navbar />\n\n      {/* Page Header */}\n      <div className=\"bg-white shadow-sm\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n          <h1 className=\"text-3xl font-bold text-gray-800 mb-4\">\n            جميع المنتجات\n          </h1>\n          <p className=\"text-gray-600\">\n            اكتشف مجموعتنا الواسعة من المنتجات عالية الجودة\n          </p>\n        </div>\n      </div>\n\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <div className=\"flex flex-col lg:flex-row gap-8\">\n          {/* Filters Sidebar */}\n          <div className=\"lg:w-1/4\">\n            <div className=\"bg-white rounded-lg shadow-md p-6 sticky top-4\">\n              <h3 className=\"text-lg font-semibold mb-4\">تصفية المنتجات</h3>\n\n              {/* Search */}\n              <div className=\"mb-6\">\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  البحث\n                </label>\n                <input\n                  type=\"text\"\n                  value={searchTerm}\n                  onChange={(e) => setSearchTerm(e.target.value)}\n                  placeholder=\"ابحث عن منتج...\"\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500\"\n                />\n              </div>\n\n              {/* Category Filter */}\n              <div className=\"mb-6\">\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  التصنيف\n                </label>\n                <select\n                  value={selectedCategory}\n                  onChange={(e) => setSelectedCategory(e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500\"\n                >\n                  <option value=\"\">جميع التصنيفات</option>\n                  {activeCategories.map((category) => (\n                    <option key={category.id} value={category.name}>\n                      {category.name}\n                    </option>\n                  ))}\n                </select>\n              </div>\n\n              {/* Price Filter */}\n              <div className=\"mb-6\">\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  نطاق السعر\n                </label>\n                <select\n                  value={priceRange}\n                  onChange={(e) => setPriceRange(e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500\"\n                >\n                  <option value=\"\">جميع الأسعار</option>\n                  <option value=\"under-500\">أقل من 500 ر.س</option>\n                  <option value=\"500-1000\">500 - 1000 ر.س</option>\n                  <option value=\"1000-3000\">1000 - 3000 ر.س</option>\n                  <option value=\"over-3000\">أكثر من 3000 ر.س</option>\n                </select>\n              </div>\n\n              {/* Clear Filters */}\n              <button\n                onClick={() => {\n                  setSelectedCategory('');\n                  setPriceRange('');\n                  setSearchTerm('');\n                }}\n                className=\"w-full bg-gray-200 hover:bg-gray-300 text-gray-700 font-medium py-2 px-4 rounded-lg transition-colors duration-200\"\n              >\n                مسح الفلاتر\n              </button>\n            </div>\n          </div>\n\n          {/* Products Grid */}\n          <div className=\"lg:w-3/4\">\n            <div className=\"flex justify-between items-center mb-6\">\n              <p className=\"text-gray-600\">\n                {isLoading ? (\n                  <span className=\"animate-pulse\">جاري التحميل...</span>\n                ) : (\n                  `عرض ${filteredProducts.length} من ${products.length} منتج`\n                )}\n              </p>\n            </div>\n\n            {isLoading ? (\n              <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6\">\n                {[...Array(6)].map((_, index) => (\n                  <div key={index} className=\"bg-white rounded-lg shadow-md overflow-hidden animate-pulse\">\n                    <div className=\"h-48 bg-gray-200\"></div>\n                    <div className=\"p-4\">\n                      <div className=\"h-4 bg-gray-200 rounded mb-2\"></div>\n                      <div className=\"h-6 bg-gray-200 rounded mb-2\"></div>\n                      <div className=\"h-4 bg-gray-200 rounded mb-3\"></div>\n                      <div className=\"flex justify-between mb-3\">\n                        <div className=\"h-6 bg-gray-200 rounded w-20\"></div>\n                        <div className=\"h-4 bg-gray-200 rounded w-16\"></div>\n                      </div>\n                      <div className=\"flex space-x-2 space-x-reverse\">\n                        <div className=\"flex-1 h-10 bg-gray-200 rounded\"></div>\n                        <div className=\"w-16 h-10 bg-gray-200 rounded\"></div>\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            ) : filteredProducts.length === 0 ? (\n              <div className=\"text-center py-12\">\n                <div className=\"text-6xl mb-4\">🔍</div>\n                <h3 className=\"text-xl font-semibold text-gray-700 mb-2\">\n                  لم يتم العثور على منتجات\n                </h3>\n                <p className=\"text-gray-500\">\n                  جرب تغيير معايير البحث أو الفلاتر\n                </p>\n              </div>\n            ) : (\n              <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6\">\n                {filteredProducts.map((product) => (\n                  <ProductCard\n                    key={product.id}\n                    product={product}\n                    onAddToCart={handleAddToCart}\n                  />\n                ))}\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;AARA;;;;;;;;AAUe,SAAS;;IACtB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACjE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACrD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAErD,MAAM,EAAE,QAAQ,EAAE,SAAS,SAAS,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IACnD,MAAM,EAAE,mBAAmB,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,gBAAa,AAAD;IAC5C,MAAM,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAC5B,MAAM,mBAAmB;IAEzB,2CAA2C;IAC3C,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,OAAO;oBAAE,UAAU;gBAAI;YACzB,KAAK;gBACH,OAAO;oBAAE,UAAU;oBAAK,UAAU;gBAAK;YACzC,KAAK;gBACH,OAAO;oBAAE,UAAU;oBAAM,UAAU;gBAAK;YAC1C,KAAK;gBACH,OAAO;oBAAE,UAAU;gBAAK;YAC1B;gBACE,OAAO,CAAC;QACZ;IACF;IAEA,MAAM,EAAE,UAAU,gBAAgB,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,sBAAmB,AAAD,EAAE;QACzD,UAAU,oBAAoB;QAC9B,YAAY,cAAc;QAC1B,GAAG,cAAc,WAAW;IAC9B;IAEA,MAAM,kBAAkB,CAAC;QACvB,MAAM,UAAU,SAAS,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAC5C,IAAI,SAAS;YACX,UAAU;YACV,MAAM;QACR;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,+HAAA,CAAA,UAAM;;;;;0BAGP,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAwC;;;;;;sCAGtD,6LAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;;;;;;0BAMjC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAG3C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,6LAAC;gDACC,MAAK;gDACL,OAAO;gDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gDAC7C,aAAY;gDACZ,WAAU;;;;;;;;;;;;kDAKd,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,6LAAC;gDACC,OAAO;gDACP,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK;gDACnD,WAAU;;kEAEV,6LAAC;wDAAO,OAAM;kEAAG;;;;;;oDAChB,iBAAiB,GAAG,CAAC,CAAC,yBACrB,6LAAC;4DAAyB,OAAO,SAAS,IAAI;sEAC3C,SAAS,IAAI;2DADH,SAAS,EAAE;;;;;;;;;;;;;;;;;kDAQ9B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,6LAAC;gDACC,OAAO;gDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gDAC7C,WAAU;;kEAEV,6LAAC;wDAAO,OAAM;kEAAG;;;;;;kEACjB,6LAAC;wDAAO,OAAM;kEAAY;;;;;;kEAC1B,6LAAC;wDAAO,OAAM;kEAAW;;;;;;kEACzB,6LAAC;wDAAO,OAAM;kEAAY;;;;;;kEAC1B,6LAAC;wDAAO,OAAM;kEAAY;;;;;;;;;;;;;;;;;;kDAK9B,6LAAC;wCACC,SAAS;4CACP,oBAAoB;4CACpB,cAAc;4CACd,cAAc;wCAChB;wCACA,WAAU;kDACX;;;;;;;;;;;;;;;;;sCAOL,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAE,WAAU;kDACV,0BACC,6LAAC;4CAAK,WAAU;sDAAgB;;;;;mDAEhC,CAAC,IAAI,EAAE,iBAAiB,MAAM,CAAC,IAAI,EAAE,SAAS,MAAM,CAAC,KAAK,CAAC;;;;;;;;;;;gCAKhE,0BACC,6LAAC;oCAAI,WAAU;8CACZ;2CAAI,MAAM;qCAAG,CAAC,GAAG,CAAC,CAAC,GAAG,sBACrB,6LAAC;4CAAgB,WAAU;;8DACzB,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;;;;;sEACf,6LAAC;4DAAI,WAAU;;;;;;sEACf,6LAAC;4DAAI,WAAU;;;;;;sEACf,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;;;;;8EACf,6LAAC;oEAAI,WAAU;;;;;;;;;;;;sEAEjB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;;;;;8EACf,6LAAC;oEAAI,WAAU;;;;;;;;;;;;;;;;;;;2CAZX;;;;;;;;;2CAkBZ,iBAAiB,MAAM,KAAK,kBAC9B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAAgB;;;;;;sDAC/B,6LAAC;4CAAG,WAAU;sDAA2C;;;;;;sDAGzD,6LAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;yDAK/B,6LAAC;oCAAI,WAAU;8CACZ,iBAAiB,GAAG,CAAC,CAAC,wBACrB,6LAAC,oIAAA,CAAA,UAAW;4CAEV,SAAS;4CACT,aAAa;2CAFR,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYnC;GA3LwB;;QAKmB,qIAAA,CAAA,cAAW;QACpB,sIAAA,CAAA,gBAAa;QACvB,kIAAA,CAAA,UAAO;QAmBU,sIAAA,CAAA,sBAAmB;;;KA1BpC"}}, {"offset": {"line": 1157, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}